<?php
/**
 * FileUpload Package - Method Chaining Usage Examples
 * 
 * This file shows practical examples of how to use the method chaining
 * configuration system similar to your template example:
 * ->setSearchConfig(true, 'templateSearch')
 */

// Include the FileUpload package (adjust path as needed)
// require_once 'package/FileUpload/vendor/autoload.php';

use Package\FileUpload\FileUploadManager;

echo "🔗 FileUpload Method Chaining - Practical Usage Examples\n";
echo "========================================================\n\n";

// ============================================================================
// EXAMPLE 1: Avatar Upload Configuration
// ============================================================================
echo "📸 EXAMPLE 1: Avatar Upload Configuration\n";
echo "-----------------------------------------\n";

echo "// Configure uploader for avatar uploads\n";
echo "\$avatarUploader = FileUploadManager::createWithChaining()\n";
echo "    ->setImageProcessing(true, 95)              // Enable high quality processing\n";
echo "    ->setThumbnailGeneration(true, [\n";
echo "        ['name' => 'thumb', 'width' => 100, 'height' => 100, 'mode' => 'crop'],\n";
echo "        ['name' => 'profile', 'width' => 200, 'height' => 200, 'mode' => 'crop']\n";
echo "    ])\n";
echo "    ->setImageCompression(true, 95)             // High quality compression\n";
echo "    ->setMetadataStripping(true)                // Remove EXIF data\n";
echo "    ->setValidation(true, [\n";
echo "        'max_file_size' => 1048576,             // 1MB limit\n";
echo "        'allowed_extensions' => ['jpg', 'jpeg', 'png']\n";
echo "    ])\n";
echo "    ->setSecurityScan(true)                     // Enable security scanning\n";
echo "    ->setUploadDirectory('uploads/avatars')     // Custom directory\n";
echo "    ->setAllowedExtensions(['jpg', 'jpeg', 'png'])\n";
echo "    ->setMaxFileSize(1048576);                  // 1MB\n\n";

// ============================================================================
// EXAMPLE 2: Document Upload Configuration
// ============================================================================
echo "📄 EXAMPLE 2: Document Upload Configuration\n";
echo "-------------------------------------------\n";

echo "// Configure uploader for document uploads (no image processing)\n";
echo "\$documentUploader = FileUploadManager::createWithChaining()\n";
echo "    ->setImageProcessing(false)                 // Disable image processing\n";
echo "    ->setThumbnailGeneration(false)             // No thumbnails for documents\n";
echo "    ->setImageCompression(false)                // No compression\n";
echo "    ->setMetadataStripping(false)               // Keep document metadata\n";
echo "    ->setValidation(true, [\n";
echo "        'max_file_size' => 10485760,            // 10MB limit\n";
echo "        'allowed_extensions' => ['pdf', 'doc', 'docx', 'txt']\n";
echo "    ])\n";
echo "    ->setSecurityScan(true)                     // Enable security scanning\n";
echo "    ->setUploadDirectory('uploads/documents')   // Documents directory\n";
echo "    ->setAllowedExtensions(['pdf', 'doc', 'docx', 'txt'])\n";
echo "    ->setMaxFileSize(10485760);                 // 10MB\n\n";

// ============================================================================
// EXAMPLE 3: Gallery Upload Configuration
// ============================================================================
echo "🖼️ EXAMPLE 3: Gallery Upload Configuration\n";
echo "-------------------------------------------\n";

echo "// Configure uploader for gallery images with multiple thumbnails\n";
echo "\$galleryUploader = FileUploadManager::createWithChaining()\n";
echo "    ->setImageProcessing(true, 90)              // Enable processing\n";
echo "    ->setThumbnailGeneration(true, [\n";
echo "        ['name' => 'thumb', 'width' => 200, 'height' => 200, 'mode' => 'crop'],\n";
echo "        ['name' => 'medium', 'width' => 600, 'height' => 400, 'mode' => 'fit'],\n";
echo "        ['name' => 'large', 'width' => 1200, 'height' => 800, 'mode' => 'fit']\n";
echo "    ])\n";
echo "    ->setImageCompression(true, 90)             // Good quality compression\n";
echo "    ->setMetadataStripping(true)                // Remove metadata\n";
echo "    ->setValidation(true, [\n";
echo "        'max_file_size' => 20971520,            // 20MB limit\n";
echo "        'allowed_extensions' => ['jpg', 'jpeg', 'png', 'gif', 'webp']\n";
echo "    ])\n";
echo "    ->setSecurityScan(false)                    // Disable for performance\n";
echo "    ->setUploadDirectory('uploads/gallery')     // Gallery directory\n";
echo "    ->setAllowedExtensions(['jpg', 'jpeg', 'png', 'gif', 'webp'])\n";
echo "    ->setMaxFileSize(20971520);                 // 20MB\n\n";

// ============================================================================
// EXAMPLE 4: Quick Upload Configuration (Minimal Processing)
// ============================================================================
echo "⚡ EXAMPLE 4: Quick Upload Configuration\n";
echo "---------------------------------------\n";

echo "// Configure uploader for quick uploads with minimal processing\n";
echo "\$quickUploader = FileUploadManager::createWithChaining()\n";
echo "    ->setImageProcessing(false)                 // Disable for speed\n";
echo "    ->setThumbnailGeneration(false)             // No thumbnails\n";
echo "    ->setImageCompression(false)                // No compression\n";
echo "    ->setMetadataStripping(false)               // Keep metadata\n";
echo "    ->setValidation(false)                      // Skip validation for speed\n";
echo "    ->setSecurityScan(false)                    // Skip security scan\n";
echo "    ->setUploadDirectory('uploads/temp')        // Temp directory\n";
echo "    ->setAllowedExtensions(['jpg', 'png', 'gif', 'pdf', 'txt'])\n";
echo "    ->setMaxFileSize(52428800);                 // 50MB\n\n";

// ============================================================================
// EXAMPLE 5: Conditional Configuration Based on File Type
// ============================================================================
echo "🎯 EXAMPLE 5: Conditional Configuration\n";
echo "---------------------------------------\n";

echo "// Configure uploader based on file type\n";
echo "\$fileExtension = pathinfo(\$_FILES['file']['name'], PATHINFO_EXTENSION);\n";
echo "\$isImage = in_array(strtolower(\$fileExtension), ['jpg', 'jpeg', 'png', 'gif', 'webp']);\n";
echo "\$isPdf = strtolower(\$fileExtension) === 'pdf';\n";
echo "\$isDocument = in_array(strtolower(\$fileExtension), ['doc', 'docx', 'txt']);\n\n";

echo "\$conditionalUploader = FileUploadManager::createWithChaining()\n";
echo "    ->setImageProcessing(\$isImage, 85)          // Only process images\n";
echo "    ->setThumbnailGeneration(\$isImage, [\n";
echo "        ['name' => 'thumb', 'width' => 150, 'height' => 150, 'mode' => 'crop']\n";
echo "    ])\n";
echo "    ->setImageCompression(\$isImage, 80)         // Only compress images\n";
echo "    ->setMetadataStripping(\$isImage)            // Only strip image metadata\n";
echo "    ->setValidation(true)                       // Always validate\n";
echo "    ->setSecurityScan(\$isPdf || \$isDocument)   // Scan documents and PDFs\n";
echo "    ->setUploadDirectory('uploads/mixed')       // Mixed content directory\n";
echo "    ->setMaxFileSize(\$isImage ? 10485760 : 20971520); // 10MB for images, 20MB for others\n\n";

// ============================================================================
// EXAMPLE 6: Environment-Based Configuration
// ============================================================================
echo "🌍 EXAMPLE 6: Environment-Based Configuration\n";
echo "---------------------------------------------\n";

echo "// Configure based on environment variables or settings\n";
echo "\$isDevelopment = (\$_ENV['APP_ENV'] ?? 'production') === 'development';\n";
echo "\$isProduction = (\$_ENV['APP_ENV'] ?? 'production') === 'production';\n\n";

echo "\$envUploader = FileUploadManager::createWithChaining()\n";
echo "    ->setImageProcessing(true, \$isProduction ? 85 : 95)  // Higher quality in dev\n";
echo "    ->setThumbnailGeneration(true)\n";
echo "    ->setImageCompression(true, \$isProduction ? 80 : 90) // Less compression in dev\n";
echo "    ->setMetadataStripping(\$isProduction)               // Strip in production only\n";
echo "    ->setValidation(true)\n";
echo "    ->setSecurityScan(\$isProduction)                    // Scan in production only\n";
echo "    ->setUploadDirectory(\$isDevelopment ? 'uploads/dev' : 'uploads/prod')\n";
echo "    ->setMaxFileSize(\$isDevelopment ? 52428800 : 10485760); // 50MB dev, 10MB prod\n\n";

// ============================================================================
// EXAMPLE 7: User Role-Based Configuration
// ============================================================================
echo "👤 EXAMPLE 7: User Role-Based Configuration\n";
echo "-------------------------------------------\n";

echo "// Configure based on user role or permissions\n";
echo "\$userRole = getCurrentUserRole(); // Your function to get user role\n";
echo "\$isAdmin = \$userRole === 'admin';\n";
echo "\$isPremium = in_array(\$userRole, ['admin', 'premium']);\n\n";

echo "\$roleBasedUploader = FileUploadManager::createWithChaining()\n";
echo "    ->setImageProcessing(true, \$isPremium ? 95 : 80)     // Higher quality for premium\n";
echo "    ->setThumbnailGeneration(\$isPremium)                // Thumbnails for premium only\n";
echo "    ->setImageCompression(true, \$isPremium ? 90 : 70)   // Better compression for premium\n";
echo "    ->setMetadataStripping(true)\n";
echo "    ->setValidation(true)\n";
echo "    ->setSecurityScan(true)\n";
echo "    ->setUploadDirectory(\$isAdmin ? 'uploads/admin' : 'uploads/users')\n";
echo "    ->setMaxFileSize(\$isAdmin ? 52428800 : (\$isPremium ? 20971520 : 5242880)); // 50MB admin, 20MB premium, 5MB basic\n\n";

// ============================================================================
// EXAMPLE 8: Complete Working Example
// ============================================================================
echo "💻 EXAMPLE 8: Complete Working Example\n";
echo "--------------------------------------\n";

echo "<?php\n";
echo "// Complete example with file upload handling\n\n";

echo "if (\$_SERVER['REQUEST_METHOD'] === 'POST' && isset(\$_FILES['upload'])) {\n";
echo "    try {\n";
echo "        // Configure uploader with method chaining\n";
echo "        \$uploader = FileUploadManager::createWithChaining()\n";
echo "            ->setImageProcessing(true, 90)\n";
echo "            ->setThumbnailGeneration(true, [\n";
echo "                ['name' => 'thumb', 'width' => 150, 'height' => 150, 'mode' => 'crop'],\n";
echo "                ['name' => 'medium', 'width' => 300, 'height' => 300, 'mode' => 'fit']\n";
echo "            ])\n";
echo "            ->setImageCompression(true, 85)\n";
echo "            ->setMetadataStripping(true)\n";
echo "            ->setValidation(true, [\n";
echo "                'max_file_size' => 5242880,\n";
echo "                'allowed_extensions' => ['jpg', 'jpeg', 'png', 'gif']\n";
echo "            ])\n";
echo "            ->setSecurityScan(false)\n";
echo "            ->setUploadDirectory('uploads/gallery')\n";
echo "            ->setAllowedExtensions(['jpg', 'jpeg', 'png', 'gif', 'webp'])\n";
echo "            ->setMaxFileSize(10485760);\n\n";

echo "        // Upload the file\n";
echo "        \$result = \$uploader->upload(\$_FILES['upload']);\n\n";

echo "        if (\$result->isSuccess()) {\n";
echo "            echo json_encode([\n";
echo "                'success' => true,\n";
echo "                'message' => 'File uploaded successfully!',\n";
echo "                'url' => \$result->getUrl(),\n";
echo "                'path' => \$result->getPath(),\n";
echo "                'thumbnails' => \$result->getThumbnails()\n";
echo "            ]);\n";
echo "        } else {\n";
echo "            echo json_encode([\n";
echo "                'success' => false,\n";
echo "                'message' => \$result->getMessage(),\n";
echo "                'errors' => \$result->getErrors()\n";
echo "            ]);\n";
echo "        }\n";
echo "    } catch (Exception \$e) {\n";
echo "        echo json_encode([\n";
echo "            'success' => false,\n";
echo "            'message' => 'Upload failed: ' . \$e->getMessage()\n";
echo "        ]);\n";
echo "    }\n";
echo "}\n";

echo "\n🎉 Method Chaining Configuration Complete!\n";
echo "==========================================\n\n";

echo "📋 Available Configuration Methods:\n";
echo "✅ setImageProcessing(bool \$enabled, int \$quality = 85)\n";
echo "✅ setThumbnailGeneration(bool \$enabled, array \$sizes = [])\n";
echo "✅ setImageCompression(bool \$enabled, int \$quality = 85)\n";
echo "✅ setMetadataStripping(bool \$enabled)\n";
echo "✅ setValidation(bool \$enabled, array \$rules = [])\n";
echo "✅ setSecurityScan(bool \$enabled)\n";
echo "✅ setUploadDirectory(string \$directory)\n";
echo "✅ setAllowedExtensions(array \$extensions)\n";
echo "✅ setMaxFileSize(int \$sizeInBytes)\n\n";

echo "🔗 Perfect Method Chaining:\n";
echo "   Each method returns \$this, allowing you to chain multiple configurations\n";
echo "   Pass true/false to enable/disable features as needed\n";
echo "   Similar to your template example: ->setSearchConfig(true, 'templateSearch')\n";
?>
