<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $title ?? 'Error' ?> | <?= isset($_ENV['APP_NAME']) ? $_ENV['APP_NAME'] : 'FrontCMS' ?></title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/styles/atom-one-dark.min.css">
    <style>
        :root {
            --primary: #4f46e5;
            --primary-dark: #4338ca;
            --danger: #ef4444;
            --warning: #f59e0b;
            --success: #10b981;
            --gray-50: #f9fafb;
            --gray-100: #f3f4f6;
            --gray-200: #e5e7eb;
            --gray-300: #d1d5db;
            --gray-400: #9ca3af;
            --gray-500: #6b7280;
            --gray-600: #4b5563;
            --gray-700: #374151;
            --gray-800: #1f2937;
            --gray-900: #111827;
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.5;
            color: var(--gray-800);
            background-color: var(--gray-50);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 1.5rem;
        }
        
        .error-container {
            max-width: 64rem;
            width: 100%;
            background: white;
            border-radius: 0.75rem;
            box-shadow: var(--shadow-lg);
            overflow: hidden;
            margin-bottom: 2rem;
            border: 1px solid var(--gray-200);
        }
        .error-header {
            background: linear-gradient(135deg, var(--primary), var(--primary-dark));
            color: white;
            padding: 2.5rem 2rem;
            position: relative;
            overflow: hidden;
        }
        
        .error-header::after {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            bottom: 0;
            left: 0;
            background: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29-22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM60 91c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM35 41c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2zM12 60c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2z' fill='%23ffffff' fill-opacity='0.1' fill-rule='evenodd'/%3E%3C/svg%3E");
            opacity: 0.5;
            z-index: 0;
        }
        
        .error-code {
            font-size: 6rem;
            font-weight: 800;
            line-height: 1;
            margin-bottom: 0.5rem;
            position: relative;
            z-index: 1;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .error-title {
            font-size: 1.75rem;
            font-weight: 700;
            margin-top: 0.5rem;
            position: relative;
            z-index: 1;
        }
        
        .error-content {
            padding: 2.5rem 2rem;
        }
        
        .error-message {
            color: var(--gray-700);
            margin-bottom: 2rem;
            font-size: 1.125rem;
            line-height: 1.6;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
            max-width: 100%;
        }
        
        .error-actions {
            display: flex;
            gap: 0.75rem;
            justify-content: center;
            margin-bottom: 2rem;
            flex-wrap: wrap;
        }
        
        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 0.625rem 1.25rem;
            border-radius: 0.5rem;
            font-weight: 500;
            text-decoration: none;
            transition: all 0.2s ease-in-out;
            cursor: pointer;
            font-size: 0.9375rem;
            gap: 0.5rem;
            border: 1px solid transparent;
        }
        
        .btn i {
            font-size: 1.1em;
            line-height: 1;
        }
        
        .btn-primary {
            background-color: var(--primary);
            color: white;
            box-shadow: var(--shadow-sm);
        }
        
        .btn-primary:hover {
            background-color: var(--primary-dark);
            transform: translateY(-1px);
            box-shadow: 0 4px 6px -1px rgba(79, 70, 229, 0.2), 0 2px 4px -1px rgba(79, 70, 229, 0.1);
        }
        
        .btn-outline {
            background-color: white;
            color: var(--primary);
            border: 1px solid var(--gray-300);
            box-shadow: var(--shadow-sm);
        }
        
        .btn-outline:hover {
            background-color: var(--gray-50);
            border-color: var(--gray-400);
            transform: translateY(-1px);
        }
        
        .error-details {
            margin-top: 2rem;
            background-color: white;
            border-radius: 0.5rem;
            overflow: hidden;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
            border: 1px solid var(--gray-200);
            display: <?= (defined('DEBUG') && DEBUG) ? 'block' : 'none' ?>;
        }
        
        .error-tabs {
            display: flex;
            border-bottom: 1px solid var(--gray-200);
            background-color: var(--gray-50);
        }
        
        .error-tab {
            padding: 0.75rem 1.25rem;
            font-size: 0.875rem;
            font-weight: 500;
            color: var(--gray-600);
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all 0.2s;
        }
        
        .error-tab.active {
            color: var(--primary);
            border-bottom-color: var(--primary);
            background-color: white;
        }
        
        .error-tab:hover:not(.active) {
            color: var(--gray-800);
            background-color: var(--gray-100);
        }
        
        .tab-content {
            display: none;
            padding: 1.5rem;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .error-info {
            margin-bottom: 1.5rem;
        }
        
        .error-info h4 {
            font-size: 0.9375rem;
            color: var(--gray-700);
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .error-info h4 i {
            color: var(--gray-500);
            font-size: 1.1em;
        }
        
        .error-info pre {
            background-color: var(--gray-50);
            border: 1px solid var(--gray-200);
            border-radius: 0.375rem;
            padding: 1rem;
            font-family: 'Fira Code', 'Fira Mono', 'Menlo', 'Consolas', 'DejaVu Sans Mono', monospace;
            font-size: 0.8125rem;
            line-height: 1.5;
            overflow-x: auto;
            margin: 0;
            white-space: pre-wrap;
            word-wrap: break-word;
        }
        
        .code-block {
            position: relative;
            margin: 1.5rem 0;
            border-radius: 0.375rem;
            overflow: hidden;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
        }
        
        .code-header {
            background-color: var(--gray-800);
            color: var(--gray-300);
            padding: 0.5rem 1rem;
            font-family: 'Fira Code', 'Fira Mono', 'Menlo', 'Consolas', 'DejaVu Sans Mono', monospace;
            font-size: 0.8125rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .code-filename {
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .code-filename i {
            color: var(--gray-500);
        }
        
        .code-lang {
            background-color: rgba(255, 255, 255, 0.1);
            padding: 0.125rem 0.5rem;
            border-radius: 0.25rem;
            font-size: 0.75rem;
        }
        
        pre[class*="language-"] {
            margin: 0;
            border-radius: 0;
            max-height: 300px;
        }
        
        .code-line {
            display: table-row;
        }
        
        .line-number {
            display: table-cell;
            text-align: right;
            padding-right: 1rem;
            user-select: none;
            opacity: 0.5;
            color: var(--gray-400);
            border-right: 1px solid var(--gray-200);
        }
        
        .line-content {
            display: table-cell;
            padding-left: 1rem;
            white-space: pre;
        }
        
        .line-highlight {
            background-color: rgba(254, 243, 199, 0.5);
            border-left: 3px solid var(--warning);
            margin-left: -1rem;
            padding-left: calc(1rem - 3px);
        }
        
        .footer {
            margin-top: 2rem;
            color: var(--gray-500);
            font-size: 0.875rem;
            text-align: center;
            max-width: 64rem;
            width: 100%;
            padding: 0 1rem;
        }
        
        .footer a {
            color: var(--primary);
            text-decoration: none;
            font-weight: 500;
        }
        
        .footer a:hover {
            text-decoration: underline;
        }
        
        @media (max-width: 768px) {
            .error-code {
                font-size: 4.5rem;
            }
            
            .error-title {
                font-size: 1.5rem;
            }
            
            .error-actions {
                flex-direction: column;
                gap: 0.5rem;
            }
            
            .btn {
                width: 100%;
                justify-content: center;
            }
            
            .error-content {
                padding: 1.5rem 1.25rem;
            }
            
            .tab-content {
                padding: 1rem;
                max-height: 300px;
            }
        }
        
        /* Animation */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .error-container {
            animation: fadeIn 0.3s ease-out;
        }
    </style>
</head>
<body>
    <?= $content ?>
    
    <div class="footer">
        &copy; <?= date('Y') ?> <?= isset($_ENV['APP_NAME']) ? htmlspecialchars($_ENV['APP_NAME']) : 'FrontCMS' ?>. All rights reserved. 
        <?php if (defined('APP_VERSION')): ?>
            <span class="text-muted">v<?= htmlspecialchars(APP_VERSION) ?></span>
        <?php endif; ?>
    </div>
    
    <script src="<?= asset('js/highlight.min.js') ?>"></script>
    <script>
        // Initialize tab functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Tabs
            const tabs = document.querySelectorAll('.error-tab');
            const tabContents = document.querySelectorAll('.tab-content');
            
            tabs.forEach(tab => {
                tab.addEventListener('click', () => {
                    // Remove active class from all tabs and contents
                    tabs.forEach(t => t.classList.remove('active'));
                    tabContents.forEach(c => c.classList.remove('active'));
                    
                    // Add active class to clicked tab and corresponding content
                    tab.classList.add('active');
                    const targetId = tab.getAttribute('data-target');
                    if (targetId) {
                        const targetContent = document.getElementById(targetId);
                        if (targetContent) {
                            targetContent.classList.add('active');
                        }
                    }
                });
            });
            
            // Activate first tab by default
            if (tabs.length > 0) {
                tabs[0].click();
            }
            
            // Initialize syntax highlighting
            document.querySelectorAll('pre code').forEach((block) => {
                hljs.highlightElement(block);
            });
        });
    </script>
</body>
</html>
