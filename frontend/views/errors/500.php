<?php
/**
 * 500 Internal Server Error Page
 * 
 * This view is displayed when an unhandled exception occurs in the application.
 */

$title = '500 Internal Server Error';

// Get application configuration
$appName = config('app.name');
$appEnv = config('app.env');
$appDebug = config('app.debug');

// Get server and request information
$requestMethod = $_SERVER['REQUEST_METHOD'] ?? 'GET';
$requestUri = $_SERVER['REQUEST_URI'] ?? '/';
$httpHost = $_SERVER['HTTP_HOST'] ?? 'localhost';
$httpReferer = $_SERVER['HTTP_REFERER'] ?? 'None';
$userAgent = $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown';
$serverSoftware = $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown';
$phpVersion = PHP_VERSION;
$phpOs = PHP_OS;
$currentTime = date('Y-m-d H:i:s');

// Format the error message
$errorMessage = 'Whoops, something went wrong on our end.';
$errorFile = '';
$errorLine = 0;
$errorClass = '';
$errorCode = 0;
$stackTrace = [];
$formattedTrace = [];

/**
 * Format trace arguments for display
 * 
 * @param array $args The arguments to format
 * @return string Formatted arguments string
 */
function formatTraceArgs($args) {
    if (empty($args)) {
        return '';
    }
    
    $result = [];
    foreach ($args as $arg) {
        if (is_string($arg)) {
            $result[] = "'" . (strlen($arg) > 50 ? substr($arg, 0, 50) . '...' : $arg) . "'";
        } elseif (is_numeric($arg)) {
            $result[] = $arg;
        } elseif (is_bool($arg)) {
            $result[] = $arg ? 'true' : 'false';
        } elseif (is_null($arg)) {
            $result[] = 'null';
        } elseif (is_array($arg)) {
            $result[] = 'Array(' . count($arg) . ')';
        } elseif (is_object($arg)) {
            $result[] = 'Object(' . get_class($arg) . ')';
        } else {
            $result[] = gettype($arg);
        }
    }
    
    return implode(', ', $result);
}

// If we have an exception, get more details
if (isset($e) && $e instanceof Throwable) {
    $errorMessage = htmlspecialchars($e->getMessage(), ENT_QUOTES, 'UTF-8');
    $errorFile = $e->getFile();
    $errorLine = $e->getLine();
    $errorClass = get_class($e);
    $errorCode = $e->getCode();
    
    // Get stack trace as array
    $stackTrace = $e->getTrace();
    
    // Format the stack trace
    $formattedTrace = array_map(function($trace) {
        $file = $trace['file'] ?? 'Internal Function';
        $line = $trace['line'] ?? 0;
        $class = $trace['class'] ?? '';
        $type = $trace['type'] ?? '';
        $function = $trace['function'] ?? '';
        $args = $trace['args'] ?? [];
        
        return [
            'file' => $file,
            'line' => $line,
            'call' => $class . $type . $function . '()',
            'args' => formatTraceArgs($args)
        ];
    }, $stackTrace);
}

// Start output buffering
ob_start();
?>

<div class="error-container">
    <div class="error-header">
        <div class="error-code">500</div>
        <h1 class="error-title">Internal Server Error</h1>
        <p class="error-subtitle"><?= $appName ?> encountered an error while processing your request.</p>
    </div>
    
    <div class="error-content">
        <div class="error-message">
            <div class="error-alert">
                <i class="fas fa-exclamation-triangle"></i>
                <div>
                    <strong>Error:</strong> <?= $errorMessage ?>
                    <?php if (isset($e)): ?>
                    <div class="error-location">
                        in <code><?= htmlspecialchars($errorFile) ?></code> on line <code><?= $errorLine ?></code>
                        <?php if ($errorClass): ?>
                        <br>Exception: <code><?= $errorClass ?></code>
                        <?php endif; ?>
                        <?php if ($errorCode): ?>
                        <br>Error Code: <code><?= $errorCode ?></code>
                        <?php endif; ?>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <div class="error-actions">
            <a href="/" class="btn btn-primary">
                <i class="fas fa-home"></i> Go to Homepage
            </a>
            <button onclick="window.history.back()" class="btn btn-outline">
                <i class="fas fa-arrow-left"></i> Go Back
            </button>
            <button onclick="window.location.reload()" class="btn btn-outline">
                <i class="fas fa-sync-alt"></i> Reload Page
            </button>
        </div>
        
        <?php if ($appDebug && isset($e)): ?>
        <div class="error-details">
            <div class="error-tabs">
                <div class="error-tab active" data-target="error-stack">
                    <i class="fas fa-layer-group"></i> Stack Trace
                </div>
                <div class="error-tab" data-target="error-request">
                    <i class="fas fa-globe"></i> Request
                </div>
                <div class="error-tab" data-target="error-server">
                    <i class="fas fa-server"></i> Server
                </div>
            </div>
            
            <div id="error-stack" class="tab-content active">
                <h4>Stack Trace</h4>
                <div class="stack-trace">
                    <?php foreach ($formattedTrace as $index => $trace): ?>
                        <div class="trace-item">
                            <div class="trace-header">
                                <span class="trace-number">#<?= $index ?></span>
                                <span class="trace-method"><?= htmlspecialchars($trace['call']) ?></span>
                            </div>
                            <?php if (!empty($trace['file'])): ?>
                                <div class="trace-file">
                                    <i class="fas fa-file-alt"></i>
                                    <?= htmlspecialchars($trace['file']) ?>
                                    <span class="trace-line">line <?= $trace['line'] ?></span>
                                </div>
                            <?php endif; ?>
                            <?php if (!empty($trace['args'])): ?>
                                <div class="trace-args">
                                    <?= $trace['args'] ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
            
            <div id="error-request" class="tab-content">
                <div class="error-info">
                    <h4><i class="fas fa-link"></i> Request URL</h4>
                    <pre><?= htmlspecialchars($httpHost . $requestUri) ?></pre>
                </div>
                
                <div class="error-info">
                    <h4><i class="fas fa-exchange-alt"></i> Request Method</h4>
                    <pre><?= htmlspecialchars($requestMethod) ?></pre>
                </div>
                
                <div class="error-info">
                    <h4><i class="fas fa-reply"></i> Referrer</h4>
                    <pre><?= htmlspecialchars($httpReferer) ?></pre>
                </div>
                
                <div class="error-info">
                    <h4><i class="fas fa-user-agent"></i> User Agent</h4>
                    <pre><?= htmlspecialchars($userAgent) ?></pre>
                </div>
                
                <div class="error-info">
                    <h4><i class="fas fa-clock"></i> Time</h4>
                    <pre><?= htmlspecialchars($currentTime) ?></pre>
                </div>
            </div>
            
            <div id="error-server" class="tab-content">
                <div class="error-info">
                    <h4><i class="fas fa-server"></i> Server Software</h4>
                    <pre><?= htmlspecialchars($serverSoftware) ?></pre>
                </div>
                
                <div class="error-info">
                    <h4><i class="fab fa-php"></i> PHP Version</h4>
                    <pre><?= htmlspecialchars($phpVersion) ?></pre>
                </div>
                
                <div class="error-info">
                    <h4><i class="fas fa-desktop"></i> Operating System</h4>
                    <pre><?= htmlspecialchars($phpOs) ?></pre>
                </div>
                
                <div class="error-info">
                    <h4><i class="fas fa-code-branch"></i> Environment</h4>
                    <pre><?= htmlspecialchars($appEnv) ?></pre>
                </div>
                
                <div class="error-info">
                    <h4><i class="fas fa-bug"></i> Debug Mode</h4>
                    <pre><?= $appDebug ? 'Enabled' : 'Disabled' ?></pre>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>
</div>

<?php
// Get the content and clean the buffer
$content = ob_get_clean();

// Include the layout
extract(get_defined_vars());
include __DIR__ . '/layout.php';
?>