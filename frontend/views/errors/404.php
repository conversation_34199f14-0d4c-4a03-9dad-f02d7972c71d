<?php
/**
 * 404 Not Found Error Page
 * 
 * This view is displayed when a requested page cannot be found.
 */

$title = '404 Page Not Found';

// Get environment configuration
$appName = config('app.name');
$requestUri = $_SERVER['REQUEST_URI'] ?? '/';
$requestMethod = $_SERVER['REQUEST_METHOD'] ?? 'GET';
$httpReferer = $_SERVER['HTTP_REFERER'] ?? 'None';

// Log the 404 error
if (function_exists('log_message')) {
    log_message('error', "404 Page Not Found: {$requestMethod} {$requestUri} (Referrer: {$httpReferer})");
}

// Escape output for security
$escapedRequestUri = htmlspecialchars($requestUri, ENT_QUOTES, 'UTF-8');
$escapedRequestMethod = htmlspecialchars($requestMethod, ENT_QUOTES, 'UTF-8');
$escapedHttpReferer = htmlspecialchars($httpReferer, ENT_QUOTES, 'UTF-8');

$content = <<<HTML
<div class="error-container">
    <div class="error-header">
        <div class="error-code">404</div>
        <h1 class="error-title">Page Not Found | {$appName}</h1>
    </div>
    <div class="error-content">
        <p class="error-message">
            The page you're looking for doesn't exist or has been moved.
        </p>
        <div class="error-actions">
            <button onclick="window.history.back()" class="btn btn-outline">
                <i class="fas fa-arrow-left"></i> Go Back
            </button>
        </div>
        <div class="error-details">
            <h4>Request Details:</h4>
            <p><strong>URL:</strong> {$escapedRequestUri}</p>
            <p><strong>Method:</strong> {$escapedRequestMethod}</p>
            <p><strong>Referrer:</strong> {$escapedHttpReferer}</p>
        </div>
    </div>
</div>
HTML;

// Extract variables and include the layout
extract(get_defined_vars());
include __DIR__ . '/layout.php';
?>