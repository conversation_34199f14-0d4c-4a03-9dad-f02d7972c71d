<?php 
    use App\Services\Auth;
    $baseUrl = config('app_url');
    $user = Auth::user() ?? null; 
    $token = Auth::token() ?? null; 
?>

    <script src="<?= asset('js/flowbite.min.js') ?>"></script>

    <script>
        window.AppConfig = {
            baseUrl: '<?= $baseUrl ?>',
            csrfToken: '<?= $_SESSION['csrf_token'] ?? '' ?>',
            authToken: '<?= $token ?? '' ?>'
        };

        const { baseUrl, csrfToken, authToken } = window.AppConfig || {};
    </script>

    <?php if ($user) { ?>
        <script src="<?= asset('js/lucide.min.js') ?>"></script>
        <script>
            lucide.createIcons();
        </script>
        <script src="<?= asset('js/main.js') ?>"></script>
        <!-- CodeMirror JS -->
        <script src="<?= asset('js/codemirror.min.js') ?>"></script>
        <script src="<?= asset('js/codemirrorCss.min.js') ?>"></script>
        <script src="<?= asset('js/javascript.min.js') ?>"></script>
    <?php } ?>

    <!-- Move these right before </body> -->
    <!-- In footer.php -->
    <script src="<?= asset('js/table-component.js') ?>"></script>

    <script src="<?= asset('js/ajax-handler.js') ?>"></script>

    <?= add_footer_content('') ?>
    <?= render_scripts() ?>

    <script>
</script>
</body>
</html>