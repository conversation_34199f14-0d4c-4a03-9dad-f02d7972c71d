<?php

use App\Services\Auth;

$baseUrl = config('app_url');
$user = Auth::user() ?? null;
$pageTitle = $data['pageTitle'] ?? null;
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= pageTitle($pageTitle ?? null) ?></title>
    <meta name="csrf-token" content="<?= csrf_token() ?>">
    
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Figtree:ital,wght@0,300..900;1,300..900&family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&family=Sora:wght@100..800&display=swap" rel="stylesheet">

    <script src="<?= asset('tailwind.conf') ?>"></script>
    <link href="<?= asset('css/flowbite.min.css') ?>" rel="stylesheet" />
    <link href="<?= asset('css/toaster.css') ?>" rel="stylesheet" />
    <?php if ($user) { ?>
        <script src="<?= asset('js/jquery-3.7.1.min.js') ?>"></script>
        <link href="<?= asset('css/select2.min.css') ?>" rel="stylesheet" />
        <script src="<?= asset('js/select2.min.js') ?>"></script>
        <script src="<?= asset('js/ckeditor.js') ?>"></script>
        <link rel="stylesheet" href="<?= asset('css/main.css') ?>">
    <?php } ?>
    <?= render_styles() ?>
</head>