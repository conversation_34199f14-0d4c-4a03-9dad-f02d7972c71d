<?php

use App\Services\Auth;
use Core\View;

$baseUrl = config('app_url');
$user = Auth::user() ?? null;
?>

<?php View::include('layouts.header'); ?>

<body class="<?php echo $user == null ? 'min-h-screen flex items-center justify-center p-4' : '' ?>">
    <?php if ($user) { ?>
        <div class="flex min-h-screen">
            <?php View::include('layouts.sidebar'); ?>
            <main class="ml-[69px] flex-1">
                <?php echo $contentView; ?>
            </main>
        </div>
    <?php } else { ?>
        <?php echo $contentView; ?>
    <?php } ?>

    <!-- Toast Container -->
    <div id="toast-container" class="fixed bottom-4 right-4 space-y-2 z-50">
    </div>

    <?php View::include('layouts.footer'); ?>