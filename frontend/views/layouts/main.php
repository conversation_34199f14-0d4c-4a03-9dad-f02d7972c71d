<?php

use App\Services\Auth;
use Core\View;

$baseUrl = config('app_url');
$user = Auth::user() ?? null;
?>

<?php View::include('layouts.header'); ?>

<body class="<?php echo $user == null ? 'min-h-screen flex' : '' ?>">
    <?php if ($user) { ?>
        <div class="flex min-h-screen flex-col">
            <?php View::include('layouts.sidebar'); ?>
            <?php if ($breadCrumb !== []) { ?>
                <?php View::include('layouts.subHeader'); ?>
            <?php } ?>
            <main class="flex-1">
                <?php echo $contentView; ?>
            </main>
        </div>
    <?php } else { ?>
        <?php echo $contentView; ?>
    <?php } ?>

    <!-- Toast Container -->
    <div id="toast-container" class="fixed bottom-4 right-4 space-y-2 z-50">
    </div>

    <?php View::include('layouts.footer'); ?>