<header class="bg-white py-3 border-b border-gray-200 mx-4" >
    <div class="flex items-center justify-between">
        <!-- Left Side - Search Bar -->
        <div class="flex-1 max-w-xl">
            <div class="flex items-center">
                <div class="flex flex-col">
                    <!-- Breadcrumb -->
                    <div class="flex items-center space-x-2 text-[12px] text-gray-500">
                        <a href="<?= url('/') ?>" class="hover:text-[#0C5BE2]">Home</a>
                        <?php if (isset($breadCrumb) && is_array($breadCrumb)): ?>
                            <?php foreach ($breadCrumb as $index => $crumb): ?>
                                <span>/</span>
                                <?php if ($index === array_key_last($breadCrumb)): ?>
                                    <span class="text-gray-700"><?= htmlspecialchars($crumb['label']); ?></span>
                                <?php else: ?>
                                    <a href="<?= htmlspecialchars($crumb['url']); ?>" class="hover:text-[#0C5BE2]"><?= htmlspecialchars($crumb['label']); ?></a>
                                <?php endif; ?>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                    <!-- page Title -->
                    <h1 class="text-xl font-semibold text-gray-900">
                        <?= isset($pageTitle) ? htmlspecialchars($pageTitle) : 'Dashboard'; ?>
                    </h1>
                </div>
            </div>
        </div>
        <!-- Right Side - Actions -->
        <div class="flex items-center space-x-6">
            <!-- Theme Toggle -->
            <div class="flex items-center hidden">
                <button type="button" 
                    class="text-gray-500 hover:bg-gray-100 rounded-lg text-sm p-2 inline-flex items-center">
                    <i data-lucide="sun" class="w-5 h-5 text-gray-500 hover:text-[#0C5BE2]"></i>
                </button>
                <button type="button" 
                    class="text-white bg-gray-900 hover:bg-gray-800 rounded-lg text-sm p-2 inline-flex items-center">
                    <i data-lucide="moon" class="w-5 h-5 text-gray-500 hover:text-[#0C5BE2]"></i>
                </button>
            </div>

            <!-- Notification -->
            <button type="button" 
                class="relative text-gray-500 hover:bg-gray-100 rounded-lg p-2 inline-flex items-center">
                <i data-lucide="bell" class="w-5 h-5 text-gray-500 hover:text-[#0C5BE2]"></i>
                <div class="absolute inline-flex items-center justify-center w-2 h-2 bg-[#0C5BE2] rounded-full top-2 right-2"></div>
            </button>
        </div>
    </div>
</header>