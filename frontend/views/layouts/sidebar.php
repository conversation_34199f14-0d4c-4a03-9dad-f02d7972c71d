<?php

use App\Services\Auth;
// Get current path
$current_path = trim(parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH), '/');
$base_dir = basename(dirname(dirname(__DIR__)));

// Remove base directory from path if it exists
if (strpos($current_path, $base_dir) === 0) {
    $current_path = trim(substr($current_path, strlen($base_dir)), '/');
}

// Get menu config
$menu_config = include dirname(__DIR__, 2) . '/config/menu.php';

$user = Auth::user() ?? null;
$userRole = $user['role'] ?? 'guest';

// Function to check permissions
function hasPermission($requiredPermission, $userRole) {
    if ($requiredPermission === 'authenticated') {
        return $userRole !== 'guest';
    }
    // Add your permission logic here
    return true; // Replace with actual permission check
}

// Function to render menu items
function renderMenuItems($items, $current_path, $userRole) {
    $output = '';
    foreach ($items as $key => $item) {
        if (isset($item['permission']) && !hasPermission($item['permission'], $userRole)) {
            continue;
        }
        
        $isActive = strpos($current_path, trim($item['url'], '/')) === 0;
        $activeClass = $isActive ? 'bg-[#0C5BE2]/5 text-[#0C5BE2]' : 'text-gray-900';
        $iconClass = $isActive ? 'text-[#0C5BE2]' : 'text-gray-500';
        
        $output .= '
        <a href="' . url($item['url']) . '" 
            class="flex justify-center items-center p-2 text-[14px] rounded-lg hover:bg-[#0C5BE2]/10 hover:text-[#0C5BE2] group ' . $activeClass . '" 
            data-tooltip-target="tooltip-' . $key . '" 
            data-tooltip-placement="right">
            <i data-lucide="' . $item['icon'] . '" class="w-5 h-5 group-hover:text-[#0C5BE2] ' . $iconClass . '"></i>
            <span class="flex-1 ml-3 whitespace-nowrap hidden">' . $item['title'] . '</span>
        </a>
        <div id="tooltip-' . $key . '" role="tooltip" class="absolute z-50 invisible inline-block px-3 py-2 text-sm font-medium text-white transition-opacity duration-300 bg-gray-900 rounded-lg shadow-sm opacity-0 tooltip">
            ' . $item['title'] . '
            <div class="tooltip-arrow" data-popper-arrow></div>
        </div>';
    }
    return $output;
}
?>

<aside class="sidebar fixed top-0 left-0 z-40 h-screen transition-transform -translate-x-full sm:translate-x-0 border-r border-gray-200" aria-label="Sidebar">
    <!-- Sidebar Header -->
    <div class="flex items-center justify-between p-5 sidebarHeaderWrapper">
        <a href="<?= url('/') ?>" class="flex items-center">
            <!-- Full Logo (visible when sidebar is expanded) -->
            <div class="logo-full hidden">
                <svg width="160px" viewBox="0 0 220 37.045">
                <!-- Add your logo SVG content here -->
                    <path id="a" d="m40.559 18.773c0-1.8537 0.36963-3.4977 1.1089-4.9321 0.73926-1.4344 1.7488-2.5377 3.0287-3.3101 1.2799-0.77236 2.7033-1.1585 4.27-1.1585 1.1916 0 2.3281 0.25929 3.4094 0.77788 1.0813 0.51858 1.9419 1.2082 2.5819 2.0688v-8.7056h4.7004v24.495h-4.7004v-2.7143c-0.57375 0.90476-1.3792 1.633-2.4164 2.1847-1.0372 0.55168-2.2398 0.82753-3.608 0.82753-1.5447 0-2.957-0.39721-4.2369-1.1916-1.2799-0.79443-2.2895-1.9143-3.0287-3.3598s-1.1089-3.106-1.1089-4.9817zm14.432 0.066202c0-1.1254-0.22067-2.0909-0.66202-2.8963-0.44135-0.80546-1.0372-1.4233-1.7875-1.8537-0.75029-0.43031-1.5558-0.64547-2.4164-0.64547s-1.6551 0.20964-2.3833 0.62892c-0.72822 0.41928-1.3185 1.0317-1.7709 1.8371-0.45238 0.80546-0.67857 1.7599-0.67857 2.8632s0.22619 2.0688 0.67857 2.8963c0.45238 0.82753 1.0482 1.462 1.7875 1.9033 0.73926 0.44135 1.5282 0.66202 2.3667 0.66202 0.86063 0 1.6661-0.21516 2.4164-0.64547 0.75029-0.43031 1.3461-1.0482 1.7875-1.8537 0.44135-0.80546 0.66202-1.7709 0.66202-2.8963zm11.552-11.354c-0.81649 0-1.4951-0.25929-2.0357-0.77788-0.54065-0.51858-0.81098-1.1641-0.81098-1.9364 0-0.77236 0.27033-1.4178 0.81098-1.9364 0.54065-0.51858 1.2192-0.77788 2.0357-0.77788 0.81649 0 1.4951 0.25929 2.0357 0.77788 0.54065 0.51858 0.81098 1.1641 0.81098 1.9364 0 0.77236-0.27033 1.4178-0.81098 1.9364-0.54065 0.51858-1.2192 0.77788-2.0357 0.77788zm2.284 2.1847v18.338h-4.6342v-18.338h4.6342zm11.751-0.29791c1.3682 0 2.5709 0.27033 3.608 0.81098 1.0372 0.54065 1.8537 1.2413 2.4495 2.1019v-2.615h4.6673v18.47c0 1.6992-0.34204 3.2163-1.0261 4.5514s-1.7102 2.3943-3.0784 3.1777c-1.3682 0.78339-3.0232 1.1751-4.9652 1.1751-2.604 0-4.739-0.60685-6.4051-1.8206-1.6661-1.2137-2.6095-2.8688-2.8301-4.9652h4.6011c0.24274 0.83856 0.76684 1.5061 1.5723 2.0026 0.80546 0.49652 1.7819 0.74477 2.9294 0.74477 1.3461 0 2.4384-0.40273 3.277-1.2082 0.83856-0.80546 1.2578-2.0247 1.2578-3.6577v-2.8467c-0.59582 0.86063-1.4178 1.5778-2.466 2.1516-1.0482 0.57375-2.2454 0.86063-3.5915 0.86063-1.5447 0-2.957-0.39721-4.2369-1.1916s-2.2895-1.9143-3.0287-3.3598c-0.73926-1.4454-1.1089-3.106-1.1089-4.9817 0-1.8537 0.36963-3.4977 1.1089-4.9321 0.73926-1.4344 1.7433-2.5377 3.0122-3.3101 1.2689-0.77236 2.6867-1.1585 4.2535-1.1585zm6.0575 9.4669c0-1.1254-0.22067-2.0909-0.66202-2.8963-0.44135-0.80546-1.0372-1.4233-1.7875-1.8537s-1.5558-0.64547-2.4164-0.64547c-0.86063 0-1.6551 0.20964-2.3833 0.62892-0.72822 0.41928-1.3185 1.0317-1.7709 1.8371-0.45238 0.80546-0.67857 1.7599-0.67857 2.8632s0.22619 2.0688 0.67857 2.8963c0.45238 0.82753 1.0482 1.462 1.7875 1.9033s1.5282 0.66202 2.3667 0.66202c0.86063 0 1.6661-0.21516 2.4164-0.64547s1.3461-1.0482 1.7875-1.8537c0.44135-0.80546 0.66202-1.7709 0.66202-2.8963zm11.552-11.354c-0.81649 0-1.4951-0.25929-2.0357-0.77788-0.54065-0.51858-0.81098-1.1641-0.81098-1.9364 0-0.77236 0.27033-1.4178 0.81098-1.9364 0.54065-0.51858 1.2192-0.77788 2.0357-0.77788 0.81649 0 1.4951 0.25929 2.0357 0.77788 0.54065 0.51858 0.81098 1.1641 0.81098 1.9364 0 0.77236-0.27032 1.4178-0.81098 1.9364-0.54065 0.51858-1.2192 0.77788-2.0357 0.77788zm2.284 2.1847v18.338h-4.6342v-18.338h4.6342zm3.3763 9.169c0-1.8978 0.38618-3.5584 1.1585-4.9817 0.77236-1.4233 1.8426-2.5267 3.2108-3.3101 1.3682-0.78339 2.935-1.1751 4.7004-1.1751 2.2729 0 4.1542 0.56824 5.6437 1.7047 1.4895 1.1365 2.4881 2.7308 2.9956 4.7831h-4.9983c-0.26481-0.79443-0.71167-1.4178-1.3406-1.8702-0.62892-0.45238-1.4068-0.67857-2.3336-0.67857-1.324 0-2.3722 0.47997-3.1446 1.4399-0.77236 0.95993-1.1585 2.3226-1.1585 4.088 0 1.7433 0.38618 3.095 1.1585 4.0549 0.77236 0.95993 1.8206 1.4399 3.1446 1.4399 1.8757 0 3.1005-0.83856 3.6742-2.5157h4.9983c-0.50755 1.9861-1.5116 3.5639-3.0122 4.7335-1.5006 1.1696-3.3763 1.7544-5.6272 1.7544-1.7654 0-3.3322-0.3917-4.7004-1.1751-1.3682-0.78339-2.4384-1.8868-3.2108-3.3101-0.77236-1.4233-1.1585-3.0839-1.1585-4.9817zm38.662-9.169-11.354 27.01h-4.9321l3.9721-9.1359-7.3484-17.875h5.1969l4.7335 12.81 4.7997-12.81h4.9321zm7.1167 2.6481c0.59582-0.83856 1.4178-1.5392 2.466-2.1019 1.0482-0.56272 2.2454-0.84408 3.5915-0.84408 1.5668 0 2.9846 0.38618 4.2535 1.1585 1.2689 0.77236 2.2729 1.8702 3.0122 3.2936s1.1089 3.0729 1.1089 4.9486-0.36963 3.5363-1.1089 4.9817-1.7433 2.5653-3.0122 3.3598-2.6867 1.1916-4.2535 1.1916c-1.3461 0-2.5322-0.27584-3.5584-0.82753-1.0261-0.55168-1.8592-1.2468-2.4991-2.0854v11.354h-4.6342v-27.077h4.6342v2.6481zm9.6986 6.4547c0-1.1034-0.22619-2.0578-0.67857-2.8632-0.45238-0.80546-1.0482-1.4178-1.7875-1.8371-0.73926-0.41928-1.5392-0.62892-2.3998-0.62892-0.83856 0-1.6275 0.21516-2.3667 0.64547-0.73926 0.43031-1.3351 1.0537-1.7875 1.8702-0.45238 0.81649-0.67857 1.7764-0.67857 2.8798s0.22619 2.0633 0.67857 2.8798c0.45238 0.81649 1.0482 1.4399 1.7875 1.8702 0.73926 0.43031 1.5282 0.64547 2.3667 0.64547 0.86063 0 1.6606-0.22067 2.3998-0.66202 0.73926-0.44135 1.3351-1.0703 1.7875-1.8868 0.45238-0.81649 0.67857-1.7875 0.67857-2.9129zm18.437-9.3676c1.3902 0 2.626 0.30343 3.7073 0.91028 1.0813 0.60685 1.9254 1.5006 2.5322 2.6812 0.60685 1.1806 0.91028 2.5984 0.91028 4.2535v10.758h-4.6342v-10.129c0-1.4564-0.36411-2.5764-1.0923-3.3598-0.72822-0.78339-1.7213-1.1751-2.9791-1.1751-1.2799 0-2.2895 0.3917-3.0288 1.1751-0.73926 0.78339-1.1089 1.9033-1.1089 3.3598v10.129h-4.6342v-24.495h4.6342v8.4408c0.59582-0.79443 1.3902-1.4178 2.3833-1.8702 0.99303-0.45238 2.0964-0.67857 3.3101-0.67857zm28.599 9.0366c0 0.66202-0.044135 1.2578-0.1324 1.7875h-13.406c0.11034 1.324 0.57375 2.3612 1.3902 3.1115 0.81649 0.75029 1.8206 1.1254 3.0122 1.1254 1.7213 0 2.946-0.73926 3.6742-2.2178h4.9983c-0.52962 1.7654-1.5447 3.2163-3.0453 4.3528-1.5006 1.1365-3.3432 1.7047-5.5279 1.7047-1.7654 0-3.3487-0.3917-4.75-1.1751-1.4013-0.78339-2.4936-1.8923-3.277-3.3267-0.78339-1.4344-1.1751-3.0894-1.1751-4.9652 0-1.8978 0.38618-3.5639 1.1585-4.9983 0.77236-1.4344 1.8537-2.5377 3.2439-3.3101 1.3902-0.77236 2.9901-1.1585 4.7997-1.1585 1.7433 0 3.3046 0.37515 4.6838 1.1254 1.3792 0.75029 2.4495 1.815 3.2108 3.1943s1.142 2.9625 1.142 4.75zm-4.7997-1.324c-0.022067-1.1916-0.45238-2.1461-1.2909-2.8632s-1.8647-1.0758-3.0784-1.0758c-1.1475 0-2.113 0.34756-2.8963 1.0427-0.78339 0.69512-1.2634 1.6606-1.4399 2.8963h8.7056zm12.81-4.6011c0.59582-0.97097 1.3737-1.7323 2.3336-2.284 0.95993-0.55168 2.0578-0.82753 3.2936-0.82753v4.8659h-1.2247c-1.4564 0-2.5543 0.34204-3.2936 1.0261-0.73926 0.68409-1.1089 1.8757-1.1089 3.5749v9.1359h-4.6342v-18.338h4.6342v2.8467z" fill="#0C5BE2"/>
                    <path d="m28.009 7.6387h-8.9118c-1.4068 0-2.5462-1.1394-2.5462-2.5462v-2.5462c0-1.4068 1.1394-2.5462 2.5462-2.5462h6.3656c2.8123 0 5.0925 2.2802 5.0925 5.0925 0 1.4068-1.1394 2.5462-2.5462 2.5462z" fill="#E65100"/>
                    <path d="m8.9118 10.185v-5.0925c0-1.4068-1.1394-2.5462-2.5462-2.5462h-1.2731c-2.8123 0-5.0925 2.2802-5.0925 5.0925v2.5462h8.9118zm1.2731 20.37h10.185v-7.6387h-10.185v7.6387zm12.731-7.6387v7.6387h2.5462c2.8123 0 5.0925-2.2802 5.0925-5.0925v-2.5462h-7.6387zm-15.277 0h-7.6387v2.5462c0 2.8123 2.2802 5.0925 5.0925 5.0925h2.5462v-7.6387zm6.3656-10.185h-14.004v7.6387h14.004v-7.6387zm2.5462 0v7.6387h14.004v-5.0925c0-1.4068-1.1394-2.5462-2.5462-2.5462h-11.458z" fill="#0C5BE2"/>
                </svg>
            </div>
            
            <!-- Icon-only Logo (visible when sidebar is collapsed) -->
            <div class="logo-icon">
                <svg viewBox="0 0 37 37" width="27" height="27">
                    <!-- Add your icon SVG content here -->
                    <path d="m28.009 7.6387h-8.9118c-1.4068 0-2.5462-1.1394-2.5462-2.5462v-2.5462c0-1.4068 1.1394-2.5462 2.5462-2.5462h6.3656c2.8123 0 5.0925 2.2802 5.0925 5.0925 0 1.4068-1.1394 2.5462-2.5462 2.5462z" fill="#E65100"/>
                    <path d="m8.9118 10.185v-5.0925c0-1.4068-1.1394-2.5462-2.5462-2.5462h-1.2731c-2.8123 0-5.0925 2.2802-5.0925 5.0925v2.5462h8.9118zm1.2731 20.37h10.185v-7.6387h-10.185v7.6387zm12.731-7.6387v7.6387h2.5462c2.8123 0 5.0925-2.2802 5.0925-5.0925v-2.5462h-7.6387zm-15.277 0h-7.6387v2.5462c0 2.8123 2.2802 5.0925 5.0925 5.0925h2.5462v-7.6387zm6.3656-10.185h-14.004v7.6387h14.004v-7.6387zm2.5462 0v7.6387h14.004v-5.0925c0-1.4068-1.1394-2.5462-2.5462-2.5462h-11.458z" fill="#0C5BE2"/>
                </svg>
            </div>
        </a>
    </div>
    
    <!-- Main Sidebar Content -->
    <div class="flex flex-col h-[calc(100vh-74px)] px-3 py-4 overflow-y-auto bg-white">
        <!-- Top Section -->
        <div class="flex-grow">
            <div class="space-y-2">
                <!-- Menu Group Header -->
                <div class="navType text-[11px] font-semibold text-gray-400 uppercase px-3 mb-4 hidden">
                    MENU
                </div>
                
                <?= renderMenuItems($menu_config['menu'] ?? [], $current_path, $userRole) ?>
            </div>

            <!-- Divider -->
            <div class="my-5 border-b border-gray-200"></div>

            <!-- Master Section -->
            <div class="space-y-2">
                <div class="navType text-[11px] font-semibold text-gray-400 uppercase px-3 mb-4 hidden">
                    MASTER
                </div>
                
                <?= renderMenuItems($menu_config['master'] ?? [], $current_path, $userRole) ?>
            </div>
        </div>

        <!-- Bottom Section -->
        <div class="pt-5 border-t border-gray-200">
            <!-- User Profile Dropdown -->
            <button type="button" 
                class="flex items-center justify-center p-2 w-full text-[14px] rounded-lg hover:bg-[#0C5BE2]/5 hover:text-[#0C5BE2] group"
                id="userMenuButton" 
                data-dropdown-toggle="userMenu" 
                data-dropdown-placement="left-end"
                aria-expanded="false">
                <i data-lucide="user" class="w-5 h-5 group-hover:text-[#0C5BE2] text-gray-500"></i>
                <span class="flex-1 ml-3 whitespace-nowrap hidden">Profile</span>
            </button>
            
            <!-- User Dropdown Menu -->
            <div class="hidden z-50 my-4 w-56 text-base list-none bg-white rounded-lg shadow-lg" id="userMenu">
                <div class="py-3 px-4">
                    <span class="block text-sm font-semibold text-gray-900"><?= htmlspecialchars($user['name'] ?? 'Guest') ?></span>
                    <span class="block text-sm text-gray-500 truncate"><?= htmlspecialchars($user['email'] ?? '') ?></span>
                </div>
                <ul class="py-1" aria-labelledby="userMenuButton">
                    <li>
                        <a href="<?= url('/profile') ?>" class="flex items-center py-2 px-4 text-sm text-gray-700 hover:bg-gray-50">
                            <i data-lucide="user" class="w-4 h-4 mr-2 text-gray-500"></i>
                            Profile
                        </a>
                    </li>
                </ul>
                <div class="py-1">
                    <a href="<?= url('/logout') ?>" class="flex items-center py-2 px-4 text-sm text-red-600 hover:bg-gray-50">
                        <i data-lucide="log-out" class="w-4 h-4 mr-2 text-red-600"></i>
                        Sign out
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div id="sidebar-toggle" class="hidden bg-gray-200 hover:bg-[#0C5BE2]/5 text-black hover:text-[#0C5BE2] p-0 absolute right-[-12px] top-[50%] -translate-y-1/2 h-12 w-3 rounded-r-lg shadow-lg cursor-pointer transition-all duration-300 flex items-center justify-center">
        <svg xmlns="http://www.w3.org/2000/svg" width="14" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="3.5" stroke-linecap="round" stroke-linejoin="round" class="transition-transform duration-300">
            <path d="M15 18l-6-6 6-6"/>
        </svg>
    </div> 
</aside>