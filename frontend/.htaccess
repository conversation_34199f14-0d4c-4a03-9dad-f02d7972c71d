RewriteEngine On

# Serve static files directly if they exist
RewriteCond %{REQUEST_FILENAME} -f
RewriteRule ^(.*)$ - [L]

# Prevent direct access to sensitive directories
RewriteRule ^(config|core|vendor|storage|bootstrap)/ - [F,L]

# Prevent access to module files except assets (assets are handled by PHP)
RewriteCond %{REQUEST_URI} ^/modules/
RewriteCond %{REQUEST_URI} !^/modules/.*/Assets/
RewriteRule ^(.*)$ - [F,L]

# Send all other requests to public/index.php (including assets)
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ public/index.php [L]

# Prevent access to .env files
RewriteRule \.env$ - [F,L]

# Prevent access to composer files
RewriteRule ^composer\.(json|lock)$ - [F,L]

# Set default character set
AddDefaultCharset UTF-8

# Enable compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# Set cache headers for static assets
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/svg+xml "access plus 1 month"
</IfModule> 