RewriteEngine On

# Dynamic image transformation - Universal N-level nested directory support
# Handle ANY number of nested directories with intelligent rules
# Supports both explicit mode and default mode (fit):
# - /uploads/P/h/P/300x300/fit=file.png (explicit mode)
# - /uploads/P/h/P/300x300/file.png (default to fit mode)

# Rule 1: Handle unlimited nested levels with explicit mode (1+ directories)
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^uploads/(.+)/(\d+)x(\d+)/([a-z]+)=(.+)$ transform.php?path=uploads/$1/$5&width=$2&height=$3&mode=$4 [L,QSA]

# Rule 2: Handle unlimited nested levels with default mode (1+ directories)
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^uploads/(.+)/(\d+)x(\d+)/(.+)$ transform.php?path=uploads/$1/$4&width=$2&height=$3&mode=fit [L,QSA]

# Rule 3: Handle simple uploads format with explicit mode (0 levels - direct in uploads)
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^uploads/(\d+)x(\d+)/([a-z]+)=(.+)$ transform.php?path=uploads/$4&width=$1&height=$2&mode=$3 [L,QSA]

# Rule 4: Handle simple uploads format with default mode (0 levels - direct in uploads)
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^uploads/(\d+)x(\d+)/(.+)$ transform.php?path=uploads/$3&width=$1&height=$2&mode=fit [L,QSA]


# Serve static files directly if they exist
RewriteCond %{REQUEST_FILENAME} -f
RewriteRule ^(.*)$ - [L]

# Prevent direct access to sensitive directories
RewriteRule ^(config|core|vendor|storage|bootstrap)/ - [F,L]

# Prevent access to module files except assets (assets are handled by PHP)
RewriteCond %{REQUEST_URI} ^/modules/
RewriteCond %{REQUEST_URI} !^/modules/.*/Assets/
RewriteRule ^(.*)$ - [F,L]

# Send all other requests to public/index.php (including assets)
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ public/index.php [L]

# Prevent access to .env files
RewriteRule \.env$ - [F,L]

# Prevent access to composer files
RewriteRule ^composer\.(json|lock)$ - [F,L]

# Set default character set
AddDefaultCharset UTF-8

# Enable compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# Set cache headers for static assets
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/svg+xml "access plus 1 month"
</IfModule>