<?php

declare(strict_types=1);

use App\Services\ApiClient;
use Core\ServiceContainer;

/**
 * Register application services in the service container
 */

return function () {
    // Register configuration
    ServiceContainer::bind('config', function () {
        return require __DIR__ . '/../config/app.php';
    });

    // Register API client

    ServiceContainer::bind(ApiClient::class, function () {
        return new ApiClient(
            config('api.v1_url'),  // e.g., http://127.0.0.1:8000/api/v1
            config('api.cms_url'), // e.g., http://127.0.0.1:8000/api/cms
            config('api.key'),
            config('api.token')
        );
    });

    ServiceContainer::bind('logger', function () {
        $logger = new Monolog\Logger('app');
        $logger->pushHandler(
            new Monolog\Handler\StreamHandler(
                __DIR__ . '/../logs/app.log',
                config('debug') ? Monolog\Logger::DEBUG : Monolog\Logger::ERROR
            )
        );
        return $logger;
    });

    // Register other services as needed
    // ServiceContainer::bind(ServiceInterface::class, function () {
    //     return new ServiceImplementation();
    // });
};
