# FileUploadManager - Simplified Upload Interface

The `FileUploadManager` class provides a simplified, zero-configuration interface for file uploads with automatic processing. All logic is encapsulated within the package, requiring minimal setup code.

## 🎯 Overview

The `FileUploadManager` automatically:
- ✅ Configures processors based on environment variables
- ✅ Handles image processing and thumbnail generation
- ✅ Manages storage drivers and validation
- ✅ Provides folder operations
- ✅ Generates dynamic transformation URLs

## 🚀 Quick Start

### Basic Usage

```php
<?php
require_once 'vendor/autoload.php';

// Create manager (automatically configures everything)
$uploadManager = \Package\FileUpload\FileUploadManager::create();

// Upload file with automatic processing
$result = $uploadManager->upload($_FILES['file'], [
    'directory' => 'uploads/documents'
]);

if ($result->isSuccess()) {
    echo "File uploaded: " . $result->getPath();
    echo "File URL: " . $result->getUrl();
} else {
    echo "Upload failed: " . $result->getMessage();
}
?>
```

### Before vs After

**❌ Old Way (Manual Configuration):**
```php
// Manual processor configuration required
$uploader = new FileUpload();

$thumbnailsEnabled = filter_var($_ENV['FILEUPLOAD_THUMBNAILS_ENABLED'] ?? false, FILTER_VALIDATE_BOOLEAN);
$processingEnabled = filter_var($_ENV['FILEUPLOAD_PROCESSING_ENABLED'] ?? true, FILTER_VALIDATE_BOOLEAN);

if ($processingEnabled) {
    $imageProcessor = new ImageProcessor();
    $uploader->addProcessor($imageProcessor);
}

$result = $uploader->upload($_FILES['file']);
```

**✅ New Way (Automatic Configuration):**
```php
// Everything configured automatically
$uploadManager = FileUploadManager::create();
$result = $uploadManager->upload($_FILES['file']);
```

## 📚 API Reference

### Core Methods

#### `create(array $config = []): FileUploadManager`
Static factory method to create a new instance.

```php
$uploadManager = FileUploadManager::create([
    'uploads_directory' => 'custom-uploads'
]);
```

#### `upload(array $file, array $options = []): UploadResultInterface`
Upload a single file with automatic processing.

```php
$result = $uploadManager->upload($_FILES['file'], [
    'directory' => 'uploads/images',
    'prefix' => 'user_avatar_'
]);
```

#### `uploadMultiple(array $files, array $options = []): array`
Upload multiple files.

```php
$results = $uploadManager->uploadMultiple($_FILES['files'], [
    'directory' => 'uploads/gallery'
]);

foreach ($results as $result) {
    if ($result->isSuccess()) {
        echo "Uploaded: " . $result->getPath() . "\n";
    }
}
```

### Folder Operations

#### `createFolder(string $path): FolderResult`
Create a directory.

```php
$result = $uploadManager->createFolder('uploads/new-gallery');
if ($result->isSuccess()) {
    echo "Folder created: " . $result->getPath();
}
```

#### `deleteFolder(string $path): FolderResult`
Delete a directory and its contents.

```php
$result = $uploadManager->deleteFolder('uploads/old-gallery');
```

#### `listFolder(string $path): FolderResult`
List directory contents.

```php
$result = $uploadManager->listFolder('uploads');
if ($result->isSuccess()) {
    foreach ($result->getContents() as $item) {
        echo $item['name'] . " (" . $item['type'] . ")\n";
    }
}
```

### Dynamic Image Transformation

#### `getDynamicUrl(string $path, int $width, int $height, string $mode = 'fit', bool $useQuery = false, bool $useDefault = false): string`
Generate dynamic transformation URLs.

```php
$originalPath = 'uploads/image.jpg';

// Different transformation modes
$thumbnail = $uploadManager->getDynamicUrl($originalPath, 150, 150, 'crop');
$medium = $uploadManager->getDynamicUrl($originalPath, 400, 300, 'fit');
$large = $uploadManager->getDynamicUrl($originalPath, 800, 600, 'fit');

// Clean URLs (default mode)
$cleanUrl = $uploadManager->getDynamicUrl($originalPath, 300, 300, 'fit', false, true);
// Result: /uploads/300x300/image.jpg

// Query parameter format
$queryUrl = $uploadManager->getDynamicUrl($originalPath, 300, 300, 'fit', true);
// Result: /transform.php?path=uploads/image.jpg&w=300&h=300&m=fit
```

### Information Methods

#### `getProcessingStatus(): array`
Get current processing configuration.

```php
$status = $uploadManager->getProcessingStatus();

echo "Processing enabled: " . ($status['processing_enabled'] ? 'Yes' : 'No') . "\n";
echo "Thumbnails enabled: " . ($status['thumbnails_enabled'] ? 'Yes' : 'No') . "\n";
echo "Thumbnail directory: " . $status['thumbnails_directory'] . "\n";

foreach ($status['thumbnail_sizes'] as $name => $size) {
    echo "Thumbnail {$name}: {$size['width']}x{$size['height']} ({$size['mode']})\n";
}
```

#### `getConfigInfo(): array`
Get complete configuration information.

```php
$config = $uploadManager->getConfigInfo();

echo "Storage driver: " . $config['storage']['driver'] . "\n";
echo "Max file size: " . number_format($config['validation']['max_size']) . " bytes\n";
echo "Allowed types: " . implode(', ', $config['validation']['allowed_types']) . "\n";
```

#### `healthCheck(): array`
Perform system health check.

```php
$health = $uploadManager->healthCheck();

foreach ($health as $check => $result) {
    echo "{$check}: " . ($result['status'] ? '✅' : '❌') . " {$result['message']}\n";
}
```

## ⚙️ Configuration

All configuration is handled automatically via environment variables:

```env
# Enable processing
FILEUPLOAD_PROCESSING_ENABLED=true

# Enable thumbnails
FILEUPLOAD_THUMBNAILS_ENABLED=true
FILEUPLOAD_THUMBNAILS_DIRECTORY=thumbnails
FILEUPLOAD_THUMBNAIL_QUALITY=80

# Configure sizes
FILEUPLOAD_THUMBNAIL_SIZES=[{"name":"small","width":150,"height":150,"mode":"crop"},{"name":"medium","width":300,"height":300,"mode":"fit"}]

# File validation
FILEUPLOAD_MAX_SIZE=10485760
FILEUPLOAD_ALLOWED_TYPES=image/jpeg,image/png,image/gif,application/pdf
```

## 🎯 Use Cases

### 1. Simple File Upload
```php
$uploadManager = FileUploadManager::create();
$result = $uploadManager->upload($_FILES['file']);
```

### 2. Image Gallery with Thumbnails
```php
$uploadManager = FileUploadManager::create();

// Upload image
$result = $uploadManager->upload($_FILES['image'], [
    'directory' => 'uploads/gallery'
]);

if ($result->isSuccess()) {
    $originalPath = $result->getPath();
    
    // Generate different sizes for gallery
    $thumbnail = $uploadManager->getDynamicUrl($originalPath, 200, 200, 'crop');
    $medium = $uploadManager->getDynamicUrl($originalPath, 600, 400, 'fit');
    $fullsize = $uploadManager->getDynamicUrl($originalPath, 1200, 800, 'fit');
    
    // Display in HTML
    echo "<img src='{$thumbnail}' onclick=\"showLarge('{$fullsize}')\">";
}
```

### 3. User Avatar Upload
```php
$uploadManager = FileUploadManager::create();

$result = $uploadManager->upload($_FILES['avatar'], [
    'directory' => 'uploads/avatars',
    'prefix' => 'user_' . $userId . '_'
]);

if ($result->isSuccess()) {
    $avatarPath = $result->getPath();
    
    // Generate avatar sizes
    $smallAvatar = $uploadManager->getDynamicUrl($avatarPath, 50, 50, 'crop');
    $mediumAvatar = $uploadManager->getDynamicUrl($avatarPath, 100, 100, 'crop');
    
    // Save to user profile
    updateUserAvatar($userId, $avatarPath);
}
```

### 4. Document Management
```php
$uploadManager = FileUploadManager::create();

// Create folder structure
$uploadManager->createFolder('uploads/documents/2024/january');

// Upload document
$result = $uploadManager->upload($_FILES['document'], [
    'directory' => 'uploads/documents/2024/january'
]);

// List all documents
$folderResult = $uploadManager->listFolder('uploads/documents/2024/january');
foreach ($folderResult->getContents() as $file) {
    echo "Document: " . $file['name'] . "\n";
}
```

## 🔧 Advanced Configuration

### Custom Configuration
```php
$uploadManager = FileUploadManager::create([
    'uploads_directory' => 'custom-uploads',
    'auto_configure_processors' => true
]);
```

### Manual Processor Configuration
```php
// For advanced use cases, you can still access the underlying FileUpload instance
$uploadManager = FileUploadManager::create();

// Get the underlying uploader if needed
$uploader = $uploadManager->getUploader(); // This method would need to be added

// Add custom processors
$customProcessor = new MyCustomProcessor();
$uploader->addProcessor($customProcessor);
```

## 🧪 Testing

### Health Check
```php
$uploadManager = FileUploadManager::create();
$health = $uploadManager->healthCheck();

if (!$health['gd_extension']['status']) {
    echo "Warning: GD extension not available - image processing disabled\n";
}

if (!$health['uploads_directory']['status']) {
    echo "Error: Uploads directory not writable\n";
}
```

### Configuration Verification
```php
$status = $uploadManager->getProcessingStatus();

if (!$status['processing_enabled']) {
    echo "Image processing is disabled\n";
}

if (!$status['thumbnails_enabled']) {
    echo "Thumbnail generation is disabled\n";
}
```

## 📊 Benefits

1. **Zero Configuration**: Works out of the box with environment variables
2. **Automatic Processing**: Handles image processing and thumbnails automatically
3. **Clean API**: Simple, intuitive method names
4. **Error Handling**: Built-in error handling and validation
5. **Flexible**: Still allows customization when needed
6. **Consistent**: Same interface for all operations
7. **Testable**: Easy to test and mock

## 🔄 Migration Guide

### From FileUpload to FileUploadManager

**Old Code:**
```php
$uploader = new FileUpload();
$imageProcessor = new ImageProcessor();
$uploader->addProcessor($imageProcessor);
$result = $uploader->upload($_FILES['file']);
```

**New Code:**
```php
$uploadManager = FileUploadManager::create();
$result = $uploadManager->upload($_FILES['file']);
```

### Updating Existing Applications

1. Replace `new FileUpload()` with `FileUploadManager::create()`
2. Remove manual processor configuration
3. Update method calls to use the manager instance
4. Configure via environment variables instead of code

The `FileUploadManager` provides a clean, simple interface while maintaining all the power and flexibility of the underlying FileUpload package!
