<?php
/**
 * Simple Environment-Based FileUpload Usage
 * 
 * This example shows how to use the FileUpload package with simple boolean methods
 * that automatically read all configuration from .env file.
 */

echo "🎬 Simple Environment-Based FileUpload Usage\n";
echo "============================================\n\n";

echo "All configuration comes from your .env file automatically!\n";
echo "Just call methods with true/false to enable/disable features.\n\n";

// ============================================================================
// EXAMPLE 1: Simple Upload with Environment Configuration
// ============================================================================
echo "📦 EXAMPLE 1: Simple Upload with Environment Configuration\n";
echo "----------------------------------------------------------\n";

echo "// Your .env file contains all the configuration:\n";
echo "// FILEUPLOAD_IMAGE_QUALITY=50\n";
echo "// FILEUPLOAD_THUMBNAIL_SIZES=[{\"name\":\"small\",\"width\":150,\"height\":150,\"mode\":\"crop\"},...]\n";
echo "// FILEUPLOAD_MAX_SIZE=10485760\n";
echo "// FILEUPLOAD_ALLOWED_EXTENSIONS=jpg,jpeg,png,gif,webp,svg,pdf,doc,docx,txt,mp4,mov,avi\n";
echo "// etc.\n\n";

echo "// Simple upload with boolean methods\n";
echo "\$uploader = FileUploadManager::createWithChaining()\n";
echo "    ->setImageProcessing(true)          // Uses FILEUPLOAD_IMAGE_QUALITY from .env\n";
echo "    ->setImageCompression(true)         // Uses FILEUPLOAD_IMAGE_QUALITY from .env\n";
echo "    ->setThumbnailGeneration(true)      // Uses FILEUPLOAD_THUMBNAIL_SIZES from .env\n";
echo "    ->setMetadataStripping(true)        // Removes EXIF data\n";
echo "    ->setFileValidation(true)           // Uses all FILEUPLOAD_* validation from .env\n";
echo "    ->setSecurityScanning(false)        // Disable security scan\n";
echo "    ->setDirectory('uploads/gallery');\n\n";

echo "\$result = \$uploader->upload(\$_FILES['file']);\n\n";

// ============================================================================
// EXAMPLE 2: Conditional Configuration
// ============================================================================
echo "🎯 EXAMPLE 2: Conditional Configuration\n";
echo "---------------------------------------\n";

echo "// Configure based on file type\n";
echo "\$fileExtension = pathinfo(\$_FILES['file']['name'], PATHINFO_EXTENSION);\n";
echo "\$isImage = in_array(strtolower(\$fileExtension), ['jpg', 'jpeg', 'png', 'gif']);\n";
echo "\$isPdf = strtolower(\$fileExtension) === 'pdf';\n\n";

echo "\$uploader = FileUploadManager::createWithChaining()\n";
echo "    ->setImageProcessing(\$isImage)      // Only for images\n";
echo "    ->setImageCompression(\$isImage)     // Only for images\n";
echo "    ->setThumbnailGeneration(\$isImage)  // Only for images\n";
echo "    ->setMetadataStripping(\$isImage)    // Only for images\n";
echo "    ->setFileValidation(true)           // Always validate\n";
echo "    ->setSecurityScanning(\$isPdf)      // Only for PDFs\n";
echo "    ->setDirectory('uploads/mixed');\n\n";

echo "\$result = \$uploader->upload(\$_FILES['file']);\n\n";

// ============================================================================
// EXAMPLE 3: MediaService Usage
// ============================================================================
echo "🎬 EXAMPLE 3: MediaService Usage\n";
echo "--------------------------------\n";

echo "// MediaService also uses simple boolean methods\n";
echo "\$mediaService = MediaService::createWithChaining()\n";
echo "    ->setImageProcessing(true)          // Enable image processing\n";
echo "    ->setImageCompression(true)         // Enable compression\n";
echo "    ->setThumbnailGeneration(true)      // Enable thumbnails\n";
echo "    ->setMetadataStripping(true)        // Strip metadata\n";
echo "    ->setFileValidation(true)           // Enable validation\n";
echo "    ->setSecurityScanning(false);       // Disable security scan\n\n";

echo "\$result = \$mediaService->uploadFile(\$_FILES['file'], 'gallery');\n\n";

echo "// Pre-configured services\n";
echo "\$avatarService = MediaService::createForAvatars();\n";
echo "// Calls: setImageProcessing(true), setImageCompression(true), setThumbnailGeneration(true), etc.\n\n";

echo "\$galleryService = MediaService::createForGallery();\n";
echo "// Calls: setImageProcessing(true), setThumbnailGeneration(true), setSecurityScanning(false), etc.\n\n";

echo "\$documentService = MediaService::createForDocuments();\n";
echo "// Calls: setImageProcessing(false), setSecurityScanning(true), etc.\n\n";

// ============================================================================
// EXAMPLE 4: Available Methods
// ============================================================================
echo "📋 EXAMPLE 4: Available Methods\n";
echo "-------------------------------\n";

echo "All methods use environment variables automatically:\n\n";

echo "✅ setImageProcessing(bool \$enabled)\n";
echo "   - Uses: FILEUPLOAD_IMAGE_QUALITY\n";
echo "   - Enables/disables image processing with quality from .env\n\n";

echo "✅ setImageCompression(bool \$enabled)\n";
echo "   - Uses: FILEUPLOAD_IMAGE_QUALITY\n";
echo "   - Enables/disables image compression with quality from .env\n\n";

echo "✅ setThumbnailGeneration(bool \$enabled)\n";
echo "   - Uses: FILEUPLOAD_THUMBNAIL_SIZES\n";
echo "   - Enables/disables thumbnail generation with sizes from .env\n\n";

echo "✅ setMetadataStripping(bool \$enabled)\n";
echo "   - Enables/disables EXIF metadata removal\n\n";

echo "✅ setFileValidation(bool \$enabled)\n";
echo "   - Uses: FILEUPLOAD_MAX_SIZE, FILEUPLOAD_ALLOWED_EXTENSIONS, etc.\n";
echo "   - Enables/disables file validation with all rules from .env\n\n";

echo "✅ setSecurityScanning(bool \$enabled)\n";
echo "   - Enables/disables security scanning for malicious content\n\n";

echo "✅ setDirectory(string \$directory)\n";
echo "   - Sets upload directory\n\n";

// ============================================================================
// EXAMPLE 5: Complete Upload Handler
// ============================================================================
echo "💻 EXAMPLE 5: Complete Upload Handler\n";
echo "-------------------------------------\n";

echo "<?php\n";
echo "// upload-handler.php\n\n";

echo "if (\$_SERVER['REQUEST_METHOD'] === 'POST' && isset(\$_FILES['file'])) {\n";
echo "    try {\n";
echo "        \$uploadType = \$_POST['upload_type'] ?? 'default';\n";
echo "        \$folder = \$_POST['folder'] ?? '';\n\n";

echo "        switch (\$uploadType) {\n";
echo "            case 'avatar':\n";
echo "                \$uploader = FileUploadManager::createWithChaining()\n";
echo "                    ->setImageProcessing(true)\n";
echo "                    ->setImageCompression(true)\n";
echo "                    ->setThumbnailGeneration(true)\n";
echo "                    ->setMetadataStripping(true)\n";
echo "                    ->setFileValidation(true)\n";
echo "                    ->setSecurityScanning(true)\n";
echo "                    ->setDirectory(\$folder ?: 'uploads/avatars');\n";
echo "                break;\n\n";

echo "            case 'gallery':\n";
echo "                \$uploader = FileUploadManager::createWithChaining()\n";
echo "                    ->setImageProcessing(true)\n";
echo "                    ->setImageCompression(true)\n";
echo "                    ->setThumbnailGeneration(true)\n";
echo "                    ->setMetadataStripping(true)\n";
echo "                    ->setFileValidation(true)\n";
echo "                    ->setSecurityScanning(false)    // Performance\n";
echo "                    ->setDirectory(\$folder ?: 'uploads/gallery');\n";
echo "                break;\n\n";

echo "            case 'document':\n";
echo "                \$uploader = FileUploadManager::createWithChaining()\n";
echo "                    ->setImageProcessing(false)\n";
echo "                    ->setImageCompression(false)\n";
echo "                    ->setThumbnailGeneration(false)\n";
echo "                    ->setMetadataStripping(false)\n";
echo "                    ->setFileValidation(true)\n";
echo "                    ->setSecurityScanning(true)\n";
echo "                    ->setDirectory(\$folder ?: 'uploads/documents');\n";
echo "                break;\n\n";

echo "            default:\n";
echo "                \$uploader = FileUploadManager::createWithChaining()\n";
echo "                    ->setImageProcessing(true)\n";
echo "                    ->setImageCompression(true)\n";
echo "                    ->setFileValidation(true)\n";
echo "                    ->setDirectory(\$folder ?: 'uploads/media');\n";
echo "        }\n\n";

echo "        \$result = \$uploader->upload(\$_FILES['file']);\n\n";

echo "        header('Content-Type: application/json');\n";
echo "        echo json_encode(\$result);\n\n";

echo "    } catch (Exception \$e) {\n";
echo "        header('Content-Type: application/json');\n";
echo "        echo json_encode([\n";
echo "            'success' => false,\n";
echo "            'message' => 'Upload failed: ' . \$e->getMessage()\n";
echo "        ]);\n";
echo "    }\n";
echo "}\n\n";

// ============================================================================
// EXAMPLE 6: HTML Form
// ============================================================================
echo "🌐 EXAMPLE 6: HTML Form\n";
echo "-----------------------\n";

echo "<!-- Simple upload form -->\n";
echo "<form id=\"uploadForm\" enctype=\"multipart/form-data\">\n";
echo "    <select name=\"upload_type\">\n";
echo "        <option value=\"avatar\">Avatar Upload</option>\n";
echo "        <option value=\"gallery\">Gallery Upload</option>\n";
echo "        <option value=\"document\">Document Upload</option>\n";
echo "        <option value=\"default\">Default Upload</option>\n";
echo "    </select>\n";
echo "    \n";
echo "    <input type=\"file\" name=\"file\" required>\n";
echo "    <input type=\"text\" name=\"folder\" placeholder=\"Folder (optional)\">\n";
echo "    \n";
echo "    <button type=\"submit\">Upload File</button>\n";
echo "</form>\n\n";

echo "<script>\n";
echo "document.getElementById('uploadForm').addEventListener('submit', function(e) {\n";
echo "    e.preventDefault();\n";
echo "    \n";
echo "    const formData = new FormData(this);\n";
echo "    \n";
echo "    fetch('/upload-handler.php', {\n";
echo "        method: 'POST',\n";
echo "        body: formData\n";
echo "    })\n";
echo "    .then(response => response.json())\n";
echo "    .then(data => {\n";
echo "        if (data.success) {\n";
echo "            alert('Upload successful!');\n";
echo "            console.log('File info:', data.data);\n";
echo "        } else {\n";
echo "            alert('Upload failed: ' + data.message);\n";
echo "        }\n";
echo "    });\n";
echo "});\n";
echo "</script>\n\n";

echo "🎉 Perfect Simple Implementation!\n";
echo "=================================\n\n";

echo "✅ Simple boolean methods: setImageProcessing(true/false)\n";
echo "✅ All configuration from .env file automatically\n";
echo "✅ No need to pass parameters - environment handles everything\n";
echo "✅ Clean method chaining like your template pattern\n";
echo "✅ Easy to enable/disable features conditionally\n\n";

echo "🚀 Your FileUpload package now works exactly as requested!\n";
echo "   Just call methods with true/false and let .env handle the rest!\n";
?>
