<?php

require_once __DIR__ . '/vendor/autoload.php';

// Load environment variables
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
$dotenv->load();

echo "=== FileUpload Package Testing ===\n\n";

// Display configuration
echo "Configuration:\n";
echo "- Max file size: " . ($_ENV['FILEUPLOAD_MAX_SIZE'] ?? '10MB') . "\n";
echo "- Allowed types: " . ($_ENV['FILEUPLOAD_ALLOWED_TYPES'] ?? 'Not set') . "\n";
echo "- Allowed extensions: " . ($_ENV['FILEUPLOAD_ALLOWED_EXTENSIONS'] ?? 'Not set') . "\n";
echo "- Thumbnails enabled: " . ($_ENV['FILEUPLOAD_THUMBNAILS_ENABLED'] ?? 'false') . "\n";
echo "- Upload root: " . ($_ENV['FILEUPLOAD_LOCAL_ROOT'] ?? 'Not set') . "\n";
echo "- Upload URL: " . ($_ENV['FILEUPLOAD_LOCAL_URL'] ?? 'Not set') . "\n\n";

// Test file upload functionality
try {
    // Initialize FileUpload package
    $config = require __DIR__ . '/config/fileupload.php';
    $fileUploadManager = new \Package\FileUpload\FileUploadManager($config);
    
    echo "✓ FileUpload package initialized successfully\n\n";
    
    // Test 1: Create a test image file
    echo "--- Test 1: Image Upload ---\n";
    $testImagePath = '/tmp/test-image-' . date('H-i-s') . '.png';
    
    // Create a simple PNG image
    $image = imagecreate(200, 100);
    $backgroundColor = imagecolorallocate($image, 100, 150, 200);
    $textColor = imagecolorallocate($image, 255, 255, 255);
    imagestring($image, 5, 50, 40, 'TEST IMAGE', $textColor);
    imagepng($image, $testImagePath);
    imagedestroy($image);
    
    // Simulate $_FILES array
    $testFile = [
        'name' => basename($testImagePath),
        'type' => 'image/png',
        'tmp_name' => $testImagePath,
        'error' => UPLOAD_ERR_OK,
        'size' => filesize($testImagePath)
    ];
    
    echo "Created test image: " . basename($testImagePath) . " (" . filesize($testImagePath) . " bytes)\n";
    
    // Upload the file
    $result = $fileUploadManager->upload($testFile);
    
    if ($result->isSuccess()) {
        echo "✓ Image upload successful\n";
        echo "  Path: " . $result->getPath() . "\n";
        echo "  Message: " . $result->getMessage() . "\n";
        
        $metadata = $result->getMetadata();
        if (!empty($metadata)) {
            echo "  Metadata: " . json_encode($metadata) . "\n";
        }
    } else {
        echo "✗ Image upload failed\n";
        echo "  Error: " . $result->getMessage() . "\n";
        foreach ($result->getErrors() as $error) {
            echo "  - $error\n";
        }
    }
    
    // Cleanup
    @unlink($testImagePath);
    echo "\n";
    
    // Test 2: Create a test PDF file
    echo "--- Test 2: PDF Upload ---\n";
    $testPdfPath = '/tmp/test-document-' . date('H-i-s') . '.pdf';
    
    // Create a simple PDF
    $pdfContent = "%PDF-1.4
1 0 obj
<<
/Type /Catalog
/Pages 2 0 R
>>
endobj
2 0 obj
<<
/Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj
3 0 obj
<<
/Type /Page
/Parent 2 0 R
/MediaBox [0 0 612 792]
/Contents 4 0 R
>>
endobj
4 0 obj
<<
/Length 44
>>
stream
BT
/F1 12 Tf
100 700 Td
(Test PDF Document) Tj
ET
endstream
endobj
xref
0 5
0000000000 65535 f 
0000000009 00000 n 
0000000058 00000 n 
0000000115 00000 n 
0000000206 00000 n 
trailer
<<
/Size 5
/Root 1 0 R
>>
startxref
300
%%EOF";
    
    file_put_contents($testPdfPath, $pdfContent);
    
    // Simulate $_FILES array for PDF
    $testPdfFile = [
        'name' => basename($testPdfPath),
        'type' => 'application/pdf',
        'tmp_name' => $testPdfPath,
        'error' => UPLOAD_ERR_OK,
        'size' => filesize($testPdfPath)
    ];
    
    echo "Created test PDF: " . basename($testPdfPath) . " (" . filesize($testPdfPath) . " bytes)\n";
    
    // Upload the PDF
    $pdfResult = $fileUploadManager->upload($testPdfFile);
    
    if ($pdfResult->isSuccess()) {
        echo "✓ PDF upload successful\n";
        echo "  Path: " . $pdfResult->getPath() . "\n";
        echo "  Message: " . $pdfResult->getMessage() . "\n";
    } else {
        echo "✗ PDF upload failed\n";
        echo "  Error: " . $pdfResult->getMessage() . "\n";
        foreach ($pdfResult->getErrors() as $error) {
            echo "  - $error\n";
        }
    }
    
    // Cleanup
    @unlink($testPdfPath);
    echo "\n";
    
    // Test 3: Test invalid file type
    echo "--- Test 3: Invalid File Type ---\n";
    $testInvalidPath = '/tmp/test-invalid-' . date('H-i-s') . '.php';
    file_put_contents($testInvalidPath, '<?php echo "This should be rejected"; ?>');
    
    $testInvalidFile = [
        'name' => basename($testInvalidPath),
        'type' => 'application/x-php',
        'tmp_name' => $testInvalidPath,
        'error' => UPLOAD_ERR_OK,
        'size' => filesize($testInvalidPath)
    ];
    
    echo "Created invalid file: " . basename($testInvalidPath) . "\n";
    
    $invalidResult = $fileUploadManager->upload($testInvalidFile);
    
    if (!$invalidResult->isSuccess()) {
        echo "✓ Invalid file correctly rejected\n";
        echo "  Error: " . $invalidResult->getMessage() . "\n";
    } else {
        echo "✗ Invalid file was accepted (should have been rejected)\n";
    }
    
    // Cleanup
    @unlink($testInvalidPath);
    echo "\n";
    
} catch (Exception $e) {
    echo "✗ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "=== Testing Complete ===\n\n";

// Display example URLs for browser testing
echo "Example URLs for browser testing:\n";
echo "- Media Manager: http://frontcmsdev.local/media\n";
echo "- Dynamic Image Transform: http://frontcmsdev.local/uploads/media/folder/200x200/image.jpg\n";
echo "- PDF Transform: http://frontcmsdev.local/uploads/media/folder/300x400/document.pdf\n\n";

echo "Note: Replace 'folder' and 'image.jpg' with actual folder and file names from your uploads.\n";
