<?php

namespace Middleware;

use Core\Request;
use Core\Response;
use App\Services\Auth;

class Authenticate
{
    public function handle(Request $request, \Closure $next)
    {
        if (!Auth::check()) {
            if ($request->isAjax()) {
                return Response::json([
                    'error' => 'Unauthenticated',
                    'redirect' => '/login'
                ], 401);
            }
            
            $_SESSION['intended_url'] = $request->uri();
            return Response::redirect('/login');
        }

        return $next($request);
    }
}
