<?php
/**
 * FileUpload Package - Configuration Demo
 * 
 * This demo showcases all the configuration approaches:
 * 1. Environment variables (.env)
 * 2. Configuration file (config/fileupload.php)
 * 3. Runtime configuration (method parameters)
 * 4. Different factory methods
 * 
 * Run this file to see how the layered configuration system works.
 */

echo "🚀 FileUpload Package - Configuration Demo\n";
echo "==========================================\n\n";

// Load environment variables from .env file
if (file_exists('.env')) {
    $lines = file('.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '=') !== false && !str_starts_with($line, '#')) {
            list($key, $value) = explode('=', $line, 2);
            $_ENV[trim($key)] = trim($value);
        }
    }
    echo "✅ Environment variables loaded from .env file\n\n";
}

// ============================================================================
// DEMO 1: Default Configuration (Environment + Config File)
// ============================================================================
echo "📋 DEMO 1: Default Configuration\n";
echo "--------------------------------\n";

try {
    // Load configuration using the standard hierarchy
    $config = include 'config/fileupload.php';
    
    echo "Configuration loaded successfully!\n";
    echo "• Storage Driver: " . ($config['default'] ?? 'Not set') . "\n";
    echo "• Upload Root: " . ($config['drivers']['local']['root'] ?? 'Not set') . "\n";
    echo "• Upload URL: " . ($config['drivers']['local']['url'] ?? 'Not set') . "\n";
    echo "• Max File Size: " . formatBytes($config['validation']['max_file_size'] ?? 0) . "\n";
    echo "• Allowed Extensions: " . implode(', ', array_slice($config['validation']['allowed_extensions'] ?? [], 0, 5)) . "...\n";
    echo "• Processing Enabled: " . (($config['processing']['enabled'] ?? false) ? 'Yes' : 'No') . "\n";
    echo "• Image Quality: " . ($config['processing']['quality'] ?? 'Not set') . "%\n\n";
    
} catch (Exception $e) {
    echo "❌ Error loading configuration: " . $e->getMessage() . "\n\n";
}

// ============================================================================
// DEMO 2: Runtime Configuration Override
// ============================================================================
echo "⚡ DEMO 2: Runtime Configuration Override\n";
echo "----------------------------------------\n";

// Simulate runtime configuration for different scenarios
$scenarios = [
    'Avatar Upload' => [
        'validation' => [
            'max_file_size' => 1048576, // 1MB
            'allowed_extensions' => ['jpg', 'jpeg', 'png'],
            'allowed_mime_types' => ['image/jpeg', 'image/png']
        ],
        'processing' => [
            'enabled' => true,
            'compress_images' => true,
            'quality' => 90
        ]
    ],
    
    'Document Upload' => [
        'validation' => [
            'max_file_size' => 10485760, // 10MB
            'allowed_extensions' => ['pdf', 'doc', 'docx', 'txt'],
            'allowed_mime_types' => [
                'application/pdf',
                'application/msword',
                'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                'text/plain'
            ]
        ],
        'processing' => [
            'enabled' => false // No processing for documents
        ]
    ],
    
    'Gallery Upload' => [
        'validation' => [
            'max_file_size' => 20971520, // 20MB
            'allowed_extensions' => ['jpg', 'jpeg', 'png', 'gif', 'webp'],
            'allowed_mime_types' => ['image/jpeg', 'image/png', 'image/gif', 'image/webp']
        ],
        'processing' => [
            'enabled' => true,
            'compress_images' => true,
            'quality' => 85,
            'thumbnails' => [
                'enabled' => true,
                'sizes' => [
                    ['name' => 'thumb', 'width' => 200, 'height' => 200, 'mode' => 'crop'],
                    ['name' => 'medium', 'width' => 800, 'height' => 600, 'mode' => 'fit']
                ]
            ]
        ]
    ]
];

foreach ($scenarios as $name => $runtimeConfig) {
    echo "📁 $name Configuration:\n";
    echo "   Max Size: " . formatBytes($runtimeConfig['validation']['max_file_size']) . "\n";
    echo "   Extensions: " . implode(', ', $runtimeConfig['validation']['allowed_extensions']) . "\n";
    echo "   Processing: " . (($runtimeConfig['processing']['enabled'] ?? false) ? 'Enabled' : 'Disabled') . "\n";
    if (isset($runtimeConfig['processing']['quality'])) {
        echo "   Quality: " . $runtimeConfig['processing']['quality'] . "%\n";
    }
    if (isset($runtimeConfig['processing']['thumbnails']['enabled']) && $runtimeConfig['processing']['thumbnails']['enabled']) {
        echo "   Thumbnails: " . count($runtimeConfig['processing']['thumbnails']['sizes']) . " sizes\n";
    }
    echo "\n";
}

// ============================================================================
// DEMO 3: Dynamic Path Resolution
// ============================================================================
echo "🔧 DEMO 3: Dynamic Path Resolution\n";
echo "----------------------------------\n";

$testPaths = [
    'public/uploads/media' => 'Standard web uploads',
    'storage/uploads' => 'Laravel-style storage',
    'uploads' => 'Simple uploads folder',
    'temp/uploads' => 'Temporary uploads'
];

foreach ($testPaths as $path => $description) {
    echo "Testing: $path ($description)\n";
    
    // Simulate the dynamic path resolution logic
    if (!str_starts_with($path, '/')) {
        $possibleBases = [
            __DIR__ . '/',
            getcwd() . '/',
            $_SERVER['DOCUMENT_ROOT'] ?? __DIR__ . '/',
        ];
        
        $resolved = null;
        foreach ($possibleBases as $base) {
            $fullPath = $base . $path;
            $dir = dirname($fullPath);
            if (is_dir($dir) && is_writable($dir)) {
                $resolved = $fullPath;
                break;
            }
        }
        
        echo "   → Resolved: " . ($resolved ?? 'Could not resolve') . "\n";
        echo "   → Exists: " . (is_dir(dirname($resolved ?? '')) ? '✅' : '❌') . "\n";
        echo "   → Writable: " . (is_writable(dirname($resolved ?? '')) ? '✅' : '❌') . "\n";
    }
    echo "\n";
}

// ============================================================================
// DEMO 4: Configuration Priority Test
// ============================================================================
echo "🎯 DEMO 4: Configuration Priority Test\n";
echo "--------------------------------------\n";

// Show how different configuration layers override each other
$layers = [
    'Default' => ['max_file_size' => 5242880], // 5MB
    'Environment' => ['max_file_size' => (int)($_ENV['FILEUPLOAD_MAX_SIZE'] ?? 10485760)], // From .env
    'Runtime' => ['max_file_size' => 52428800] // 50MB
];

echo "Configuration Priority Demonstration:\n";
foreach ($layers as $layer => $config) {
    echo "   $layer: " . formatBytes($config['max_file_size']) . "\n";
}

// Simulate the priority resolution
$final = array_merge($layers['Default'], $layers['Environment'], $layers['Runtime']);
echo "   Final Result: " . formatBytes($final['max_file_size']) . " (Runtime wins!)\n\n";

// ============================================================================
// DEMO 5: Environment Variable Parsing
// ============================================================================
echo "🔍 DEMO 5: Environment Variable Parsing\n";
echo "---------------------------------------\n";

$envTests = [
    'FILEUPLOAD_PROCESSING_ENABLED' => $_ENV['FILEUPLOAD_PROCESSING_ENABLED'] ?? 'true',
    'FILEUPLOAD_MAX_SIZE' => $_ENV['FILEUPLOAD_MAX_SIZE'] ?? '10485760',
    'FILEUPLOAD_ALLOWED_EXTENSIONS' => $_ENV['FILEUPLOAD_ALLOWED_EXTENSIONS'] ?? 'jpg,png,gif',
    'FILEUPLOAD_IMAGE_QUALITY' => $_ENV['FILEUPLOAD_IMAGE_QUALITY'] ?? '85'
];

foreach ($envTests as $key => $value) {
    echo "$key = '$value'\n";
    
    // Show how each type is parsed
    if ($key === 'FILEUPLOAD_PROCESSING_ENABLED') {
        $parsed = filter_var($value, FILTER_VALIDATE_BOOLEAN);
        echo "   → Parsed as boolean: " . ($parsed ? 'true' : 'false') . "\n";
    } elseif (in_array($key, ['FILEUPLOAD_MAX_SIZE', 'FILEUPLOAD_IMAGE_QUALITY'])) {
        $parsed = (int) $value;
        if ($key === 'FILEUPLOAD_MAX_SIZE') {
            echo "   → Parsed as size: " . formatBytes($parsed) . "\n";
        } else {
            echo "   → Parsed as integer: " . $parsed . "%\n";
        }
    } elseif ($key === 'FILEUPLOAD_ALLOWED_EXTENSIONS') {
        $parsed = array_map('trim', explode(',', $value));
        echo "   → Parsed as array: [" . implode(', ', $parsed) . "]\n";
    }
    echo "\n";
}

// ============================================================================
// DEMO 6: Practical Usage Examples
// ============================================================================
echo "💡 DEMO 6: Practical Usage Examples\n";
echo "-----------------------------------\n";

echo "Here's how you would use the configuration system in practice:\n\n";

echo "// 1. Basic usage (uses environment + config file)\n";
echo "\$uploader = FileUploadManager::create();\n";
echo "\$result = \$uploader->upload(\$_FILES['file']);\n\n";

echo "// 2. Avatar upload with specific limits\n";
echo "\$avatarUploader = FileUploadManager::createWithConfig([\n";
echo "    'validation' => ['max_file_size' => 1048576], // 1MB\n";
echo "    'processing' => ['quality' => 90]\n";
echo "]);\n\n";

echo "// 3. Document upload (no image processing)\n";
echo "\$docUploader = FileUploadManager::createWithValidation([\n";
echo "    'max_file_size' => 10485760, // 10MB\n";
echo "    'allowed_extensions' => ['pdf', 'doc', 'docx']\n";
echo "]);\n\n";

echo "// 4. Custom directory upload\n";
echo "\$galleryUploader = FileUploadManager::createForDirectory(\n";
echo "    'public/gallery', '/gallery'\n";
echo ");\n\n";

// Helper function
function formatBytes($bytes, $precision = 2) {
    if ($bytes == 0) return '0 B';
    
    $units = array('B', 'KB', 'MB', 'GB', 'TB');
    
    for ($i = 0; $bytes > 1024; $i++) {
        $bytes /= 1024;
    }
    
    return round($bytes, $precision) . ' ' . $units[$i];
}

echo "🎉 Demo Complete!\n";
echo "================\n\n";

echo "📋 Summary of Configuration Features:\n";
echo "✅ Environment variables for deployment settings\n";
echo "✅ Configuration file for application defaults\n";
echo "✅ Runtime parameters for dynamic overrides\n";
echo "✅ Dynamic path resolution for portability\n";
echo "✅ Type-safe environment variable parsing\n";
echo "✅ Multiple factory methods for convenience\n";
echo "✅ Clear configuration priority hierarchy\n\n";

echo "🚀 Your FileUpload package now has a professional-grade configuration system!\n";
echo "   Use the right configuration method for each use case and enjoy maximum flexibility.\n";
?>
