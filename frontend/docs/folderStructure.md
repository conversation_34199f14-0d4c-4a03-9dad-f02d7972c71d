### Folder Structure

```
frontcms/
├── app/                        # Optional app-wide services or helpers
│   └── Helpers/
│       └── ResponseHelper.php
│
├── config/                     # Config files
│   └── app.php
│
├── core/                       # Core framework classes
│   ├── Controller.php
│   ├── Middleware.php
│   ├── Request.php
│   ├── Response.php
│   ├── Router.php
│   ├── ServiceContainer.php
│   ├── View.php
│   └── ErrorHandler.php
│
├── middleware/                 # Global and route middleware
│   └── AuthMiddleware.php
│
├── modules/                    # Feature modules
│   ├── Auth/
│   │   ├── Controllers/
│   │   │   └── AuthController.php
│   │   ├── Services/
│   │   │   └── AuthService.php
│   │   ├── Views/
│   │   │   └── login.php
│   │   └── routes.php
│   │
│   └── User/
│       ├── Controllers/
│       │   └── UserController.php
│       ├── Services/
│       │   └── UserService.php
│       ├── Views/
│       │   ├── index.php
│       │   └── partials/
│       │       ├── header.php
│       │       └── footer.php
│       └── routes.php
│
├── public/                     # Publicly accessible directory
│   ├── assets/                 # JS/CSS for AJAX, jQuery etc.
│   └── index.php               # Entry point
│
├── routes/                     # Central route registry
│   └── web.php
│
├── storage/                    # Logs, sessions (if needed), cache
│   └── logs/
│       └── app.log
│
├── vendor/                     # Composer vendor directory (generated)
│
├── .htaccess                   # For Apache (public folder rewrite)
├── composer.json               # Composer config
├── composer.lock
├── README.md
└── .env                        # Optional, for env config

```

### Folder and File Structure Explanation

```
# FrontCMS - Project Structure Documentation

This document provides a comprehensive overview of the FrontCMS project structure, following modern PHP development practices with a custom MVC architecture.

## Core Directories

### `/app` - Application Core
Contains application-wide services, helpers, and shared components.
- `Helpers/` - Reusable helper classes
  - `ResponseHelper.php` - Standardized response formatting

### `/config` - Configuration Files
Central location for all configuration settings.
- `app.php` - Main application configuration
- `database.php` - Database connection settings
- `routes.php` - Route configurations

### `/core` - Framework Core
Custom MVC framework implementation.
- `Controller.php` - Base controller class
- `Middleware.php` - Middleware interface and base class
- `Request.php` - HTTP request handler
- `Response.php` - HTTP response handler
- `Router.php` - URL routing system
- `ServiceContainer.php` - Dependency injection container
- `View.php` - Template rendering engine
- `ErrorHandler.php` - Global error and exception handler

### `/middleware` - HTTP Middleware
Global and route-specific middleware components.
- `AuthMiddleware.php` - Authentication verification
- `CorsMiddleware.php` - CORS headers management
- `LoggingMiddleware.php` - Request/Response logging

## Feature Modules

### `/modules` - Feature Modules
Self-contained feature modules following the same structure:
- `ModuleName/`
  - `Controllers/` - Request handlers
  - `Services/` - Business logic
  - `Views/` - Presentation layer
  - `routes.php` - Module-specific routes

#### Example Module: Auth
- `Auth/`
  - `Controllers/AuthController.php` - Authentication logic
  - `Services/AuthService.php` - Authentication business logic
  - `Views/login.php` - Login template
  - `routes.php` - Authentication routes

## Public Assets

### `/public` - Web Root
Publicly accessible directory served by the web server.
- `assets/` - Frontend assets (JS, CSS, images)
  - `js/` - JavaScript files
  - `css/` - Compiled CSS
  - `images/` - Image assets
- `index.php` - Single entry point

## Support Files

### `/routes` - Route Definitions
- `web.php` - Main route definitions
- `api.php` - API route definitions (if applicable)

### `/storage` - Application Storage
- `logs/` - Application logs
  - `app.log` - Main application log
- `cache/` - Compiled views and cache files
- `sessions/` - Session files (if file-based sessions are used)

### `/vendor` - Dependencies
- Managed by Composer
- Contains all third-party packages

## Configuration Files
- `.env` - Environment variables (not versioned)
- `.env.example` - Example environment variables
- `composer.json` - PHP dependencies
- `composer.lock` - Locked dependency versions
- `.htaccess` - Apache configuration
- `README.md` - Project documentation

## Development Workflow
1. Add new features as modules in `/modules`
2. Place shared utilities in `/app`
3. Configure environment in `.env`
4. Define routes in `/routes`
5. Access application through `/public`

## Best Practices
- Follow PSR-4 autoloading standards
- Keep controllers thin, move logic to services
- Use dependency injection for better testability
- Follow semantic versioning for releases
- Document all public APIs and complex logic

```

