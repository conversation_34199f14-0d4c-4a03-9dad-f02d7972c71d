<?php
/**
 * FileUpload Package - Method Chaining Configuration Demo
 * 
 * This demo shows how to use the method chaining configuration system
 * similar to your example: ->setSearchConfig(true, 'templateSearch')
 * 
 * You can enable/disable features by passing true/false parameters.
 */

echo "🔗 FileUpload Package - Method Chaining Configuration Demo\n";
echo "=========================================================\n\n";

// Load environment variables
if (file_exists('.env')) {
    $lines = file('.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '=') !== false && !str_starts_with($line, '#')) {
            list($key, $value) = explode('=', $line, 2);
            $_ENV[trim($key)] = trim($value);
        }
    }
}

// ============================================================================
// DEMO 1: Basic Method Chaining Configuration
// ============================================================================
echo "📋 DEMO 1: Basic Method Chaining Configuration\n";
echo "----------------------------------------------\n";

echo "// Create uploader with method chaining configuration\n";
echo "\$uploader = FileUploadManager::createWithChaining()\n";
echo "    ->setImageProcessing(true, 90)           // Enable with 90% quality\n";
echo "    ->setThumbnailGeneration(true, [\n";
echo "        ['name' => 'small', 'width' => 150, 'height' => 150, 'mode' => 'crop'],\n";
echo "        ['name' => 'medium', 'width' => 300, 'height' => 300, 'mode' => 'fit']\n";
echo "    ])\n";
echo "    ->setImageCompression(true, 85)          // Enable with 85% quality\n";
echo "    ->setMetadataStripping(true)             // Enable metadata removal\n";
echo "    ->setValidation(true, [\n";
echo "        'max_file_size' => 5242880,          // 5MB\n";
echo "        'allowed_extensions' => ['jpg', 'png', 'gif']\n";
echo "    ])\n";
echo "    ->setSecurityScan(false)                 // Disable security scanning\n";
echo "    ->setUploadDirectory('uploads/gallery')  // Custom directory\n";
echo "    ->setAllowedExtensions(['jpg', 'jpeg', 'png', 'gif', 'webp'])\n";
echo "    ->setMaxFileSize(10485760);              // 10MB\n\n";

// ============================================================================
// DEMO 2: Different Configuration Scenarios
// ============================================================================
echo "⚡ DEMO 2: Different Configuration Scenarios\n";
echo "--------------------------------------------\n";

$scenarios = [
    'Avatar Upload' => [
        'description' => 'Small profile images with high quality',
        'config' => [
            'imageProcessing' => [true, 95],
            'thumbnailGeneration' => [true, [
                ['name' => 'thumb', 'width' => 100, 'height' => 100, 'mode' => 'crop'],
                ['name' => 'profile', 'width' => 200, 'height' => 200, 'mode' => 'crop']
            ]],
            'imageCompression' => [true, 95],
            'metadataStripping' => [true],
            'validation' => [true, ['max_file_size' => 1048576]], // 1MB
            'securityScan' => [true],
            'uploadDirectory' => ['uploads/avatars'],
            'allowedExtensions' => [['jpg', 'jpeg', 'png']],
            'maxFileSize' => [1048576] // 1MB
        ]
    ],
    
    'Document Upload' => [
        'description' => 'Documents with no image processing',
        'config' => [
            'imageProcessing' => [false],
            'thumbnailGeneration' => [false],
            'imageCompression' => [false],
            'metadataStripping' => [false],
            'validation' => [true, ['max_file_size' => 10485760]], // 10MB
            'securityScan' => [true],
            'uploadDirectory' => ['uploads/documents'],
            'allowedExtensions' => [['pdf', 'doc', 'docx', 'txt']],
            'maxFileSize' => [10485760] // 10MB
        ]
    ],
    
    'Gallery Upload' => [
        'description' => 'High-quality images with multiple thumbnails',
        'config' => [
            'imageProcessing' => [true, 90],
            'thumbnailGeneration' => [true, [
                ['name' => 'thumb', 'width' => 200, 'height' => 200, 'mode' => 'crop'],
                ['name' => 'medium', 'width' => 600, 'height' => 400, 'mode' => 'fit'],
                ['name' => 'large', 'width' => 1200, 'height' => 800, 'mode' => 'fit']
            ]],
            'imageCompression' => [true, 90],
            'metadataStripping' => [true],
            'validation' => [true, ['max_file_size' => 20971520]], // 20MB
            'securityScan' => [false],
            'uploadDirectory' => ['uploads/gallery'],
            'allowedExtensions' => [['jpg', 'jpeg', 'png', 'gif', 'webp']],
            'maxFileSize' => [20971520] // 20MB
        ]
    ],
    
    'Quick Upload' => [
        'description' => 'Fast upload with minimal processing',
        'config' => [
            'imageProcessing' => [false],
            'thumbnailGeneration' => [false],
            'imageCompression' => [false],
            'metadataStripping' => [false],
            'validation' => [false],
            'securityScan' => [false],
            'uploadDirectory' => ['uploads/temp'],
            'allowedExtensions' => [['jpg', 'png', 'gif', 'pdf', 'txt']],
            'maxFileSize' => [52428800] // 50MB
        ]
    ]
];

foreach ($scenarios as $name => $scenario) {
    echo "📁 $name:\n";
    echo "   Description: {$scenario['description']}\n";
    echo "   Configuration:\n";
    
    foreach ($scenario['config'] as $method => $params) {
        $methodName = 'set' . ucfirst($method);
        if (is_array($params[0])) {
            if ($method === 'thumbnailGeneration' && count($params) > 1) {
                echo "   ->$methodName({$params[0]}, [thumbnails array])\n";
            } else {
                echo "   ->$methodName([" . implode(', ', $params[0]) . "])\n";
            }
        } else {
            $paramStr = implode(', ', array_map(function($p) {
                return is_bool($p) ? ($p ? 'true' : 'false') : 
                       (is_string($p) ? "'$p'" : $p);
            }, $params));
            echo "   ->$methodName($paramStr)\n";
        }
    }
    echo "\n";
}

// ============================================================================
// DEMO 3: Conditional Configuration
// ============================================================================
echo "🎯 DEMO 3: Conditional Configuration\n";
echo "------------------------------------\n";

echo "You can conditionally enable features based on your needs:\n\n";

echo "// Example: Enable processing only for images\n";
echo "\$isImage = in_array(\$fileExtension, ['jpg', 'jpeg', 'png', 'gif']);\n";
echo "\$isPdf = \$fileExtension === 'pdf';\n\n";

echo "\$uploader = FileUploadManager::createWithChaining()\n";
echo "    ->setImageProcessing(\$isImage, 85)      // Only for images\n";
echo "    ->setThumbnailGeneration(\$isImage)      // Only for images\n";
echo "    ->setImageCompression(\$isImage, 80)     // Only for images\n";
echo "    ->setMetadataStripping(\$isImage)        // Only for images\n";
echo "    ->setValidation(true)                   // Always validate\n";
echo "    ->setSecurityScan(\$isPdf)              // Only for PDFs\n";
echo "    ->setUploadDirectory('uploads/mixed');\n\n";

// ============================================================================
// DEMO 4: Feature Toggle Examples
// ============================================================================
echo "🔧 DEMO 4: Feature Toggle Examples\n";
echo "----------------------------------\n";

$features = [
    'Image Processing' => [
        'enabled' => 'setImageProcessing(true, 90)',
        'disabled' => 'setImageProcessing(false)',
        'description' => 'Resize, compress, and optimize images'
    ],
    'Thumbnail Generation' => [
        'enabled' => 'setThumbnailGeneration(true, $thumbnailSizes)',
        'disabled' => 'setThumbnailGeneration(false)',
        'description' => 'Generate multiple thumbnail sizes'
    ],
    'Image Compression' => [
        'enabled' => 'setImageCompression(true, 85)',
        'disabled' => 'setImageCompression(false)',
        'description' => 'Compress images to reduce file size'
    ],
    'Metadata Stripping' => [
        'enabled' => 'setMetadataStripping(true)',
        'disabled' => 'setMetadataStripping(false)',
        'description' => 'Remove EXIF and other metadata from images'
    ],
    'File Validation' => [
        'enabled' => 'setValidation(true, $validationRules)',
        'disabled' => 'setValidation(false)',
        'description' => 'Validate file size, type, and extension'
    ],
    'Security Scanning' => [
        'enabled' => 'setSecurityScan(true)',
        'disabled' => 'setSecurityScan(false)',
        'description' => 'Scan files for malicious content'
    ]
];

foreach ($features as $feature => $config) {
    echo "🔹 $feature:\n";
    echo "   Description: {$config['description']}\n";
    echo "   Enable:  ->{$config['enabled']}\n";
    echo "   Disable: ->{$config['disabled']}\n\n";
}

// ============================================================================
// DEMO 5: Complete Usage Example
// ============================================================================
echo "💡 DEMO 5: Complete Usage Example\n";
echo "---------------------------------\n";

echo "Here's a complete example of how to use the method chaining configuration:\n\n";

echo "<?php\n";
echo "use Package\\FileUpload\\FileUploadManager;\n\n";

echo "// Create uploader with method chaining\n";
echo "\$uploader = FileUploadManager::createWithChaining()\n";
echo "    ->setImageProcessing(true, 90)\n";
echo "    ->setThumbnailGeneration(true, [\n";
echo "        ['name' => 'small', 'width' => 150, 'height' => 150, 'mode' => 'crop'],\n";
echo "        ['name' => 'medium', 'width' => 300, 'height' => 300, 'mode' => 'fit']\n";
echo "    ])\n";
echo "    ->setImageCompression(true, 85)\n";
echo "    ->setMetadataStripping(true)\n";
echo "    ->setValidation(true, [\n";
echo "        'max_file_size' => 5242880,\n";
echo "        'allowed_extensions' => ['jpg', 'png', 'gif']\n";
echo "    ])\n";
echo "    ->setSecurityScan(false)\n";
echo "    ->setUploadDirectory('uploads/gallery')\n";
echo "    ->setAllowedExtensions(['jpg', 'jpeg', 'png', 'gif', 'webp'])\n";
echo "    ->setMaxFileSize(10485760);\n\n";

echo "// Upload file\n";
echo "\$result = \$uploader->upload(\$_FILES['file']);\n\n";

echo "if (\$result->isSuccess()) {\n";
echo "    echo 'File uploaded successfully!';\n";
echo "    echo 'URL: ' . \$result->getUrl();\n";
echo "    echo 'Path: ' . \$result->getPath();\n";
echo "} else {\n";
echo "    echo 'Upload failed: ' . \$result->getMessage();\n";
echo "}\n";

echo "\n🎉 Demo Complete!\n";
echo "================\n\n";

echo "📋 Summary of Method Chaining Configuration:\n";
echo "✅ setImageProcessing(bool \$enabled, int \$quality = 85)\n";
echo "✅ setThumbnailGeneration(bool \$enabled, array \$sizes = [])\n";
echo "✅ setImageCompression(bool \$enabled, int \$quality = 85)\n";
echo "✅ setMetadataStripping(bool \$enabled)\n";
echo "✅ setValidation(bool \$enabled, array \$rules = [])\n";
echo "✅ setSecurityScan(bool \$enabled)\n";
echo "✅ setUploadDirectory(string \$directory)\n";
echo "✅ setAllowedExtensions(array \$extensions)\n";
echo "✅ setMaxFileSize(int \$sizeInBytes)\n\n";

echo "🚀 Each method returns \$this for perfect method chaining!\n";
echo "   Pass true to enable features, false to disable them.\n";
?>
