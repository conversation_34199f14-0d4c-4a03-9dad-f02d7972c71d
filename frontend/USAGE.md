# FileUpload Package - Step-by-Step Usage Guide

This guide provides detailed step-by-step instructions for using the FileUpload package with the new refactored architecture.

## 📋 Prerequisites

Before starting, ensure you have:

- PHP 8.0 or higher
- GD extension installed
- Composer installed
- Web server (Apache/Nginx) with mod_rewrite enabled
- Writable uploads directory

## 🔧 Step 1: System Check

### 1.1 Run Dependency Check

Open your browser and navigate to:

```
http://your-domain/demo-dependency-check.php
```

This will verify:

- ✅ PHP version compatibility
- ✅ GD extension availability
- ✅ Composer autoload
- ✅ Package installation
- ✅ Transform endpoint
- ✅ Directory permissions

### 1.2 Fix Any Issues

If the dependency check shows errors:

**PHP Version Issues:**

```bash
# Update PHP (Ubuntu/Debian)
sudo apt update
sudo apt install php8.0 php8.0-gd php8.0-curl php8.0-zip

# Update PHP (CentOS/RHEL)
sudo yum install php80 php80-gd php80-curl php80-zip
```

**GD Extension Missing:**

```bash
# Ubuntu/Debian
sudo apt install php-gd

# CentOS/RHEL
sudo yum install php-gd

# Restart web server
sudo systemctl restart apache2  # or nginx
```

**Permission Issues:**

```bash
# Make uploads directory writable
sudo chmod 755 uploads
sudo chown -R www-data:www-data uploads  # Ubuntu/Debian
sudo chown -R apache:apache uploads      # CentOS/RHEL
```

## 🚀 Step 2: Basic Setup

### 2.1 Environment Configuration

Copy the comprehensive example environment file and configure it:

```bash
cp .env.example .env
```

The `.env.example` file contains **100+ configuration options** for the FileUpload package. Here are the essential settings:

```env
# Storage Configuration
FILEUPLOAD_DRIVER=local
FILEUPLOAD_LOCAL_ROOT=uploads
FILEUPLOAD_LOCAL_URL=/uploads

# File Validation
FILEUPLOAD_MAX_SIZE=10485760
FILEUPLOAD_ALLOWED_TYPES=image/jpeg,image/png,image/gif,application/pdf
FILEUPLOAD_FORBIDDEN_EXTENSIONS=php,exe,bat,sh,js

# Image Processing
FILEUPLOAD_PROCESSING_ENABLED=true
FILEUPLOAD_IMAGE_QUALITY=85

# Security
FILEUPLOAD_VALIDATE_HEADERS=true
FILEUPLOAD_SANITIZE_FILENAMES=true

# Dynamic Transformation
FILEUPLOAD_TRANSFORM_ENABLED=true
FILEUPLOAD_TRANSFORM_DEFAULT_MODE=fit

# Folder Operations
FILEUPLOAD_FOLDERS_ENABLED=true
FILEUPLOAD_MAX_FOLDER_DEPTH=10
```

**📋 Complete Configuration Available:**

The `.env.example` file includes settings for:

- ✅ **Multiple Storage Drivers**: Local, S3, Google Cloud, Azure
- ✅ **Advanced Security**: File validation, virus scanning, rate limiting
- ✅ **Image Processing**: Quality control, dimension limits, orientation
- ✅ **Thumbnail Generation**: Automatic thumbnail creation with custom sizes
- ✅ **Dynamic Transformation**: URL-based image resizing and cropping
- ✅ **Performance Optimization**: Caching, compression, memory management
- ✅ **Logging & Debugging**: Detailed error reporting and statistics
- ✅ **Webhook Integration**: Upload event notifications
- ✅ **Backup Configuration**: Automatic file backup systems
- ✅ **Custom Patterns**: Directory structures and filename patterns
- ✅ **CDN Integration**: Content delivery network support

### 2.2 Directory Structure

Ensure your directory structure looks like this:

```
frontend/
├── uploads/                    # Upload directory (writable)
├── transform.php              # Auto-installed transform endpoint
├── demo-dependency-check.php  # System check tool
├── test-fileupload.php        # Full feature test
├── .env                       # Environment configuration
├── package/FileUpload/        # Package source
└── vendor/                    # Composer dependencies
```

### 2.3 Verify Installation

Run the test page:

```
http://your-domain/test-fileupload.php
```

## 📁 Step 3: Basic File Upload

### 3.1 Simple Upload Form

Create a basic upload form:

```html
<!DOCTYPE html>
<html>
  <head>
    <title>File Upload Test</title>
  </head>
  <body>
    <h1>Upload File</h1>
    <form method="POST" enctype="multipart/form-data">
      <input type="file" name="upload_file" required />
      <button type="submit">Upload</button>
    </form>
  </body>
</html>
```

### 3.2 Handle Upload

Add PHP code to handle the upload:

```php
<?php
require_once 'vendor/autoload.php';

use Package\FileUpload\FileUpload;

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['upload_file'])) {
    try {
        // Create uploader instance
        $uploader = new FileUpload();

        // Upload file
        $result = $uploader->upload($_FILES['upload_file'], [
            'directory' => 'uploads/documents'
        ]);

        if ($result->isSuccess()) {
            echo "<div style='color: green;'>";
            echo "✅ Upload successful!<br>";
            echo "File: " . htmlspecialchars($result->getPath()) . "<br>";
            echo "URL: <a href='" . htmlspecialchars($result->getUrl()) . "' target='_blank'>" . htmlspecialchars($result->getUrl()) . "</a>";
            echo "</div>";
        } else {
            echo "<div style='color: red;'>";
            echo "❌ Upload failed: " . htmlspecialchars($result->getMessage());
            echo "</div>";
        }
    } catch (Exception $e) {
        echo "<div style='color: red;'>Error: " . htmlspecialchars($e->getMessage()) . "</div>";
    }
}
?>
```

### 3.3 Test the Upload

1. Save the file as `simple-upload.php`
2. Open in browser: `http://your-domain/simple-upload.php`
3. Select a file and click Upload
4. Verify the file appears in the `uploads/documents/` directory

## 🖼️ Step 4: Dynamic Image Transformation

### 4.1 Upload an Image

First, upload an image using the form above. Note the file path (e.g., `uploads/documents/image.jpg`).

### 4.2 Test Transformation URLs

Try these URL formats in your browser:

**Explicit Mode:**

```
http://your-domain/uploads/documents/300x200/fit=image.jpg
http://your-domain/uploads/documents/150x150/crop=image.jpg
http://your-domain/uploads/documents/400x300/fill=image.jpg
```

**Default Mode (cleaner URLs):**

```
http://your-domain/uploads/documents/300x200/image.jpg
http://your-domain/uploads/documents/150x150/image.jpg
```

**Query Parameter Mode:**

```
http://your-domain/transform.php?path=uploads/documents/image.jpg&w=300&h=200&m=fit
http://your-domain/transform.php?path=uploads/documents/image.jpg&w=150&h=150&m=crop
```

### 4.3 Generate URLs Programmatically

```php
<?php
$uploader = new FileUpload();

// Assume you have uploaded an image
$imagePath = 'uploads/documents/image.jpg';

// Generate different sizes
$thumbnail = $uploader->getDynamicUrl($imagePath, 150, 150, 'crop');
$medium = $uploader->getDynamicUrl($imagePath, 400, 300, 'fit');
$large = $uploader->getDynamicUrl($imagePath, 800, 600, 'fit');

// Generate clean URLs (default mode)
$cleanThumb = $uploader->getDynamicUrl($imagePath, 150, 150, 'fit', false, true);
$cleanMedium = $uploader->getDynamicUrl($imagePath, 400, 300, 'fit', false, true);

echo "<h3>Generated URLs:</h3>";
echo "<p>Thumbnail: <img src='$thumbnail' style='max-width: 150px;'></p>";
echo "<p>Medium: <img src='$medium' style='max-width: 400px;'></p>";
echo "<p>Large: <img src='$large' style='max-width: 800px;'></p>";
echo "<p>Clean Thumbnail: <img src='$cleanThumb' style='max-width: 150px;'></p>";
?>
```

## 📂 Step 5: Folder Operations

### 5.1 Create Folders

```php
<?php
$uploader = new FileUpload();

// Create a simple folder
$result = $uploader->createFolder('uploads/gallery');
if ($result->isSuccess()) {
    echo "✅ Folder created: " . $result->getPath();
} else {
    echo "❌ Failed: " . $result->getMessage();
}

// Create nested folders
$result = $uploader->createFolder('uploads/users/john123/photos');
if ($result->isSuccess()) {
    echo "✅ Nested folder created: " . $result->getPath();
}
?>
```

### 5.2 List Folder Contents

```php
<?php
$result = $uploader->listFolder('uploads');
if ($result->isSuccess()) {
    $contents = $result->getContents();
    echo "<h3>Folder Contents:</h3>";
    echo "<ul>";
    foreach ($contents as $item) {
        $icon = $item['type'] === 'directory' ? '📁' : '📄';
        echo "<li>$icon " . htmlspecialchars($item['name']) . " (" . $item['type'] . ")</li>";
    }
    echo "</ul>";
} else {
    echo "❌ Failed to list folder: " . $result->getMessage();
}
?>
```

### 5.3 Delete Folders

```php
<?php
// Delete empty folder
$result = $uploader->deleteFolder('uploads/empty-folder');
if ($result->isSuccess()) {
    echo "✅ Folder deleted successfully";
} else {
    echo "❌ Failed to delete folder: " . $result->getMessage();
}

// Delete folder with contents (use with caution!)
$result = $uploader->deleteFolder('uploads/old-gallery');
if ($result->isSuccess()) {
    echo "✅ Folder and contents deleted";
}
?>
```

## 🧪 Step 6: Advanced Usage

### 6.1 Custom Validation

```php
<?php
$uploader = new FileUpload([
    'max_size' => 2 * 1024 * 1024,  // 2MB limit
    'allowed_types' => ['jpg', 'png', 'gif'],  // Images only
    'directory' => 'uploads/gallery'
]);

$result = $uploader->upload($_FILES['image'], [
    'prefix' => 'gallery_',
    'generate_unique_name' => true
]);
?>
```

### 6.2 Multiple File Upload

```php
<?php
if (isset($_FILES['multiple_files'])) {
    $uploader = new FileUpload();

    foreach ($_FILES['multiple_files']['tmp_name'] as $index => $tmpName) {
        if ($_FILES['multiple_files']['error'][$index] === UPLOAD_ERR_OK) {
            $file = [
                'name' => $_FILES['multiple_files']['name'][$index],
                'type' => $_FILES['multiple_files']['type'][$index],
                'tmp_name' => $tmpName,
                'error' => $_FILES['multiple_files']['error'][$index],
                'size' => $_FILES['multiple_files']['size'][$index]
            ];

            $result = $uploader->upload($file, [
                'directory' => 'uploads/batch'
            ]);

            if ($result->isSuccess()) {
                echo "✅ Uploaded: " . $result->getPath() . "<br>";
            } else {
                echo "❌ Failed: " . $result->getMessage() . "<br>";
            }
        }
    }
}
?>
```

### 6.3 Image Gallery with Transformations

```php
<?php
$uploader = new FileUpload();

// List all images in gallery
$result = $uploader->listFolder('uploads/gallery');
if ($result->isSuccess()) {
    echo "<div style='display: grid; grid-template-columns: repeat(auto-fill, minmax(200px, 1fr)); gap: 20px;'>";

    foreach ($result->getContents() as $item) {
        if ($item['type'] === 'file' && in_array(strtolower(pathinfo($item['name'], PATHINFO_EXTENSION)), ['jpg', 'jpeg', 'png', 'gif'])) {
            $imagePath = 'uploads/gallery/' . $item['name'];
            $thumbnail = $uploader->getDynamicUrl($imagePath, 200, 200, 'crop');
            $fullSize = $uploader->getDynamicUrl($imagePath, 800, 600, 'fit');

            echo "<div style='border: 1px solid #ddd; padding: 10px; border-radius: 8px;'>";
            echo "<a href='$fullSize' target='_blank'>";
            echo "<img src='$thumbnail' style='width: 100%; height: 200px; object-fit: cover;' alt='" . htmlspecialchars($item['name']) . "'>";
            echo "</a>";
            echo "<p style='margin: 10px 0 0 0; font-size: 12px;'>" . htmlspecialchars($item['name']) . "</p>";
            echo "</div>";
        }
    }

    echo "</div>";
}
?>
```

## 🔧 Step 7: Troubleshooting

### 7.1 Common Issues and Solutions

**Issue: "Transform endpoint not working"**

```bash
# Check if transform.php exists
ls -la transform.php

# Check .htaccess rules
cat .htaccess

# Test mod_rewrite
echo "<?php phpinfo(); ?>" > info.php
# Open http://your-domain/info.php and search for "mod_rewrite"
```

**Issue: "Permission denied"**

```bash
# Fix directory permissions
sudo chmod 755 uploads
sudo chown -R www-data:www-data uploads

# Check current permissions
ls -la uploads/
```

**Issue: "File too large"**

```bash
# Check PHP settings
php -i | grep -E "(upload_max_filesize|post_max_size|memory_limit)"

# Update php.ini if needed
sudo nano /etc/php/8.0/apache2/php.ini
# Set: upload_max_filesize = 10M
# Set: post_max_size = 10M
# Restart: sudo systemctl restart apache2
```

### 7.2 Debug Mode

Enable debug mode for detailed error information:

```php
<?php
$uploader = new FileUpload(['debug' => true]);

try {
    $result = $uploader->upload($_FILES['file']);
    // ... handle result
} catch (Exception $e) {
    echo "Debug info: " . $e->getMessage();
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>
```

### 7.3 Logging

Enable logging for production debugging:

```php
<?php
// Add to your upload script
error_log("FileUpload attempt: " . json_encode($_FILES));

$result = $uploader->upload($_FILES['file']);
if (!$result->isSuccess()) {
    error_log("Upload failed: " . $result->getMessage());
}
?>
```

## 📊 Step 8: Performance Optimization

### 8.1 Image Optimization

```php
<?php
// Add image processor for automatic optimization
$imageProcessor = new \Package\FileUpload\Processing\ImageProcessor();
$uploader->addProcessor($imageProcessor);

$result = $uploader->upload($_FILES['image'], [
    'directory' => 'uploads/optimized',
    'quality' => 85  // JPEG quality
]);
?>
```

### 8.2 Caching Headers

The transform endpoint automatically sets proper caching headers:

- `Cache-Control: public, max-age=31536000` (1 year)
- `Expires` header set to 1 year from now
- `Last-Modified` header for conditional requests

### 8.3 CDN Integration

For production, consider using a CDN:

```php
<?php
// Configure for CDN usage
$uploader = new FileUpload([
    'base_url' => 'https://cdn.yourdomain.com'
]);

$result = $uploader->upload($_FILES['file']);
if ($result->isSuccess()) {
    $cdnUrl = $result->getUrl();  // Will use CDN base URL
}
?>
```

## 🎯 Step 9: Production Deployment

### 9.1 Security Checklist

- ✅ Validate file types and sizes
- ✅ Scan uploaded files for malware
- ✅ Use proper directory permissions
- ✅ Disable PHP execution in uploads directory
- ✅ Implement rate limiting
- ✅ Use HTTPS for file uploads

### 9.2 .htaccess Security

Add to your uploads/.htaccess:

```apache
# Disable PHP execution
<Files "*.php">
    Order Deny,Allow
    Deny from all
</Files>

# Disable script execution
AddHandler cgi-script .php .pl .py .jsp .asp .sh .cgi
Options -ExecCGI
```

### 9.3 Monitoring

Monitor your upload system:

```php
<?php
// Log upload statistics
$stats = [
    'timestamp' => time(),
    'file_size' => $_FILES['file']['size'],
    'file_type' => $_FILES['file']['type'],
    'user_ip' => $_SERVER['REMOTE_ADDR']
];
error_log("Upload stats: " . json_encode($stats));
?>
```

## 🎉 Congratulations!

You have successfully set up and configured the FileUpload package with:

- ✅ File upload functionality
- ✅ Dynamic image transformation
- ✅ Folder operations
- ✅ Security measures
- ✅ Performance optimization

For additional help, refer to:

- `demo-dependency-check.php` - System verification
- `test-fileupload.php` - Feature testing
- `README.md` - Complete documentation
- Package documentation in `package/FileUpload/docs/`
