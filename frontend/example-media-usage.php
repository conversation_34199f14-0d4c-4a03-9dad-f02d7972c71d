<?php
/**
 * Media Module - Usage Examples with Method Chaining
 * 
 * This file shows practical examples of how to use the enhanced media module
 * with method chaining configuration.
 */

// Include the MediaService
require_once 'modules/media/Services/MediaService.php';
use Modules\Media\Services\MediaService;

echo "🎬 Media Module - Practical Usage Examples\n";
echo "==========================================\n\n";

// ============================================================================
// EXAMPLE 1: Avatar Upload Service
// ============================================================================
echo "📸 EXAMPLE 1: Avatar Upload Service\n";
echo "-----------------------------------\n";

echo "// Create avatar upload service with high quality settings\n";
echo "\$avatarService = MediaService::createForAvatars();\n";
echo "// OR customize it:\n";
echo "\$avatarService = MediaService::createWithChaining()\n";
echo "    ->setImageProcessing(true, 95)              // High quality\n";
echo "    ->setThumbnailGeneration(true, [\n";
echo "        ['name' => 'thumb', 'width' => 100, 'height' => 100, 'mode' => 'crop'],\n";
echo "        ['name' => 'profile', 'width' => 200, 'height' => 200, 'mode' => 'crop']\n";
echo "    ])\n";
echo "    ->setImageCompression(true, 95)             // High quality compression\n";
echo "    ->setMetadataStripping(true)                // Remove EXIF data\n";
echo "    ->setValidation(true, ['max_file_size' => 1048576]) // 1MB limit\n";
echo "    ->setSecurityScan(true)                     // Enable security\n";
echo "    ->setAllowedExtensions(['jpg', 'jpeg', 'png'])\n";
echo "    ->setMaxFileSize(1048576);                  // 1MB\n\n";

// ============================================================================
// EXAMPLE 2: Gallery Upload Service
// ============================================================================
echo "🖼️ EXAMPLE 2: Gallery Upload Service\n";
echo "------------------------------------\n";

echo "// Create gallery upload service with multiple thumbnails\n";
echo "\$galleryService = MediaService::createForGallery();\n";
echo "// OR customize it:\n";
echo "\$galleryService = MediaService::createWithChaining()\n";
echo "    ->setImageProcessing(true, 90)              // Good quality\n";
echo "    ->setThumbnailGeneration(true, [\n";
echo "        ['name' => 'thumb', 'width' => 200, 'height' => 200, 'mode' => 'crop'],\n";
echo "        ['name' => 'medium', 'width' => 600, 'height' => 400, 'mode' => 'fit'],\n";
echo "        ['name' => 'large', 'width' => 1200, 'height' => 800, 'mode' => 'fit']\n";
echo "    ])\n";
echo "    ->setImageCompression(true, 90)             // Good compression\n";
echo "    ->setMetadataStripping(true)                // Remove metadata\n";
echo "    ->setValidation(true, ['max_file_size' => 20971520]) // 20MB\n";
echo "    ->setSecurityScan(false)                    // Disable for performance\n";
echo "    ->setAllowedExtensions(['jpg', 'jpeg', 'png', 'gif', 'webp'])\n";
echo "    ->setMaxFileSize(20971520);                 // 20MB\n\n";

// ============================================================================
// EXAMPLE 3: Document Upload Service
// ============================================================================
echo "📄 EXAMPLE 3: Document Upload Service\n";
echo "-------------------------------------\n";

echo "// Create document upload service (no image processing)\n";
echo "\$documentService = MediaService::createForDocuments();\n";
echo "// OR customize it:\n";
echo "\$documentService = MediaService::createWithChaining()\n";
echo "    ->setImageProcessing(false)                 // No image processing\n";
echo "    ->setThumbnailGeneration(false)             // No thumbnails\n";
echo "    ->setImageCompression(false)                // No compression\n";
echo "    ->setMetadataStripping(false)               // Keep metadata\n";
echo "    ->setValidation(true, ['max_file_size' => 10485760]) // 10MB\n";
echo "    ->setSecurityScan(true)                     // Enable security\n";
echo "    ->setAllowedExtensions(['pdf', 'doc', 'docx', 'txt'])\n";
echo "    ->setMaxFileSize(10485760);                 // 10MB\n\n";

// ============================================================================
// EXAMPLE 4: Using in Upload Forms
// ============================================================================
echo "📤 EXAMPLE 4: Using in Upload Forms\n";
echo "-----------------------------------\n";

echo "<!-- HTML Form with upload type -->\n";
echo "<form id=\"uploadForm\" enctype=\"multipart/form-data\">\n";
echo "    <select name=\"upload_type\" id=\"uploadType\">\n";
echo "        <option value=\"default\">Default Upload</option>\n";
echo "        <option value=\"avatar\">Avatar Upload</option>\n";
echo "        <option value=\"gallery\">Gallery Upload</option>\n";
echo "        <option value=\"document\">Document Upload</option>\n";
echo "        <option value=\"custom\">Custom Upload</option>\n";
echo "    </select>\n";
echo "    \n";
echo "    <input type=\"file\" name=\"file\" required>\n";
echo "    <input type=\"text\" name=\"folder\" placeholder=\"Folder (optional)\">\n";
echo "    \n";
echo "    <button type=\"submit\">Upload File</button>\n";
echo "</form>\n\n";

echo "<!-- JavaScript for handling uploads -->\n";
echo "<script>\n";
echo "document.getElementById('uploadForm').addEventListener('submit', function(e) {\n";
echo "    e.preventDefault();\n";
echo "    \n";
echo "    const formData = new FormData(this);\n";
echo "    \n";
echo "    fetch('/media/upload', {\n";
echo "        method: 'POST',\n";
echo "        body: formData\n";
echo "    })\n";
echo "    .then(response => response.json())\n";
echo "    .then(data => {\n";
echo "        if (data.success) {\n";
echo "            console.log('Upload successful:', data.data);\n";
echo "            alert('File uploaded successfully!');\n";
echo "            \n";
echo "            // Handle thumbnails if generated\n";
echo "            if (data.data.thumbnails) {\n";
echo "                console.log('Generated thumbnails:', data.data.thumbnails);\n";
echo "            }\n";
echo "        } else {\n";
echo "            console.error('Upload failed:', data.message);\n";
echo "            alert('Upload failed: ' + data.message);\n";
echo "        }\n";
echo "    })\n";
echo "    .catch(error => {\n";
echo "        console.error('Error:', error);\n";
echo "        alert('Upload error: ' + error.message);\n";
echo "    });\n";
echo "});\n";
echo "</script>\n\n";

// ============================================================================
// EXAMPLE 5: Conditional Configuration
// ============================================================================
echo "🎯 EXAMPLE 5: Conditional Configuration\n";
echo "---------------------------------------\n";

echo "// Configure based on file type\n";
echo "\$fileExtension = pathinfo(\$_FILES['file']['name'], PATHINFO_EXTENSION);\n";
echo "\$isImage = in_array(strtolower(\$fileExtension), ['jpg', 'jpeg', 'png', 'gif', 'webp']);\n";
echo "\$isPdf = strtolower(\$fileExtension) === 'pdf';\n\n";

echo "\$conditionalService = MediaService::createWithChaining()\n";
echo "    ->setImageProcessing(\$isImage, 85)          // Only process images\n";
echo "    ->setThumbnailGeneration(\$isImage, [\n";
echo "        ['name' => 'thumb', 'width' => 150, 'height' => 150, 'mode' => 'crop']\n";
echo "    ])\n";
echo "    ->setImageCompression(\$isImage, 80)         // Only compress images\n";
echo "    ->setMetadataStripping(\$isImage)            // Only strip image metadata\n";
echo "    ->setValidation(true)                       // Always validate\n";
echo "    ->setSecurityScan(\$isPdf)                  // Scan PDFs for security\n";
echo "    ->setMaxFileSize(\$isImage ? 10485760 : 20971520); // 10MB images, 20MB others\n\n";

// ============================================================================
// EXAMPLE 6: User Role-Based Configuration
// ============================================================================
echo "👤 EXAMPLE 6: User Role-Based Configuration\n";
echo "-------------------------------------------\n";

echo "// Configure based on user permissions\n";
echo "// \$userRole = getCurrentUserRole(); // Your function\n";
echo "\$userRole = 'premium'; // Example\n";
echo "\$isAdmin = \$userRole === 'admin';\n";
echo "\$isPremium = in_array(\$userRole, ['admin', 'premium']);\n\n";

echo "\$roleBasedService = MediaService::createWithChaining()\n";
echo "    ->setImageProcessing(true, \$isPremium ? 95 : 80)     // Higher quality for premium\n";
echo "    ->setThumbnailGeneration(\$isPremium, [\n";
echo "        ['name' => 'thumb', 'width' => 150, 'height' => 150, 'mode' => 'crop'],\n";
echo "        ['name' => 'medium', 'width' => 300, 'height' => 300, 'mode' => 'fit']\n";
echo "    ])\n";
echo "    ->setImageCompression(true, \$isPremium ? 90 : 70)   // Better compression for premium\n";
echo "    ->setMetadataStripping(true)\n";
echo "    ->setValidation(true)\n";
echo "    ->setSecurityScan(true)\n";
echo "    ->setMaxFileSize(\$isAdmin ? 52428800 : (\$isPremium ? 20971520 : 5242880)); // 50MB admin, 20MB premium, 5MB basic\n\n";

// ============================================================================
// EXAMPLE 7: Complete Upload Handler
// ============================================================================
echo "💻 EXAMPLE 7: Complete Upload Handler\n";
echo "-------------------------------------\n";

echo "<?php\n";
echo "// complete-upload-handler.php\n\n";

echo "if (\$_SERVER['REQUEST_METHOD'] === 'POST' && isset(\$_FILES['file'])) {\n";
echo "    try {\n";
echo "        \$uploadType = \$_POST['upload_type'] ?? 'default';\n";
echo "        \$folder = \$_POST['folder'] ?? '';\n\n";

echo "        // Get configured service based on upload type\n";
echo "        switch (\$uploadType) {\n";
echo "            case 'avatar':\n";
echo "                \$service = MediaService::createForAvatars();\n";
echo "                break;\n";
echo "            case 'gallery':\n";
echo "                \$service = MediaService::createForGallery();\n";
echo "                break;\n";
echo "            case 'document':\n";
echo "                \$service = MediaService::createForDocuments();\n";
echo "                break;\n";
echo "            case 'custom':\n";
echo "                \$service = MediaService::createWithChaining()\n";
echo "                    ->setImageProcessing(true, 85)\n";
echo "                    ->setThumbnailGeneration(true)\n";
echo "                    ->setImageCompression(true, 80)\n";
echo "                    ->setMetadataStripping(true)\n";
echo "                    ->setValidation(true)\n";
echo "                    ->setSecurityScan(false);\n";
echo "                break;\n";
echo "            default:\n";
echo "                \$service = new MediaService();\n";
echo "        }\n\n";

echo "        // Upload the file\n";
echo "        \$result = \$service->uploadFile(\$_FILES['file'], \$folder);\n\n";

echo "        // Return JSON response\n";
echo "        header('Content-Type: application/json');\n";
echo "        echo json_encode(\$result);\n\n";

echo "    } catch (Exception \$e) {\n";
echo "        header('Content-Type: application/json');\n";
echo "        echo json_encode([\n";
echo "            'success' => false,\n";
echo "            'message' => 'Upload failed: ' . \$e->getMessage()\n";
echo "        ]);\n";
echo "    }\n";
echo "}\n";

echo "\n🎉 Media Module Usage Examples Complete!\n";
echo "========================================\n\n";

echo "📋 Key Features:\n";
echo "✅ Method chaining configuration: ->setImageProcessing(true, 90)\n";
echo "✅ Pre-configured services: createForAvatars(), createForGallery(), createForDocuments()\n";
echo "✅ Custom configuration: createWithChaining()->setFeature(true/false)\n";
echo "✅ Upload type support in forms and JavaScript\n";
echo "✅ Conditional configuration based on file type or user role\n";
echo "✅ Complete upload handling with proper error management\n\n";

echo "🚀 Your media module now uses the same method chaining pattern as your templates!\n";
?>
