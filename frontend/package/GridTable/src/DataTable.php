<?php

namespace Package\GridTable\Src;

use Core\Request;
use Package\GridTable\Src\Components\ActionBarRender;
use Package\GridTable\Src\Components\BreadcrumbRender;
use Package\GridTable\Src\Components\GrindViewRender;
use Package\GridTable\Src\Components\TableViewRender;

class DataTable implements DataTableRenderInterface {
    
    protected Request $request;  // Add this line
    protected array $columns = []; 
    protected array $data = []; 
    protected array $pagination = []; 
    protected array $sortColumn = []; 
    protected string $sortDirection = 'asc'; 
    protected string $tableId = 'dataTable'; 
    protected string $tableCustomClass = '';
    protected bool $searchable = true;
    protected string $searchId = 'tableSearch';
    protected array $actionButtons = [];
    protected array $rowActions = [];
    protected array $viewButtons = [];
    protected array $gridConfig = [];
    protected string $defaultView = 'table'; // 'table' or 'grid'
    protected array $breadcrumbs = [];
    protected $pageTitle = '';

    protected bool $showTitleBreadcrumb = false;

    protected string $ajaxUrl = '';

    public function __construct(array $data, array $columns, Request $request, ?array $pagination = [], ?array $sortColumn = [], ?string $sortDirection = 'asc') {

        $this->data = $data;
        $this->columns = $columns;
        $this->request = $request;
        $this->pagination = $pagination;
        $this->sortColumn = $sortColumn;
        $this->sortDirection = $sortDirection;

        $this->ajaxUrl = $this->getDefaultAjaxUrl();

        // Initialize features
        $this->initializePagination();
        $this->initializeSorting();
    }

    private function initializePagination() 
    {
        $currentPage = $this->request->get('page', 1);
        $currentLimit = $this->request->get('limit', 10);
        
        if (is_array($this->pagination)) {
            $this->pagination = [
                'current_page' => $this->pagination['current_page'] ?? $currentPage,
                'per_page' => $this->pagination['per_page'] ?? $currentLimit,
                'total' => $this->pagination['total'] ?? null,
                'next_page_url' => $this->pagination['next_page_url'] ?? null,
                'prev_page_url' => $this->pagination['prev_page_url'] ?? null,
                'first_page_url' => $this->pagination['first_page_url'] ?? null,
                'last_page_url' => $this->pagination['last_page_url'] ?? null,
                'has_more_pages' => !empty($this->pagination['next_page_url']),
                'has_previous_pages' => !empty($this->pagination['prev_page_url'])
            ];
        } else {
            $this->pagination = [
                'current_page' => 1,
                'per_page' => $currentLimit,
                'total' => count($this->data),
                'next_page_url' => null,
                'prev_page_url' => null,
                'first_page_url' => null,
                'last_page_url' => null,
                'has_more_pages' => false,
                'has_previous_pages' => false
            ];
        }
    }
    
    private function initializeSorting() 
    {
        // Initialize as empty array if not already set
        if (!is_array($this->sortColumn)) {
            $this->sortColumn = [];
        }
        
        $sort = $this->request->get('sort', 'id');
        
        if ($sort) {
            if (str_starts_with($sort, '-')) {
                // Add the column to the sort columns array
                $this->sortColumn = [substr($sort, 1)];
                $this->sortDirection = 'desc';
            } else {
                $this->sortColumn = [$sort];  // Wrap in array
                $this->sortDirection = 'asc';
            }
        } else {
            // Default sort column if none provided
            $this->sortColumn = ['id'];
            $this->sortDirection = 'asc';
        }
    }

    public function setAjaxUrl(string $url): self
    {
        $this->ajaxUrl = $url;
        return $this;
    }

    /**
     * NEW: Set only breadcrumbs
     */
    public function setBreadcrumbs($title, $breadcrumbs) {
        $this->pageTitle = $title;
        $this->breadcrumbs = $breadcrumbs;
        $this->showTitleBreadcrumb = true;
        return $this;
    }

    /**
     * Set grid view configuration
     */
    public function setGridConfig($config) {
        $defaultGridConfig = [
            'enabled' => false,
            'columns' => 'grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6',
            'cardTemplate' => null,
            'imageKey' => 'image',
            'titleKey' => 'name',
            'subtitleKey' => 'id',
            'descriptionKey' => 'description',
            'metaKey' => 'created_at',
            'actions' => [],
            'cardClass' => 'group bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-200 overflow-hidden border border-gray-200',
            'imageClass' => 'h-28 rounded-lg overflow-hidden',
            'titleClass' => 'text-sm font-medium text-gray-800 mb-2 truncate',
            'subtitleClass' => 'text-xs text-gray-500 mb-2',
            'descriptionClass' => 'text-xs text-gray-500 mb-3 line-clamp-2 flex-grow',
            'layout' => 'default' // 'default', 'horizontal', 'minimal', 'detailed'
        ];
        
        $this->gridConfig = array_merge($defaultGridConfig, $config);
        return $this;
    }
    
    /**
     * Set default view
     */
    public function setDefaultView($view) {
        $this->defaultView = $view;
        return $this;
    }
    
    /**
     * Set view buttons
     */
    public function setViewButtons($buttons) {
        $this->viewButtons = $buttons;
        return $this;
    }
    
    /**
     * Set table ID
     */
    public function setTableId($id) {
        $this->tableId = $id;
        return $this;
    }
    /**
     * Set table ID
     */
    public function setTableCustomClass($class) {
        $this->tableCustomClass = $class;
        return $this;
    }
    
    /**
     * Set search configuration
     */
    public function setSearchConfig($searchable, $searchId = 'tableSearch') {
        $this->searchable = $searchable;
        $this->searchId = $searchId;
        return $this;
    }
    
    /**
     * Set action buttons
     */
    public function setActionButtons($buttons) {
        $this->actionButtons = $buttons;
        return $this;
    }
    
    /**
     * Set row actions
     */
    public function setRowActions($actions) {
        $this->rowActions = $actions;
        return $this;
    }

    public function render(): string {
        $output = '';

        $output .= '<header class="bg-white py-3 border-b border-gray-200 mx-4"><div class="flex items-center justify-between">';
        // NEW: Render title and breadcrumb if enabled
        if ($this->showTitleBreadcrumb) {
            $output .= BreadcrumbRender::render($this->pageTitle, $this->breadcrumbs);
        }

        $output .= ActionBarRender::render($this->viewButtons, $this->gridConfig, $this->defaultView, $this->searchable, $this->searchId, $this->actionButtons);
        $output .= '</div></header>';
        
        // Always render table view, but hide it if grid is enabled and default view is grid

        if ($this->defaultView === 'table') {
            $output .= '<div class="table-view">';
            $output .= TableViewRender::render($this->defaultView, $this->gridConfig, $this->tableCustomClass, $this->tableId, $this->columns, $this->sortColumn, $this->sortDirection, $this->rowActions, $this->data);
            $output .= '</div>';
        }
        
        // Always render grid view if enabled, but hide it if default view is table
        if ($this->defaultView === 'grid') {
            $output .= '<div class="grid-view">';
            $output .= GrindViewRender::render($this->defaultView, $this->gridConfig, $this->data);
            $output .= '</div>';
        }
        
        $output .= $this->renderPagination();

        $this->renderJavaScript();
        
        return $output;
    }

    /**
     * Render pagination with proper URL handling
     */
    private function renderPagination() {
        if (empty($this->data)) {
            return '';
        }
        
        $output = '<div class="bg-white mx-6 paginationWrapper">';
        $output .= '<div class="flex items-center justify-between py-3">';
        
        // Left side: Results info and per-page dropdown
        $output .= '<div class="flex items-center space-x-4">';
        
        // Results info
        if (!empty($this->pagination['from']) && !empty($this->pagination['to'])) {
            $output .= '<div class="text-sm text-gray-700">';
            $output .= 'Showing <span class="font-medium">' . $this->pagination['from'] . '</span>';
            $output .= ' to <span class="font-medium">' . $this->pagination['to'] . '</span>';
            if (!empty($this->pagination['total'])) {
                $output .= ' of <span class="font-medium">' . $this->pagination['total'] . '</span> results';
            }
            $output .= '</div>';
        }
        
        // Per-page limit dropdown
        $currentLimit = $this->pagination['per_page'];
        $limitOptions = [10, 25, 50, 100];
        
        $output .= '<div class="flex items-center space-x-2">';
        $output .= '<label class="text-sm text-gray-700">Show:</label>';
        $output .= '<select id="perPageSelect" class="border border-gray-300 rounded px-3 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">';
        
        foreach ($limitOptions as $option) {
            $selected = ($currentLimit == $option) ? 'selected' : '';
            $output .= '<option value="' . $option . '" ' . $selected . '>' . $option . '</option>';
        }
        
        $output .= '</select>';
        $output .= '<span class="text-sm text-gray-700">per page</span>';
        $output .= '</div>';
        
        $output .= '</div>';
        
        // Right side: Navigation buttons
        $output .= '<div class="flex items-center space-x-2">';
        
        // Previous button
        if ($this->pagination['has_previous_pages'] && !empty($this->pagination['prev_page_url'])) {
            $prevPage = $this->extractPageFromUrl($this->pagination['prev_page_url']);
            $queryParams = $_GET;
            $queryParams['page'] = $prevPage;
            $prevUrl = $this->request->uri(). '?' . http_build_query($queryParams);
            
            $output .= '<a href="javascript:void(0)" data-url="'.url($prevUrl).'" class="previous-pagination relative inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors">';
            $output .= '<svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">';
            $output .= '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>';
            $output .= '</svg>';
            $output .= 'Previous';
            $output .= '</a>';
        } else {
            $output .= '<span class="relative inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-300 bg-gray-100 cursor-not-allowed">';
            $output .= '<svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">';
            $output .= '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>';
            $output .= '</svg>';
            $output .= 'Previous';
            $output .= '</span>';
        }
        
        // Page info
        $output .= '<span class="text-sm text-gray-700 px-3">Page ' . $this->pagination['current_page'] . '</span>';
        
        // Next button
        if ($this->pagination['has_more_pages'] && !empty($this->pagination['next_page_url'])) {
            $nextPage = $this->extractPageFromUrl($this->pagination['next_page_url']);
            $queryParams = $_GET;
            $queryParams['page'] = $nextPage;
            $nextUrl = $this->request->uri().'?' . http_build_query($queryParams);
            
            $output .= '<a  href="javascript:void(0)" data-url="'.url($nextUrl).'" class="next-pagination relative inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors">';
            $output .= 'Next';
            $output .= '<svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">';
            $output .= '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>';
            $output .= '</svg>';
            $output .= '</a>';
        } else {
            $output .= '<span class="relative inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-300 bg-gray-100 cursor-not-allowed">';
            $output .= 'Next';
            $output .= '<svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">';
            $output .= '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>';
            $output .= '</svg>';
            $output .= '</span>';
        }
        
        $output .= '</div>';
        $output .= '</div>';
        $output .= '</div>';
        
        return $output;
    }

    /**
     * Extract page number from URL
     */
    private function extractPageFromUrl($url) {
        if (empty($url)) {
            return 1;
        }
        
        $parsedUrl = parse_url($url);
        if (isset($parsedUrl['query'])) {
            parse_str($parsedUrl['query'], $queryParams);
            return isset($queryParams['page']) ? (int)$queryParams['page'] : 1;
        }
        
        return 1;
    }

    private function getDefaultAjaxUrl(): string
    {
        // Get current URL without query parameters
        $url = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);

        // Add a query parameter to identify AJAX requests

        return config('app_url') . (strpos($url, '/') === 0 ? '' : '/') . ltrim($url, '/') . '?ajax=1';
    }

    /**
     * IMPROVED JavaScript with better search and view toggle functionality
     */
    private function renderJavaScript() {
        $config = [
            'tableId' => $this->tableId,
            'searchId' => $this->searchId,
            'currentData' => $this->data,
            'clientSideSearch' => $this->searchable,
            'gridEnabled' => $this->gridConfig['enabled'],
            'defaultView' => $this->defaultView,
            'ajaxUrl' => $this->ajaxUrl // You'll need to set this property
        ];

        // In your DataTable.php's renderJavaScript method
        $output = '
            <script>
            if (typeof window.TableGridManager === "function") {
                try {
                    new window.TableGridManager(' . json_encode($config) . ');
                } catch (e) {
                    console.error("Error initializing TableGridManager:", e);
                }
            } else {
            }
                </script>
            ';
        
        return add_footer_content($output);
    }
}