<?php

namespace Package\GridTable\Src\Components;


class TableViewRender
{

    private static function renderInternal($defaultView, $gridConfig, $tableCustomClass, $tableId, $columns, $sortColumn, $sortDirection, $rowActions, $data)
    {
        $hiddenClass = ($defaultView === 'grid' && $gridConfig['enabled']) ? 'table-view-hidden' : '';
        $output = '<div class="table-view ' . $hiddenClass . ' w-full">';
        $output .= '<div class="p-6 tableWrapper ' . $tableCustomClass . '"><div class="relative overflow-x-auto border overflow-hidden border-gray-200 rounded-xl gridTableOverflowWrapper">';
        $output .= '<table class="w-full text-sm text-left rtl:text-right text-gray-500" id="' . $tableId . '">';

        $output .= self::renderTableHeader($columns, $sortColumn, $sortDirection, $rowActions);
        $output .= self::renderTableBody($data, $columns, $rowActions);

        $output .= '</table>';
        $output .= '</div></div>';
        $output .= '</div>';

        return $output;
    }

    /**
     * Render table header with FIXED sorting URLs
     */
    private static function renderTableHeader($columns, $sortColumn, $sortDirection, $rowActions)
    {
        $output = '<thead class="text-xs text-gray-700 uppercase bg-gray-50">';
        $output .= '<tr class="border-b">';

        foreach ($columns as $key => $column) {
            $sortable = isset($column['sortable']) && $column['sortable'] ? true : false;
            $classes = isset($column['class']) ? $column['class'] : 'px-6 py-3';

            $output .= '<th scope="col" class="' . $classes . '">';

            if ($sortable) {
                // Check if this column is currently being sorted
                $isCurrentlySorted = (is_array($sortColumn) && in_array($key, $sortColumn)) ||
                    (is_string($sortColumn) && $sortColumn === $key);
                $currentDirection = $isCurrentlySorted ? $sortDirection : 'none';
                $nextDirection = ($currentDirection === 'asc') ? 'desc' : 'asc';

                $output .= '<button type="button" data-sort="' . $key . '" data-direction="' . $nextDirection . '" class="sorting flex items-center justify-between hover:bg-gray-100 transition-colors cursor-pointer w-full text-left p-1 rounded group">';
                $output .= '<span>' . htmlspecialchars($column['label']) . '</span>';

                // Add sort indicator
                if ($currentDirection !== 'none') {
                    $iconColor = 'text-blue-600';
                    $output .= '<svg class="w-4 h-4 ml-2 ' . $iconColor . '" fill="currentColor" viewBox="0 0 24 24">';
                    if ($currentDirection === 'asc') {
                        $output .= '<path d="M7 14l5-5 5 5H7z"/>';
                    } else {
                        $output .= '<path d="M7 10l5 5 5-5H7z"/>';
                    }
                    $output .= '</svg>';
                } else {
                    // Show neutral sort icon on hover
                    $output .= '<svg class="w-4 h-4 ml-2 opacity-0 group-hover:opacity-50 text-gray-400" fill="currentColor" viewBox="0 0 24 24">';
                    $output .= '<path d="M12 5.83L15.17 9l1.41-1.41L12 3 7.41 7.59 8.83 9 12 5.83zm0 12.34L8.83 15l-1.41 1.41L12 21l4.59-4.59L15.17 15 12 18.17z"/>';
                    $output .= '</svg>';
                }

                $output .= '</button>';
            } else {
                $output .= htmlspecialchars($column['label']);
            }

            $output .= '</th>';
        }

        if (!empty($rowActions)) {
            $output .= '<th scope="col" class="px-6 py-3 text-center">Actions</th>';
        }

        $output .= '</tr>';
        $output .= '</thead>';

        return $output;
    }

    /**
     * Render table body
     */
    private static function renderTableBody($data, $columns, $rowActions)
    {
        $output = '<tbody class="bg-white divide-y divide-gray-200">';

        if (empty($data)) {
            $colSpan = count($columns) + (!empty($rowActions) ? 1 : 0);
            $output .= '<tr>';
            $output .= '<td colspan="' . $colSpan . '" class="px-6 py-3 text-center text-gray-500">No data found</td>';
            $output .= '</tr>';
        } else {
            foreach ($data as $row) {
                $output .= '<tr class="hover:bg-gray-50 searchable-row">';

                foreach ($columns as $key => $column) {
                    $value = isset($row[$key]) ? $row[$key] : '';
                    $classes = isset($column['cellClass']) ? $column['cellClass'] : 'px-6 py-3 whitespace-nowrap';

                    $output .= '<td class="' . $classes . '">';

                    if (isset($column['formatter']) && is_callable($column['formatter'])) {
                        $output .= call_user_func($column['formatter'], $value, $row);
                    } else {
                        $output .= htmlspecialchars($value);
                    }

                    $output .= '</td>';
                }

                if (!empty($rowActions)) {
                    $output .= '<td class="px-6 py-3 whitespace-nowrap text-sm text-center actionColumn">';
                    $output .= '<div class="flex items-center space-x-1 justify-center">';

                    foreach ($rowActions as $action) {
                        if (isset($action['condition']) && is_callable($action['condition']) && !call_user_func($action['condition'], $row)) {
                            continue;
                        }

                        $url = isset($action['href']) ? $action['href'] : '#';

                        if (isset($action['urlFormatter']) && is_callable($action['urlFormatter'])) {
                            $url = call_user_func($action['urlFormatter'], $row);
                        }

                        $textColor = isset($action['textColor']) ? $action['textColor'] : 'text-gray-500';
                        $hoverTextColor = isset($action['hoverTextColor']) ? $action['hoverTextColor'] : 'hover:text-gray-700';
                        $bgHoverColor = isset($action['bgHoverColor']) ? $action['bgHoverColor'] : 'hover:bg-gray-100';

                        $output .= '<div class="relative group">';

                        if (isset($action['type']) && $action['type'] === 'button') {
                            $btnAttr = '';
                            if (isset($action['attributes'])) {
                                foreach ($action['attributes'] as $attr => $attrFormatter) {
                                    if ($attr === 'class') {
                                        continue;
                                    }
                                    $attrValue = is_callable($attrFormatter) ? call_user_func($attrFormatter, $row) : $attrFormatter;
                                    $btnAttr .= ' ' . $attr . '="' . htmlspecialchars($attrValue) . '"';
                                }
                            }

                            $baseClass = "p-2 rounded-lg transition-colors duration-200";
                            $colorClass = "$textColor $hoverTextColor $bgHoverColor";

                            $additionalClasses = '';
                            if (isset($action['attributes']['class'])) {
                                $classValue = $action['attributes']['class'];
                                if (is_callable($classValue)) {
                                    $additionalClasses = call_user_func($classValue, $row);
                                } else {
                                    $additionalClasses = $classValue;
                                }

                                $additionalClasses = preg_replace('/(text-[a-z]+-[0-9]+\s*|hover:text-[a-z]+-[0-9]+\s*|hover:bg-[a-z]+-[0-9]+\s*)/', '', $additionalClasses);
                            }

                            $fullClass = trim("$baseClass $colorClass $additionalClasses");

                            $output .= '<button type="button" class="' . $fullClass . '"' . $btnAttr . '>';
                        } else {
                            $baseClass = "block p-2 rounded-lg transition-colors duration-200";
                            $colorClass = "$textColor $hoverTextColor $bgHoverColor";

                            $additionalClasses = isset($action['class']) ? $action['class'] : '';
                            $additionalClasses = preg_replace('/(text-[a-z]+-[0-9]+\s*|hover:text-[a-z]+-[0-9]+\s*|hover:bg-[a-z]+-[0-9]+\s*)/', '', $additionalClasses);

                            $fullClass = trim("$baseClass $colorClass $additionalClasses");

                            $output .= '<a href="' . $url . '" class="' . $fullClass . '">';
                        }

                        if (isset($action['icon'])) {
                            $output .= '<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">';
                            $output .= $action['icon'];
                            $output .= '</svg>';
                        } else {
                            $output .= htmlspecialchars($action['label']);
                        }

                        if (isset($action['type']) && $action['type'] === 'button') {
                            $output .= '</button>';
                        } else {
                            $output .= '</a>';
                        }

                        if (isset($action['tooltip'])) {
                            $output .= '<div class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 hidden group-hover:block w-auto z-10">';
                            $output .= '<div class="bg-gray-800 text-white text-xs rounded py-1 px-2 whitespace-nowrap">';
                            $output .= htmlspecialchars($action['tooltip']);
                            $output .= '<div class="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-800"></div>';
                            $output .= '</div>';
                            $output .= '</div>';
                        }

                        $output .= '</div>';
                    }

                    $output .= '</div>';
                    $output .= '</td>';
                }

                $output .= '</tr>';
            }
        }

        $output .= '</tbody>';

        return $output;
    }

    public static function render($defaultView, $gridConfig, $tableCustomClass, $tableId, $columns, $sortColumn, $sortDirection, $rowActions, $data)
    {
        return self::renderInternal($defaultView, $gridConfig, $tableCustomClass, $tableId, $columns, $sortColumn, $sortDirection, $rowActions, $data);
    }
}
