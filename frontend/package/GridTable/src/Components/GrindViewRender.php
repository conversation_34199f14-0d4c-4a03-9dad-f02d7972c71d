<?php 

namespace Package\GridTable\Src\Components;


class GrindViewRender {
    
    private static function renderInternal($defaultView, $gridConfig, $data) {
        if (!$gridConfig['enabled']) {
            return '';
        }
        $hiddenClass = ($defaultView === 'table' && !$gridConfig['enabled']) ? 'grid-view-hidden' : '';
        $gridColumns = $gridConfig['columns'];
        
        $output = '<div class="grid-view ' . $hiddenClass . ' grid ' . $gridColumns . ' gap-4 mb-6 p-6">';
        
        if (empty($data)) {
            $output .= '<div class="col-span-full text-center text-gray-500 py-8">No data found</div>';
        } else {
            foreach ($data as $item) {
                if (isset($gridConfig['cardTemplate']) && is_callable($gridConfig['cardTemplate'])) {
                    $output .= call_user_func($gridConfig['cardTemplate'], $item, $gridConfig);
                } else {
                    $output .= self::renderCard($item, $gridConfig);
                }
            }
        }
        
        $output .= '</div>';
        
        return $output;
    }

    /**
     * Render card based on layout
     */
    private static function renderCard($item, $gridConfig) {
        $layout = $gridConfig['layout'] ?? 'default';
        
        switch ($layout) {
            case 'horizontal':
                return self::renderHorizontalCard($item, $gridConfig);
            case 'minimal':
                return self::renderMinimalCard($item , $gridConfig);
            case 'detailed':
                return self::renderDetailedCard($item, $gridConfig);
            default:
                return self::renderDefaultCard($item, $gridConfig);
        }
    }
    
    /**
     * Default card layout
     */
    private static function renderDefaultCard($item, $gridConfig) {
        $imageKey = $gridConfig['imageKey'];
        $titleKey = $gridConfig['titleKey'];
        $subtitleKey = $gridConfig['subtitleKey'];
        $descriptionKey = $gridConfig['descriptionKey'];
        
        $image = isset($item[$imageKey]) ? $item[$imageKey] : '';
        $title = isset($item[$titleKey]) ? $item[$titleKey] : '';
        $subtitle = isset($item[$subtitleKey]) ? $item[$subtitleKey] : '';
        $description = isset($item[$descriptionKey]) ? $item[$descriptionKey] : '';
        
        $output = '<div class="grid-item ' . $gridConfig['cardClass'] . '">';
        $output .= '<div class="flex flex-col h-full">';
        
        // Image section
        if ($image) {
            $output .= '<div class="relative p-3 pb-0">';
            $output .= '<div class="' . $gridConfig['imageClass'] . '">';
            $output .= '<img src="' . htmlspecialchars($image) . '" alt="' . htmlspecialchars($title) . '" class="w-full h-full object-cover">';
            $output .= '</div>';
            $output .= '</div>';
        }
        
        // Content section
        $output .= '<div class="p-3 flex flex-col flex-grow">';
        
        if ($title) {
            $output .= '<h3 class="' . $gridConfig['titleClass'] . '">' . htmlspecialchars($title) . '</h3>';
        }
        
        if ($subtitle) {
            $output .= '<div class="' . $gridConfig['subtitleClass'] . '">ID: ' . htmlspecialchars($subtitle) . '</div>';
        }
        
        if ($description) {
            $output .= '<p class="' . $gridConfig['descriptionClass'] . '">' . htmlspecialchars($description) . '</p>';
        }
        
        // Actions
        $output .= self::renderCardActions($item, $gridConfig);
        
        $output .= '</div>';
        $output .= '</div>';
        $output .= '</div>';
        
        return $output;
    }
    
    /**
     * Horizontal card layout
     */
    private static function renderHorizontalCard($item, $gridConfig ) {
        $imageKey = $gridConfig['imageKey'];
        $titleKey = $gridConfig['titleKey'];
        $subtitleKey = $gridConfig['subtitleKey'];
        $descriptionKey = $gridConfig['descriptionKey'];
        
        $image = isset($item[$imageKey]) ? $item[$imageKey] : '';
        $title = isset($item[$titleKey]) ? $item[$titleKey] : '';
        $subtitle = isset($item[$subtitleKey]) ? $item[$subtitleKey] : '';
        $description = isset($item[$descriptionKey]) ? $item[$descriptionKey] : '';
        
        $output = '<div class="grid-item ' . $gridConfig['cardClass'] . '">';
        $output .= '<div class="flex h-full">';
        
        // Image section (left side)
        if ($image) {
            $output .= '<div class="w-1/3 p-3">';
            $output .= '<div class="h-full rounded-lg overflow-hidden">';
            $output .= '<img src="' . htmlspecialchars($image) . '" alt="' . htmlspecialchars($title) . '" class="w-full h-full object-cover">';
            $output .= '</div>';
            $output .= '</div>';
        }
        
        // Content section (right side)
        $output .= '<div class="flex-1 p-3 flex flex-col">';
        
        if ($title) {
            $output .= '<h3 class="' . $gridConfig['titleClass'] . '">' . htmlspecialchars($title) . '</h3>';
        }
        
        if ($subtitle) {
            $output .= '<div class="' . $gridConfig['subtitleClass'] . '">ID: ' . htmlspecialchars($subtitle) . '</div>';
        }
        
        if ($description) {
            $output .= '<p class="' . $gridConfig['descriptionClass'] . '">' . htmlspecialchars($description) . '</p>';
        }
        
        $output .= self::renderCardActions($item, $gridConfig);
        
        $output .= '</div>';
        $output .= '</div>';
        $output .= '</div>';
        
        return $output;
    }
    
    /**
     * Minimal card layout
     */
    private static function renderMinimalCard($item, $gridConfig) {
        $titleKey = $gridConfig['titleKey'];
        $subtitleKey = $gridConfig['subtitleKey'];
        
        $title = isset($item[$titleKey]) ? $item[$titleKey] : '';
        $subtitle = isset($item[$subtitleKey]) ? $item[$subtitleKey] : '';
        
        $output = '<div class="grid-item ' . str_replace('shadow-sm hover:shadow-md', 'shadow-none hover:shadow-sm', $gridConfig['cardClass']) . '">';
        $output .= '<div class="p-4 text-center">';
        
        if ($title) {
            $output .= '<h3 class="text-sm font-medium text-gray-800 mb-1">' . htmlspecialchars($title) . '</h3>';
        }
        
        if ($subtitle) {
            $output .= '<div class="text-xs text-gray-500 mb-2">' . htmlspecialchars($subtitle) . '</div>';
        }
        
        $output .= self::renderCardActions($item, $gridConfig);
        
        $output .= '</div>';
        $output .= '</div>';
        
        return $output;
    }
    
    /**
     * Detailed card layout
     */
    private static function renderDetailedCard($item, $gridConfig) {
        return self::renderDefaultCard($item, $gridConfig);
    }
    
    /**
     * Render card actions
     */
    private static function renderCardActions($item, $gridConfig, $justify = 'justify-between') {
        if (empty($gridConfig['actions'])) {
            return '';
        }
        
        $output = '<div class="flex items-center ' . $justify . ' mt-auto pt-2 border-t border-gray-100">';
        
        foreach ($gridConfig['actions'] as $action) {
            if (isset($action['condition']) && is_callable($action['condition']) && !call_user_func($action['condition'], $item)) {
                continue;
            }
            
            $url = isset($action['urlFormatter']) && is_callable($action['urlFormatter']) ? 
                call_user_func($action['urlFormatter'], $item) : 
                (isset($action['url']) ? $action['url'] : '#');
            
            $label = isset($action['label']) ? $action['label'] : '';
            $class = isset($action['class']) ? $action['class'] : 'text-xs text-blue-600 font-medium hover:text-blue-800 hover:underline';
            
            if (isset($action['type']) && $action['type'] === 'button') {
                $btnAttr = '';
                if (isset($action['attributes'])) {
                    foreach ($action['attributes'] as $attr => $attrFormatter) {
                        $attrValue = is_callable($attrFormatter) ? call_user_func($attrFormatter, $item) : $attrFormatter;
                        $btnAttr .= ' ' . $attr . '="' . htmlspecialchars($attrValue) . '"';
                    }
                }
                $output .= '<button class="' . $class . '"' . $btnAttr . '>' . htmlspecialchars($label) . '</button>';
            } else {
                $output .= '<a href="' . htmlspecialchars($url) . '" class="' . $class . '">' . htmlspecialchars($label) . '</a>';
            }
        }
        
        $output .= '</div>';
        
        return $output;
    }
    
    public static function render($defaultView, $gridConfig, $data) {
        return self::renderInternal($defaultView, $gridConfig, $data);
    }
}