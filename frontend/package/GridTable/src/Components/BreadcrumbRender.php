<?php

namespace Package\GridTable\Src\Components;

class BreadcrumbRender {

    private static function renderInternal(string $pageTitle, array $breadcrumbs) {
        $output = '';
        
        // Left section: Title and Breadcrumb
        $output .= '<div class="flex items-center">';
        
        // Breadcrumb and Title together
        if (!empty($breadcrumbs) || !empty($pageTitle)) {
            $output .= '<div class="flex flex-col">';
            // Title
            if (!empty($pageTitle)) {
                $output .= '<h1 class="text-xl font-semibold text-gray-900">' . htmlspecialchars($pageTitle) . '</h1>';
            }

            // Breadcrumb
            if (!empty($breadcrumbs)) {
                $output .= '<nav class="flex items-center space-x-2 text-[12px] text-gray-500">';
                foreach ($breadcrumbs as $index => $breadcrumb) {
                    if ($index > 0) {
                        $output .= '<span>/</span>';
                    }
                    
                    if (isset($breadcrumb['url']) && !empty($breadcrumb['url'])) {
                        $output .= '<a href="' . htmlspecialchars($breadcrumb['url']) . '" class="hover:text-[#0C5BE2] transition-colors">';
                        $output .= htmlspecialchars($breadcrumb['label']);
                        $output .= '</a>';
                    } else {
                        $output .= '<span class="text-gray-700">' . htmlspecialchars($breadcrumb['label']) . '</span>';
                    }
                }
                $output .= '</nav>';
            }
            
            $output .= '</div>';
        }
        
        $output .= '</div>';
        
        return $output;
    }

    public static function render(string $pageTitle, array $breadcrumbs) {
        return self::renderInternal($pageTitle, $breadcrumbs);
    }
}