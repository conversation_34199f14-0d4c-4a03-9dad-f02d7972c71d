<?php

namespace Package\GridTable\Src\Components;

class ActionBarRender {
    private static function renderInternal(array $viewButtons, array $gridConfig, string $defaultView, bool $searchable, string $searchId, array $actionButtons) {
        // Auto-add view toggle buttons if grid is enabled and no custom buttons set
        if ($gridConfig['enabled'] && empty($viewButtons)) {
            $gridActiveClass = $defaultView === 'grid' ? 'active' : '';
            $tableActiveClass = $defaultView === 'table' ? 'active' : '';
            
            $viewButtons = [
                [
                    'label' => 'Grid View',
                    'icon' => 'layout-grid',
                    'attributes' => [
                        'class' => "view-toggle {$gridActiveClass} p-1.5 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-100 transition-all",
                        'data-view' => 'grid'
                    ]
                ],
                [
                    'label' => 'List View',
                    'icon' => 'align-justify',
                    'attributes' => [
                        'class' => "view-toggle {$tableActiveClass} p-1.5 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-100 transition-all",
                        'data-view' => 'table'
                    ]
                ]
            ];
        }
        
        $output = '<div>';
        $output .= '<div class="flex justify-between items-center">';
        
        // Search input with improved styling
        if ($searchable) {
            $searchValue = isset($_GET['search']) ? htmlspecialchars($_GET['search']) : '';
            $output .= '<div class="flex items-center gap-4 mr-4">';
            $output .= '<div class="relative flex-1 max-w-md">';
            $output .= '<div class="absolute inset-y-0 start-0 flex items-center ps-3 pointer-events-none">';
            $output .= '<svg class="w-4 h-4 text-gray-500" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 20">';
            $output .= '<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z"/>';
            $output .= '</svg>';
            $output .= '</div>';
            $output .= '<input type="text" id="' . $searchId . '" ';
            $output .= 'class="block w-full p-2 ps-10 text-sm text-gray-900 border border-gray-300 rounded-lg bg-white focus:ring-blue-500 focus:border-blue-500" ';
            $output .= 'placeholder="Search..." value="' . $searchValue . '">';
            $output .= '</div>';
            $output .= '</div>';
        } else {
            $output .= '<div></div>';
        }

        // View toggle buttons with better styling
        if (!empty($viewButtons)) {
            $output .= '<div class="ms-auto me-2 flex items-center border rounded-lg overflow-hidden bg-white">';
            
            foreach ($viewButtons as $button) {
                $btnClass = isset($button['attributes']['class']) ? $button['attributes']['class'] : 'p-2 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-100 transition-all';
                $btnAttr = '';
                
                if (isset($button['attributes'])) {
                    foreach ($button['attributes'] as $attr => $value) {
                        if ($attr !== 'class') {
                            $btnAttr .= ' ' . $attr . '="' . htmlspecialchars($value) . '"';
                        }
                    }
                }
                
                $output .= '<button type="button" class="' . $btnClass . '"' . $btnAttr . '>';
                
                if (isset($button['icon'])) {
                    $output .= '<i data-lucide="' . $button['icon'] . '" class="w-4 h-4"></i>';
                }
                
                $output .= '</button>';
            }
            
            $output .= '</div>';
        }

        // Action buttons
        if (!empty($actionButtons)) {
            $output .= '<div class="flex items-center gap-2">';
            
            foreach ($actionButtons as $button) {
                $btnClass = isset($button['class']) ? $button['class'] : 'text-xs text-white bg-[#0C5BE2] px-2 py-1.5 rounded hover:bg-blue-700 flex items-center gap-1';
                $btnAttr = '';
                
                if (isset($button['attributes'])) {
                    foreach ($button['attributes'] as $attr => $value) {
                        $btnAttr .= ' ' . $attr . '="' . htmlspecialchars($value) . '"';
                    }
                }
                
                $output .= '<button type="button" class="' . $btnClass . '"' . $btnAttr . '>';
                
                if (isset($button['icon'])) {
                    $output .= '<i data-lucide="' . $button['icon'] . '" class="w-3.5 h-3.5"></i>';
                }
                
                $output .= '<span>' . htmlspecialchars($button['label']) . '</span>';
                $output .= '</button>';
            }
            
            $output .= '</div>';
        }
        
        $output .= '</div>';
        $output .= '</div>';
        
        return $output;
    }

    public static function render(array $viewButtons, array $gridConfig, string $defaultView, bool $searchable, string $searchId, array $actionButtons) {
        return self::renderInternal($viewButtons, $gridConfig, $defaultView, $searchable, $searchId, $actionButtons);
    }
}