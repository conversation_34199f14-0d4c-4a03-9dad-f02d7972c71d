<?php
// package/GridTable/src/Traits/SortingTrait.php
namespace Package\GridTable\Traits;

use Core\Request;

trait SortingTrait {
    protected string $sortColumn = '';
    protected string $sortDirection = 'asc';

    public function setRequest($request) {
        $this->request = $request;
        return $this;
    }
    
    protected function initializeSorting() {
        if ($sort = $this->request->get('sort')) {
            if (str_starts_with($sort, '-')) {
                $this->sortColumn = substr($sort, 1);
                $this->sortDirection = 'desc';
            } else {
                $this->sortColumn = $sort;
                $this->sortDirection = 'asc';
            }
        }
    }
    
    public function getSorting() {
        return [
            'column' => $this->sortColumn,
            'direction' => $this->sortDirection
        ];
    }
}