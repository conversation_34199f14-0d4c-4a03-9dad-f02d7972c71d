<?php
// package/GridTable/src/Traits/PaginationTrait.php
namespace Package\GridTable\Traits;

use Core\Request;

trait PaginationTrait {
    protected array $pagination = [];

    public function setRequest($request) {
        $this->request = $request;
        return $this;
    }
    
    protected function initializePagination($data, $pagination = null) {
        $currentPage = $this->request->get('page', 1);
        $currentLimit = $this->request->get('limit', 10);
        
        if (is_array($pagination)) {
            $this->pagination = [
                'current_page' => $pagination['current_page'] ?? $currentPage,
                'per_page' => $pagination['per_page'] ?? $currentLimit,
                'total' => $pagination['total'] ?? null,
                'next_page_url' => $pagination['next_page_url'] ?? null,
                'prev_page_url' => $pagination['prev_page_url'] ?? null,
                'first_page_url' => $pagination['first_page_url'] ?? null,
                'last_page_url' => $pagination['last_page_url'] ?? null,
                'has_more_pages' => !empty($pagination['next_page_url']),
                'has_previous_pages' => !empty($pagination['prev_page_url'])
            ];
        } else {
            $this->pagination = [
                'current_page' => 1,
                'per_page' => $currentLimit,
                'total' => count($data),
                'next_page_url' => null,
                'prev_page_url' => null,
                'first_page_url' => null,
                'last_page_url' => null,
                'has_more_pages' => false,
                'has_previous_pages' => false
            ];
        }
    }
    
    public function getPagination() {
        return $this->pagination;
    }
}