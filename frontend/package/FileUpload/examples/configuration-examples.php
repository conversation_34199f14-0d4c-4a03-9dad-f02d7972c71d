<?php
/**
 * FileUpload Package - Configuration Examples
 * 
 * This file demonstrates the different ways to configure the FileUpload package:
 * 1. Environment variables (.env file)
 * 2. Configuration file (config/fileupload.php)
 * 3. Runtime configuration (method parameters)
 * 4. Hybrid approaches
 */

require_once __DIR__ . '/../vendor/autoload.php';

use Package\FileUpload\FileUploadManager;
use Package\FileUpload\FileUpload;
use Package\FileUpload\Storage\LocalStorageDriver;

echo "=== FileUpload Configuration Examples ===\n\n";

// ============================================================================
// 1. DEFAULT CONFIGURATION (Environment + Config File)
// ============================================================================
echo "1. Default Configuration (Environment + Config File):\n";

// This uses the standard configuration hierarchy:
// Environment variables -> Config file -> Defaults
$uploader1 = FileUploadManager::create();
$config1 = $uploader1->getConfigInfo();

echo "   Storage Root: " . $config1['storage']['local_root'] . "\n";
echo "   Max Size: " . formatBytes($config1['validation']['max_size']) . "\n";
echo "   Allowed Types: " . implode(', ', array_slice($config1['validation']['allowed_types'], 0, 3)) . "...\n\n";

// ============================================================================
// 2. RUNTIME CONFIGURATION OVERRIDE
// ============================================================================
echo "2. Runtime Configuration Override:\n";

// Override specific settings at runtime
$runtimeConfig = [
    'validation' => [
        'max_file_size' => 5242880, // 5MB
        'allowed_extensions' => ['jpg', 'png', 'gif'],
        'allowed_mime_types' => ['image/jpeg', 'image/png', 'image/gif']
    ],
    'processing' => [
        'enabled' => true,
        'compress_images' => true,
        'quality' => 90
    ]
];

$uploader2 = FileUploadManager::createWithConfig($runtimeConfig);
$config2 = $uploader2->getConfigInfo();

echo "   Max Size (overridden): " . formatBytes($config2['validation']['max_size']) . "\n";
echo "   Allowed Extensions: " . implode(', ', $config2['validation']['allowed_extensions'] ?? []) . "\n";
echo "   Image Quality: " . ($config2['processing']['quality'] ?? 'N/A') . "\n\n";

// ============================================================================
// 3. DIRECTORY-SPECIFIC CONFIGURATION
// ============================================================================
echo "3. Directory-Specific Configuration:\n";

// Create uploader for a specific directory
$uploader3 = FileUploadManager::createForDirectory(
    '/tmp/custom-uploads',
    '/custom-uploads'
);
$config3 = $uploader3->getConfigInfo();

echo "   Custom Root: " . $config3['storage']['local_root'] . "\n";
echo "   Custom URL: " . $config3['storage']['local_url'] . "\n\n";

// ============================================================================
// 4. VALIDATION-SPECIFIC CONFIGURATION
// ============================================================================
echo "4. Validation-Specific Configuration:\n";

// Create uploader with specific validation rules
$validationRules = [
    'max_file_size' => 2097152, // 2MB
    'allowed_extensions' => ['pdf', 'doc', 'docx'],
    'allowed_mime_types' => [
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    ]
];

$uploader4 = FileUploadManager::createWithValidation($validationRules);
$config4 = $uploader4->getConfigInfo();

echo "   Document Max Size: " . formatBytes($config4['validation']['max_size']) . "\n";
echo "   Document Types: " . implode(', ', $config4['validation']['allowed_extensions'] ?? []) . "\n\n";

// ============================================================================
// 5. ENVIRONMENT-BASED CONFIGURATION
// ============================================================================
echo "5. Environment-Based Configuration:\n";

// Simulate different environment variables
$_ENV['FILEUPLOAD_LOCAL_ROOT'] = 'storage/app/uploads';
$_ENV['FILEUPLOAD_MAX_SIZE'] = '20971520'; // 20MB
$_ENV['FILEUPLOAD_ALLOWED_EXTENSIONS'] = 'jpg,jpeg,png,gif,webp,svg';
$_ENV['FILEUPLOAD_PROCESSING_ENABLED'] = 'true';
$_ENV['FILEUPLOAD_COMPRESS_IMAGES'] = 'true';
$_ENV['FILEUPLOAD_IMAGE_QUALITY'] = '75';

// Create new instance to pick up environment changes
$uploader5 = FileUploadManager::create();
$config5 = $uploader5->getConfigInfo();

echo "   Environment Root: " . $config5['storage']['local_root'] . "\n";
echo "   Environment Max Size: " . formatBytes($config5['validation']['max_size']) . "\n\n";

// ============================================================================
// 6. DIRECT FILEUPLOAD CLASS USAGE
// ============================================================================
echo "6. Direct FileUpload Class Usage:\n";

// Use FileUpload class directly with custom storage driver
$customStorageConfig = [
    'root' => sys_get_temp_dir() . '/fileupload-test',
    'url' => '/temp-uploads',
    'permissions' => [
        'file' => ['public' => 0644],
        'dir' => ['public' => 0755]
    ]
];

$storageDriver = new LocalStorageDriver($customStorageConfig);
$customConfig = [
    'validation' => [
        'max_file_size' => 1048576, // 1MB
        'allowed_extensions' => ['txt', 'csv', 'json']
    ]
];

$uploader6 = new FileUpload($storageDriver, null, $customConfig);

echo "   Direct Usage: Custom storage driver with 1MB limit for text files\n";
echo "   Storage Root: " . $customStorageConfig['root'] . "\n\n";

// ============================================================================
// 7. CONFIGURATION PRIORITY DEMONSTRATION
// ============================================================================
echo "7. Configuration Priority Demonstration:\n";

// Show how runtime config overrides environment and defaults
$_ENV['FILEUPLOAD_MAX_SIZE'] = '10485760'; // 10MB in environment

$runtimeOverride = [
    'validation' => [
        'max_file_size' => 52428800 // 50MB runtime override
    ]
];

$uploader7 = FileUploadManager::createWithConfig($runtimeOverride);
$config7 = $uploader7->getConfigInfo();

echo "   Environment Setting: 10MB\n";
echo "   Runtime Override: 50MB\n";
echo "   Actual Value: " . formatBytes($config7['validation']['max_size']) . "\n";
echo "   Priority: Runtime > Environment > Config File > Defaults\n\n";

// ============================================================================
// HELPER FUNCTIONS
// ============================================================================

function formatBytes($bytes, $precision = 2) {
    $units = array('B', 'KB', 'MB', 'GB', 'TB');
    
    for ($i = 0; $bytes > 1024; $i++) {
        $bytes /= 1024;
    }
    
    return round($bytes, $precision) . ' ' . $units[$i];
}

echo "=== Configuration Examples Complete ===\n";
echo "\n📋 Summary of Configuration Methods:\n";
echo "✅ Environment variables (.env) - Good for deployment-specific settings\n";
echo "✅ Configuration file (config/fileupload.php) - Good for application defaults\n";
echo "✅ Runtime parameters - Good for dynamic, context-specific settings\n";
echo "✅ Factory methods - Good for common use cases and convenience\n";
echo "\n🎯 Best Practice: Use environment for deployment settings, runtime for dynamic needs!\n";
?>
