# FileUpload Package - Installation Guide

Quick installation guide to get the FileUpload package up and running in your project.

## 🚀 Quick Start (5 Minutes)

### Step 1: Install the Package

#### Option A: Composer (Recommended)

```bash
composer require your-vendor/fileupload
```

#### Option B: Manual Installation

```bash
# Copy package to your project
cp -r package/FileUpload /path/to/your/project/

# Include in your project
require_once 'FileUpload/vendor/autoload.php';
```

### Step 2: Create Configuration

Create a `.env` file in your project root:

```env
# Basic Configuration
FILEUPLOAD_PROCESSING_ENABLED=true
FILEUPLOAD_COMPRESS_IMAGES=true
FILEUPLOAD_IMAGE_QUALITY=85

# Storage (use relative paths for portability)
FILEUPLOAD_LOCAL_ROOT=public/uploads/media
FILEUPLOAD_LOCAL_URL=/uploads/media

# Thumbnails
FILEUPLOAD_THUMBNAILS_ENABLED=true
FILEUPLOAD_THUMBNAIL_SIZES=[{"name":"small","width":150,"height":150,"mode":"crop"},{"name":"medium","width":300,"height":300,"mode":"fit"}]

# Security
FILEUPLOAD_MAX_FILE_SIZE=10485760
FILEUPLOAD_ALLOWED_EXTENSIONS=jpg,jpeg,png,gif,webp,pdf
```

### Step 3: Create Upload Directory

```bash
mkdir uploads
chmod 755 uploads
```

### Step 4: Copy Transform Script

```bash
# Copy transform.php to your project root
cp package/FileUpload/transform.php ./
```

### Step 5: Test Upload

Create a simple test file:

```php
<?php
require_once 'vendor/autoload.php';

// Load environment variables
if (file_exists('.env')) {
    $lines = file('.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) continue;
        if (strpos($line, '=') !== false) {
            list($key, $value) = explode('=', $line, 2);
            $_ENV[trim($key)] = trim($value);
        }
    }
}

// Create upload manager
$uploadManager = \Package\FileUpload\FileUploadManager::create();

// Handle upload
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['file'])) {
    $result = $uploadManager->upload($_FILES['file']);

    if ($result->isSuccess()) {
        echo "✅ Upload successful!<br>";
        echo "Path: " . $result->getPath() . "<br>";
        echo "URL: " . $result->getUrl() . "<br>";

        // Show thumbnails
        $thumbnails = $result->getProcessedFiles();
        foreach ($thumbnails as $size => $info) {
            echo "$size: <img src='" . $info['url'] . "' style='max-width:100px;'><br>";
        }
    } else {
        echo "❌ Upload failed: " . $result->getMessage();
    }
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>FileUpload Test</title>
</head>
<body>
    <h1>FileUpload Package Test</h1>
    <form method="post" enctype="multipart/form-data">
        <input type="file" name="file" accept="image/*" required>
        <button type="submit">Upload</button>
    </form>
</body>
</html>
```

## 🔧 Framework Integration

### Laravel

1. **Add Service Provider** (config/app.php):

```php
'providers' => [
    // ...
    Package\FileUpload\Providers\FileUploadServiceProvider::class,
],
```

2. **Publish Configuration**:

```bash
php artisan vendor:publish --provider="Package\FileUpload\Providers\FileUploadServiceProvider"
```

3. **Use in Controller**:

```php
<?php
namespace App\Http\Controllers;

use Package\FileUpload\FileUploadManager;

class UploadController extends Controller
{
    public function upload(Request $request)
    {
        $uploadManager = FileUploadManager::create();
        $result = $uploadManager->upload($request->file('file'));

        if ($result->isSuccess()) {
            return response()->json([
                'success' => true,
                'path' => $result->getPath(),
                'url' => $result->getUrl(),
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => $result->getMessage(),
        ], 400);
    }
}
```

### Symfony

1. **Register Service** (config/services.yaml):

```yaml
services:
  Package\FileUpload\FileUploadManager:
    factory: ['Package\FileUpload\FileUploadManager', "create"]
    public: true
```

2. **Use in Controller**:

```php
<?php
namespace App\Controller;

use Package\FileUpload\FileUploadManager;
use Symfony\Component\HttpFoundation\Request;

class UploadController extends AbstractController
{
    public function upload(Request $request, FileUploadManager $uploadManager)
    {
        $file = $request->files->get('file');
        $result = $uploadManager->upload([
            'name' => $file->getClientOriginalName(),
            'type' => $file->getMimeType(),
            'tmp_name' => $file->getPathname(),
            'error' => $file->getError(),
            'size' => $file->getSize(),
        ]);

        if ($result->isSuccess()) {
            return $this->json([
                'success' => true,
                'path' => $result->getPath(),
                'url' => $result->getUrl(),
            ]);
        }

        return $this->json([
            'success' => false,
            'message' => $result->getMessage(),
        ], 400);
    }
}
```

## 🌐 Web Server Configuration

### Apache (.htaccess)

Create `.htaccess` in your project root:

```apache
RewriteEngine On

# Dynamic image transformation
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^uploads/(\d+)x(\d+)/(crop|fit|fill|stretch)=(.+)$ transform.php?path=uploads/$4&w=$1&h=$2&m=$3 [QSA,L]

# Clean URLs (default mode)
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^uploads/(\d+)x(\d+)/(.+)$ transform.php?path=uploads/$3&w=$1&h=$2&m=fit [QSA,L]
```

### Nginx

Add to your server configuration:

```nginx
location ~ ^/uploads/(\d+)x(\d+)/(crop|fit|fill|stretch)=(.+)$ {
    try_files $uri /transform.php?path=uploads/$4&w=$1&h=$2&m=$3;
}

location ~ ^/uploads/(\d+)x(\d+)/(.+)$ {
    try_files $uri /transform.php?path=uploads/$3&w=$1&h=$2&m=fit;
}
```

## 🔒 Security Setup

### File Permissions

```bash
# Set proper permissions
chmod 755 uploads/
chmod 644 uploads/*

# For web server user (e.g., www-data)
chown -R www-data:www-data uploads/
```

### PHP Configuration

Update `php.ini` for larger uploads:

```ini
upload_max_filesize = 50M
post_max_size = 50M
max_execution_time = 300
memory_limit = 256M
```

### Security Headers

Add to your web server configuration:

```apache
# Apache
<Files "*.php">
    <IfModule mod_headers.c>
        Header set X-Content-Type-Options nosniff
        Header set X-Frame-Options DENY
    </IfModule>
</Files>
```

```nginx
# Nginx
add_header X-Content-Type-Options nosniff;
add_header X-Frame-Options DENY;
```

## 🧪 Verification

### Health Check

```php
<?php
require_once 'vendor/autoload.php';

$uploadManager = \Package\FileUpload\FileUploadManager::create();
$health = $uploadManager->healthCheck();

foreach ($health as $check => $result) {
    echo $check . ": " . ($result['status'] ? '✅' : '❌') . " " . $result['message'] . "\n";
}
?>
```

### Test Upload

```bash
# Test with curl
curl -X POST -F "file=@test-image.jpg" http://your-domain/test-upload.php
```

## 🆘 Troubleshooting

### Common Issues

1. **Permission Denied**

   ```bash
   chmod -R 755 uploads/
   ```

2. **GD Extension Missing**

   ```bash
   sudo apt-get install php-gd
   ```

3. **Memory Limit**

   ```php
   ini_set('memory_limit', '256M');
   ```

4. **Upload Size Limit**
   ```ini
   upload_max_filesize = 50M
   post_max_size = 50M
   ```

## 📞 Support

- **Documentation**: [README.md](README.md) | [USAGE.md](USAGE.md)
- **API Reference**: [docs/API.md](docs/API.md)
- **Examples**: Check the `/examples` directory

---

🎉 **You're ready to go!** The FileUpload package is now installed and configured.
