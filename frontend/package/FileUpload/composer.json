{"name": "package/file-upload", "description": "A comprehensive and scalable file upload package with support for multiple storage drivers, validation, and image processing using PHP GD.", "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "version": "0.1", "type": "library", "license": "MIT", "require": {"php": ">=8.0", "ext-gd": "*"}, "suggest": {"aws/aws-sdk-php": "Required for S3 storage driver (^3.0)", "google/cloud-storage": "Required for Google Cloud storage driver (^1.0)", "microsoft/azure-storage-blob": "Required for Azure storage driver (^1.0)", "league/flysystem": "Required for advanced storage features (^3.0)", "league/flysystem-aws-s3-v3": "Required for S3 storage driver (^3.0)", "league/flysystem-google-cloud-storage": "Required for Google Cloud storage driver (^3.0)", "league/flysystem-azure-blob-storage": "Required for Azure storage driver (^3.0)"}, "require-dev": {"phpunit/phpunit": "^9.5", "mockery/mockery": "^1.4", "phpstan/phpstan": "^1.0"}, "autoload": {"psr-4": {"Package\\FileUpload\\": "src/"}}, "autoload-dev": {"psr-4": {"Package\\FileUpload\\Tests\\": "tests/"}}, "scripts": {"test": "phpunit", "test-coverage": "phpunit --coverage-html coverage", "analyse": "phpstan analyse src --level=7", "post-install-cmd": ["Package\\FileUpload\\Installer::copyTransformFile", "Package\\FileUpload\\Installer::setupPackage"], "post-update-cmd": ["Package\\FileUpload\\Installer::copyTransformFile"]}, "config": {"sort-packages": true}, "minimum-stability": "stable", "prefer-stable": true}