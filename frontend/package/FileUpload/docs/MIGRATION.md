# Migration Guide: From File<PERSON>p<PERSON><PERSON>elper to FileUpload Package

This guide helps you migrate from the legacy FileUploadHelper class to the new FileUpload package that uses PHP's built-in GD extension instead of intervention/image.

## Overview

The new FileUpload package provides:

- ✅ **Better Architecture**: Modular design with pluggable components
- ✅ **Zero External Dependencies**: Uses only PHP core and GD extension
- ✅ **Enhanced Security**: Comprehensive validation and security checks
- ✅ **Multiple Storage Drivers**: Local, S3, Google Cloud, Azure support
- ✅ **Improved Error Handling**: Detailed error reporting and exceptions
- ✅ **Environment Configuration**: Fully configurable via .env variables

## Breaking Changes

### 1. Class Name and Namespace

**Before (FileUploadHelper):**

```php
require_once 'FileUploadHelper.php';
$uploader = new FileUploadHelper();
```

**After (FileUpload Package):**

```php
require_once 'vendor/autoload.php';
use Package\FileUpload\FileUpload;
$uploader = new FileUpload();
```

### 2. Upload Method Signature

**Before:**

```php
$result = $uploader->uploadFile($_FILES['file'], $uploadDir, $options);
```

**After:**

```php
$result = $uploader->upload($_FILES['file'], [
    'directory' => $uploadDir,
    // ... other options
]);
```

### 3. Return Value Structure

**Before:**

```php
if ($result['success']) {
    echo $result['file_path'];
    echo $result['file_url'];
}
```

**After:**

```php
if ($result->isSuccess()) {
    echo $result->getPath();
    echo $result->getUrl();
}
```

## Step-by-Step Migration

### Step 1: Update Dependencies

Remove any intervention/image dependencies and ensure GD extension is available:

```bash
# Check GD extension
php -m | grep -i gd

# If not installed (Ubuntu/Debian)
sudo apt-get install php-gd

# If not installed (CentOS/RHEL)
sudo yum install php-gd
```

### Step 2: Update Configuration

**Before (FileUploadHelper config):**

```php
$config = [
    'upload_path' => '/uploads/',
    'allowed_types' => 'gif|jpg|png',
    'max_size' => 1024,
    'max_width' => 1024,
    'max_height' => 768
];
```

**After (Environment variables in .env):**

```env
FILEUPLOAD_LOCAL_ROOT=uploads
FILEUPLOAD_LOCAL_URL=/uploads
FILEUPLOAD_ALLOWED_EXTENSIONS=gif,jpg,jpeg,png
FILEUPLOAD_MAX_SIZE=1048576
FILEUPLOAD_MAX_WIDTH=1024
FILEUPLOAD_MAX_HEIGHT=768
```

### Step 3: Update Upload Code

**Before:**

```php
require_once 'FileUploadHelper.php';

$uploader = new FileUploadHelper();
$uploader->initialize([
    'upload_path' => './uploads/',
    'allowed_types' => 'gif|jpg|png|jpeg',
    'max_size' => 2048,
    'encrypt_name' => true
]);

if ($uploader->do_upload('userfile')) {
    $data = $uploader->data();
    echo "File uploaded: " . $data['file_name'];
} else {
    echo "Error: " . $uploader->display_errors();
}
```

**After:**

```php
require_once 'vendor/autoload.php';
use Package\FileUpload\FileUpload;
use Package\FileUpload\Processing\ImageProcessor;

$uploader = new FileUpload();

// Add image processor for thumbnails
$processor = new ImageProcessor();
$uploader->addProcessor($processor);

$result = $uploader->upload($_FILES['userfile'], [
    'directory' => 'uploads',
    'validation' => [
        'allowed_extensions' => ['gif', 'jpg', 'png', 'jpeg'],
        'max_file_size' => 2097152 // 2MB in bytes
    ]
]);

if ($result->isSuccess()) {
    echo "File uploaded: " . $result->getPath();
    echo "URL: " . $result->getUrl();

    // Access thumbnails
    foreach ($result->getProcessedFiles() as $type => $file) {
        echo ucfirst($type) . ": " . $file['url'];
    }
} else {
    echo "Error: " . $result->getMessage();
    foreach ($result->getErrors() as $error) {
        echo "Error: " . $error;
    }
}
```

### Step 4: Update Image Processing

**Before (with intervention/image):**

```php
// Manual thumbnail creation
$image = Image::make($uploadedFile);
$image->resize(300, 300, function ($constraint) {
    $constraint->aspectRatio();
});
$image->save($thumbnailPath);
```

**After (automatic with GD):**

```php
// Automatic thumbnail generation
$processor = new ImageProcessor([
    'thumbnails' => [
        'enabled' => true,
        'sizes' => [
            'small' => ['width' => 150, 'height' => 150, 'mode' => 'crop'],
            'medium' => ['width' => 300, 'height' => 300, 'mode' => 'fit']
        ]
    ]
]);

$uploader->addProcessor($processor);
$result = $uploader->upload($_FILES['image']);

// Thumbnails are automatically generated and available in result
$thumbnails = $result->getProcessedFiles();
```

## Common Migration Patterns

### Pattern 1: Basic File Upload

**Before:**

```php
function uploadFile($file, $destination) {
    $uploader = new FileUploadHelper();
    $uploader->initialize([
        'upload_path' => $destination,
        'allowed_types' => 'jpg|jpeg|png|gif'
    ]);

    if ($uploader->do_upload('file')) {
        return $uploader->data();
    }
    return false;
}
```

**After:**

```php
function uploadFile($file, $destination) {
    $uploader = new \Package\FileUpload\FileUpload();

    $result = $uploader->upload($file, [
        'directory' => $destination,
        'validation' => [
            'allowed_extensions' => ['jpg', 'jpeg', 'png', 'gif']
        ]
    ]);

    return $result->isSuccess() ? $result->toArray() : false;
}
```

### Pattern 2: Image Upload with Validation

**Before:**

```php
function uploadImage($file) {
    $uploader = new FileUploadHelper();
    $uploader->initialize([
        'upload_path' => './images/',
        'allowed_types' => 'jpg|jpeg|png',
        'max_size' => 2048,
        'max_width' => 1920,
        'max_height' => 1080,
        'encrypt_name' => true
    ]);

    if ($uploader->do_upload('image')) {
        $data = $uploader->data();

        // Manual thumbnail creation
        $image = Image::make($data['full_path']);
        $image->fit(300, 300);
        $image->save('./images/thumbs/' . $data['file_name']);

        return $data;
    }
    return false;
}
```

**After:**

```php
function uploadImage($file) {
    $uploader = new \Package\FileUpload\FileUpload();

    // Add automatic thumbnail generation
    $processor = new \Package\FileUpload\Processing\ImageProcessor();
    $uploader->addProcessor($processor);

    $result = $uploader->upload($file, [
        'directory' => 'images',
        'validation' => [
            'allowed_extensions' => ['jpg', 'jpeg', 'png'],
            'max_file_size' => 2097152, // 2MB
            'max_width' => 1920,
            'max_height' => 1080
        ]
    ]);

    if ($result->isSuccess()) {
        return [
            'original' => $result->toArray(),
            'thumbnails' => $result->getProcessedFiles()
        ];
    }

    return false;
}
```

### Pattern 3: Multiple File Upload

**Before:**

```php
function uploadMultiple($files) {
    $results = [];
    foreach ($files['name'] as $index => $name) {
        $file = [
            'name' => $files['name'][$index],
            'type' => $files['type'][$index],
            'tmp_name' => $files['tmp_name'][$index],
            'error' => $files['error'][$index],
            'size' => $files['size'][$index]
        ];

        $uploader = new FileUploadHelper();
        // ... upload logic
        $results[] = $uploader->do_upload($file);
    }
    return $results;
}
```

**After:**

```php
function uploadMultiple($files) {
    $uploader = new \Package\FileUpload\FileUpload();
    return $uploader->uploadMultiple($files);
}
```

## Configuration Migration

### Environment Variables Mapping

| FileUploadHelper | FileUpload Package              | Description                  |
| ---------------- | ------------------------------- | ---------------------------- |
| `upload_path`    | `FILEUPLOAD_LOCAL_ROOT`         | Upload directory             |
| `allowed_types`  | `FILEUPLOAD_ALLOWED_EXTENSIONS` | Allowed file extensions      |
| `max_size`       | `FILEUPLOAD_MAX_SIZE`           | Maximum file size (in bytes) |
| `max_width`      | `FILEUPLOAD_MAX_WIDTH`          | Maximum image width          |
| `max_height`     | `FILEUPLOAD_MAX_HEIGHT`         | Maximum image height         |
| `encrypt_name`   | `FILEUPLOAD_RANDOMIZE_NAMES`    | Randomize filenames          |

### Complete Environment Configuration

```env
# Storage
FILEUPLOAD_DRIVER=local
FILEUPLOAD_LOCAL_ROOT=uploads
FILEUPLOAD_LOCAL_URL=/uploads

# Validation
FILEUPLOAD_MAX_SIZE=10485760
FILEUPLOAD_ALLOWED_TYPES=image/jpeg,image/png,image/gif,image/webp
FILEUPLOAD_ALLOWED_EXTENSIONS=jpg,jpeg,png,gif,webp
FILEUPLOAD_FORBIDDEN_EXTENSIONS=php,exe,bat,sh
FILEUPLOAD_MAX_WIDTH=2048
FILEUPLOAD_MAX_HEIGHT=2048

# Processing
FILEUPLOAD_PROCESSING_ENABLED=true
FILEUPLOAD_THUMBNAILS_ENABLED=true
FILEUPLOAD_PROCESSING_QUALITY=85

# Security
FILEUPLOAD_CHECK_HEADERS=true
FILEUPLOAD_RANDOMIZE_NAMES=true
FILEUPLOAD_PRESERVE_NAME=false
```

## Error Handling Migration

### Before (FileUploadHelper)

```php
if (!$uploader->do_upload('file')) {
    $errors = $uploader->display_errors();
    echo "Upload failed: " . $errors;
}
```

### After (FileUpload Package)

```php
use Package\FileUpload\Exceptions\ValidationException;
use Package\FileUpload\Exceptions\StorageException;

try {
    $result = $uploader->upload($_FILES['file']);

    if (!$result->isSuccess()) {
        echo "Upload failed: " . $result->getMessage();
        foreach ($result->getErrors() as $error) {
            echo "Error: " . $error;
        }
    }
} catch (ValidationException $e) {
    echo "Validation error: " . $e->getMessage();
} catch (StorageException $e) {
    echo "Storage error: " . $e->getMessage();
}
```

## Testing Your Migration

### 1. Basic Functionality Test

```bash
cd frontend/
php test-package-basic.php
```

Expected output should show:

- ✅ GD Extension loaded
- ✅ FileUpload instance created
- ✅ Configuration loaded
- ✅ Storage driver working

### 2. Upload Test

Create a test script:

```php
<?php
require_once 'vendor/autoload.php';
use Package\FileUpload\FileUpload;

// Test upload
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['test'])) {
    $uploader = new FileUpload();
    $result = $uploader->upload($_FILES['test']);

    if ($result->isSuccess()) {
        echo "✅ Migration successful!";
        echo "File: " . $result->getPath();
        echo "URL: " . $result->getUrl();
    } else {
        echo "❌ Migration issue: " . $result->getMessage();
    }
}
?>

<form method="post" enctype="multipart/form-data">
    <input type="file" name="test" accept="image/*">
    <button type="submit">Test Upload</button>
</form>
```

## Common Issues and Solutions

### Issue 1: GD Extension Not Found

**Error:** `Extension 'gd' not found`

**Solution:**

```bash
# Ubuntu/Debian
sudo apt-get install php-gd
sudo systemctl restart apache2

# CentOS/RHEL
sudo yum install php-gd
sudo systemctl restart httpd

# Verify installation
php -m | grep -i gd
```

### Issue 2: Upload Directory Permissions

**Error:** `Permission denied` or `Directory not writable`

**Solution:**

```bash
# Create upload directory
mkdir -p frontend/uploads

# Set proper permissions
chmod 755 frontend/uploads
chown www-data:www-data frontend/uploads  # For Apache
```

### Issue 3: File Size Limits

**Error:** `File too large` or upload fails silently

**Solution:**
Check PHP configuration:

```ini
; php.ini
upload_max_filesize = 10M
post_max_size = 10M
max_execution_time = 300
memory_limit = 256M
```

### Issue 4: Missing Environment Variables

**Error:** Configuration not loading properly

**Solution:**
Ensure `.env` file exists in `frontend/` directory:

```bash
# Check if .env exists
ls -la frontend/.env

# Copy from example if needed
cp frontend/.env.example frontend/.env
```

## Performance Considerations

### GD vs intervention/image

| Feature           | GD (Built-in)               | intervention/image        |
| ----------------- | --------------------------- | ------------------------- |
| **Memory Usage**  | Lower                       | Higher                    |
| **Performance**   | Faster for basic operations | Slower due to abstraction |
| **Dependencies**  | None (built-in)             | External package          |
| **Features**      | Basic image operations      | Advanced features         |
| **Compatibility** | Universal                   | Requires installation     |

### Optimization Tips

1. **Use appropriate JPEG quality:**

```env
FILEUPLOAD_PROCESSING_QUALITY=85  # Good balance of quality/size
```

2. **Optimize thumbnail sizes:**

```env
# Only generate needed sizes
FILEUPLOAD_THUMB_SMALL_WIDTH=150
FILEUPLOAD_THUMB_MEDIUM_WIDTH=300
# Don't generate large thumbnails if not needed
```

3. **Enable memory limit for large images:**

```php
ini_set('memory_limit', '256M');
```

## Rollback Plan

If you need to rollback to FileUploadHelper:

1. **Backup current code:**

```bash
cp -r frontend/package/FileUpload frontend/package/FileUpload.backup
```

2. **Restore old FileUploadHelper:**

```bash
# Restore from backup
git checkout HEAD~1 -- path/to/FileUploadHelper.php
```

3. **Update composer.json:**

```bash
# Add intervention/image back if needed
composer require intervention/image:^2.7
```

## Migration Checklist

- [ ] ✅ GD extension installed and working
- [ ] ✅ Upload directory created with proper permissions
- [ ] ✅ Environment variables configured in `.env`
- [ ] ✅ Code updated to use new FileUpload class
- [ ] ✅ Error handling updated to use new exception system
- [ ] ✅ Image processing updated to use automatic thumbnails
- [ ] ✅ Basic functionality test passes
- [ ] ✅ Upload test with real files works
- [ ] ✅ Thumbnail generation working
- [ ] ✅ All existing functionality preserved
- [ ] ✅ Performance acceptable for your use case

## Support

If you encounter issues during migration:

1. **Check the test page:** `http://your-domain/test-fileupload.php`
2. **Review error logs:** Check PHP error logs for detailed messages
3. **Verify configuration:** Ensure all environment variables are set
4. **Test incrementally:** Migrate one feature at a time

The new FileUpload package provides better architecture, security, and performance while maintaining all the functionality of the legacy FileUploadHelper class.
