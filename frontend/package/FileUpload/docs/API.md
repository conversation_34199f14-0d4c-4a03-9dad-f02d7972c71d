# FileUpload Package - API Reference

Complete API documentation for the FileUpload package.

## 📚 Table of Contents

1. [FileUploadManager](#fileuploadmanager)
2. [FileUpload](#fileupload)
3. [UploadResult](#uploadresult)
4. [FolderResult](#folderresult)
5. [Storage Drivers](#storage-drivers)
6. [Processors](#processors)
7. [Configuration](#configuration)

## 🚀 FileUploadManager

The main entry point for file upload operations with zero-configuration setup.

### Static Methods

#### `create(): FileUploadManager`
Creates a new FileUploadManager instance with automatic configuration.

```php
$uploadManager = \Package\FileUpload\FileUploadManager::create();
```

### Instance Methods

#### `upload(array $file, array $options = []): UploadResult`
Uploads a single file with optional configuration.

**Parameters:**
- `$file` (array): $_FILES array element
- `$options` (array): Upload options

**Returns:** `UploadResult`

**Example:**
```php
$result = $uploadManager->upload($_FILES['file'], [
    'directory' => 'user-uploads',
    'filename' => 'custom-name.jpg',
    'validation' => [
        'max_file_size' => 5242880,
        'allowed_extensions' => ['jpg', 'png'],
    ]
]);
```

#### `uploadMultiple(array $files, array $options = []): array`
Uploads multiple files.

**Parameters:**
- `$files` (array): $_FILES array for multiple files
- `$options` (array): Upload options

**Returns:** `array` of `UploadResult` objects

**Example:**
```php
$results = $uploadManager->uploadMultiple($_FILES['files']);
foreach ($results as $result) {
    if ($result->isSuccess()) {
        echo "Uploaded: " . $result->getPath();
    }
}
```

#### `getDynamicUrl(string $path, int $width, int $height, string $mode = 'fit', bool $useQuery = false, bool $cleanUrl = false, int $quality = null): string`
Generates dynamic transformation URLs.

**Parameters:**
- `$path` (string): Original file path
- `$width` (int): Target width
- `$height` (int): Target height
- `$mode` (string): Transformation mode ('crop', 'fit', 'fill', 'stretch')
- `$useQuery` (bool): Use query parameters instead of path-based URLs
- `$cleanUrl` (bool): Generate clean URLs without mode specification
- `$quality` (int): Image quality (1-100)

**Returns:** `string` - Transformation URL

**Example:**
```php
// Path-based URL
$url = $uploadManager->getDynamicUrl('uploads/image.jpg', 300, 200, 'crop');
// Result: /uploads/300x200/crop=image.jpg

// Query parameter URL
$url = $uploadManager->getDynamicUrl('uploads/image.jpg', 300, 200, 'crop', true);
// Result: /transform.php?path=uploads/image.jpg&w=300&h=200&m=crop
```

#### `getResponsiveUrls(string $path, string $mode = 'fit', int $quality = 85): array`
Generates responsive image URLs for different screen sizes.

**Parameters:**
- `$path` (string): Original file path
- `$mode` (string): Transformation mode
- `$quality` (int): Image quality

**Returns:** `array` - Associative array of breakpoint => URL

**Example:**
```php
$urls = $uploadManager->getResponsiveUrls('uploads/image.jpg');
// Returns: ['xs' => '...', 'sm' => '...', 'md' => '...', ...]
```

#### `getSrcSet(string $path, string $mode = 'fit', int $quality = 85): string`
Generates srcset attribute for responsive images.

**Parameters:**
- `$path` (string): Original file path
- `$mode` (string): Transformation mode
- `$quality` (int): Image quality

**Returns:** `string` - Srcset attribute value

**Example:**
```php
$srcset = $uploadManager->getSrcSet('uploads/image.jpg');
// Returns: "/uploads/320x0/fit=image.jpg 320w, /uploads/576x0/fit=image.jpg 576w, ..."
```

#### `createFolder(string $path): FolderResult`
Creates a new folder.

**Parameters:**
- `$path` (string): Folder path to create

**Returns:** `FolderResult`

**Example:**
```php
$result = $uploadManager->createFolder('user-documents/2024');
if ($result->isSuccess()) {
    echo "Folder created: " . $result->getPath();
}
```

#### `deleteFolder(string $path): FolderResult`
Deletes a folder and all its contents.

**Parameters:**
- `$path` (string): Folder path to delete

**Returns:** `FolderResult`

**Example:**
```php
$result = $uploadManager->deleteFolder('old-uploads');
if ($result->isSuccess()) {
    echo "Folder deleted successfully";
}
```

#### `listFolder(string $path): array`
Lists contents of a folder.

**Parameters:**
- `$path` (string): Folder path to list

**Returns:** `array` - Array of file/folder information

**Example:**
```php
$contents = $uploadManager->listFolder('uploads');
foreach ($contents as $item) {
    echo $item['type'] . ": " . $item['name'];
}
```

#### `getProcessingStatus(): array`
Gets current processing configuration and status.

**Returns:** `array` - Processing status information

**Example:**
```php
$status = $uploadManager->getProcessingStatus();
echo "Processing enabled: " . ($status['processing_enabled'] ? 'Yes' : 'No');
echo "Image quality: " . $status['image_quality'];
```

#### `getConfigInfo(): array`
Gets complete configuration information.

**Returns:** `array` - Configuration details

**Example:**
```php
$config = $uploadManager->getConfigInfo();
echo "Storage driver: " . $config['storage']['driver'];
echo "Upload directory: " . $config['storage']['local_root'];
```

#### `healthCheck(): array`
Performs system health check.

**Returns:** `array` - Health check results

**Example:**
```php
$health = $uploadManager->healthCheck();
foreach ($health as $check => $result) {
    echo "$check: " . ($result['status'] ? '✅' : '❌') . " " . $result['message'];
}
```

## 📁 FileUpload

Lower-level file upload class for advanced usage.

### Constructor

#### `__construct(StorageDriverInterface $storageDriver = null)`
Creates a new FileUpload instance.

**Parameters:**
- `$storageDriver` (StorageDriverInterface): Optional storage driver

### Methods

#### `upload(array $file, array $options = []): UploadResult`
Uploads a file with the configured processors.

#### `addProcessor(ProcessorInterface $processor): void`
Adds a file processor.

**Example:**
```php
$uploader = new \Package\FileUpload\FileUpload();
$uploader->addProcessor(new \Package\FileUpload\Processing\ImageProcessor($config));
```

#### `setStorageDriver(StorageDriverInterface $driver): void`
Sets the storage driver.

#### `getTransformUrl(string $path, int $width, int $height, string $mode = 'fit', int $quality = 85, ?string $baseUrl = null): string`
Generates transformation URL.

## 📊 UploadResult

Result object returned by upload operations.

### Methods

#### `isSuccess(): bool`
Checks if upload was successful.

#### `getPath(): string`
Gets the uploaded file path.

#### `getUrl(): string`
Gets the public URL of uploaded file.

#### `getOriginalName(): string`
Gets the original filename.

#### `getFileSize(): int`
Gets the file size in bytes.

#### `getMimeType(): string`
Gets the MIME type.

#### `getMessage(): string`
Gets result message.

#### `getErrors(): array`
Gets array of error messages.

#### `hasErrors(): bool`
Checks if there are errors.

#### `getProcessedFiles(): array`
Gets information about processed files (thumbnails, etc.).

**Example:**
```php
if ($result->isSuccess()) {
    echo "File: " . $result->getPath();
    echo "Size: " . $result->getFileSize() . " bytes";
    echo "Type: " . $result->getMimeType();
    
    $thumbnails = $result->getProcessedFiles();
    foreach ($thumbnails as $size => $info) {
        echo "$size thumbnail: " . $info['url'];
    }
} else {
    echo "Upload failed: " . $result->getMessage();
    foreach ($result->getErrors() as $error) {
        echo "Error: $error";
    }
}
```

## 📂 FolderResult

Result object returned by folder operations.

### Methods

#### `isSuccess(): bool`
Checks if operation was successful.

#### `getPath(): string`
Gets the folder path.

#### `getMessage(): string`
Gets result message.

#### `getErrors(): array`
Gets array of error messages.

## 💾 Storage Drivers

### LocalStorageDriver

Default storage driver for local file system.

#### Configuration
```php
[
    'driver' => 'local',
    'root' => 'uploads',
    'url' => '/uploads',
    'permissions' => [
        'file' => ['public' => 0644],
        'dir' => ['public' => 0755],
    ],
]
```

### S3StorageDriver

Amazon S3 storage driver.

#### Configuration
```php
[
    'driver' => 's3',
    'key' => 'your-access-key',
    'secret' => 'your-secret-key',
    'region' => 'us-east-1',
    'bucket' => 'your-bucket',
    'url' => 'https://your-bucket.s3.amazonaws.com',
]
```

## ⚙️ Processors

### ImageProcessor

Handles image processing, compression, and thumbnail generation.

#### Configuration
```php
[
    'enabled' => true,
    'compress_images' => true,
    'quality' => 85,
    'auto_orient' => true,
    'strip_metadata' => true,
]
```

### Custom Processor

Implement `ProcessorInterface` to create custom processors.

```php
class CustomProcessor implements \Package\FileUpload\Contracts\ProcessorInterface
{
    public function process(string $filePath, array $options): ProcessingResult
    {
        // Custom processing logic
        return ProcessingResult::success($filePath);
    }
    
    public function supports(string $mimeType): bool
    {
        return strpos($mimeType, 'image/') === 0;
    }
}
```
