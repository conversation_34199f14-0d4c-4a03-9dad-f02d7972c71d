# FileUpload Package - Configuration Guide

This guide explains the **best practices for configuring the FileUpload package** using a layered configuration approach that provides maximum flexibility and portability.

## 🎯 Configuration Philosophy

The FileUpload package uses a **4-layer configuration system** with the following priority order:

1. **Runtime Parameters** (highest priority) - Method calls and constructor parameters
2. **Configuration File** - `config/fileupload.php`
3. **Environment Variables** - `.env` file
4. **Default Values** (lowest priority) - Built-in package defaults

Each layer can override settings from lower-priority layers, giving you complete control over configuration.

## 📋 Configuration Methods

### 1. Environment Variables (.env file) - **Recommended for Deployment Settings**

Use environment variables for settings that change between environments (development, staging, production).

```env
# Storage Configuration
FILEUPLOAD_DRIVER=local
FILEUPLOAD_LOCAL_ROOT=public/uploads/media
FILEUPLOAD_LOCAL_URL=/uploads/media

# File Validation
FILEUPLOAD_MAX_SIZE=10485760
FILEUPLOAD_ALLOWED_EXTENSIONS=jpg,jpeg,png,gif,webp,pdf

# Image Processing
FILEUPLOAD_PROCESSING_ENABLED=true
FILEUPLOAD_COMPRESS_IMAGES=true
FILEUPLOAD_IMAGE_QUALITY=85
```

**✅ Best for:** Server-specific paths, file size limits, allowed file types, feature toggles

### 2. Configuration File (config/fileupload.php) - **Recommended for Application Defaults**

Use the configuration file for application-wide defaults and complex settings.

```php
<?php
return [
    'default' => 'local',
    'drivers' => [
        'local' => [
            'driver' => 'local',
            'root' => resolveUploadPath($_ENV['FILEUPLOAD_LOCAL_ROOT'] ?? 'public/uploads'),
            'url' => $_ENV['FILEUPLOAD_LOCAL_URL'] ?? '/uploads',
        ],
    ],
    'validation' => [
        'max_file_size' => (int)($_ENV['FILEUPLOAD_MAX_SIZE'] ?? 10485760),
        'allowed_extensions' => explode(',', $_ENV['FILEUPLOAD_ALLOWED_EXTENSIONS'] ?? 'jpg,png,gif'),
    ],
];
```

**✅ Best for:** Complex nested configurations, computed values, application-wide defaults

### 3. Runtime Parameters - **Recommended for Dynamic Settings**

Use runtime parameters for context-specific or user-specific settings.

```php
// Override configuration at runtime
$uploader = FileUploadManager::createWithConfig([
    'validation' => [
        'max_file_size' => 5242880, // 5MB for this specific upload
        'allowed_extensions' => ['jpg', 'png'] // Only images for this context
    ]
]);

// Directory-specific uploader
$uploader = FileUploadManager::createForDirectory('/tmp/temp-uploads', '/temp');

// Validation-specific uploader
$uploader = FileUploadManager::createWithValidation([
    'max_file_size' => 2097152, // 2MB
    'allowed_extensions' => ['pdf', 'doc', 'docx']
]);
```

**✅ Best for:** User-specific limits, context-dependent validation, temporary overrides

## 🚀 Usage Examples

### Basic Usage (Environment + Config File)

```php
// Uses environment variables and config file
$uploader = FileUploadManager::create();
$result = $uploader->upload($_FILES['file']);
```

### Runtime Configuration Override

```php
// Override specific settings at runtime
$uploader = FileUploadManager::createWithConfig([
    'validation' => [
        'max_file_size' => 20971520, // 20MB
        'allowed_extensions' => ['jpg', 'png', 'gif', 'webp']
    ],
    'processing' => [
        'enabled' => true,
        'quality' => 95
    ]
]);

$result = $uploader->upload($_FILES['file']);
```

### Multiple Upload Contexts

```php
// Different uploaders for different contexts
$avatarUploader = FileUploadManager::createWithConfig([
    'validation' => ['max_file_size' => 1048576], // 1MB for avatars
    'processing' => ['quality' => 90]
]);

$documentUploader = FileUploadManager::createWithValidation([
    'max_file_size' => 10485760, // 10MB for documents
    'allowed_extensions' => ['pdf', 'doc', 'docx']
]);

$galleryUploader = FileUploadManager::createForDirectory('public/gallery', '/gallery');
```

## 🔧 Dynamic Path Handling

The package automatically resolves relative paths to absolute paths for maximum portability:

```env
# Relative paths (recommended)
FILEUPLOAD_LOCAL_ROOT=public/uploads/media
FILEUPLOAD_LOCAL_ROOT=storage/uploads
FILEUPLOAD_LOCAL_ROOT=uploads

# Absolute paths (if needed)
FILEUPLOAD_LOCAL_ROOT=/var/www/html/uploads
```

The package tests multiple base paths:
1. Current working directory
2. Script directory
3. Document root
4. Fallback to temp directory

## 📊 Configuration Priority Example

```php
// Environment variable
$_ENV['FILEUPLOAD_MAX_SIZE'] = '10485760'; // 10MB

// Runtime override
$uploader = FileUploadManager::createWithConfig([
    'validation' => [
        'max_file_size' => 52428800 // 50MB - This takes priority!
    ]
]);

// Result: 50MB limit (runtime overrides environment)
```

## 🎯 Best Practices

### ✅ DO

- **Use relative paths** in environment variables for portability
- **Use environment variables** for deployment-specific settings
- **Use runtime configuration** for dynamic, context-specific needs
- **Use configuration files** for complex, application-wide defaults
- **Test configuration** in different environments

### ❌ DON'T

- **Don't hardcode absolute paths** in environment variables
- **Don't put sensitive data** in configuration files (use environment variables)
- **Don't override everything** at runtime (use sparingly for specific needs)
- **Don't ignore the configuration hierarchy** - understand the priority order

## 🔍 Configuration Debugging

```php
// Get current configuration
$uploader = FileUploadManager::create();
$config = $uploader->getConfigInfo();

print_r($config);
// Shows resolved configuration from all layers
```

## 📁 File Structure

```
project/
├── .env                           # Environment variables
├── config/
│   └── fileupload.php            # Main configuration file
├── package/FileUpload/
│   ├── examples/
│   │   └── configuration-examples.php  # Usage examples
│   └── docs/
│       └── CONFIGURATION.md      # This guide
```

## 🎉 Summary

The layered configuration approach provides:

- **Flexibility**: Override any setting at any level
- **Portability**: Relative paths work across environments
- **Maintainability**: Clear separation of concerns
- **Scalability**: Easy to add new configuration options
- **Security**: Sensitive data in environment variables only

Choose the right configuration method for each use case and enjoy the flexibility of the FileUpload package!
