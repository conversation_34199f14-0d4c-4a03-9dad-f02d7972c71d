# FileUpload Package Usage Guide

## Table of Contents

1. [Basic Usage](#basic-usage)
2. [Configuration](#configuration)
3. [Storage Drivers](#storage-drivers)
4. [File Validation](#file-validation)
5. [Image Processing](#image-processing)
6. [Folder Operations](#folder-operations)
7. [Advanced Features](#advanced-features)
8. [Error Handling](#error-handling)
9. [API Reference](#api-reference)

## Basic Usage

### Simple File Upload

```php
<?php
require_once 'vendor/autoload.php';

use Package\FileUpload\FileUpload;

// Basic upload with default configuration
$uploader = new FileUpload();

if (isset($_FILES['file'])) {
    $result = $uploader->upload($_FILES['file']);

    if ($result->isSuccess()) {
        echo "File uploaded successfully!";
        echo "URL: " . $result->getUrl();
        echo "Path: " . $result->getPath();

        // Get file metadata
        $metadata = $result->getMetadata();
        echo "Size: " . $metadata['size'] . " bytes";
        echo "Type: " . $metadata['mime_type'];
    } else {
        echo "Upload failed: " . $result->getMessage();
        foreach ($result->getErrors() as $error) {
            echo "Error: " . $error;
        }
    }
}
?>
```

### Multiple File Upload

```php
<?php
use Package\FileUpload\FileUpload;

$uploader = new FileUpload();

if (isset($_FILES['files'])) {
    $results = $uploader->uploadMultiple($_FILES['files']);

    foreach ($results as $index => $result) {
        if ($result->isSuccess()) {
            echo "File {$index} uploaded: " . $result->getUrl() . "\n";
        } else {
            echo "File {$index} failed: " . $result->getMessage() . "\n";
        }
    }
}
?>
```

## Configuration

### Environment Variables

Configure the package through `frontend/.env`:

```env
# Storage Configuration
FILEUPLOAD_DRIVER=local
FILEUPLOAD_LOCAL_ROOT=uploads
FILEUPLOAD_LOCAL_URL=/uploads

# Validation Configuration
FILEUPLOAD_MAX_SIZE=10485760
FILEUPLOAD_ALLOWED_TYPES=image/jpeg,image/png,image/gif,image/webp,application/pdf
FILEUPLOAD_ALLOWED_EXTENSIONS=jpg,jpeg,png,gif,webp,pdf
FILEUPLOAD_FORBIDDEN_EXTENSIONS=php,exe,bat,sh,js,html

# Processing Configuration
FILEUPLOAD_PROCESSING_ENABLED=true
FILEUPLOAD_PROCESSING_QUALITY=85
FILEUPLOAD_THUMBNAILS_ENABLED=true

# Thumbnail Sizes
FILEUPLOAD_THUMB_SMALL_WIDTH=150
FILEUPLOAD_THUMB_SMALL_HEIGHT=150
FILEUPLOAD_THUMB_SMALL_MODE=crop
FILEUPLOAD_THUMB_MEDIUM_WIDTH=300
FILEUPLOAD_THUMB_MEDIUM_HEIGHT=300
FILEUPLOAD_THUMB_MEDIUM_MODE=crop
FILEUPLOAD_THUMB_LARGE_WIDTH=600
FILEUPLOAD_THUMB_LARGE_HEIGHT=600
FILEUPLOAD_THUMB_LARGE_MODE=fit

# Security Configuration
FILEUPLOAD_CHECK_HEADERS=true
FILEUPLOAD_RANDOMIZE_NAMES=true
FILEUPLOAD_PRESERVE_NAME=false
```

### Programmatic Configuration

```php
<?php
use Package\FileUpload\FileUpload;
use Package\FileUpload\Config\ConfigManager;

// Configure via ConfigManager
$config = ConfigManager::getInstance();
$config->set('validation.max_file_size', 5242880); // 5MB
$config->set('validation.allowed_mime_types', ['image/jpeg', 'image/png']);

// Or pass config directly to FileUpload
$uploader = new FileUpload(null, null, [
    'validation' => [
        'max_file_size' => 5242880,
        'allowed_mime_types' => ['image/jpeg', 'image/png']
    ]
]);
?>
```

## Storage Drivers

### Local Storage

```php
<?php
use Package\FileUpload\FileUpload;
use Package\FileUpload\Storage\LocalStorageDriver;

$localStorage = new LocalStorageDriver([
    'root' => __DIR__ . '/uploads',
    'url' => '/uploads',
    'permissions' => [
        'file' => ['public' => 0644],
        'dir' => ['public' => 0755]
    ]
]);

$uploader = new FileUpload($localStorage);
?>
```

### Amazon S3 Storage

```php
<?php
use Package\FileUpload\FileUpload;
use Package\FileUpload\Storage\S3StorageDriver;

$s3Storage = new S3StorageDriver([
    'key' => $_ENV['AWS_ACCESS_KEY_ID'],
    'secret' => $_ENV['AWS_SECRET_ACCESS_KEY'],
    'region' => $_ENV['AWS_DEFAULT_REGION'],
    'bucket' => $_ENV['AWS_BUCKET'],
    'visibility' => 'public'
]);

$uploader = new FileUpload($s3Storage);
?>
```

### Dynamic Storage Driver Selection

```php
<?php
use Package\FileUpload\Storage\StorageDriverFactory;

// Create driver from configuration
$driver = StorageDriverFactory::createFromConfig('s3');

// Or create with custom config
$driver = StorageDriverFactory::create('s3', [
    'bucket' => 'my-custom-bucket'
]);
?>
```

## File Validation

### Built-in Validation Rules

```php
<?php
$result = $uploader->upload($_FILES['file'], [
    'validation' => [
        'max_file_size' => 5242880, // 5MB
        'allowed_mime_types' => ['image/jpeg', 'image/png', 'image/gif'],
        'allowed_extensions' => ['jpg', 'jpeg', 'png', 'gif'],
        'forbidden_extensions' => ['php', 'exe', 'bat'],
        'check_file_headers' => true,
        'check_image_dimensions' => true,
        'max_width' => 2048,
        'max_height' => 2048,
        'min_width' => 100,
        'min_height' => 100
    ]
]);
?>
```

### Custom Validation Rules

```php
<?php
use Package\FileUpload\FileUpload;
use Package\FileUpload\Validation\FileValidator;

$validator = new FileValidator();

// Add custom validation rule
$validator->addRule('custom_size_check', function($file, $ruleValue, $result) {
    if ($file['size'] > $ruleValue) {
        $result->addError("File too large for custom rule");
    }
});

// Add custom image validation
$validator->addRule('aspect_ratio', function($file, $ratio, $result) {
    $imageInfo = getimagesize($file['tmp_name']);
    if ($imageInfo) {
        $actualRatio = $imageInfo[0] / $imageInfo[1];
        if (abs($actualRatio - $ratio) > 0.1) {
            $result->addError("Image aspect ratio must be approximately {$ratio}:1");
        }
    }
});

$uploader = new FileUpload(null, $validator);

// Use custom rules in upload
$result = $uploader->upload($_FILES['file'], [
    'validation' => [
        'custom_size_check' => 1048576, // 1MB
        'aspect_ratio' => 1.5 // 3:2 ratio
    ]
]);
?>
```

## Image Processing

### Automatic Thumbnail Generation

```php
<?php
use Package\FileUpload\FileUpload;
use Package\FileUpload\Processing\ImageProcessor;

$uploader = new FileUpload();

// Add image processor for automatic thumbnails
$processor = new ImageProcessor([
    'thumbnails' => [
        'enabled' => true,
        'sizes' => [
            'small' => ['width' => 150, 'height' => 150, 'mode' => 'crop'],
            'medium' => ['width' => 300, 'height' => 300, 'mode' => 'crop'],
            'large' => ['width' => 600, 'height' => 600, 'mode' => 'fit']
        ]
    ]
]);

$uploader->addProcessor($processor);

$result = $uploader->upload($_FILES['image']);

if ($result->isSuccess()) {
    echo "Original: " . $result->getUrl() . "\n";

    // Access generated thumbnails
    $processedFiles = $result->getProcessedFiles();
    foreach ($processedFiles as $type => $fileInfo) {
        echo ucfirst($type) . ": " . $fileInfo['url'] . "\n";
        echo "Size: " . $fileInfo['metadata']['width'] . "x" . $fileInfo['metadata']['height'] . "\n";
    }
}
?>
```

### Custom Processing Options

```php
<?php
$result = $uploader->upload($_FILES['image'], [
    'processing' => [
        'quality' => 90,
        'auto_orient' => true,
        'strip_metadata' => true,
        'thumbnails' => [
            'sizes' => [
                'custom' => ['width' => 400, 'height' => 400, 'mode' => 'fit'],
                'banner' => ['width' => 1200, 'height' => 300, 'mode' => 'crop']
            ]
        ]
    ]
]);
?>
```

### Resize Modes

- **crop**: Crops the image to exact dimensions
- **fit**: Fits image within dimensions, maintaining aspect ratio
- **stretch**: Stretches image to exact dimensions (may distort)

```php
<?php
// Different resize modes
$thumbnails = [
    'crop_thumb' => ['width' => 200, 'height' => 200, 'mode' => 'crop'],
    'fit_thumb' => ['width' => 200, 'height' => 200, 'mode' => 'fit'],
    'stretch_thumb' => ['width' => 200, 'height' => 200, 'mode' => 'stretch']
];
?>
```

## Folder Operations

The FileUpload package supports independent folder operations that work across all storage drivers without requiring file uploads.

### Create Folders

```php
<?php
use Package\FileUpload\FileUpload;

$uploader = new FileUpload();

// Create a simple folder
$result = $uploader->createFolder('user-uploads');

if ($result->isSuccess()) {
    echo "Folder created: " . $result->getPath() . "\n";
    echo "URL: " . $result->getUrl() . "\n";
    echo "Message: " . $result->getMessage() . "\n";
} else {
    echo "Failed: " . $result->getMessage() . "\n";
    foreach ($result->getErrors() as $error) {
        echo "Error: " . $error . "\n";
    }
}

// Create nested folders
$result = $uploader->createFolder('products/images/thumbnails', [
    'visibility' => 'public' // For local storage
]);

// Create folder with custom options
$result = $uploader->createFolder('private-docs', [
    'visibility' => 'private'
]);
?>
```

### Check Folder Existence

```php
<?php
// Simple existence check
if ($uploader->folderExists('user-uploads')) {
    echo "Folder exists!";
} else {
    echo "Folder not found.";
}

// Check nested folder
if ($uploader->folderExists('products/images/thumbnails')) {
    echo "Nested folder exists!";
}
?>
```

### List Folder Contents

```php
<?php
// List folder contents (non-recursive)
$result = $uploader->listFolder('user-uploads');

if ($result->isSuccess()) {
    echo "Found " . count($result->getContents()) . " items:\n";

    foreach ($result->getContents() as $item) {
        echo $item['type'] . ": " . $item['name'] . "\n";
        echo "  Path: " . $item['path'] . "\n";
        echo "  Size: " . ($item['size'] ? number_format($item['size']) . ' bytes' : 'N/A') . "\n";
        echo "  Modified: " . date('Y-m-d H:i:s', $item['last_modified']) . "\n\n";
    }
}

// List recursively
$result = $uploader->listFolder('products', true);

if ($result->isSuccess()) {
    echo "Recursive listing:\n";
    foreach ($result->getContents() as $item) {
        echo str_repeat('  ', substr_count($item['path'], '/')) . $item['name'] . " (" . $item['type'] . ")\n";
    }
}
?>
```

### Delete Folders

```php
<?php
// Delete empty folder
$result = $uploader->deleteFolder('empty-folder');

if ($result->isSuccess()) {
    echo "Folder deleted: " . $result->getMessage();
} else {
    echo "Delete failed: " . $result->getMessage();
}

// Delete folder with contents (recursive)
$result = $uploader->deleteFolder('old-uploads', true);

if ($result->isSuccess()) {
    echo "Folder and contents deleted successfully";
}

// Safe deletion with confirmation
$folderPath = 'user-data/temp';
if ($uploader->folderExists($folderPath)) {
    $listResult = $uploader->listFolder($folderPath, true);
    $itemCount = count($listResult->getContents());

    echo "Folder contains {$itemCount} items. Proceed with deletion? (y/n): ";
    $confirm = trim(fgets(STDIN));

    if (strtolower($confirm) === 'y') {
        $result = $uploader->deleteFolder($folderPath, true);
        echo $result->getMessage();
    }
}
?>
```

### Storage Driver Specific Behavior

#### Local Storage

- Creates actual directories on the filesystem
- Supports file permissions (public/private)
- Recursive operations work with filesystem hierarchy

#### Amazon S3

- Creates virtual directories using folder markers
- Directories are represented as objects ending with '/'
- Recursive deletion removes all objects with the folder prefix

#### Google Cloud Storage & Azure Blob

- Similar to S3 with virtual directory concepts
- Implementation pending (placeholder methods available)

### Folder Operations with Different Storage Drivers

```php
<?php
use Package\FileUpload\FileUpload;
use Package\FileUpload\Storage\LocalStorageDriver;
use Package\FileUpload\Storage\S3StorageDriver;

// Local storage
$localStorage = new LocalStorageDriver([
    'root' => '/var/www/uploads',
    'url' => '/uploads'
]);
$localUploader = new FileUpload($localStorage);
$localUploader->createFolder('local-folder');

// S3 storage
$s3Storage = new S3StorageDriver([
    'key' => $_ENV['AWS_ACCESS_KEY_ID'],
    'secret' => $_ENV['AWS_SECRET_ACCESS_KEY'],
    'region' => $_ENV['AWS_DEFAULT_REGION'],
    'bucket' => $_ENV['AWS_BUCKET']
]);
$s3Uploader = new FileUpload($s3Storage);
$s3Uploader->createFolder('s3-folder');

// Both will work with the same API
?>
```

### Error Handling for Folder Operations

```php
<?php
use Package\FileUpload\Exceptions\StorageException;

try {
    $result = $uploader->createFolder('new-folder');

    if (!$result->isSuccess()) {
        echo "Operation failed: " . $result->getMessage() . "\n";

        foreach ($result->getErrors() as $error) {
            echo "Error: " . $error . "\n";
        }
    }

} catch (StorageException $e) {
    echo "Storage error: " . $e->getMessage() . "\n";
} catch (\Exception $e) {
    echo "General error: " . $e->getMessage() . "\n";
}
?>
```

## Advanced Features

### Upload with Custom Directory and Filename

```php
<?php
$result = $uploader->upload($_FILES['file'], [
    'directory' => 'user-uploads/2024/01',
    'prefix' => 'user_123_',
    'suffix' => '_processed',
    'preserve_name' => false // Use random filename
]);
?>
```

### File Metadata Access

```php
<?php
$result = $uploader->upload($_FILES['image']);

if ($result->isSuccess()) {
    $metadata = $result->getMetadata();

    echo "Original name: " . $metadata['original_name'] . "\n";
    echo "File size: " . $metadata['size'] . " bytes\n";
    echo "MIME type: " . $metadata['mime_type'] . "\n";
    echo "Extension: " . $metadata['extension'] . "\n";

    // Image-specific metadata
    if (isset($metadata['width'])) {
        echo "Dimensions: {$metadata['width']}x{$metadata['height']}\n";
        echo "Image type: " . $metadata['image_type'] . "\n";
    }
}
?>
```

### Batch Upload with Different Options

```php
<?php
$files = $_FILES['multiple_files'];
$results = [];

foreach ($files['name'] as $index => $name) {
    $file = [
        'name' => $files['name'][$index],
        'type' => $files['type'][$index],
        'tmp_name' => $files['tmp_name'][$index],
        'error' => $files['error'][$index],
        'size' => $files['size'][$index]
    ];

    // Different options per file type
    $options = [];
    if (strpos($file['type'], 'image/') === 0) {
        $options = [
            'directory' => 'images',
            'processing' => ['thumbnails' => ['enabled' => true]]
        ];
    } else {
        $options = [
            'directory' => 'documents',
            'validation' => ['max_file_size' => 20971520] // 20MB for docs
        ];
    }

    $results[] = $uploader->upload($file, $options);
}
?>
```

## Error Handling

### Comprehensive Error Handling

```php
<?php
use Package\FileUpload\Exceptions\FileUploadException;
use Package\FileUpload\Exceptions\ValidationException;
use Package\FileUpload\Exceptions\StorageException;

try {
    $result = $uploader->upload($_FILES['file']);

    if (!$result->isSuccess()) {
        // Handle upload failure
        echo "Upload failed: " . $result->getMessage() . "\n";

        foreach ($result->getErrors() as $error) {
            echo "Error: {$error}\n";
        }
    }

} catch (ValidationException $e) {
    echo "Validation failed: " . $e->getMessage() . "\n";
    foreach ($e->getErrors() as $error) {
        echo "Validation error: {$error}\n";
    }

} catch (StorageException $e) {
    echo "Storage error: " . $e->getMessage() . "\n";
    $context = $e->getContext();
    if (!empty($context)) {
        echo "Context: " . json_encode($context) . "\n";
    }

} catch (FileUploadException $e) {
    echo "Upload exception: " . $e->getMessage() . "\n";

} catch (\Exception $e) {
    echo "General error: " . $e->getMessage() . "\n";
}
?>
```

### Validation Result Handling

```php
<?php
use Package\FileUpload\Validation\FileValidator;

$validator = new FileValidator();
$validationResult = $validator->validate($_FILES['file']);

if (!$validationResult->isValid()) {
    echo "Validation failed:\n";
    foreach ($validationResult->getErrors() as $error) {
        echo "- {$error}\n";
    }
}

if ($validationResult->hasWarnings()) {
    echo "Warnings:\n";
    foreach ($validationResult->getWarnings() as $warning) {
        echo "- {$warning}\n";
    }
}

// Access validation metadata
$metadata = $validationResult->getMetadata();
echo "File info: " . json_encode($metadata) . "\n";
?>
```

## API Reference

### FileUpload Class

#### Methods

**`upload(array $file, array $options = []): UploadResult`**

- Uploads a single file
- `$file`: $\_FILES array element
- `$options`: Upload configuration options
- Returns: UploadResult object

**`uploadMultiple(array $files, array $options = []): array`**

- Uploads multiple files
- `$files`: Array of $\_FILES elements
- `$options`: Upload configuration options
- Returns: Array of UploadResult objects

**`delete(string $path): bool`**

- Deletes an uploaded file
- `$path`: File path to delete
- Returns: Success boolean

**`getFileInfo(string $path): array`**

- Gets file information
- `$path`: File path
- Returns: File metadata array

**`setStorageDriver(StorageDriverInterface $driver): void`**

- Sets the storage driver

**`setValidator(ValidatorInterface $validator): void`**

- Sets the file validator

**`addProcessor(ProcessorInterface $processor): void`**

- Adds a file processor

**`createFolder(string $path, array $options = []): FolderResult`**

- Creates a folder/directory at the specified path
- `$path`: The folder path to create
- `$options`: Additional options (visibility, etc.)
- Returns: FolderResult object

**`folderExists(string $path): bool`**

- Checks if a folder exists at the specified path
- `$path`: The folder path to check
- Returns: Boolean indicating existence

**`deleteFolder(string $path, bool $recursive = false): FolderResult`**

- Deletes a folder at the specified path
- `$path`: The folder path to delete
- `$recursive`: Whether to delete contents recursively
- Returns: FolderResult object

**`listFolder(string $path, bool $recursive = false): FolderResult`**

- Lists contents of a folder
- `$path`: The folder path to list
- `$recursive`: Whether to list recursively
- Returns: FolderResult object with contents

### UploadResult Class

#### Methods

**`isSuccess(): bool`**

- Returns whether upload was successful

**`getPath(): ?string`**

- Returns the stored file path

**`getUrl(): ?string`**

- Returns the public file URL

**`getMessage(): ?string`**

- Returns the result message

**`getMetadata(): array`**

- Returns file metadata

**`getErrors(): array`**

- Returns array of error messages

**`getProcessedFiles(): array`**

- Returns array of processed files (thumbnails, etc.)

### FolderResult Class

#### Methods

**`isSuccess(): bool`**

- Returns whether folder operation was successful

**`getPath(): ?string`**

- Returns the folder path

**`getUrl(): ?string`**

- Returns the public folder URL (if applicable)

**`getMessage(): ?string`**

- Returns the operation result message

**`getMetadata(): array`**

- Returns folder metadata

**`getErrors(): array`**

- Returns array of error messages

**`getContents(): array`**

- Returns array of folder contents (for list operations)

### Upload Options

```php
$options = [
    'directory' => 'custom/path',           // Upload directory
    'prefix' => 'file_',                    // Filename prefix
    'suffix' => '_v1',                      // Filename suffix
    'preserve_name' => false,               // Keep original filename

    'validation' => [
        'max_file_size' => 5242880,         // Maximum file size
        'allowed_mime_types' => [...],      // Allowed MIME types
        'allowed_extensions' => [...],      // Allowed extensions
        'forbidden_extensions' => [...],    // Forbidden extensions
        'check_file_headers' => true,       // Validate file headers
        'check_image_dimensions' => true,   // Validate image dimensions
        'max_width' => 2048,               // Maximum image width
        'max_height' => 2048,              // Maximum image height
        'min_width' => 100,                // Minimum image width
        'min_height' => 100,               // Minimum image height
    ],

    'processing' => [
        'quality' => 85,                    // JPEG quality (0-100)
        'auto_orient' => true,              // Auto-orient images
        'strip_metadata' => true,           // Strip EXIF data
        'thumbnails' => [
            'enabled' => true,              // Enable thumbnails
            'sizes' => [
                'small' => [
                    'width' => 150,
                    'height' => 150,
                    'mode' => 'crop'        // crop, fit, stretch
                ]
            ]
        ]
    ],

    'storage' => [
        'visibility' => 'public',           // File visibility
        'options' => [...]                  // Driver-specific options
    ]
];
?>
```

This comprehensive usage guide covers all aspects of the FileUpload package using PHP's built-in GD extension for image processing.
