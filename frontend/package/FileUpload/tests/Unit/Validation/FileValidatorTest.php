<?php

namespace Package\FileUpload\Tests\Unit\Validation;

use PHPUnit\Framework\TestCase;
use Package\FileUpload\Validation\FileValidator;
use Package\FileUpload\Validation\ValidationResult;

class FileValidatorTest extends TestCase
{
    private FileValidator $validator;

    protected function setUp(): void
    {
        $this->validator = new FileValidator([
            'max_file_size' => 1048576, // 1MB
            'allowed_mime_types' => ['image/jpeg', 'image/png'],
            'allowed_extensions' => ['jpg', 'jpeg', 'png'],
            'forbidden_extensions' => ['php', 'exe'],
        ]);
    }

    public function testValidFilePassesValidation(): void
    {
        $file = createMockFile('test-image.jpg', 'image/jpeg', 500000);
        
        $result = $this->validator->validate($file);
        
        $this->assertTrue($result->isValid());
        $this->assertEmpty($result->getErrors());
    }

    public function testFileSizeExceedsLimit(): void
    {
        $file = createMockFile('test-image.jpg', 'image/jpeg', 2097152); // 2MB
        
        $result = $this->validator->validate($file);
        
        $this->assertFalse($result->isValid());
        $this->assertContains('File size exceeds the maximum allowed size of 1MB', $result->getErrors());
    }

    public function testInvalidMimeType(): void
    {
        $file = createMockFile('test-document.pdf', 'application/pdf', 500000);
        
        $result = $this->validator->validate($file);
        
        $this->assertFalse($result->isValid());
        $this->assertStringContainsString('File type \'application/pdf\' is not allowed', $result->getErrors()[0]);
    }

    public function testInvalidExtension(): void
    {
        $file = createMockFile('test-document.pdf', 'image/jpeg', 500000);
        
        $result = $this->validator->validate($file);
        
        $this->assertFalse($result->isValid());
        $this->assertStringContainsString('File extension \'pdf\' is not allowed', $result->getErrors()[0]);
    }

    public function testForbiddenExtension(): void
    {
        $file = createMockFile('malicious.php', 'text/plain', 500000);
        
        $result = $this->validator->validate($file);
        
        $this->assertFalse($result->isValid());
        $this->assertStringContainsString('File extension \'php\' is forbidden', $result->getErrors()[0]);
    }

    public function testUploadError(): void
    {
        $file = createMockFile('test-image.jpg', 'image/jpeg', 500000, UPLOAD_ERR_PARTIAL);
        
        $result = $this->validator->validate($file);
        
        $this->assertFalse($result->isValid());
        $this->assertContains('The uploaded file was only partially uploaded', $result->getErrors());
    }

    public function testCustomValidationRule(): void
    {
        $this->validator->addRule('custom_test', function($file, $ruleValue, $result) {
            if ($file['size'] > $ruleValue) {
                $result->addError('Custom rule failed');
            }
        });

        $file = createMockFile('test-image.jpg', 'image/jpeg', 500000);
        
        $result = $this->validator->validate($file, ['custom_test' => 100000]);
        
        $this->assertFalse($result->isValid());
        $this->assertContains('Custom rule failed', $result->getErrors());
    }

    public function testFileHeaderValidation(): void
    {
        // Test file with mismatched extension and content
        $file = createMockFile('test-image.png', 'image/jpeg', 500000);
        
        $result = $this->validator->validate($file, ['check_file_headers' => true]);
        
        $this->assertFalse($result->isValid());
        $this->assertStringContainsString('does not match the file content type', $result->getErrors()[0]);
    }

    public function testMetadataCollection(): void
    {
        $file = createMockFile('test-image.jpg', 'image/jpeg', 500000);
        
        $result = $this->validator->validate($file);
        
        $metadata = $result->getMetadata();
        $this->assertArrayHasKey('original_name', $metadata);
        $this->assertArrayHasKey('size', $metadata);
        $this->assertArrayHasKey('mime_type', $metadata);
        $this->assertArrayHasKey('extension', $metadata);
        
        $this->assertEquals('test-image.jpg', $metadata['original_name']);
        $this->assertEquals(500000, $metadata['size']);
        $this->assertEquals('image/jpeg', $metadata['mime_type']);
        $this->assertEquals('jpg', $metadata['extension']);
    }

    public function testGetAvailableRules(): void
    {
        $rules = $this->validator->getAvailableRules();
        
        $this->assertContains('max_file_size', $rules);
        $this->assertContains('allowed_mime_types', $rules);
        $this->assertContains('allowed_extensions', $rules);
        $this->assertContains('forbidden_extensions', $rules);
    }

    public function testSetAndGetDefaultRules(): void
    {
        $newRules = ['max_file_size' => 2097152];
        
        $this->validator->setDefaultRules($newRules);
        $defaultRules = $this->validator->getDefaultRules();
        
        $this->assertEquals(2097152, $defaultRules['max_file_size']);
    }
}
