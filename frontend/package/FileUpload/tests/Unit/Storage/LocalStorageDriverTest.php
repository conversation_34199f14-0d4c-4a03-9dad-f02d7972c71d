<?php

namespace Package\FileUpload\Tests\Unit\Storage;

use PHPUnit\Framework\TestCase;
use Package\FileUpload\Storage\LocalStorageDriver;
use Package\FileUpload\Exceptions\StorageException;

class LocalStorageDriverTest extends TestCase
{
    private LocalStorageDriver $driver;
    private string $testRoot;

    protected function setUp(): void
    {
        $this->testRoot = __DIR__ . '/../../temp/storage-test';
        
        $this->driver = new LocalStorageDriver([
            'root' => $this->testRoot,
            'url' => '/test-uploads',
        ]);
    }

    protected function tearDown(): void
    {
        if (is_dir($this->testRoot)) {
            $this->removeDirectory($this->testRoot);
        }
    }

    public function testStoreFile(): void
    {
        $path = 'test/file.txt';
        $contents = 'Hello, World!';

        $result = $this->driver->store($path, $contents);

        $this->assertEquals($path, $result);
        $this->assertTrue($this->driver->exists($path));
        $this->assertEquals($contents, $this->driver->get($path));
    }

    public function testStoreFileCreatesDirectories(): void
    {
        $path = 'deep/nested/directory/file.txt';
        $contents = 'Test content';

        $this->driver->store($path, contents);

        $this->assertTrue($this->driver->exists($path));
        $this->assertTrue(is_dir($this->testRoot . '/deep/nested/directory'));
    }

    public function testGetNonExistentFile(): void
    {
        $this->expectException(StorageException::class);
        $this->expectExceptionMessage('File not found: nonexistent.txt');

        $this->driver->get('nonexistent.txt');
    }

    public function testDeleteFile(): void
    {
        $path = 'test-delete.txt';
        $this->driver->store($path, 'content');

        $this->assertTrue($this->driver->exists($path));
        $this->assertTrue($this->driver->delete($path));
        $this->assertFalse($this->driver->exists($path));
    }

    public function testDeleteNonExistentFile(): void
    {
        $result = $this->driver->delete('nonexistent.txt');
        $this->assertTrue($result); // Should return true for non-existent files
    }

    public function testUrl(): void
    {
        $path = 'test/file.txt';
        $expectedUrl = '/test-uploads/test/file.txt';

        $url = $this->driver->url($path);

        $this->assertEquals($expectedUrl, $url);
    }

    public function testMetadata(): void
    {
        $path = 'metadata-test.txt';
        $contents = 'Test content for metadata';
        
        $this->driver->store($path, $contents);
        $metadata = $this->driver->metadata($path);

        $this->assertArrayHasKey('path', $metadata);
        $this->assertArrayHasKey('size', $metadata);
        $this->assertArrayHasKey('mime_type', $metadata);
        $this->assertArrayHasKey('last_modified', $metadata);
        
        $this->assertEquals($path, $metadata['path']);
        $this->assertEquals(strlen($contents), $metadata['size']);
        $this->assertEquals('text/plain', $metadata['mime_type']);
    }

    public function testCopyFile(): void
    {
        $sourcePath = 'source.txt';
        $targetPath = 'target.txt';
        $contents = 'Copy test content';

        $this->driver->store($sourcePath, $contents);
        $result = $this->driver->copy($sourcePath, $targetPath);

        $this->assertTrue($result);
        $this->assertTrue($this->driver->exists($sourcePath));
        $this->assertTrue($this->driver->exists($targetPath));
        $this->assertEquals($contents, $this->driver->get($targetPath));
    }

    public function testMoveFile(): void
    {
        $sourcePath = 'source-move.txt';
        $targetPath = 'target-move.txt';
        $contents = 'Move test content';

        $this->driver->store($sourcePath, $contents);
        $result = $this->driver->move($sourcePath, $targetPath);

        $this->assertTrue($result);
        $this->assertFalse($this->driver->exists($sourcePath));
        $this->assertTrue($this->driver->exists($targetPath));
        $this->assertEquals($contents, $this->driver->get($targetPath));
    }

    public function testGetName(): void
    {
        $this->assertEquals('local', $this->driver->getName());
    }

    private function removeDirectory(string $dir): void
    {
        if (!is_dir($dir)) {
            return;
        }

        $files = array_diff(scandir($dir), ['.', '..']);
        
        foreach ($files as $file) {
            $path = $dir . '/' . $file;
            if (is_dir($path)) {
                $this->removeDirectory($path);
            } else {
                unlink($path);
            }
        }
        
        rmdir($dir);
    }
}
