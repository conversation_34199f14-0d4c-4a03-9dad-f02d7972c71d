<?php

require_once __DIR__ . '/../vendor/autoload.php';

// Create test directories
$testDirs = [
    __DIR__ . '/temp',
    __DIR__ . '/temp/test-uploads',
    __DIR__ . '/temp/fixtures',
];

foreach ($testDirs as $dir) {
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
    }
}

// Create test fixtures
$fixtures = [
    'test-image.jpg' => base64_decode('/9j/4AAQSkZJRgABAQEAYABgAAD/2wBDAAEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQH/2wBDAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQH/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwA/8A'),
    'test-image.png' => base64_decode('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=='),
    'test-document.pdf' => '%PDF-1.4\n1 0 obj\n<<\n/Type /Catalog\n/Pages 2 0 R\n>>\nendobj\n2 0 obj\n<<\n/Type /Pages\n/Kids [3 0 R]\n/Count 1\n>>\nendobj\n3 0 obj\n<<\n/Type /Page\n/Parent 2 0 R\n/MediaBox [0 0 612 792]\n>>\nendobj\nxref\n0 4\n0000000000 65535 f \n0000000009 00000 n \n0000000058 00000 n \n0000000115 00000 n \ntrailer\n<<\n/Size 4\n/Root 1 0 R\n>>\nstartxref\n174\n%%EOF',
    'malicious.php' => '<?php echo "This should not be uploaded"; ?>',
];

foreach ($fixtures as $filename => $content) {
    $path = __DIR__ . '/temp/fixtures/' . $filename;
    if (!file_exists($path)) {
        file_put_contents($path, $content);
    }
}

// Helper function to create mock $_FILES array
function createMockFile($filename, $type = 'image/jpeg', $size = null, $error = UPLOAD_ERR_OK)
{
    $fixturePath = __DIR__ . '/temp/fixtures/' . $filename;
    
    if (!file_exists($fixturePath)) {
        throw new InvalidArgumentException("Fixture file not found: {$filename}");
    }
    
    $actualSize = $size ?? filesize($fixturePath);
    
    // Create temporary file for testing
    $tempPath = tempnam(sys_get_temp_dir(), 'test_upload_');
    copy($fixturePath, $tempPath);
    
    return [
        'name' => $filename,
        'type' => $type,
        'tmp_name' => $tempPath,
        'error' => $error,
        'size' => $actualSize,
    ];
}

// Cleanup function
function cleanupTestFiles()
{
    $testDir = __DIR__ . '/temp';
    if (is_dir($testDir)) {
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($testDir, RecursiveDirectoryIterator::SKIP_DOTS),
            RecursiveIteratorIterator::CHILD_FIRST
        );
        
        foreach ($iterator as $file) {
            if ($file->isDir()) {
                rmdir($file->getRealPath());
            } else {
                unlink($file->getRealPath());
            }
        }
        
        rmdir($testDir);
    }
}

// Register cleanup function
register_shutdown_function('cleanupTestFiles');
