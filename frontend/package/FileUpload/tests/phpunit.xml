<?xml version="1.0" encoding="UTF-8"?>
<phpunit xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="https://schema.phpunit.de/9.5/phpunit.xsd"
         bootstrap="bootstrap.php"
         cacheResultFile=".phpunit.result.cache"
         executionOrder="depends,defects"
         forceCoversAnnotation="false"
         beStrictAboutCoversAnnotation="true"
         beStrictAboutOutputDuringTests="true"
         beStrictAboutTodoAnnotatedTests="true"
         convertDeprecationsToExceptions="true"
         failOnRisky="true"
         failOnWarning="true"
         verbose="true">
    <testsuites>
        <testsuite name="Unit">
            <directory suffix="Test.php">./Unit</directory>
        </testsuite>
        <testsuite name="Integration">
            <directory suffix="Test.php">./Integration</directory>
        </testsuite>
        <testsuite name="Feature">
            <directory suffix="Test.php">./Feature</directory>
        </testsuite>
    </testsuites>

    <coverage cacheDirectory=".phpunit.cache/code-coverage"
              processUncoveredFiles="true">
        <include>
            <directory suffix=".php">../src</directory>
        </include>
        <exclude>
            <directory>../src/Storage/GoogleCloudStorageDriver.php</directory>
            <directory>../src/Storage/AzureStorageDriver.php</directory>
        </exclude>
        <report>
            <html outputDirectory="coverage-html"/>
            <text outputFile="coverage.txt"/>
            <clover outputFile="coverage.xml"/>
        </report>
    </coverage>

    <php>
        <env name="APP_ENV" value="testing"/>
        <env name="FILEUPLOAD_DRIVER" value="local"/>
        <env name="FILEUPLOAD_LOCAL_ROOT" value="./temp/test-uploads"/>
        <env name="FILEUPLOAD_LOCAL_URL" value="/test-uploads"/>
    </php>
</phpunit>
