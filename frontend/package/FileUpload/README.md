# FileUpload Package

A powerful, flexible, and framework-agnostic PHP file upload package with advanced image processing, thumbnail generation, and dynamic transformation capabilities.

## ✨ Features

- **🚀 Zero Configuration** - Works out of the box with sensible defaults
- **📁 Multi-Storage Support** - Local, AWS S3, and extensible storage drivers
- **🖼️ Advanced Image Processing** - Automatic compression, resizing, and optimization
- **🔄 Dynamic Image Transformation** - On-the-fly resizing via URL parameters
- **📸 Automatic Thumbnail Generation** - Configurable sizes and modes
- **🛡️ Security First** - File type validation, size limits, and sanitization
- **🌐 Framework Agnostic** - Works with any PHP project (Laravel, Symfony, etc.)
- **⚡ High Performance** - Optimized for speed and memory efficiency
- **🔧 Highly Configurable** - Environment-driven configuration

## 📋 Requirements

- PHP 8.0 or higher
- GD extension (for image processing)
- Composer (for installation)

## 🚀 Quick Start

### Installation

```bash
# Install via Composer
composer require your-vendor/fileupload

# Or copy the package to your project
cp -r package/FileUpload /path/to/your/project/
```

### Basic Usage

```php
<?php
require_once 'vendor/autoload.php';

// Zero-configuration upload
$uploadManager = \Package\FileUpload\FileUploadManager::create();
$result = $uploadManager->upload($_FILES['file']);

if ($result->isSuccess()) {
    echo "File uploaded: " . $result->getPath();
    echo "URL: " . $result->getUrl();
    
    // Get generated thumbnails
    $thumbnails = $result->getProcessedFiles();
    foreach ($thumbnails as $size => $info) {
        echo "$size thumbnail: " . $info['url'];
    }
} else {
    echo "Upload failed: " . $result->getMessage();
}
?>
```

## 🔧 Configuration

Create a `.env` file in your project root:

```env
# Basic Configuration
FILEUPLOAD_PROCESSING_ENABLED=true
FILEUPLOAD_COMPRESS_IMAGES=true
FILEUPLOAD_IMAGE_QUALITY=85

# Storage Configuration
FILEUPLOAD_LOCAL_ROOT=uploads
FILEUPLOAD_LOCAL_URL=/uploads

# Thumbnail Configuration
FILEUPLOAD_THUMBNAILS_ENABLED=true
FILEUPLOAD_THUMBNAIL_SIZES=[{"name":"small","width":150,"height":150,"mode":"crop"},{"name":"medium","width":300,"height":300,"mode":"fit"}]

# Security
FILEUPLOAD_MAX_FILE_SIZE=10485760
FILEUPLOAD_ALLOWED_EXTENSIONS=jpg,jpeg,png,gif,webp,pdf,doc,docx
```

## 📖 Documentation

- **[Complete Usage Guide](USAGE.md)** - Detailed examples and advanced features
- **[Migration Guide](docs/MIGRATION.md)** - Upgrading from older versions
- **[API Reference](docs/API.md)** - Complete method documentation

## 🖼️ Image Features

### Automatic Compression
```php
// Images are automatically compressed based on quality settings
$result = $uploadManager->upload($_FILES['image']);
// Original: 2.5MB → Compressed: 800KB (with quality=85)
```

### Dynamic Transformation
```php
// Generate transformation URLs
$originalPath = $result->getPath();

// Path-based URLs (requires .htaccess)
$thumbnailUrl = $uploadManager->getDynamicUrl($originalPath, 300, 200, 'crop');
// Result: /uploads/300x200/crop=image.jpg

// Query parameter URLs (universal compatibility)
$thumbnailUrl = $uploadManager->getDynamicUrl($originalPath, 300, 200, 'crop', true);
// Result: /transform.php?path=uploads/image.jpg&w=300&h=200&m=crop
```

### Thumbnail Generation
```php
// Automatic thumbnail generation based on configuration
$thumbnails = $result->getProcessedFiles();
// Returns: ['small' => [...], 'medium' => [...], 'large' => [...]]
```

## 🗂️ Storage Drivers

### Local Storage (Default)
```php
// Automatically configured - no setup required
$uploadManager = \Package\FileUpload\FileUploadManager::create();
```

### AWS S3 Storage
```php
// Configure S3 in .env
FILEUPLOAD_DEFAULT_DRIVER=s3
FILEUPLOAD_S3_KEY=your-access-key
FILEUPLOAD_S3_SECRET=your-secret-key
FILEUPLOAD_S3_REGION=us-east-1
FILEUPLOAD_S3_BUCKET=your-bucket-name
```

## 🛡️ Security Features

- **File Type Validation** - Whitelist/blacklist extensions
- **MIME Type Checking** - Verify actual file content
- **Size Limits** - Configurable maximum file sizes
- **Path Sanitization** - Prevent directory traversal
- **Virus Scanning** - Optional integration with antivirus

## 🔄 Transformation Modes

| Mode | Description | Use Case |
|------|-------------|----------|
| `crop` | Crop to exact dimensions | Profile pictures, thumbnails |
| `fit` | Fit within dimensions (maintain aspect ratio) | Gallery images |
| `fill` | Fill dimensions (may crop) | Background images |
| `stretch` | Stretch to exact dimensions | Icons, logos |

## 📁 Directory Structure

```
your-project/
├── uploads/                 # Default upload directory
├── config/
│   └── fileupload.php      # Optional configuration file
├── .env                    # Environment variables
└── transform.php           # Dynamic transformation endpoint
```

## 🧪 Testing

```bash
# Run the test suite
php test-fileupload.php

# Or use the debug tool
php debug-compression.php
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

This package is open-sourced software licensed under the [MIT license](LICENSE).

## 🆘 Support

- **Documentation**: [USAGE.md](USAGE.md)
- **Issues**: [GitHub Issues](https://github.com/your-repo/issues)
- **Discussions**: [GitHub Discussions](https://github.com/your-repo/discussions)

## 🏆 Credits

Developed with ❤️ for the PHP community.
