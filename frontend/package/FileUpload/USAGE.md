# FileUpload Package - Complete Usage Guide

This guide covers all features and advanced usage patterns of the FileUpload package.

## 📚 Table of Contents

1. [Installation & Setup](#installation--setup)
2. [Basic Usage](#basic-usage)
3. [Configuration](#configuration)
4. [Image Processing](#image-processing)
5. [Storage Drivers](#storage-drivers)
6. [Dynamic Transformations](#dynamic-transformations)
7. [Folder Management](#folder-management)
8. [Security & Validation](#security--validation)
9. [Advanced Examples](#advanced-examples)
10. [Troubleshooting](#troubleshooting)

## 🚀 Installation & Setup

### Method 1: Composer Installation

```bash
composer require your-vendor/fileupload
```

### Method 2: Manual Installation

```bash
# Copy package to your project
cp -r package/FileUpload /path/to/your/project/

# Include autoloader
require_once 'vendor/autoload.php';
```

### Method 3: Framework Integration

#### Laravel

```php
// Add to config/app.php providers array
'providers' => [
    // ...
    Package\FileUpload\Providers\FileUploadServiceProvider::class,
],
```

#### Symfony

```yaml
# config/services.yaml
services:
  Package\FileUpload\FileUploadManager:
    factory: ['Package\FileUpload\FileUploadManager', "create"]
```

## 🎯 Basic Usage

### Simple File Upload

```php
<?php
require_once 'vendor/autoload.php';

// Create upload manager (zero configuration)
$uploadManager = \Package\FileUpload\FileUploadManager::create();

// Upload file
$result = $uploadManager->upload($_FILES['file']);

if ($result->isSuccess()) {
    echo "✅ Upload successful!\n";
    echo "Path: " . $result->getPath() . "\n";
    echo "URL: " . $result->getUrl() . "\n";
    echo "Size: " . $result->getFileSize() . " bytes\n";
} else {
    echo "❌ Upload failed: " . $result->getMessage() . "\n";
    foreach ($result->getErrors() as $error) {
        echo "  - $error\n";
    }
}
?>
```

### Upload with Options

```php
$options = [
    'directory' => 'user-uploads/profile-pictures',
    'filename' => 'custom-name.jpg',
    'validation' => [
        'max_file_size' => 5242880, // 5MB
        'allowed_extensions' => ['jpg', 'jpeg', 'png'],
        'min_dimensions' => ['width' => 100, 'height' => 100],
        'max_dimensions' => ['width' => 2000, 'height' => 2000],
    ]
];

$result = $uploadManager->upload($_FILES['file'], $options);
```

### Multiple File Upload

```php
$results = $uploadManager->uploadMultiple($_FILES['files']);

foreach ($results as $index => $result) {
    if ($result->isSuccess()) {
        echo "File $index uploaded: " . $result->getPath() . "\n";
    } else {
        echo "File $index failed: " . $result->getMessage() . "\n";
    }
}
```

## ⚙️ Configuration

### Path Handling

The FileUpload package uses dynamic path resolution to ensure portability across different environments:

- **Relative paths** (recommended): `public/uploads/media`, `storage/uploads`
- **Absolute paths**: `/var/www/html/project/uploads`

The package automatically detects the best base path by testing:

1. Current working directory
2. Script directory
3. Document root (for web environments)
4. Fallback to temp directory

This ensures the package works consistently across different server configurations and deployment scenarios.

### Configuration Hierarchy

The package uses a **4-layer configuration system** with the following priority order:

1. **Runtime Parameters** (highest priority) - Method calls and constructor parameters
2. **Configuration File** - `config/fileupload.php`
3. **Environment Variables** - `.env` file
4. **Default Values** (lowest priority) - Built-in package defaults

### Environment Variables (.env)

```env
# === BASIC CONFIGURATION ===
FILEUPLOAD_PROCESSING_ENABLED=true
FILEUPLOAD_COMPRESS_IMAGES=true
FILEUPLOAD_IMAGE_QUALITY=85

# === STORAGE CONFIGURATION ===
FILEUPLOAD_DEFAULT_DRIVER=local
FILEUPLOAD_LOCAL_ROOT=public/uploads/media
FILEUPLOAD_LOCAL_URL=/uploads/media

# === THUMBNAIL CONFIGURATION ===
FILEUPLOAD_THUMBNAILS_ENABLED=true
FILEUPLOAD_THUMBNAILS_DIRECTORY=thumbnails
FILEUPLOAD_THUMBNAIL_QUALITY=80
FILEUPLOAD_THUMBNAIL_SIZES=[{"name":"small","width":150,"height":150,"mode":"crop"},{"name":"medium","width":300,"height":300,"mode":"fit"},{"name":"large","width":600,"height":600,"mode":"fit"}]

# === VALIDATION CONFIGURATION ===
FILEUPLOAD_MAX_FILE_SIZE=10485760
FILEUPLOAD_ALLOWED_EXTENSIONS=jpg,jpeg,png,gif,webp,pdf,doc,docx,txt
FILEUPLOAD_FORBIDDEN_EXTENSIONS=php,exe,bat,sh,js,html

# === SECURITY CONFIGURATION ===
FILEUPLOAD_SANITIZE_FILENAMES=true
FILEUPLOAD_GENERATE_UNIQUE_NAMES=true
FILEUPLOAD_VIRUS_SCAN_ENABLED=false
```

### Configuration File (config/fileupload.php)

```php
<?php
return [
    'default' => 'local',

    'drivers' => [
        'local' => [
            'driver' => 'local',
            'root' => env('FILEUPLOAD_LOCAL_ROOT', 'public/uploads/media'),
            'url' => env('FILEUPLOAD_LOCAL_URL', '/uploads'),
            'permissions' => [
                'file' => ['public' => 0644, 'private' => 0600],
                'dir' => ['public' => 0755, 'private' => 0700],
            ],
        ],

        's3' => [
            'driver' => 's3',
            'key' => env('FILEUPLOAD_S3_KEY'),
            'secret' => env('FILEUPLOAD_S3_SECRET'),
            'region' => env('FILEUPLOAD_S3_REGION'),
            'bucket' => env('FILEUPLOAD_S3_BUCKET'),
            'url' => env('FILEUPLOAD_S3_URL'),
        ],
    ],

    'processing' => [
        'enabled' => env('FILEUPLOAD_PROCESSING_ENABLED', true),
        'compress_images' => env('FILEUPLOAD_COMPRESS_IMAGES', true),
        'quality' => env('FILEUPLOAD_IMAGE_QUALITY', 85),
        'auto_orient' => env('FILEUPLOAD_AUTO_ORIENT', true),
        'strip_metadata' => env('FILEUPLOAD_STRIP_METADATA', true),
    ],

    'thumbnails' => [
        'enabled' => env('FILEUPLOAD_THUMBNAILS_ENABLED', true),
        'directory' => env('FILEUPLOAD_THUMBNAILS_DIRECTORY', 'thumbnails'),
        'quality' => env('FILEUPLOAD_THUMBNAIL_QUALITY', 80),
        'sizes' => json_decode(env('FILEUPLOAD_THUMBNAIL_SIZES', '[]'), true) ?: [
            'small' => ['width' => 150, 'height' => 150, 'mode' => 'crop'],
            'medium' => ['width' => 300, 'height' => 300, 'mode' => 'fit'],
            'large' => ['width' => 600, 'height' => 600, 'mode' => 'fit'],
        ],
    ],
];
```

## 🖼️ Image Processing

### Compression Control

```php
// Enable/disable compression
$_ENV['FILEUPLOAD_COMPRESS_IMAGES'] = 'true';  // Enable compression
$_ENV['FILEUPLOAD_IMAGE_QUALITY'] = '50';      // 50% quality (aggressive compression)

// Upload with compression
$result = $uploadManager->upload($_FILES['image']);
// Original: 2.5MB → Compressed: 600KB
```

### Quality Settings

```php
// Quality levels and their effects:
// 1-30:   High compression, smaller files, lower quality
// 31-70:  Medium compression, balanced size/quality
// 71-100: Low compression, larger files, higher quality

$_ENV['FILEUPLOAD_IMAGE_QUALITY'] = '85';  // High quality
$_ENV['FILEUPLOAD_IMAGE_QUALITY'] = '50';  // Medium quality
$_ENV['FILEUPLOAD_IMAGE_QUALITY'] = '20';  // High compression
```

### Thumbnail Generation

```php
// Configure thumbnail sizes
$thumbnailSizes = [
    ['name' => 'thumb', 'width' => 100, 'height' => 100, 'mode' => 'crop'],
    ['name' => 'small', 'width' => 300, 'height' => 200, 'mode' => 'fit'],
    ['name' => 'medium', 'width' => 600, 'height' => 400, 'mode' => 'fit'],
    ['name' => 'large', 'width' => 1200, 'height' => 800, 'mode' => 'fit'],
];

$_ENV['FILEUPLOAD_THUMBNAIL_SIZES'] = json_encode($thumbnailSizes);

// Upload and get thumbnails
$result = $uploadManager->upload($_FILES['image']);
$thumbnails = $result->getProcessedFiles();

foreach ($thumbnails as $size => $info) {
    echo "$size: " . $info['url'] . " (" . $info['width'] . "x" . $info['height'] . ")\n";
}
```

### Image Transformation Modes

```php
// crop: Crop to exact dimensions
$url = $uploadManager->getDynamicUrl($path, 300, 200, 'crop');

// fit: Fit within dimensions (maintain aspect ratio)
$url = $uploadManager->getDynamicUrl($path, 300, 200, 'fit');

// fill: Fill dimensions (may crop to maintain aspect ratio)
$url = $uploadManager->getDynamicUrl($path, 300, 200, 'fill');

// stretch: Stretch to exact dimensions (may distort)
$url = $uploadManager->getDynamicUrl($path, 300, 200, 'stretch');
```

## 💾 Storage Drivers

### Local Storage

```php
// Default configuration - no setup required
$uploadManager = \Package\FileUpload\FileUploadManager::create();
```

### AWS S3 Storage

```env
# S3 Configuration
FILEUPLOAD_DEFAULT_DRIVER=s3
FILEUPLOAD_S3_KEY=AKIAIOSFODNN7EXAMPLE
FILEUPLOAD_S3_SECRET=wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY
FILEUPLOAD_S3_REGION=us-east-1
FILEUPLOAD_S3_BUCKET=my-upload-bucket
FILEUPLOAD_S3_URL=https://my-upload-bucket.s3.amazonaws.com
```

### Custom Storage Driver

```php
class CustomStorageDriver implements \Package\FileUpload\Contracts\StorageDriverInterface
{
    public function store(string $path, $contents): bool
    {
        // Custom storage logic
        return true;
    }

    public function exists(string $path): bool
    {
        // Check if file exists
        return file_exists($path);
    }

    public function delete(string $path): bool
    {
        // Delete file
        return unlink($path);
    }

    public function url(string $path): string
    {
        // Return public URL
        return "https://cdn.example.com/$path";
    }
}

// Register custom driver
$uploadManager->addStorageDriver('custom', new CustomStorageDriver());
```

## 🔄 Dynamic Transformations

### Path-Based URLs (Recommended)

```php
// Requires .htaccess configuration
$originalPath = 'uploads/image.jpg';

// Generate transformation URLs
$thumbnail = $uploadManager->getDynamicUrl($originalPath, 300, 200, 'crop');
// Result: /uploads/300x200/crop=image.jpg

$medium = $uploadManager->getDynamicUrl($originalPath, 600, 400, 'fit');
// Result: /uploads/600x400/fit=image.jpg

// Clean URLs (default mode)
$clean = $uploadManager->getDynamicUrl($originalPath, 400, 300, 'fit', false, true);
// Result: /uploads/400x300/image.jpg
```

### Query Parameter URLs (Universal)

```php
// Works without .htaccess configuration
$queryUrl = $uploadManager->getDynamicUrl($originalPath, 300, 200, 'crop', true);
// Result: /transform.php?path=uploads/image.jpg&w=300&h=200&m=crop&q=85

// With custom quality
$highQuality = $uploadManager->getDynamicUrl($originalPath, 300, 200, 'crop', true, false, 95);
// Result: /transform.php?path=uploads/image.jpg&w=300&h=200&m=crop&q=95
```

### Responsive Images

```php
// Generate responsive image URLs
$responsiveUrls = $uploadManager->getResponsiveUrls($originalPath, 'fit', 85);

// Returns:
// [
//     'xs' => '/uploads/320x0/fit=image.jpg',    // Mobile portrait
//     'sm' => '/uploads/576x0/fit=image.jpg',    // Mobile landscape
//     'md' => '/uploads/768x0/fit=image.jpg',    // Tablet
//     'lg' => '/uploads/992x0/fit=image.jpg',    // Desktop
//     'xl' => '/uploads/1200x0/fit=image.jpg',   // Large desktop
//     'xxl' => '/uploads/1400x0/fit=image.jpg',  // Extra large
// ]

// Generate srcset attribute
$srcset = $uploadManager->getSrcSet($originalPath, 'fit', 85);
// Result: "/uploads/320x0/fit=image.jpg 320w, /uploads/576x0/fit=image.jpg 576w, ..."
```

### .htaccess Configuration

```apache
# Add to your .htaccess file for path-based URLs
RewriteEngine On

# Dynamic image transformation
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^uploads/(\d+)x(\d+)/(crop|fit|fill|stretch)=(.+)$ transform.php?path=uploads/$4&w=$1&h=$2&m=$3 [QSA,L]

# Clean URLs (default mode)
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^uploads/(\d+)x(\d+)/(.+)$ transform.php?path=uploads/$3&w=$1&h=$2&m=fit [QSA,L]
```

## 📁 Folder Management

### Create Folders

```php
// Create folder
$folderResult = $uploadManager->createFolder('user-documents/2024');

if ($folderResult->isSuccess()) {
    echo "Folder created: " . $folderResult->getPath();
} else {
    echo "Failed to create folder: " . $folderResult->getMessage();
}
```

### List Folder Contents

```php
// List files and folders
$contents = $uploadManager->listFolder('uploads');

foreach ($contents as $item) {
    if ($item['type'] === 'file') {
        echo "File: " . $item['name'] . " (" . $item['size'] . " bytes)\n";
    } else {
        echo "Folder: " . $item['name'] . "\n";
    }
}
```

### Delete Folders

```php
// Delete folder and all contents
$deleteResult = $uploadManager->deleteFolder('old-uploads');

if ($deleteResult->isSuccess()) {
    echo "Folder deleted successfully";
} else {
    echo "Failed to delete folder: " . $deleteResult->getMessage();
}
```

## 🛡️ Security & Validation

### File Type Validation

```php
$options = [
    'validation' => [
        // Allow only specific extensions
        'allowed_extensions' => ['jpg', 'jpeg', 'png', 'gif', 'webp'],

        // Forbidden extensions (security)
        'forbidden_extensions' => ['php', 'exe', 'bat', 'sh', 'js'],

        // MIME type validation
        'allowed_mime_types' => ['image/jpeg', 'image/png', 'image/gif'],

        // File size limits
        'max_file_size' => 10485760, // 10MB
        'min_file_size' => 1024,     // 1KB
    ]
];

$result = $uploadManager->upload($_FILES['file'], $options);
```

### Image Dimension Validation

```php
$options = [
    'validation' => [
        // Minimum dimensions
        'min_dimensions' => [
            'width' => 100,
            'height' => 100,
        ],

        // Maximum dimensions
        'max_dimensions' => [
            'width' => 4000,
            'height' => 4000,
        ],

        // Exact dimensions
        'exact_dimensions' => [
            'width' => 1920,
            'height' => 1080,
        ],

        // Aspect ratio validation
        'aspect_ratio' => '16:9', // or 1.777
    ]
];
```

## 🐛 Troubleshooting

### Common Issues

#### Permission Errors

```bash
# Fix upload directory permissions
chmod -R 755 uploads/
chown -R www-data:www-data uploads/

# Or make writable by all (less secure)
chmod -R 777 uploads/
```

#### Memory Limit Issues

```php
// Increase memory limit for large files
ini_set('memory_limit', '256M');
ini_set('upload_max_filesize', '50M');
ini_set('post_max_size', '50M');
```

#### GD Extension Missing

```bash
# Install GD extension
sudo apt-get install php-gd  # Ubuntu/Debian
sudo yum install php-gd      # CentOS/RHEL

# Or enable in php.ini
extension=gd
```

### Debug Mode

```php
// Enable debug mode
$_ENV['FILEUPLOAD_DEBUG'] = 'true';

// Check system status
$status = $uploadManager->healthCheck();
foreach ($status as $check => $result) {
    echo "$check: " . ($result['status'] ? '✅' : '❌') . " " . $result['message'] . "\n";
}
```

### Performance Optimization

```php
// Disable thumbnail generation for better performance
$_ENV['FILEUPLOAD_THUMBNAILS_ENABLED'] = 'false';

// Reduce image quality for smaller files
$_ENV['FILEUPLOAD_IMAGE_QUALITY'] = '70';

// Use progressive JPEG for faster loading
$_ENV['FILEUPLOAD_PROGRESSIVE_JPEG'] = 'true';
```

## 📞 Support

- **Documentation**: Complete guides and examples
- **GitHub Issues**: Bug reports and feature requests
- **Community**: Join our Discord server
- **Professional Support**: Available for enterprise users

---

For more examples and advanced usage patterns, check the `/examples` directory in the package.
