<?php

namespace Package\FileUpload\Contracts;

interface StorageDriverInterface
{
    /**
     * Store a file and return its path/URL
     *
     * @param string $path The path where the file should be stored
     * @param resource|string $contents The file contents or resource
     * @param array $options Additional options for storage
     * @return string The stored file path/URL
     * @throws \Package\FileUpload\Exceptions\StorageException
     */
    public function store(string $path, $contents, array $options = []): string;

    /**
     * Retrieve file contents
     *
     * @param string $path The file path
     * @return string The file contents
     * @throws \Package\FileUpload\Exceptions\StorageException
     */
    public function get(string $path): string;

    /**
     * Check if a file exists
     *
     * @param string $path The file path
     * @return bool
     */
    public function exists(string $path): bool;

    /**
     * Delete a file
     *
     * @param string $path The file path
     * @return bool
     * @throws \Package\FileUpload\Exceptions\StorageException
     */
    public function delete(string $path): bool;

    /**
     * Get the public URL for a file
     *
     * @param string $path The file path
     * @return string The public URL
     */
    public function url(string $path): string;

    /**
     * Get file metadata
     *
     * @param string $path The file path
     * @return array File metadata (size, mime_type, last_modified, etc.)
     * @throws \Package\FileUpload\Exceptions\StorageException
     */
    public function metadata(string $path): array;

    /**
     * Copy a file to a new location
     *
     * @param string $from Source path
     * @param string $to Destination path
     * @return bool
     * @throws \Package\FileUpload\Exceptions\StorageException
     */
    public function copy(string $from, string $to): bool;

    /**
     * Move a file to a new location
     *
     * @param string $from Source path
     * @param string $to Destination path
     * @return bool
     * @throws \Package\FileUpload\Exceptions\StorageException
     */
    public function move(string $from, string $to): bool;

    /**
     * Get the driver name
     *
     * @return string
     */
    public function getName(): string;

    /**
     * Set driver configuration
     *
     * @param array $config
     * @return void
     */
    public function setConfig(array $config): void;

    /**
     * Create a directory/folder
     *
     * @param string $path The directory path to create
     * @param array $options Additional options for directory creation
     * @return bool
     * @throws \Package\FileUpload\Exceptions\StorageException
     */
    public function createDirectory(string $path, array $options = []): bool;

    /**
     * Check if a directory exists
     *
     * @param string $path The directory path
     * @return bool
     */
    public function directoryExists(string $path): bool;

    /**
     * Delete a directory (and optionally its contents)
     *
     * @param string $path The directory path
     * @param bool $recursive Whether to delete contents recursively
     * @return bool
     * @throws \Package\FileUpload\Exceptions\StorageException
     */
    public function deleteDirectory(string $path, bool $recursive = false): bool;

    /**
     * List contents of a directory
     *
     * @param string $path The directory path
     * @param bool $recursive Whether to list recursively
     * @return array Array of file/directory information
     * @throws \Package\FileUpload\Exceptions\StorageException
     */
    public function listDirectory(string $path, bool $recursive = false): array;
}
