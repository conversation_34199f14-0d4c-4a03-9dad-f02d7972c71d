<?php

namespace Package\FileUpload\Contracts;

interface FileUploadInterface
{
    /**
     * Upload a file
     *
     * @param array $file The $_FILES array element
     * @param array $options Upload options
     * @return \Package\FileUpload\UploadResult
     */
    public function upload(array $file, array $options = []): \Package\FileUpload\UploadResult;

    /**
     * Upload multiple files
     *
     * @param array $files Array of $_FILES elements
     * @param array $options Upload options
     * @return array Array of UploadResult objects
     */
    public function uploadMultiple(array $files, array $options = []): array;

    /**
     * Delete an uploaded file
     *
     * @param string $path File path
     * @return bool
     */
    public function delete(string $path): bool;

    /**
     * Get file information
     *
     * @param string $path File path
     * @return array File information
     */
    public function getFileInfo(string $path): array;

    /**
     * Set storage driver
     *
     * @param \Package\FileUpload\Contracts\StorageDriverInterface $driver
     * @return void
     */
    public function setStorageDriver(StorageDriverInterface $driver): void;

    /**
     * Set validator
     *
     * @param \Package\FileUpload\Contracts\ValidatorInterface $validator
     * @return void
     */
    public function setValidator(ValidatorInterface $validator): void;

    /**
     * Add processor
     *
     * @param \Package\FileUpload\Contracts\ProcessorInterface $processor
     * @return void
     */
    public function addProcessor(ProcessorInterface $processor): void;

    /**
     * Set configuration
     *
     * @param array $config
     * @return void
     */
    public function setConfig(array $config): void;
}
