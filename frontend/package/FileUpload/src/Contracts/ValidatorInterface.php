<?php

namespace Package\FileUpload\Contracts;

interface ValidatorInterface
{
    /**
     * Validate an uploaded file
     *
     * @param array $file The $_FILES array element
     * @param array $rules Validation rules
     * @return \Package\FileUpload\Validation\ValidationResult
     */
    public function validate(array $file, array $rules = []): \Package\FileUpload\Validation\ValidationResult;

    /**
     * Add a custom validation rule
     *
     * @param string $name Rule name
     * @param callable $callback Validation callback
     * @return void
     */
    public function addRule(string $name, callable $callback): void;

    /**
     * Get available validation rules
     *
     * @return array
     */
    public function getAvailableRules(): array;

    /**
     * Set default validation rules
     *
     * @param array $rules
     * @return void
     */
    public function setDefaultRules(array $rules): void;

    /**
     * Get default validation rules
     *
     * @return array
     */
    public function getDefaultRules(): array;
}
