<?php

namespace Package\FileUpload\Contracts;

interface ProcessorInterface
{
    /**
     * Process an uploaded file
     *
     * @param string $filePath Path to the uploaded file
     * @param array $options Processing options
     * @return \Package\FileUpload\Processing\ProcessingResult
     */
    public function process(string $filePath, array $options = []): \Package\FileUpload\Processing\ProcessingResult;

    /**
     * Check if the processor can handle the given file type
     *
     * @param string $mimeType File MIME type
     * @return bool
     */
    public function canProcess(string $mimeType): bool;

    /**
     * Get supported MIME types
     *
     * @return array
     */
    public function getSupportedMimeTypes(): array;

    /**
     * Get processor name
     *
     * @return string
     */
    public function getName(): string;

    /**
     * Set processor configuration
     *
     * @param array $config
     * @return void
     */
    public function setConfig(array $config): void;
}
