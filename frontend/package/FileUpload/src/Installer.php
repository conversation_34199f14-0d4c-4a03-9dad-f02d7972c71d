<?php

namespace Package\FileUpload;

use Composer\Script\Event;

/**
 * Package Installer
 * 
 * Handles automatic installation tasks when the package is installed via Composer.
 * This includes copying necessary files to the frontend directory.
 */
class Installer
{
    /**
     * Copy transform.php file to the project root directory
     *
     * This method is called automatically by Composer after package installation/update.
     * It ensures that the transform.php file is available in the project root directory
     * for dynamic URL routing purposes. The file will work from any directory structure.
     *
     * @param Event $event Composer event
     */
    public static function copyTransformFile(Event $event): void
    {
        $vendorDir = $event->getComposer()->getConfig()->get('vendor-dir');
        $packageDir = dirname($vendorDir);

        // Source file in the package
        $sourceFile = $vendorDir . '/package/file-upload/public/transform.php';

        // Destination file in the frontend
        $destinationFile = $packageDir . '/transform.php';

        // Only copy if source exists and destination doesn't exist or is older
        if (file_exists($sourceFile)) {
            $shouldCopy = !file_exists($destinationFile) ||
                filemtime($sourceFile) > filemtime($destinationFile);

            if ($shouldCopy) {
                if (copy($sourceFile, $destinationFile)) {
                    $event->getIO()->write('✅ FileUpload: transform.php copied to frontend directory');
                } else {
                    $event->getIO()->writeError('❌ FileUpload: Failed to copy transform.php');
                }
            } else {
                $event->getIO()->write('ℹ️  FileUpload: transform.php is already up to date');
            }
        } else {
            $event->getIO()->writeError('❌ FileUpload: Source transform.php not found');
        }
    }

    /**
     * Create necessary directories and files for the package
     * 
     * @param Event $event Composer event
     */
    public static function setupPackage(Event $event): void
    {
        $vendorDir = $event->getComposer()->getConfig()->get('vendor-dir');
        $packageDir = dirname($vendorDir);

        // Create uploads directory if it doesn't exist
        $uploadsDir = $packageDir . '/uploads';
        if (!is_dir($uploadsDir)) {
            if (mkdir($uploadsDir, 0755, true)) {
                $event->getIO()->write('✅ FileUpload: Created uploads directory');
            } else {
                $event->getIO()->writeError('❌ FileUpload: Failed to create uploads directory');
            }
        }

        // Create .htaccess file if it doesn't exist
        $htaccessFile = $packageDir . '/.htaccess';
        if (!file_exists($htaccessFile)) {
            $htaccessContent = self::getDefaultHtaccessContent();
            if (file_put_contents($htaccessFile, $htaccessContent)) {
                $event->getIO()->write('✅ FileUpload: Created .htaccess file with image transformation rules');
            } else {
                $event->getIO()->writeError('❌ FileUpload: Failed to create .htaccess file');
            }
        }
    }

    /**
     * Get default .htaccess content for image transformation
     * 
     * @return string Default .htaccess content
     */
    private static function getDefaultHtaccessContent(): string
    {
        return <<<'HTACCESS'
RewriteEngine On

# Dynamic image transformation - Universal N-level nested directory support
# Handle ANY number of nested directories with intelligent rules
# Supports both explicit mode and default mode (fit):
# - /uploads/P/h/P/300x300/fit=file.png (explicit mode)
# - /uploads/P/h/P/300x300/file.png (default to fit mode)

# Rule 1: Handle unlimited nested levels with explicit mode (1+ directories)
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^uploads/(.+)/(\d+)x(\d+)/([a-z]+)=(.+)$ transform.php?path=uploads/$1/$5&width=$2&height=$3&mode=$4 [L,QSA]

# Rule 2: Handle unlimited nested levels with default mode (1+ directories)
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^uploads/(.+)/(\d+)x(\d+)/(.+)$ transform.php?path=uploads/$1/$4&width=$2&height=$3&mode=fit [L,QSA]

# Rule 3: Handle simple uploads format with explicit mode (0 levels - direct in uploads)
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^uploads/(\d+)x(\d+)/([a-z]+)=(.+)$ transform.php?path=uploads/$4&width=$1&height=$2&mode=$3 [L,QSA]

# Rule 4: Handle simple uploads format with default mode (0 levels - direct in uploads)
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^uploads/(\d+)x(\d+)/(.+)$ transform.php?path=uploads/$3&width=$1&height=$2&mode=fit [L,QSA]

# Serve static files directly
<IfModule mod_rewrite.c>
    RewriteCond %{REQUEST_FILENAME} -f
    RewriteRule ^(.*)$ $1 [L]
</IfModule>
HTACCESS;
    }
}
