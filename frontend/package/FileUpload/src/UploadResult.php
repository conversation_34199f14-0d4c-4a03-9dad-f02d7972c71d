<?php

namespace Package\FileUpload;

class UploadResult
{
    protected bool $success;
    protected ?string $path;
    protected ?string $url;
    protected ?string $message;
    protected array $metadata;
    protected array $errors;
    protected array $processedFiles;

    public function __construct(
        bool $success = false,
        ?string $path = null,
        ?string $url = null,
        ?string $message = null,
        array $metadata = [],
        array $errors = [],
        array $processedFiles = []
    ) {
        $this->success = $success;
        $this->path = $path;
        $this->url = $url;
        $this->message = $message;
        $this->metadata = $metadata;
        $this->errors = $errors;
        $this->processedFiles = $processedFiles;
    }

    public function isSuccess(): bool
    {
        return $this->success;
    }

    public function getPath(): ?string
    {
        return $this->path;
    }

    public function getUrl(): ?string
    {
        return $this->url;
    }

    public function getMessage(): ?string
    {
        return $this->message;
    }

    public function getMetadata(): array
    {
        return $this->metadata;
    }

    public function getErrors(): array
    {
        return $this->errors;
    }

    public function hasErrors(): bool
    {
        return !empty($this->errors);
    }

    public function getProcessedFiles(): array
    {
        return $this->processedFiles;
    }

    public function toArray(): array
    {
        return [
            'success' => $this->success,
            'path' => $this->path,
            'url' => $this->url,
            'message' => $this->message,
            'metadata' => $this->metadata,
            'errors' => $this->errors,
            'processed_files' => $this->processedFiles,
        ];
    }

    public static function success(
        string $path,
        string $url,
        string $message = 'File uploaded successfully',
        array $metadata = [],
        array $processedFiles = []
    ): self {
        return new self(true, $path, $url, $message, $metadata, [], $processedFiles);
    }

    public static function failure(string $message, array $errors = []): self
    {
        return new self(false, null, null, $message, [], $errors);
    }
}
