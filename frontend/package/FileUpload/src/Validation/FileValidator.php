<?php

namespace Package\FileUpload\Validation;

use Package\FileUpload\Contracts\ValidatorInterface;
use Package\FileUpload\Config\ConfigManager;

class FileValidator implements ValidatorInterface
{
    protected array $rules = [];
    protected array $defaultRules = [];
    protected array $customRules = [];

    public function __construct(array $rules = [])
    {
        $this->loadDefaultRules();
        $this->setDefaultRules($rules);
    }

    public function validate(array $file, array $rules = []): ValidationResult
    {
        $rulesToApply = array_merge($this->defaultRules, $rules);
        $result = new ValidationResult();

        // Check for upload errors first
        if ($file['error'] !== UPLOAD_ERR_OK) {
            $result->addError($this->getUploadErrorMessage($file['error']));
            return $result;
        }

        // Apply validation rules
        foreach ($rulesToApply as $ruleName => $ruleValue) {
            $this->applyRule($file, $ruleName, $ruleValue, $result);
        }

        // Collect file metadata
        $metadata = $this->collectMetadata($file);
        $result->setMetadata($metadata);

        return $result;
    }

    public function addRule(string $name, callable $callback): void
    {
        $this->customRules[$name] = $callback;
    }

    public function getAvailableRules(): array
    {
        return array_merge(
            array_keys($this->rules),
            array_keys($this->customRules)
        );
    }

    public function setDefaultRules(array $rules): void
    {
        $this->defaultRules = array_merge($this->defaultRules, $rules);
    }

    public function getDefaultRules(): array
    {
        return $this->defaultRules;
    }

    protected function loadDefaultRules(): void
    {
        $config = ConfigManager::getInstance();
        $validationConfig = $config->getValidationConfig();

        $this->defaultRules = [
            'max_file_size' => $validationConfig['max_file_size'] ?? 10485760,
            'allowed_mime_types' => $validationConfig['allowed_mime_types'] ?? ['image/jpeg', 'image/png'],
            'allowed_extensions' => $validationConfig['allowed_extensions'] ?? ['jpg', 'jpeg', 'png'],
            'forbidden_extensions' => $validationConfig['forbidden_extensions'] ?? ['php', 'exe'],
            'check_file_headers' => $validationConfig['check_file_headers'] ?? true,
            'scan_for_viruses' => $validationConfig['scan_for_viruses'] ?? false,
            'check_image_dimensions' => $validationConfig['check_image_dimensions'] ?? false,
            'max_width' => $validationConfig['max_width'] ?? 4096,
            'max_height' => $validationConfig['max_height'] ?? 4096,
            'min_width' => $validationConfig['min_width'] ?? 1,
            'min_height' => $validationConfig['min_height'] ?? 1,
        ];

        $this->rules = [
            'max_file_size' => [$this, 'validateFileSize'],
            'allowed_mime_types' => [$this, 'validateMimeType'],
            'allowed_extensions' => [$this, 'validateExtension'],
            'forbidden_extensions' => [$this, 'validateForbiddenExtensions'],
            'check_file_headers' => [$this, 'validateFileHeaders'],
            'scan_for_viruses' => [$this, 'validateVirusScan'],
            'check_image_dimensions' => [$this, 'validateImageDimensions'],
        ];
    }

    protected function applyRule(array $file, string $ruleName, $ruleValue, ValidationResult $result): void
    {
        if (isset($this->customRules[$ruleName])) {
            $callback = $this->customRules[$ruleName];
            $callback($file, $ruleValue, $result);
            return;
        }

        if (isset($this->rules[$ruleName])) {
            $callback = $this->rules[$ruleName];
            $callback($file, $ruleValue, $result);
            return;
        }
    }

    protected function validateFileSize(array $file, int $maxSize, ValidationResult $result): void
    {
        if ($file['size'] > $maxSize) {
            $maxSizeMB = round($maxSize / 1024 / 1024, 2);
            $result->addError("File size exceeds the maximum allowed size of {$maxSizeMB}MB");
        }
    }

    protected function validateMimeType(array $file, ?array $allowedTypes, ValidationResult $result): void
    {
        if (empty($allowedTypes) || $allowedTypes === null) {
            return;
        }

        $mimeType = $this->getMimeType($file['tmp_name']);

        if (!in_array($mimeType, $allowedTypes)) {
            $result->addError("File type '{$mimeType}' is not allowed. Allowed types: " . implode(', ', $allowedTypes));
        }
    }

    protected function validateExtension(array $file, ?array $allowedExtensions, ValidationResult $result): void
    {
        if (empty($allowedExtensions) || $allowedExtensions === null) {
            return;
        }

        $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));

        if (!in_array($extension, array_map('strtolower', $allowedExtensions))) {
            $result->addError("File extension '{$extension}' is not allowed. Allowed extensions: " . implode(', ', $allowedExtensions));
        }
    }

    protected function validateForbiddenExtensions(array $file, ?array $forbiddenExtensions, ValidationResult $result): void
    {
        if (empty($forbiddenExtensions) || $forbiddenExtensions === null) {
            return;
        }

        $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));

        if (in_array($extension, array_map('strtolower', $forbiddenExtensions))) {
            $result->addError("File extension '{$extension}' is forbidden for security reasons");
        }
    }

    protected function validateFileHeaders(array $file, bool $checkHeaders, ValidationResult $result): void
    {
        if (!$checkHeaders) {
            return;
        }

        $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        $mimeType = $this->getMimeType($file['tmp_name']);

        // Check if file extension matches MIME type
        $expectedMimeTypes = [
            'jpg' => ['image/jpeg'],
            'jpeg' => ['image/jpeg'],
            'png' => ['image/png'],
            'gif' => ['image/gif'],
            'webp' => ['image/webp'],
            'svg' => ['image/svg+xml', 'text/xml', 'application/xml'],
            'pdf' => ['application/pdf'],
            'txt' => ['text/plain'],
            'doc' => ['application/msword'],
            'docx' => ['application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
            'xls' => ['application/vnd.ms-excel'],
            'xlsx' => ['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'],
            'ppt' => ['application/vnd.ms-powerpoint'],
            'pptx' => ['application/vnd.openxmlformats-officedocument.presentationml.presentation'],
        ];

        if (isset($expectedMimeTypes[$extension])) {
            if (!in_array($mimeType, $expectedMimeTypes[$extension])) {
                $result->addError("File extension '{$extension}' does not match the file content type '{$mimeType}'");
            }
        }
    }

    protected function validateVirusScan(array $file, bool $scanEnabled, ValidationResult $result): void
    {
        if (!$scanEnabled) {
            return;
        }

        // This is a placeholder for virus scanning
        // In a real implementation, you would integrate with ClamAV or similar
        $result->addWarning("Virus scanning is enabled but not implemented in this version");
    }

    protected function validateImageDimensions(array $file, bool $checkDimensions, ValidationResult $result): void
    {
        if (!$checkDimensions) {
            return;
        }

        $mimeType = $this->getMimeType($file['tmp_name']);

        if (strpos($mimeType, 'image/') !== 0) {
            return; // Not an image
        }

        // Handle SVG files separately as getimagesize() doesn't work with them
        $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        if ($extension === 'svg' || $mimeType === 'image/svg+xml') {
            // SVG files are vector-based and don't have fixed dimensions
            // We'll skip dimension validation for SVG files
            return;
        }

        $imageInfo = getimagesize($file['tmp_name']);

        if ($imageInfo === false) {
            $result->addError("Unable to determine image dimensions");
            return;
        }

        $width = $imageInfo[0];
        $height = $imageInfo[1];

        $maxWidth = $this->defaultRules['max_width'] ?? 4096;
        $maxHeight = $this->defaultRules['max_height'] ?? 4096;
        $minWidth = $this->defaultRules['min_width'] ?? 1;
        $minHeight = $this->defaultRules['min_height'] ?? 1;

        if ($width > $maxWidth || $height > $maxHeight) {
            $result->addError("Image dimensions ({$width}x{$height}) exceed maximum allowed size ({$maxWidth}x{$maxHeight})");
        }

        if ($width < $minWidth || $height < $minHeight) {
            $result->addError("Image dimensions ({$width}x{$height}) are below minimum required size ({$minWidth}x{$minHeight})");
        }
    }

    protected function getMimeType(string $filePath): string
    {
        if (function_exists('finfo_file')) {
            $finfo = finfo_open(FILEINFO_MIME_TYPE);
            $mimeType = finfo_file($finfo, $filePath);
            finfo_close($finfo);

            if ($mimeType !== false) {
                return $mimeType;
            }
        }

        return 'application/octet-stream';
    }

    protected function collectMetadata(array $file): array
    {
        $metadata = [
            'original_name' => $file['name'],
            'size' => $file['size'],
            'mime_type' => $this->getMimeType($file['tmp_name']),
            'extension' => strtolower(pathinfo($file['name'], PATHINFO_EXTENSION)),
        ];

        // Add image-specific metadata (skip SVG files as getimagesize doesn't work with them)
        if (strpos($metadata['mime_type'], 'image/') === 0 && $metadata['extension'] !== 'svg' && $metadata['mime_type'] !== 'image/svg+xml') {
            $imageInfo = getimagesize($file['tmp_name']);
            if ($imageInfo !== false) {
                $metadata['width'] = $imageInfo[0];
                $metadata['height'] = $imageInfo[1];
                $metadata['image_type'] = $imageInfo[2];
                $metadata['bits'] = $imageInfo['bits'] ?? null;
                $metadata['channels'] = $imageInfo['channels'] ?? null;
            }
        } elseif ($metadata['extension'] === 'svg' || $metadata['mime_type'] === 'image/svg+xml') {
            // For SVG files, we can't get dimensions easily, so we'll set default values
            $metadata['width'] = null;
            $metadata['height'] = null;
            $metadata['image_type'] = 'svg';
            $metadata['is_vector'] = true;
        }

        return $metadata;
    }

    protected function getUploadErrorMessage(int $errorCode): string
    {
        $messages = [
            UPLOAD_ERR_INI_SIZE => 'The uploaded file exceeds the upload_max_filesize directive in php.ini',
            UPLOAD_ERR_FORM_SIZE => 'The uploaded file exceeds the MAX_FILE_SIZE directive in the HTML form',
            UPLOAD_ERR_PARTIAL => 'The uploaded file was only partially uploaded',
            UPLOAD_ERR_NO_FILE => 'No file was uploaded',
            UPLOAD_ERR_NO_TMP_DIR => 'Missing a temporary folder',
            UPLOAD_ERR_CANT_WRITE => 'Failed to write file to disk',
            UPLOAD_ERR_EXTENSION => 'A PHP extension stopped the file upload',
        ];

        return $messages[$errorCode] ?? 'Unknown upload error';
    }
}
