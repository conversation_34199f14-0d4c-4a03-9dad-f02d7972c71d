<?php

namespace Package\FileUpload\Config;

class ConfigManager
{
    protected array $config = [];
    protected array $runtimeConfig = [];
    protected static ?self $instance = null;

    private function __construct()
    {
        $this->loadConfig();
    }

    public static function getInstance(): self
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }

        return self::$instance;
    }

    protected function loadConfig(): void
    {
        // Look for config file in multiple possible locations
        $possiblePaths = [
            // Current working directory + config
            getcwd() . '/config/fileupload.php',
            // Package relative path (for development)
            __DIR__ . '/../../../../config/fileupload.php',
            // Composer vendor path
            __DIR__ . '/../../../../../config/fileupload.php',
            // Laravel-style config path
            dirname($_SERVER['SCRIPT_FILENAME']) . '/config/fileupload.php',
        ];

        foreach ($possiblePaths as $configPath) {
            if (file_exists($configPath)) {
                $this->config = require_once $configPath;
                return;
            }
        }

        // If no config file found, use defaults
        $this->config = $this->getDefaultConfig();
    }

    public function get(string $key, $default = null)
    {
        // 1. Check runtime configuration first (highest priority)
        $runtimeValue = $this->getNestedValue($this->runtimeConfig, $key);
        if ($runtimeValue !== null) {
            return $runtimeValue;
        }

        // 2. Check config file (second priority)
        $configValue = $this->getNestedValue($this->config, $key);
        if ($configValue !== null) {
            return $configValue;
        }

        // 3. Check environment variables (third priority)
        $envValue = $this->getEnvironmentValue($key);
        if ($envValue !== null) {
            return $envValue;
        }

        // 4. Return default value (lowest priority)
        return $default;
    }

    public function set(string $key, $value): void
    {
        $this->setNestedValue($this->config, $key, $value);
    }

    public function has(string $key): bool
    {
        return $this->getNestedValue($this->config, $key) !== null;
    }

    public function all(): array
    {
        return $this->config;
    }

    public function merge(array $config): void
    {
        $this->config = array_merge_recursive($this->config, $config);
    }

    /**
     * Set runtime configuration (highest priority)
     */
    public function setRuntime(string $key, $value): void
    {
        $this->setNestedValue($this->runtimeConfig, $key, $value);
    }

    /**
     * Merge runtime configuration
     */
    public function mergeRuntime(array $config): void
    {
        $this->runtimeConfig = array_merge_recursive($this->runtimeConfig, $config);
    }

    /**
     * Clear runtime configuration
     */
    public function clearRuntime(): void
    {
        $this->runtimeConfig = [];
    }

    /**
     * Get all runtime configuration
     */
    public function getRuntime(): array
    {
        return $this->runtimeConfig;
    }

    public function getDriverConfig(string $driver): array
    {
        return $this->get("drivers.{$driver}", []);
    }

    public function getValidationConfig(): array
    {
        return $this->get('validation', []);
    }

    public function getProcessingConfig(): array
    {
        return $this->get('processing', []);
    }

    public function getSecurityConfig(): array
    {
        return $this->get('security', []);
    }

    protected function getNestedValue(array $array, string $key, $default = null)
    {
        if (strpos($key, '.') === false) {
            return $array[$key] ?? $default;
        }

        $keys = explode('.', $key);
        $value = $array;

        foreach ($keys as $segment) {
            if (!is_array($value) || !array_key_exists($segment, $value)) {
                return $default;
            }
            $value = $value[$segment];
        }

        return $value;
    }

    protected function setNestedValue(array &$array, string $key, $value): void
    {
        if (strpos($key, '.') === false) {
            $array[$key] = $value;
            return;
        }

        $keys = explode('.', $key);
        $current = &$array;

        foreach ($keys as $segment) {
            if (!isset($current[$segment]) || !is_array($current[$segment])) {
                $current[$segment] = [];
            }
            $current = &$current[$segment];
        }

        $current = $value;
    }

    /**
     * Get value from environment variables with proper mapping
     */
    protected function getEnvironmentValue(string $key)
    {
        // Map config keys to environment variable names
        $envMapping = [
            'default' => 'FILEUPLOAD_DRIVER',
            'drivers.local.root' => 'FILEUPLOAD_LOCAL_ROOT',
            'drivers.local.url' => 'FILEUPLOAD_LOCAL_URL',
            'validation.max_file_size' => 'FILEUPLOAD_MAX_SIZE',
            'validation.allowed_mime_types' => 'FILEUPLOAD_ALLOWED_TYPES',
            'validation.allowed_extensions' => 'FILEUPLOAD_ALLOWED_EXTENSIONS',
            'validation.forbidden_extensions' => 'FILEUPLOAD_FORBIDDEN_EXTENSIONS',
            'processing.enabled' => 'FILEUPLOAD_PROCESSING_ENABLED',
            'processing.compress_images' => 'FILEUPLOAD_COMPRESS_IMAGES',
            'processing.quality' => 'FILEUPLOAD_IMAGE_QUALITY',
            'processing.thumbnails.enabled' => 'FILEUPLOAD_THUMBNAILS_ENABLED',
            'processing.thumbnails.directory' => 'FILEUPLOAD_THUMBNAILS_DIRECTORY',
            'processing.thumbnails.quality' => 'FILEUPLOAD_THUMBNAIL_QUALITY',
            'processing.thumbnails.sizes' => 'FILEUPLOAD_THUMBNAIL_SIZES',
        ];

        if (isset($envMapping[$key])) {
            $envKey = $envMapping[$key];
            $envValue = $_ENV[$envKey] ?? null;

            if ($envValue !== null) {
                return $this->parseEnvironmentValue($envKey, $envValue);
            }
        }

        return null;
    }

    /**
     * Parse environment value based on its type
     */
    protected function parseEnvironmentValue(string $envKey, string $envValue)
    {
        // Handle boolean values
        if (in_array($envKey, ['FILEUPLOAD_PROCESSING_ENABLED', 'FILEUPLOAD_COMPRESS_IMAGES', 'FILEUPLOAD_THUMBNAILS_ENABLED'])) {
            return filter_var($envValue, FILTER_VALIDATE_BOOLEAN);
        }

        // Handle integer values
        if (in_array($envKey, ['FILEUPLOAD_MAX_SIZE', 'FILEUPLOAD_IMAGE_QUALITY', 'FILEUPLOAD_THUMBNAIL_QUALITY'])) {
            return (int) $envValue;
        }

        // Handle array values (comma-separated)
        if (in_array($envKey, ['FILEUPLOAD_ALLOWED_TYPES', 'FILEUPLOAD_ALLOWED_EXTENSIONS', 'FILEUPLOAD_FORBIDDEN_EXTENSIONS'])) {
            return array_map('trim', explode(',', $envValue));
        }

        // Handle JSON values
        if ($envKey === 'FILEUPLOAD_THUMBNAIL_SIZES') {
            $decoded = json_decode($envValue, true);
            if (json_last_error() === JSON_ERROR_NONE && is_array($decoded)) {
                $sizes = [];
                foreach ($decoded as $size) {
                    if (isset($size['name'], $size['width'], $size['height'])) {
                        $sizes[$size['name']] = [
                            'width' => (int)$size['width'],
                            'height' => (int)$size['height'],
                            'mode' => $size['mode'] ?? 'crop'
                        ];
                    }
                }
                return $sizes;
            }
        }

        // Handle path values (make them dynamic)
        if ($envKey === 'FILEUPLOAD_LOCAL_ROOT') {
            return $this->resolveDynamicPath($envValue);
        }

        // Return as string for other values
        return $envValue;
    }

    /**
     * Resolve dynamic path from relative or absolute path
     */
    protected function resolveDynamicPath(string $path): string
    {
        // If already absolute, return as-is
        if (str_starts_with($path, '/')) {
            return $path;
        }

        // Try different base paths for relative paths
        $possibleBases = [
            getcwd() . '/',
            dirname($_SERVER['SCRIPT_FILENAME'] ?? __FILE__) . '/',
            $_SERVER['DOCUMENT_ROOT'] ?? getcwd() . '/',
        ];

        foreach ($possibleBases as $base) {
            $fullPath = $base . $path;
            $dir = dirname($fullPath);
            if (is_dir($dir) && is_writable($dir)) {
                return $fullPath;
            }
        }

        // Fallback to current working directory
        return getcwd() . '/' . $path;
    }

    protected function getDefaultConfig(): array
    {
        // Determine dynamic upload path based on current working directory
        $uploadsPath = $this->getDefaultUploadsPath();
        $uploadsUrl = $this->getDefaultUploadsUrl();

        return [
            'default' => 'local',
            'drivers' => [
                'local' => [
                    'driver' => 'local',
                    'root' => $uploadsPath,
                    'url' => $uploadsUrl,
                    'permissions' => [
                        'file' => ['public' => 0644, 'private' => 0600],
                        'dir' => ['public' => 0755, 'private' => 0700],
                    ],
                ],
            ],
            'validation' => [
                'max_file_size' => 10485760, // 10MB
                'allowed_mime_types' => ['image/jpeg', 'image/png', 'image/gif'],
                'allowed_extensions' => ['jpg', 'jpeg', 'png', 'gif'],
                'forbidden_extensions' => ['php', 'exe', 'bat', 'sh'],
            ],
            'processing' => [
                'enabled' => filter_var($_ENV['FILEUPLOAD_PROCESSING_ENABLED'] ?? true, FILTER_VALIDATE_BOOLEAN),
                'driver' => $_ENV['FILEUPLOAD_PROCESSING_DRIVER'] ?? 'gd',
                'compress_images' => filter_var($_ENV['FILEUPLOAD_COMPRESS_IMAGES'] ?? true, FILTER_VALIDATE_BOOLEAN),
                'quality' => (int)($_ENV['FILEUPLOAD_IMAGE_QUALITY'] ?? 85),
                'thumbnails' => [
                    'enabled' => filter_var($_ENV['FILEUPLOAD_THUMBNAILS_ENABLED'] ?? false, FILTER_VALIDATE_BOOLEAN),
                    'directory' => $_ENV['FILEUPLOAD_THUMBNAILS_DIRECTORY'] ?? 'thumbnails',
                    'quality' => (int)($_ENV['FILEUPLOAD_THUMBNAIL_QUALITY'] ?? 80),
                    'sizes' => $this->getThumbnailSizesFromEnv(),
                ],
            ],
            'security' => [
                'scan_uploads' => true,
                'check_file_headers' => true,
                'randomize_names' => true,
            ],
            'logging' => [
                'enabled' => true,
                'level' => 'info',
            ],
        ];
    }

    /**
     * Get thumbnail sizes from environment variable
     *
     * @return array
     */
    private function getThumbnailSizesFromEnv(): array
    {
        $envSizes = $_ENV['FILEUPLOAD_THUMBNAIL_SIZES'] ?? '';

        if (!empty($envSizes)) {
            $decoded = json_decode($envSizes, true);
            if (json_last_error() === JSON_ERROR_NONE && is_array($decoded)) {
                // Convert array format to associative array with size names as keys
                $sizes = [];
                foreach ($decoded as $size) {
                    if (isset($size['name'], $size['width'], $size['height'])) {
                        $sizes[$size['name']] = [
                            'width' => (int)$size['width'],
                            'height' => (int)$size['height'],
                            'mode' => $size['mode'] ?? 'crop'
                        ];
                    }
                }
                return $sizes;
            }
        }

        // Default thumbnail sizes if environment variable is not set or invalid
        return [
            'small' => ['width' => 150, 'height' => 150, 'mode' => 'crop'],
            'medium' => ['width' => 300, 'height' => 300, 'mode' => 'crop'],
            'large' => ['width' => 600, 'height' => 600, 'mode' => 'fit'],
        ];
    }

    /**
     * Get default uploads path based on current working directory
     */
    protected function getDefaultUploadsPath(): string
    {
        // Try to determine the best uploads path
        $possiblePaths = [
            // Current working directory + uploads
            getcwd() . '/uploads',
            // Script directory + uploads
            dirname($_SERVER['SCRIPT_FILENAME']) . '/uploads',
            // Package relative path (fallback)
            __DIR__ . '/../../../../../uploads',
        ];

        foreach ($possiblePaths as $path) {
            $dir = dirname($path);
            if (is_dir($dir) && is_writable($dir)) {
                return $path;
            }
        }

        // Final fallback - use temp directory
        return sys_get_temp_dir() . '/fileupload_uploads';
    }

    /**
     * Get default uploads URL based on current request
     */
    protected function getDefaultUploadsUrl(): string
    {
        // Try to determine the base URL from the current request
        if (isset($_SERVER['HTTP_HOST']) && isset($_SERVER['REQUEST_URI'])) {
            $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
            $host = $_SERVER['HTTP_HOST'];
            $requestUri = $_SERVER['REQUEST_URI'];

            // Get the directory part of the request URI
            $basePath = dirname($requestUri);
            if ($basePath === '/' || $basePath === '\\') {
                $basePath = '';
            }

            return $basePath . '/uploads';
        }

        // Fallback URL
        return '/uploads';
    }
}
