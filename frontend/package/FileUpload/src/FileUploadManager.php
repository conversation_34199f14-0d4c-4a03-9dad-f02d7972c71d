<?php

namespace Package\FileUpload;

use Package\FileUpload\FileUpload;
use Package\FileUpload\Processing\ImageProcessor;
use Package\FileUpload\Config\ConfigManager;
use Package\FileUpload\UploadResult;
use Package\FileUpload\FolderResult;

/**
 * FileUpload Manager
 * 
 * This class encapsulates all file upload logic and processing decisions.
 * It automatically configures processors based on environment variables
 * and provides a simple interface for file uploads.
 */
class FileUploadManager
{
    private FileUpload $uploader;
    private ConfigManager $configManager;
    private array $config;
    private bool $initialized = false;

    public function __construct(array $config = [])
    {
        $this->configManager = ConfigManager::getInstance();

        // Apply runtime configuration if provided
        if (!empty($config)) {
            $this->configManager->mergeRuntime($config);
        }

        $this->config = array_merge($this->getDefaultConfig(), $config);
        $this->uploader = new FileUpload();
        $this->initialize();
    }

    /**
     * Initialize the upload manager with processors based on configuration
     */
    private function initialize(): void
    {
        if ($this->initialized) {
            return;
        }

        // Get processing configuration
        $processingConfig = $this->configManager->getProcessingConfig();

        // Auto-configure image processor if processing is enabled
        if ($processingConfig['enabled'] ?? false) {
            $this->addImageProcessor($processingConfig);
        }

        $this->initialized = true;
    }

    /**
     * Add image processor with automatic configuration
     * 
     * @param array $processingConfig Processing configuration
     */
    private function addImageProcessor(array $processingConfig): void
    {
        try {
            $imageProcessor = new ImageProcessor($processingConfig);
            $this->uploader->addProcessor($imageProcessor);
        } catch (\Exception $e) {
            // Log error but don't fail - continue without image processing
            error_log("Failed to add image processor: " . $e->getMessage());
        }
    }

    /**
     * Upload a file with automatic processing
     * 
     * @param array $file $_FILES array element
     * @param array $options Upload options
     * @return UploadResult Upload result
     */
    public function upload(array $file, array $options = []): UploadResult
    {
        // Ensure manager is initialized
        $this->initialize();

        // Merge options with default configuration
        $uploadOptions = array_merge($this->getDefaultUploadOptions(), $options);

        // Perform upload with all configured processors
        return $this->uploader->upload($file, $uploadOptions);
    }

    /**
     * Upload multiple files with automatic processing
     * 
     * @param array $files Array of $_FILES elements
     * @param array $options Upload options
     * @return array Array of UploadResult results
     */
    public function uploadMultiple(array $files, array $options = []): array
    {
        $results = [];

        foreach ($files as $index => $file) {
            $fileOptions = $options;

            // Add index to options for unique naming if needed
            if (isset($options['prefix'])) {
                $fileOptions['prefix'] = $options['prefix'] . '_' . $index;
            }

            $results[] = $this->upload($file, $fileOptions);
        }

        return $results;
    }

    /**
     * Create a folder
     * 
     * @param string $path Folder path
     * @return \Package\FileUpload\FolderResult
     */
    public function createFolder(string $path): \Package\FileUpload\FolderResult
    {
        return $this->uploader->createFolder($path);
    }

    /**
     * Delete a folder
     * 
     * @param string $path Folder path
     * @return \Package\FileUpload\FolderResult
     */
    public function deleteFolder(string $path): \Package\FileUpload\FolderResult
    {
        return $this->uploader->deleteFolder($path);
    }

    /**
     * List folder contents
     * 
     * @param string $path Folder path
     * @return \Package\FileUpload\FolderResult
     */
    public function listFolder(string $path): \Package\FileUpload\FolderResult
    {
        return $this->uploader->listFolder($path);
    }

    /**
     * Generate dynamic transformation URL
     * 
     * @param string $originalPath Original image path
     * @param int $width Desired width
     * @param int $height Desired height
     * @param string $mode Transformation mode
     * @param bool $useQueryParams Use query parameters
     * @param bool $useDefaultMode Use default mode format
     * @return string Dynamic transformation URL
     */
    public function getDynamicUrl(string $originalPath, int $width, int $height, string $mode = 'fit', bool $useQueryParams = false, bool $useDefaultMode = false): string
    {
        return $this->uploader->getDynamicUrl($originalPath, $width, $height, $mode, $useQueryParams, $useDefaultMode);
    }

    /**
     * Get thumbnail service
     * 
     * @return \Package\FileUpload\Services\ThumbnailService
     */
    public function getThumbnailService(): \Package\FileUpload\Services\ThumbnailService
    {
        return $this->uploader->getThumbnailService();
    }

    /**
     * Get processing status information
     * 
     * @return array Processing status information
     */
    public function getProcessingStatus(): array
    {
        $processingConfig = $this->configManager->getProcessingConfig();

        return [
            'processing_enabled' => $processingConfig['enabled'] ?? false,
            'thumbnails_enabled' => $processingConfig['thumbnails']['enabled'] ?? false,
            'thumbnails_directory' => $processingConfig['thumbnails']['directory'] ?? 'thumbnails',
            'image_quality' => $processingConfig['quality'] ?? 85,
            'thumbnail_quality' => $processingConfig['thumbnails']['quality'] ?? 80,
            'thumbnail_sizes' => $processingConfig['thumbnails']['sizes'] ?? [],
            'processors_loaded' => $this->getLoadedProcessors(),
        ];
    }

    /**
     * Delete a file
     *
     * @param string $path File path to delete
     * @return UploadResult Delete result
     */
    public function deleteFile(string $path): UploadResult
    {
        // Ensure manager is initialized
        $this->initialize();

        try {
            $success = $this->uploader->delete($path);

            if ($success) {
                return UploadResult::success(
                    $path,
                    '', // Empty string instead of null for URL since file is deleted
                    'File deleted successfully'
                );
            } else {
                return UploadResult::failure('Failed to delete file');
            }
        } catch (\Exception $e) {
            return UploadResult::failure('Delete failed: ' . $e->getMessage());
        }
    }

    /**
     * Get configuration information
     *
     * @return array Configuration information
     */
    public function getConfigInfo(): array
    {
        $config = $this->configManager->all();
        $processingStatus = $this->getProcessingStatus();

        return [
            'storage' => [
                'driver' => $config['default'] ?? 'local',
                'local_root' => $config['drivers']['local']['root'] ?? 'uploads',
                'local_url' => $config['drivers']['local']['url'] ?? '/uploads',
            ],
            'validation' => [
                'max_size' => $config['validation']['max_file_size'] ?? 10485760,
                'allowed_types' => $config['validation']['allowed_mime_types'] ?? [],
                'forbidden_extensions' => $config['validation']['forbidden_extensions'] ?? [],
            ],
            'processing' => $processingStatus,
        ];
    }

    /**
     * Check if the manager is properly configured
     * 
     * @return array Status check results
     */
    public function healthCheck(): array
    {
        $checks = [];

        // Check if GD extension is loaded
        $checks['gd_extension'] = [
            'status' => extension_loaded('gd'),
            'message' => extension_loaded('gd') ? 'GD extension is loaded' : 'GD extension is not loaded'
        ];

        // Check processing configuration
        $processingConfig = $this->configManager->getProcessingConfig();
        $checks['processing_config'] = [
            'status' => $processingConfig['enabled'] ?? false,
            'message' => ($processingConfig['enabled'] ?? false) ? 'Image processing is enabled' : 'Image processing is disabled'
        ];

        // Check thumbnail configuration
        $checks['thumbnail_config'] = [
            'status' => $processingConfig['thumbnails']['enabled'] ?? false,
            'message' => ($processingConfig['thumbnails']['enabled'] ?? false) ? 'Thumbnail generation is enabled' : 'Thumbnail generation is disabled'
        ];

        // Check uploads directory
        $uploadsDir = $this->config['uploads_directory'] ?? 'uploads';
        $checks['uploads_directory'] = [
            'status' => is_dir($uploadsDir) && is_writable($uploadsDir),
            'message' => (is_dir($uploadsDir) && is_writable($uploadsDir)) ? 'Uploads directory is writable' : 'Uploads directory is not writable'
        ];

        return $checks;
    }

    /**
     * Get list of loaded processors
     * 
     * @return array List of loaded processor names
     */
    private function getLoadedProcessors(): array
    {
        // This would need to be implemented in the FileUpload class
        // For now, return based on configuration
        $processors = [];
        $processingConfig = $this->configManager->getProcessingConfig();

        if ($processingConfig['enabled'] ?? false) {
            $processors[] = 'ImageProcessor';
        }

        return $processors;
    }

    /**
     * Get default configuration
     * 
     * @return array Default configuration
     */
    private function getDefaultConfig(): array
    {
        return [
            'uploads_directory' => 'uploads',
            'auto_configure_processors' => true,
        ];
    }

    /**
     * Get default upload options
     * 
     * @return array Default upload options
     */
    private function getDefaultUploadOptions(): array
    {
        return [
            'directory' => $this->config['uploads_directory'] ?? 'uploads',
        ];
    }

    /**
     * Create a static factory method for easy instantiation
     *
     * @param array $config Optional configuration
     * @return self
     */
    public static function create(array $config = []): self
    {
        return new self($config);
    }

    /**
     * Create FileUploadManager with runtime configuration
     * This allows overriding any configuration at runtime
     */
    public static function createWithConfig(array $runtimeConfig): self
    {
        return new self($runtimeConfig);
    }

    /**
     * Create FileUploadManager with specific upload directory
     */
    public static function createForDirectory(string $uploadPath, string $uploadUrl = null): self
    {
        $config = [
            'drivers' => [
                'local' => [
                    'root' => $uploadPath,
                    'url' => $uploadUrl ?? '/uploads'
                ]
            ]
        ];

        return new self($config);
    }

    /**
     * Create FileUploadManager with specific validation rules
     */
    public static function createWithValidation(array $validationRules): self
    {
        $config = [
            'validation' => $validationRules
        ];

        return new self($config);
    }

    /**
     * Create FileUploadManager with method chaining configuration
     */
    public static function createWithChaining(): FileUpload
    {
        return new FileUpload();
    }

    /**
     * Enable/disable image processing with method chaining
     */
    public function setImageProcessing(bool $enabled, int $quality = 85): self
    {
        if ($this->uploader instanceof FileUpload) {
            $this->uploader->setImageProcessing($enabled, $quality);
        }
        return $this;
    }

    /**
     * Enable/disable thumbnail generation with method chaining
     */
    public function setThumbnailGeneration(bool $enabled, array $sizes = []): self
    {
        if ($this->uploader instanceof FileUpload) {
            $this->uploader->setThumbnailGeneration($enabled, $sizes);
        }
        return $this;
    }

    /**
     * Enable/disable image compression with method chaining
     */
    public function setImageCompression(bool $enabled, int $quality = 85): self
    {
        if ($this->uploader instanceof FileUpload) {
            $this->uploader->setImageCompression($enabled, $quality);
        }
        return $this;
    }

    /**
     * Enable/disable metadata stripping with method chaining
     */
    public function setMetadataStripping(bool $enabled): self
    {
        if ($this->uploader instanceof FileUpload) {
            $this->uploader->setMetadataStripping($enabled);
        }
        return $this;
    }

    /**
     * Enable/disable file validation with method chaining
     */
    public function setValidation(bool $enabled, array $rules = []): self
    {
        if ($this->uploader instanceof FileUpload) {
            $this->uploader->setValidation($enabled, $rules);
        }
        return $this;
    }

    /**
     * Enable/disable security scanning with method chaining
     */
    public function setSecurityScan(bool $enabled): self
    {
        if ($this->uploader instanceof FileUpload) {
            $this->uploader->setSecurityScan($enabled);
        }
        return $this;
    }

    /**
     * Set upload directory with method chaining
     */
    public function setUploadDirectory(string $directory): self
    {
        if ($this->uploader instanceof FileUpload) {
            $this->uploader->setUploadDirectory($directory);
        }
        return $this;
    }

    /**
     * Set allowed file extensions with method chaining
     */
    public function setAllowedExtensions(array $extensions): self
    {
        if ($this->uploader instanceof FileUpload) {
            $this->uploader->setAllowedExtensions($extensions);
        }
        return $this;
    }

    /**
     * Set maximum file size with method chaining
     */
    public function setMaxFileSize(int $sizeInBytes): self
    {
        if ($this->uploader instanceof FileUpload) {
            $this->uploader->setMaxFileSize($sizeInBytes);
        }
        return $this;
    }

    /**
     * Configure for image uploads with method chaining
     */
    public function setImage(bool $enabled, int $quality = 85, array $thumbnailSizes = []): self
    {
        if ($this->uploader instanceof FileUpload) {
            $this->uploader->setImageProcessing($enabled, $quality);
            $this->uploader->setImageCompression($enabled, $quality);
            $this->uploader->setMetadataStripping($enabled);

            if ($enabled && !empty($thumbnailSizes)) {
                $this->uploader->setThumbnailGeneration(true, $thumbnailSizes);
            }
        }
        return $this;
    }

    /**
     * Configure for document uploads with method chaining
     */
    public function setDocument(bool $enabled, array $allowedTypes = []): self
    {
        if ($this->uploader instanceof FileUpload) {
            // Disable image processing for documents
            $this->uploader->setImageProcessing(false);
            $this->uploader->setThumbnailGeneration(false);
            $this->uploader->setImageCompression(false);
            $this->uploader->setMetadataStripping(false);

            if ($enabled) {
                $this->uploader->setSecurityScan(true);
                if (!empty($allowedTypes)) {
                    $this->uploader->setAllowedExtensions($allowedTypes);
                }
            }
        }
        return $this;
    }

    /**
     * Configure for avatar uploads with method chaining
     */
    public function setAvatar(bool $enabled, int $quality = 95): self
    {
        if ($enabled && $this->uploader instanceof FileUpload) {
            $this->uploader->setImageProcessing(true, $quality);
            $this->uploader->setImageCompression(true, $quality);
            $this->uploader->setMetadataStripping(true);
            $this->uploader->setSecurityScan(true);
            $this->uploader->setMaxFileSize(1048576); // 1MB
            $this->uploader->setAllowedExtensions(['jpg', 'jpeg', 'png']);

            // Default avatar thumbnails
            $this->uploader->setThumbnailGeneration(true, [
                ['name' => 'thumb', 'width' => 100, 'height' => 100, 'mode' => 'crop'],
                ['name' => 'profile', 'width' => 200, 'height' => 200, 'mode' => 'crop']
            ]);
        }
        return $this;
    }

    /**
     * Configure for gallery uploads with method chaining
     */
    public function setGallery(bool $enabled, int $quality = 90): self
    {
        if ($enabled && $this->uploader instanceof FileUpload) {
            $this->uploader->setImageProcessing(true, $quality);
            $this->uploader->setImageCompression(true, $quality);
            $this->uploader->setMetadataStripping(true);
            $this->uploader->setSecurityScan(false); // Disabled for performance
            $this->uploader->setMaxFileSize(20971520); // 20MB
            $this->uploader->setAllowedExtensions(['jpg', 'jpeg', 'png', 'gif', 'webp']);

            // Default gallery thumbnails
            $this->uploader->setThumbnailGeneration(true, [
                ['name' => 'thumb', 'width' => 200, 'height' => 200, 'mode' => 'crop'],
                ['name' => 'medium', 'width' => 600, 'height' => 400, 'mode' => 'fit'],
                ['name' => 'large', 'width' => 1200, 'height' => 800, 'mode' => 'fit']
            ]);
        }
        return $this;
    }

    /**
     * Configure validation with method chaining
     */
    public function setValidate(bool $enabled, array $rules = []): self
    {
        if ($this->uploader instanceof FileUpload) {
            $this->uploader->setValidation($enabled, $rules);
        }
        return $this;
    }

    /**
     * Configure security scanning with method chaining
     */
    public function setSecurity(bool $enabled): self
    {
        if ($this->uploader instanceof FileUpload) {
            $this->uploader->setSecurityScan($enabled);
        }
        return $this;
    }

    /**
     * Configure upload directory with method chaining
     */
    public function setDirectory(string $directory): self
    {
        if ($this->uploader instanceof FileUpload) {
            $this->uploader->setUploadDirectory($directory);
        }
        return $this;
    }
}
