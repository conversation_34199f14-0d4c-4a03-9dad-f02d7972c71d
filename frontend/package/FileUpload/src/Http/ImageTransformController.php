<?php

namespace Package\FileUpload\Http;

use Package\FileUpload\Storage\StorageDriverFactory;
use Package\FileUpload\Config\ConfigManager;

class ImageTransformController
{
    protected $storage;
    protected $config;
    protected $supportedMimeTypes = [
        'image/jpeg',
        'image/png',
        'image/gif',
        'image/webp'
    ];

    public function __construct()
    {
        $this->storage = StorageDriverFactory::createFromConfig();
        $this->config = ConfigManager::getInstance();
    }

    /**
     * Handle dynamic image transformation
     * URL format: /path/to/image/WIDTHxHEIGHT/MODE=filename.jpg
     * Example: /uploads/1000x1000/fix=Dashboard2.jpg
     */
    public function transform($requestUri = null)
    {
        try {
            $uri = $requestUri ?: $_SERVER['REQUEST_URI'];

            // Check if this is a query parameter request (from .htaccess rewrite or direct)
            if (isset($_GET['path']) || isset($_GET['width']) || isset($_GET['w']) || isset($_GET['h'])) {
                $params = $this->parseQueryParameters();
            } else {
                // Parse the URL path to extract parameters (fallback)
                $params = $this->parseUrlPath($uri);
            }

            if (empty($params['file']) || empty($params['width']) || empty($params['height'])) {
                $this->sendErrorResponse('Invalid URL format', 400);
                return;
            }

            // Get original image path
            $originalPath = $this->getOriginalImagePath($params);

            // Check if file exists
            if (!$this->storage->exists($originalPath)) {
                $this->sendNotFoundResponse();
                return;
            }

            // Check if this is an SVG file - serve original SVG without transformation
            if ($this->isSvgFile($originalPath)) {
                $this->serveOriginalSvg($originalPath);
                return;
            }

            // Validate image type
            if (!$this->isValidImageType($originalPath)) {
                $this->sendErrorResponse('Unsupported image type', 415);
                return;
            }

            // Process and serve the transformed image
            $this->processAndServeImage($originalPath, $params);
        } catch (\Exception $e) {
            error_log("Image transformation error: " . $e->getMessage());
            $this->sendErrorResponse('Image processing failed', 500);
        }
    }

    /**
     * Parse URL path to extract transformation parameters
     * Expected formats:
     * - /directory/WIDTHxHEIGHT/MODE=filename.ext
     * - /uploads/subfolder/WIDTHxHEIGHT/MODE=filename.ext
     * - /any/nested/path/WIDTHxHEIGHT/MODE=filename.ext
     */
    protected function parseUrlPath($uri)
    {
        $params = [];

        // Remove query string if present
        $path = parse_url($uri, PHP_URL_PATH);

        // Pattern to match: /any/path/WIDTHxHEIGHT/MODE=filename.ext
        if (preg_match('/^(.+)\/(\d+)x(\d+)\/([a-z]+)=(.+)$/', $path, $matches)) {
            $params['basePath'] = $matches[1];
            $params['width'] = (int)$matches[2];
            $params['height'] = (int)$matches[3];
            $params['mode'] = $matches[4];
            $params['file'] = $matches[5];
        }

        // Validate mode
        if (isset($params['mode']) && !in_array($params['mode'], ['fix', 'fit', 'crop', 'fill', 'stretch'])) {
            $params['mode'] = 'fix'; // Default to fix mode
        }

        return $params;
    }

    /**
     * Parse query parameters for image transformation
     * Supports both formats:
     * - Direct: ?path=uploads/image.jpg&w=300&h=200&m=crop
     * - .htaccess rewrite: ?path=uploads/dir/image.jpg&width=300&height=200&mode=crop
     */
    protected function parseQueryParameters()
    {
        $params = [];

        // Get parameters from query string (support both formats)
        $params['file'] = $_GET['path'] ?? $_GET['file'] ?? '';
        $params['width'] = (int)($_GET['width'] ?? $_GET['w'] ?? 0);
        $params['height'] = (int)($_GET['height'] ?? $_GET['h'] ?? 0);
        $params['mode'] = $_GET['mode'] ?? $_GET['m'] ?? 'fix';

        // For query parameters, the basePath is extracted from the file path
        if (!empty($params['file'])) {
            $pathInfo = pathinfo($params['file']);
            $params['basePath'] = '/' . $pathInfo['dirname'];
            $params['file'] = $pathInfo['basename'];
        }

        // Validate mode
        if (!in_array($params['mode'], ['fix', 'fit', 'crop', 'fill', 'stretch'])) {
            $params['mode'] = 'fix';
        }

        return $params;
    }

    /**
     * Get the original image path from parsed parameters
     */
    protected function getOriginalImagePath($params)
    {
        // Construct the original path: basePath + filename
        $originalPath = trim($params['basePath'], '/') . '/' . $params['file'];

        // Remove leading slash if present
        $originalPath = ltrim($originalPath, '/');

        // Try different path combinations to find the file
        $pathsToTry = [
            $originalPath,                                    // Full path as parsed
            $params['file'],                                  // Just filename
        ];

        // If basePath contains subdirectories, try various combinations
        $basePath = trim($params['basePath'], '/');
        if (!empty($basePath)) {
            // Since storage driver root is 'uploads', we need to remove 'uploads' prefix if present
            if (strpos($basePath, 'uploads/') === 0) {
                // Remove 'uploads/' prefix since storage driver already points to uploads
                $relativePath = substr($basePath, 8); // Remove 'uploads/'
                if (!empty($relativePath)) {
                    $pathsToTry[] = $relativePath . '/' . $params['file'];
                }
            } elseif ($basePath === 'uploads') {
                // Just the uploads directory, file is in root
                $pathsToTry[] = $params['file'];
            } else {
                // basePath doesn't start with uploads, so it's a subdirectory
                $pathsToTry[] = $basePath . '/' . $params['file'];
            }

            // Also try the full path as-is
            $pathsToTry[] = $basePath . '/' . $params['file'];
        }

        // Also try just filename for simple cases
        $pathsToTry[] = $params['file'];

        // Test each path until we find one that exists
        foreach ($pathsToTry as $testPath) {
            if ($this->storage->exists($testPath)) {
                return $testPath;
            }
        }

        // If nothing found, return the original constructed path
        return $originalPath;
    }

    /**
     * Check if the file is a valid image type
     */
    protected function isValidImageType($path)
    {
        try {
            $content = $this->storage->get($path);
            $tempFile = tempnam(sys_get_temp_dir(), 'img_check_');
            file_put_contents($tempFile, $content);

            $imageInfo = getimagesize($tempFile);
            unlink($tempFile);

            if (!$imageInfo) {
                return false;
            }

            return in_array($imageInfo['mime'], $this->supportedMimeTypes);
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Process and serve the transformed image
     */
    protected function processAndServeImage($originalPath, $params)
    {
        // Create temporary file for original image
        $tempOriginal = tempnam(sys_get_temp_dir(), 'img_orig_');
        $content = $this->storage->get($originalPath);
        file_put_contents($tempOriginal, $content);

        // Get image info
        $imageInfo = getimagesize($tempOriginal);
        if (!$imageInfo) {
            unlink($tempOriginal);
            $this->sendErrorResponse('Invalid image file', 500);
            return;
        }

        // Create source image
        $sourceImage = $this->createImageFromFile($tempOriginal, $imageInfo[2]);
        if (!$sourceImage) {
            unlink($tempOriginal);
            $this->sendErrorResponse('Cannot create image resource', 500);
            return;
        }

        // Calculate transformation dimensions
        $originalWidth = $imageInfo[0];
        $originalHeight = $imageInfo[1];
        $targetWidth = $params['width'];
        $targetHeight = $params['height'];
        $mode = $params['mode'];

        // Calculate dimensions based on mode
        [$newWidth, $newHeight, $srcX, $srcY, $srcWidth, $srcHeight] =
            $this->calculateDimensions($originalWidth, $originalHeight, $targetWidth, $targetHeight, $mode);

        // Create new image
        $newImage = imagecreatetruecolor($newWidth, $newHeight);

        // Preserve transparency for PNG, GIF, and WebP
        if ($imageInfo[2] === IMAGETYPE_PNG || $imageInfo[2] === IMAGETYPE_GIF || $imageInfo[2] === IMAGETYPE_WEBP) {
            imagealphablending($newImage, false);
            imagesavealpha($newImage, true);
            $transparent = imagecolorallocatealpha($newImage, 255, 255, 255, 127);
            imagefill($newImage, 0, 0, $transparent);
        }

        // Resample the image
        imagecopyresampled(
            $newImage,
            $sourceImage,
            0,
            0,
            $srcX,
            $srcY,
            $newWidth,
            $newHeight,
            $srcWidth,
            $srcHeight
        );

        // Set headers and output image
        $this->setImageHeaders($imageInfo[2]);
        $this->outputImage($newImage, $imageInfo[2]);

        // Clean up
        imagedestroy($sourceImage);
        imagedestroy($newImage);
        unlink($tempOriginal);
    }

    /**
     * Create image resource from file
     */
    protected function createImageFromFile($filePath, $imageType)
    {
        switch ($imageType) {
            case IMAGETYPE_JPEG:
                return imagecreatefromjpeg($filePath);
            case IMAGETYPE_PNG:
                return imagecreatefrompng($filePath);
            case IMAGETYPE_GIF:
                return imagecreatefromgif($filePath);
            case IMAGETYPE_WEBP:
                return function_exists('imagecreatefromwebp') ? imagecreatefromwebp($filePath) : false;
            default:
                return false;
        }
    }

    /**
     * Calculate dimensions based on transformation mode
     */
    protected function calculateDimensions($originalWidth, $originalHeight, $targetWidth, $targetHeight, $mode)
    {
        switch ($mode) {
            case 'fit':
                // Fit image within target dimensions, maintaining aspect ratio
                $ratio = min($targetWidth / $originalWidth, $targetHeight / $originalHeight);
                $newWidth = round($originalWidth * $ratio);
                $newHeight = round($originalHeight * $ratio);
                return [$newWidth, $newHeight, 0, 0, $originalWidth, $originalHeight];

            case 'fill':
                // Fill target dimensions, may crop image
                $ratio = max($targetWidth / $originalWidth, $targetHeight / $originalHeight);
                $newWidth = $targetWidth;
                $newHeight = $targetHeight;
                $srcWidth = round($targetWidth / $ratio);
                $srcHeight = round($targetHeight / $ratio);
                $srcX = round(($originalWidth - $srcWidth) / 2);
                $srcY = round(($originalHeight - $srcHeight) / 2);
                return [$newWidth, $newHeight, $srcX, $srcY, $srcWidth, $srcHeight];

            case 'stretch':
                // Stretch image to exact target dimensions
                return [$targetWidth, $targetHeight, 0, 0, $originalWidth, $originalHeight];

            case 'crop':
                // Crop image to exact target dimensions
                $ratio = max($targetWidth / $originalWidth, $targetHeight / $originalHeight);
                $srcWidth = round($targetWidth / $ratio);
                $srcHeight = round($targetHeight / $ratio);
                $srcX = round(($originalWidth - $srcWidth) / 2);
                $srcY = round(($originalHeight - $srcHeight) / 2);
                return [$targetWidth, $targetHeight, $srcX, $srcY, $srcWidth, $srcHeight];

            case 'fix':
            default:
                // Fix mode - crop to exact dimensions (same as crop)
                $ratio = max($targetWidth / $originalWidth, $targetHeight / $originalHeight);
                $srcWidth = round($targetWidth / $ratio);
                $srcHeight = round($targetHeight / $ratio);
                $srcX = round(($originalWidth - $srcWidth) / 2);
                $srcY = round(($originalHeight - $srcHeight) / 2);
                return [$targetWidth, $targetHeight, $srcX, $srcY, $srcWidth, $srcHeight];
        }
    }

    /**
     * Set appropriate image headers
     */
    protected function setImageHeaders($imageType)
    {
        // Set content type
        switch ($imageType) {
            case IMAGETYPE_JPEG:
                header('Content-Type: image/jpeg');
                break;
            case IMAGETYPE_PNG:
                header('Content-Type: image/png');
                break;
            case IMAGETYPE_GIF:
                header('Content-Type: image/gif');
                break;
            case IMAGETYPE_WEBP:
                header('Content-Type: image/webp');
                break;
        }

        // Set caching headers
        header('Cache-Control: public, max-age=31536000'); // 1 year
        header('Expires: ' . gmdate('D, d M Y H:i:s', time() + 31536000) . ' GMT');
        header('Last-Modified: ' . gmdate('D, d M Y H:i:s') . ' GMT');
    }

    /**
     * Output image to browser
     */
    protected function outputImage($image, $imageType, $quality = 85)
    {
        switch ($imageType) {
            case IMAGETYPE_JPEG:
                imagejpeg($image, null, $quality);
                break;
            case IMAGETYPE_PNG:
                imagepng($image);
                break;
            case IMAGETYPE_GIF:
                imagegif($image);
                break;
            case IMAGETYPE_WEBP:
                if (function_exists('imagewebp')) {
                    imagewebp($image, null, $quality);
                }
                break;
        }
    }

    /**
     * Send 404 Not Found response
     */
    protected function sendNotFoundResponse()
    {
        header('HTTP/1.0 404 Not Found');
        header('Content-Type: text/plain');
        echo 'Image not found';
        exit;
    }

    /**
     * Check if file is an SVG
     */
    protected function isSvgFile($filePath)
    {
        $extension = strtolower(pathinfo($filePath, PATHINFO_EXTENSION));
        return $extension === 'svg';
    }

    /**
     * Serve original SVG file without transformation
     */
    protected function serveOriginalSvg($originalPath)
    {
        // SVG files are vector-based and don't need transformation
        // Serve the original file with proper headers
        $content = $this->storage->get($originalPath);

        header('Content-Type: image/svg+xml');
        header('Content-Length: ' . strlen($content));
        header('Cache-Control: public, max-age=31536000'); // Cache for 1 year
        header('Expires: ' . gmdate('D, d M Y H:i:s', time() + 31536000) . ' GMT');

        echo $content;
        exit;
    }

    /**
     * Send error response
     */
    protected function sendErrorResponse($message, $code = 500)
    {
        header("HTTP/1.0 {$code} Error");
        header('Content-Type: text/plain');
        echo $message;
        exit;
    }
}
