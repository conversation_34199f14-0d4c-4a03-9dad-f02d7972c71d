<?php

namespace Package\FileUpload;

class FolderResult
{
    protected bool $success;
    protected ?string $path;
    protected ?string $url;
    protected ?string $message;
    protected array $metadata;
    protected array $errors;
    protected array $contents;

    public function __construct(
        bool $success = false,
        ?string $path = null,
        ?string $url = null,
        ?string $message = null,
        array $metadata = [],
        array $errors = [],
        array $contents = []
    ) {
        $this->success = $success;
        $this->path = $path;
        $this->url = $url;
        $this->message = $message;
        $this->metadata = $metadata;
        $this->errors = $errors;
        $this->contents = $contents;
    }

    public function isSuccess(): bool
    {
        return $this->success;
    }

    public function getPath(): ?string
    {
        return $this->path;
    }

    public function getUrl(): ?string
    {
        return $this->url;
    }

    public function getMessage(): ?string
    {
        return $this->message;
    }

    public function getMetadata(): array
    {
        return $this->metadata;
    }

    public function getErrors(): array
    {
        return $this->errors;
    }

    public function hasErrors(): bool
    {
        return !empty($this->errors);
    }

    public function getContents(): array
    {
        return $this->contents;
    }

    public function toArray(): array
    {
        return [
            'success' => $this->success,
            'path' => $this->path,
            'url' => $this->url,
            'message' => $this->message,
            'metadata' => $this->metadata,
            'errors' => $this->errors,
            'contents' => $this->contents,
        ];
    }

    public static function success(
        string $path,
        string $url = null,
        string $message = 'Folder operation completed successfully',
        array $metadata = [],
        array $contents = []
    ): self {
        return new self(true, $path, $url, $message, $metadata, [], $contents);
    }

    public static function failure(string $message, array $errors = []): self
    {
        return new self(false, null, null, $message, [], $errors);
    }
}
