<?php

namespace Package\FileUpload;

use Package\FileUpload\Contracts\FileUploadInterface;
use Package\FileUpload\Contracts\StorageDriverInterface;
use Package\FileUpload\Contracts\ValidatorInterface;
use Package\FileUpload\Contracts\ProcessorInterface;
use Package\FileUpload\Storage\StorageDriverFactory;
use Package\FileUpload\Validation\FileValidator;
use Package\FileUpload\Config\ConfigManager;
use Package\FileUpload\Exceptions\FileUploadException;
use Package\FileUpload\Exceptions\ValidationException;
use Package\FileUpload\Services\ThumbnailService;

class FileUpload implements FileUploadInterface
{
    protected StorageDriverInterface $storageDriver;
    protected ValidatorInterface $validator;
    protected array $processors = [];
    protected array $config = [];
    protected ?ThumbnailService $thumbnailService = null;

    public function __construct(
        ?StorageDriverInterface $storageDriver = null,
        ?ValidatorInterface $validator = null,
        array $config = []
    ) {
        $this->config = $config;

        $this->storageDriver = $storageDriver ?? StorageDriverFactory::createFromConfig();
        $this->validator = $validator ?? new FileValidator();

        $this->loadConfig();
    }

    public function upload(array $file, array $options = []): UploadResult
    {
        try {
            // Validate the file
            $validationResult = $this->validator->validate($file, $options['validation'] ?? []);

            if (!$validationResult->isValid()) {
                return UploadResult::failure(
                    'File validation failed',
                    $validationResult->getErrors()
                );
            }

            // Generate file path
            $path = $this->generateFilePath($file, $options);

            // Read file contents
            $contents = file_get_contents($file['tmp_name']);
            if ($contents === false) {
                throw new FileUploadException('Failed to read uploaded file');
            }

            // Store the file
            $storedPath = $this->storageDriver->store($path, $contents, $options['storage'] ?? []);

            // Get the public URL
            $url = $this->storageDriver->url($storedPath);

            // Process the file if processors are available
            $processedFiles = [];
            if (!empty($this->processors) && ($options['process'] ?? true)) {
                // Get the full path to the stored file for processing
                $fullStoredPath = $this->getFullStoredPath($storedPath);
                $processedFiles = $this->processFile($fullStoredPath, $storedPath, $options);
            }

            // Collect metadata
            $metadata = array_merge(
                $validationResult->getMetadata(),
                $this->storageDriver->metadata($storedPath)
            );

            return UploadResult::success(
                $storedPath,
                $url,
                'File uploaded successfully',
                $metadata,
                $processedFiles
            );
        } catch (ValidationException $e) {
            return UploadResult::failure($e->getMessage(), $e->getErrors());
        } catch (\Exception $e) {
            return UploadResult::failure(
                'Upload failed: ' . $e->getMessage(),
                [$e->getMessage()]
            );
        }
    }

    public function uploadMultiple(array $files, array $options = []): array
    {
        $results = [];

        foreach ($files as $index => $file) {
            $fileOptions = $options;
            $fileOptions['index'] = $index;

            $results[] = $this->upload($file, $fileOptions);
        }

        return $results;
    }

    public function delete(string $path): bool
    {
        try {
            return $this->storageDriver->delete($path);
        } catch (\Exception $e) {
            return false;
        }
    }

    public function getFileInfo(string $path): array
    {
        try {
            return $this->storageDriver->metadata($path);
        } catch (\Exception $e) {
            return [];
        }
    }

    /**
     * Create a folder/directory
     *
     * @param string $path The folder path to create
     * @param array $options Additional options for folder creation
     * @return FolderResult
     */
    public function createFolder(string $path, array $options = []): FolderResult
    {
        try {
            // Normalize the path
            $normalizedPath = trim($path, '/');

            if (empty($normalizedPath)) {
                return FolderResult::failure('Folder path cannot be empty');
            }

            // Check if folder already exists
            if ($this->storageDriver->directoryExists($normalizedPath)) {
                return FolderResult::success(
                    $normalizedPath,
                    $this->storageDriver->url($normalizedPath),
                    'Folder already exists'
                );
            }

            // Create the folder
            $success = $this->storageDriver->createDirectory($normalizedPath, $options);

            if ($success) {
                return FolderResult::success(
                    $normalizedPath,
                    $this->storageDriver->url($normalizedPath),
                    'Folder created successfully'
                );
            } else {
                return FolderResult::failure('Failed to create folder');
            }
        } catch (\Exception $e) {
            return FolderResult::failure(
                'Folder creation failed: ' . $e->getMessage(),
                [$e->getMessage()]
            );
        }
    }

    /**
     * Check if a folder exists
     *
     * @param string $path The folder path to check
     * @return bool
     */
    public function folderExists(string $path): bool
    {
        try {
            return $this->storageDriver->directoryExists(trim($path, '/'));
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Delete a folder
     *
     * @param string $path The folder path to delete
     * @param bool $recursive Whether to delete contents recursively
     * @return FolderResult
     */
    public function deleteFolder(string $path, bool $recursive = false): FolderResult
    {
        try {
            $normalizedPath = trim($path, '/');

            if (empty($normalizedPath)) {
                return FolderResult::failure('Folder path cannot be empty');
            }

            // Check if folder exists
            if (!$this->storageDriver->directoryExists($normalizedPath)) {
                return FolderResult::success(
                    $normalizedPath,
                    null,
                    'Folder does not exist (already deleted)'
                );
            }

            // Delete the folder
            $success = $this->storageDriver->deleteDirectory($normalizedPath, $recursive);

            if ($success) {
                return FolderResult::success(
                    $normalizedPath,
                    null,
                    'Folder deleted successfully'
                );
            } else {
                return FolderResult::failure('Failed to delete folder');
            }
        } catch (\Exception $e) {
            return FolderResult::failure(
                'Folder deletion failed: ' . $e->getMessage(),
                [$e->getMessage()]
            );
        }
    }

    /**
     * List folder contents
     *
     * @param string $path The folder path to list
     * @param bool $recursive Whether to list recursively
     * @return FolderResult
     */
    public function listFolder(string $path, bool $recursive = false): FolderResult
    {
        try {
            $normalizedPath = trim($path, '/');

            // Check if folder exists
            if (!$this->storageDriver->directoryExists($normalizedPath)) {
                return FolderResult::failure('Folder does not exist');
            }

            // List folder contents
            $contents = $this->storageDriver->listDirectory($normalizedPath, $recursive);

            return FolderResult::success(
                $normalizedPath,
                $this->storageDriver->url($normalizedPath),
                'Folder contents retrieved successfully',
                ['item_count' => count($contents)],
                $contents
            );
        } catch (\Exception $e) {
            return FolderResult::failure(
                'Failed to list folder contents: ' . $e->getMessage(),
                [$e->getMessage()]
            );
        }
    }

    public function setStorageDriver(StorageDriverInterface $driver): void
    {
        $this->storageDriver = $driver;
    }

    public function getStorageDriver(): StorageDriverInterface
    {
        return $this->storageDriver;
    }

    public function setValidator(ValidatorInterface $validator): void
    {
        $this->validator = $validator;
    }

    public function addProcessor(ProcessorInterface $processor): void
    {
        $this->processors[] = $processor;
    }

    public function setConfig(array $config): void
    {
        $this->config = array_merge($this->config, $config);
    }

    protected function loadConfig(): void
    {
        if (empty($this->config)) {
            $configManager = ConfigManager::getInstance();
            $this->config = $configManager->all();
        }
    }

    protected function generateFilePath(array $file, array $options): string
    {
        $directory = $options['directory'] ?? 'uploads';
        $filename = $this->generateFilename($file, $options);

        // Handle empty directory (root uploads)
        if (empty($directory)) {
            return $filename;
        }

        return trim($directory, '/') . '/' . $filename;
    }

    protected function generateFilename(array $file, array $options): string
    {
        $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));

        if ($options['preserve_name'] ?? false) {
            $basename = pathinfo($file['name'], PATHINFO_FILENAME);
            $basename = preg_replace('/[^a-zA-Z0-9_-]/', '_', $basename);
            return $basename . '.' . $extension;
        }

        $prefix = $options['prefix'] ?? '';
        $suffix = $options['suffix'] ?? '';

        return $prefix . uniqid() . $suffix . '.' . $extension;
    }

    protected function processFile(string $filePath, string $storedPath, array $options): array
    {
        $processedFiles = [];

        foreach ($this->processors as $processor) {
            if ($processor->canProcess($this->getMimeType($filePath))) {
                try {
                    // Pass both the file path and stored path for context
                    $processingOptions = $options['processing'] ?? [];
                    $processingOptions['stored_path'] = $storedPath;
                    $processingOptions['storage_driver'] = $this->storageDriver;

                    $result = $processor->process($filePath, $processingOptions);

                    if ($result->isSuccess()) {
                        $processedFiles = array_merge($processedFiles, $result->getProcessedFiles());
                    }
                } catch (\Exception $e) {
                    // Log processor error but don't fail the upload
                    error_log("File processor error: " . $e->getMessage());
                }
            }
        }

        return $processedFiles;
    }

    protected function getMimeType(string $filePath): string
    {
        if (function_exists('finfo_file')) {
            $finfo = finfo_open(FILEINFO_MIME_TYPE);
            $mimeType = finfo_file($finfo, $filePath);
            finfo_close($finfo);

            if ($mimeType !== false) {
                return $mimeType;
            }
        }

        return 'application/octet-stream';
    }

    protected function getFullStoredPath(string $storedPath): string
    {
        // For local storage, get the full file system path
        if ($this->storageDriver->getName() === 'local') {
            $config = $this->config['drivers']['local'] ?? [];
            $root = $config['root'] ?? 'uploads';
            return $root . '/' . ltrim($storedPath, '/');
        }

        // For other storage drivers, return the stored path as-is
        // (they would need different handling for processing)
        return $storedPath;
    }

    /**
     * Generate a URL for dynamic image transformation
     *
     * @param string $originalPath Original image path (relative to storage root)
     * @param int $width Desired width
     * @param int $height Desired height
     * @param string $mode Transformation mode (crop, fit, stretch, fill)
     * @param int $quality Image quality (1-100)
     * @param string $baseUrl Base URL for transform endpoint (null for auto-detection)
     * @return string Transformed image URL
     */
    public function getTransformUrl(
        string $originalPath,
        int $width,
        int $height,
        string $mode = 'fit',
        int $quality = 85,
        ?string $baseUrl = null
    ): string {
        // Auto-detect base URL if not provided
        if ($baseUrl === null) {
            $baseUrl = $this->getTransformEndpoint();
        }

        $params = [
            'path' => $originalPath,
            'w' => $width,
            'h' => $height,
            'm' => $mode,
            'q' => $quality
        ];

        return $baseUrl . '?' . http_build_query($params);
    }

    /**
     * Get the dynamic transform endpoint URL
     *
     * @return string Transform endpoint URL
     */
    protected function getTransformEndpoint(): string
    {
        // Try to determine the base URL from the current request
        if (isset($_SERVER['REQUEST_URI'])) {
            $requestUri = $_SERVER['REQUEST_URI'];

            // Get the directory part of the request URI
            $basePath = dirname($requestUri);
            if ($basePath === '/' || $basePath === '\\') {
                $basePath = '';
            }

            return $basePath . '/transform.php';
        }

        // Fallback to relative path
        return '/transform.php';
    }

    /**
     * Generate multiple transformation URLs for different sizes
     *
     * @param string $originalPath Original image path
     * @param array $sizes Array of size configurations
     * @param string $baseUrl Base URL for transform endpoint
     * @return array Array of transformation URLs
     */
    public function getTransformUrls(string $originalPath, array $sizes, ?string $baseUrl = null): array
    {
        // Auto-detect base URL if not provided
        if ($baseUrl === null) {
            $baseUrl = $this->getTransformEndpoint();
        }

        $urls = [];

        foreach ($sizes as $sizeName => $config) {
            $width = $config['width'] ?? 0;
            $height = $config['height'] ?? 0;
            $mode = $config['mode'] ?? 'fit';
            $quality = $config['quality'] ?? 85;

            if ($width > 0 || $height > 0) {
                $urls[$sizeName] = $this->getTransformUrl($originalPath, $width, $height, $mode, $quality, $baseUrl);
            }
        }

        return $urls;
    }

    /**
     * Generate responsive image URLs for different screen sizes
     *
     * @param string $originalPath Original image path
     * @param string $mode Transformation mode
     * @param int $quality Image quality
     * @param string $baseUrl Base URL for transform endpoint
     * @return array Array of responsive URLs
     */
    public function getResponsiveUrls(
        string $originalPath,
        string $mode = 'fit',
        int $quality = 85,
        ?string $baseUrl = null
    ): array {
        // Auto-detect base URL if not provided
        if ($baseUrl === null) {
            $baseUrl = $this->getTransformEndpoint();
        }

        $responsiveSizes = [
            'xs' => ['width' => 320, 'height' => 0],   // Mobile portrait
            'sm' => ['width' => 576, 'height' => 0],   // Mobile landscape
            'md' => ['width' => 768, 'height' => 0],   // Tablet
            'lg' => ['width' => 992, 'height' => 0],   // Desktop
            'xl' => ['width' => 1200, 'height' => 0],  // Large desktop
            'xxl' => ['width' => 1400, 'height' => 0], // Extra large
        ];

        $urls = [];
        foreach ($responsiveSizes as $breakpoint => $size) {
            $size['mode'] = $mode;
            $size['quality'] = $quality;
            $urls[$breakpoint] = $this->getTransformUrl(
                $originalPath,
                $size['width'],
                $size['height'],
                $mode,
                $quality,
                $baseUrl
            );
        }

        return $urls;
    }

    /**
     * Generate a srcset string for responsive images
     *
     * @param string $originalPath Original image path
     * @param string $mode Transformation mode
     * @param int $quality Image quality
     * @param string $baseUrl Base URL for transform endpoint
     * @return string Srcset string for use in img tags
     */
    public function getSrcSet(
        string $originalPath,
        string $mode = 'fit',
        int $quality = 85,
        ?string $baseUrl = null
    ): string {
        $responsiveUrls = $this->getResponsiveUrls($originalPath, $mode, $quality, $baseUrl);

        $srcset = [];
        $widths = [
            'xs' => '320w',
            'sm' => '576w',
            'md' => '768w',
            'lg' => '992w',
            'xl' => '1200w',
            'xxl' => '1400w'
        ];

        foreach ($responsiveUrls as $breakpoint => $url) {
            if (isset($widths[$breakpoint])) {
                $srcset[] = $url . ' ' . $widths[$breakpoint];
            }
        }

        return implode(', ', $srcset);
    }

    /**
     * Generate dynamic transformation URL
     * Supports both explicit mode and default mode formats:
     * - /path/WIDTHxHEIGHT/MODE=filename.jpg (explicit mode)
     * - /path/WIDTHxHEIGHT/filename.jpg (default to fit mode)
     *
     * @param string $originalPath Original image path (can be any path structure)
     * @param int $width Desired width
     * @param int $height Desired height
     * @param string $mode Transformation mode (fix, fit, crop, fill, stretch)
     * @param bool $useQueryParams Use query parameters instead of path-based URLs
     * @param bool $useDefaultMode Use default mode format (without explicit mode in URL)
     * @return string Dynamic transformation URL
     */
    public function getDynamicUrl(string $originalPath, int $width, int $height, string $mode = 'fit', bool $useQueryParams = false, bool $useDefaultMode = false): string
    {
        $transformService = new \Package\FileUpload\Services\ImageTransformService();
        return $transformService->generateUrl($originalPath, $width, $height, $mode, $useQueryParams, $useDefaultMode);
    }

    /**
     * Get the thumbnail service instance
     *
     * @return ThumbnailService
     */
    public function getThumbnailService(): ThumbnailService
    {
        if ($this->thumbnailService === null) {
            $this->thumbnailService = new ThumbnailService($this->storageDriver, $this->config);
        }

        return $this->thumbnailService;
    }

    /**
     * Generate thumbnails for an uploaded file
     *
     * @param string $originalPath Path to the original image
     * @param array $sizes Array of thumbnail sizes to generate
     * @return array Array of generated thumbnail paths
     */
    public function generateThumbnails(string $originalPath, array $sizes = []): array
    {
        return $this->getThumbnailService()->generateThumbnails($originalPath, $sizes);
    }

    /**
     * Generate multiple dynamic URLs for different sizes
     *
     * @param string $originalPath Original image path
     * @param array $sizes Array of size configurations
     * @return array Array of dynamic URLs
     */
    public function getDynamicUrls(string $originalPath, array $sizes): array
    {
        $urls = [];

        foreach ($sizes as $sizeName => $config) {
            $width = $config['width'] ?? 0;
            $height = $config['height'] ?? 0;
            $mode = $config['mode'] ?? 'fix';

            if ($width > 0 && $height > 0) {
                $urls[$sizeName] = $this->getDynamicUrl($originalPath, $width, $height, $mode);
            }
        }

        return $urls;
    }
}
