<?php

namespace Package\FileUpload\Services;

use Package\FileUpload\Contracts\StorageDriverInterface;
use Package\FileUpload\Image\ImageProcessor;

/**
 * Thumbnail Service
 * 
 * Handles all thumbnail generation and management logic.
 * This service encapsulates thumbnail creation, caching, and cleanup operations.
 */
class ThumbnailService
{
    private StorageDriverInterface $storage;
    private ImageProcessor $imageProcessor;
    private array $config;

    public function __construct(StorageDriverInterface $storage, array $config = [])
    {
        $this->storage = $storage;
        $this->imageProcessor = new ImageProcessor();
        $this->config = array_merge($this->getDefaultConfig(), $config);
    }

    /**
     * Generate thumbnail for an uploaded file
     * 
     * @param string $originalPath Path to the original image
     * @param array $sizes Array of thumbnail sizes to generate
     * @return array Array of generated thumbnail paths
     */
    public function generateThumbnails(string $originalPath, array $sizes = []): array
    {
        if (!$this->config['enabled']) {
            return [];
        }

        $sizes = empty($sizes) ? $this->config['default_sizes'] : $sizes;
        $thumbnails = [];

        foreach ($sizes as $size) {
            $thumbnailPath = $this->generateThumbnail($originalPath, $size);
            if ($thumbnailPath) {
                $thumbnails[$size['name']] = $thumbnailPath;
            }
        }

        return $thumbnails;
    }

    /**
     * Generate a single thumbnail
     * 
     * @param string $originalPath Path to the original image
     * @param array $size Thumbnail size configuration
     * @return string|null Path to generated thumbnail or null on failure
     */
    public function generateThumbnail(string $originalPath, array $size): ?string
    {
        try {
            // Get original image content
            $originalContent = $this->storage->get($originalPath);
            if (!$originalContent) {
                return null;
            }

            // Generate thumbnail path
            $thumbnailPath = $this->getThumbnailPath($originalPath, $size);

            // Process the image
            $thumbnailContent = $this->imageProcessor->resize(
                $originalContent,
                $size['width'],
                $size['height'],
                $size['mode'] ?? 'fit'
            );

            // Store the thumbnail
            $storedPath = $this->storage->store($thumbnailPath, $thumbnailContent);
            if ($storedPath) {
                return $thumbnailPath;
            }

            return null;
        } catch (\Exception $e) {
            error_log("Thumbnail generation failed: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Get thumbnail path for a given original path and size
     * 
     * @param string $originalPath Original image path
     * @param array $size Thumbnail size configuration
     * @return string Thumbnail path
     */
    public function getThumbnailPath(string $originalPath, array $size): string
    {
        $pathInfo = pathinfo($originalPath);
        $directory = $pathInfo['dirname'];
        $filename = $pathInfo['filename'];
        $extension = $pathInfo['extension'];

        $thumbnailFilename = $filename . '_' . $size['name'] . '.' . $extension;

        return $directory . '/' . $this->config['directory'] . '/' . $thumbnailFilename;
    }

    /**
     * Clean up thumbnails for a deleted file
     * 
     * @param string $originalPath Path to the original image that was deleted
     * @return bool True if cleanup was successful
     */
    public function cleanupThumbnails(string $originalPath): bool
    {
        try {
            $pathInfo = pathinfo($originalPath);
            $directory = $pathInfo['dirname'];
            $filename = $pathInfo['filename'];
            $extension = $pathInfo['extension'];

            $thumbnailDirectory = $directory . '/' . $this->config['directory'];

            // Get all files in thumbnail directory
            if ($this->storage->directoryExists($thumbnailDirectory)) {
                $files = $this->storage->listDirectory($thumbnailDirectory);
            } else {
                $files = [];
            }

            foreach ($files as $fileInfo) {
                $fileName = is_array($fileInfo) ? $fileInfo['name'] : $fileInfo;
                // Check if this file is a thumbnail of the deleted image
                if (
                    strpos($fileName, $filename . '_') === 0 &&
                    pathinfo($fileName, PATHINFO_EXTENSION) === $extension
                ) {
                    $this->storage->delete($thumbnailDirectory . '/' . $fileName);
                }
            }

            return true;
        } catch (\Exception $e) {
            error_log("Thumbnail cleanup failed: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Check if thumbnails are enabled
     * 
     * @return bool True if thumbnails are enabled
     */
    public function isEnabled(): bool
    {
        return $this->config['enabled'];
    }

    /**
     * Get default thumbnail configuration
     * 
     * @return array Default configuration
     */
    private function getDefaultConfig(): array
    {
        return [
            'enabled' => $_ENV['FILEUPLOAD_THUMBNAILS_ENABLED'] ?? true,
            'directory' => $_ENV['FILEUPLOAD_THUMBNAILS_DIRECTORY'] ?? 'thumbnails',
            'default_sizes' => [
                [
                    'name' => 'small',
                    'width' => 150,
                    'height' => 150,
                    'mode' => 'crop'
                ],
                [
                    'name' => 'medium',
                    'width' => 300,
                    'height' => 300,
                    'mode' => 'fit'
                ],
                [
                    'name' => 'large',
                    'width' => 600,
                    'height' => 600,
                    'mode' => 'fit'
                ]
            ]
        ];
    }

    /**
     * Get thumbnail sizes configuration
     * 
     * @return array Thumbnail sizes configuration
     */
    public function getThumbnailSizes(): array
    {
        return $this->config['default_sizes'];
    }

    /**
     * Update thumbnail configuration
     * 
     * @param array $config New configuration
     */
    public function updateConfig(array $config): void
    {
        $this->config = array_merge($this->config, $config);
    }
}
