<?php

namespace Package\FileUpload\Services;

use Package\FileUpload\Http\ImageTransformController;

/**
 * Image Transformation Service
 * 
 * This service encapsulates all image transformation logic and acts as the main
 * entry point for dynamic image transformations. It handles URL parsing,
 * parameter validation, and delegates to the appropriate transformation methods.
 */
class ImageTransformService
{
    private ImageTransformController $controller;

    public function __construct()
    {
        $this->controller = new ImageTransformController();
    }

    /**
     * Handle the incoming transformation request
     * 
     * This method acts as the main entry point for all image transformation requests.
     * It processes the request and delegates to the appropriate transformation logic.
     */
    public function handleRequest(): void
    {
        $this->controller->transform();
    }

    /**
     * Generate dynamic transformation URL
     * 
     * @param string $originalPath Original image path
     * @param int $width Desired width
     * @param int $height Desired height
     * @param string $mode Transformation mode (fix, fit, crop, fill, stretch)
     * @param bool $useQueryParams Use query parameters instead of path-based URLs
     * @param bool $useDefaultMode Use default mode format (without explicit mode in URL)
     * @return string Dynamic transformation URL
     */
    public function generateUrl(
        string $originalPath, 
        int $width, 
        int $height, 
        string $mode = 'fit', 
        bool $useQueryParams = false, 
        bool $useDefaultMode = false
    ): string {
        if ($useQueryParams) {
            return '/transform.php?path=' . urlencode($originalPath) . '&w=' . $width . '&h=' . $height . '&m=' . $mode;
        }
        
        // Extract directory and filename
        $pathInfo = pathinfo($originalPath);
        $directory = $pathInfo['dirname'];
        $filename = $pathInfo['basename'];
        
        // Normalize the directory path
        $directory = trim($directory, '/');
        
        // For path-based URLs, we need to handle the directory structure properly
        if (empty($directory) || $directory === '.') {
            // File is in root, assume uploads
            $basePath = '/uploads';
        } else {
            // Always ensure the path starts with /uploads for the rewrite rules to work
            if (strpos($directory, 'uploads') === 0) {
                // Directory already starts with uploads
                $basePath = '/' . $directory;
            } else {
                // Add uploads prefix
                $basePath = '/uploads/' . $directory;
            }
        }

        // Generate URL based on format preference
        if ($useDefaultMode && $mode === 'fit') {
            // Use default mode format (no explicit mode in URL, defaults to fit)
            return $basePath . '/' . $width . 'x' . $height . '/' . $filename;
        } else {
            // Use explicit mode format
            return $basePath . '/' . $width . 'x' . $height . '/' . $mode . '=' . $filename;
        }
    }

    /**
     * Validate transformation parameters
     * 
     * @param array $params Transformation parameters
     * @return bool True if parameters are valid
     */
    public function validateParameters(array $params): bool
    {
        // Check required parameters
        if (empty($params['file']) || empty($params['width']) || empty($params['height'])) {
            return false;
        }

        // Validate dimensions
        if ($params['width'] <= 0 || $params['height'] <= 0) {
            return false;
        }

        // Validate mode
        $validModes = ['fix', 'fit', 'crop', 'fill', 'stretch'];
        if (!empty($params['mode']) && !in_array($params['mode'], $validModes)) {
            return false;
        }

        return true;
    }

    /**
     * Get supported transformation modes
     * 
     * @return array List of supported transformation modes
     */
    public function getSupportedModes(): array
    {
        return ['fix', 'fit', 'crop', 'fill', 'stretch'];
    }

    /**
     * Get default transformation mode
     * 
     * @return string Default transformation mode
     */
    public function getDefaultMode(): string
    {
        return 'fit';
    }
}
