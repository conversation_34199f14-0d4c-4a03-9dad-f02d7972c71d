<?php

namespace Package\FileUpload\Exceptions;

class ValidationException extends FileUploadException
{
    protected array $errors = [];

    public function __construct(string $message = "", array $errors = [], int $code = 0, ?\Exception $previous = null)
    {
        parent::__construct($message, $code, $previous);
        $this->errors = $errors;
    }

    public function getErrors(): array
    {
        return $this->errors;
    }

    public function setErrors(array $errors): void
    {
        $this->errors = $errors;
    }

    public function hasErrors(): bool
    {
        return !empty($this->errors);
    }
}
