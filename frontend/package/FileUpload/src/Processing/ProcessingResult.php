<?php

namespace Package\FileUpload\Processing;

class ProcessingResult
{
    protected bool $success;
    protected string $originalPath;
    protected array $processedFiles;
    protected array $metadata;
    protected array $errors;

    public function __construct(
        bool $success,
        string $originalPath,
        array $processedFiles = [],
        array $metadata = [],
        array $errors = []
    ) {
        $this->success = $success;
        $this->originalPath = $originalPath;
        $this->processedFiles = $processedFiles;
        $this->metadata = $metadata;
        $this->errors = $errors;
    }

    public function isSuccess(): bool
    {
        return $this->success;
    }

    public function getOriginalPath(): string
    {
        return $this->originalPath;
    }

    public function getProcessedFiles(): array
    {
        return $this->processedFiles;
    }

    public function getMetadata(): array
    {
        return $this->metadata;
    }

    public function getErrors(): array
    {
        return $this->errors;
    }

    public function hasErrors(): bool
    {
        return !empty($this->errors);
    }

    public function addProcessedFile(string $type, string $path, array $metadata = []): void
    {
        $this->processedFiles[$type] = [
            'path' => $path,
            'metadata' => $metadata,
        ];
    }

    public function getProcessedFile(string $type): ?array
    {
        return $this->processedFiles[$type] ?? null;
    }

    public function toArray(): array
    {
        return [
            'success' => $this->success,
            'original_path' => $this->originalPath,
            'processed_files' => $this->processedFiles,
            'metadata' => $this->metadata,
            'errors' => $this->errors,
        ];
    }

    public static function success(string $originalPath, array $processedFiles = [], array $metadata = []): self
    {
        return new self(true, $originalPath, $processedFiles, $metadata);
    }

    public static function failure(string $originalPath, array $errors): self
    {
        return new self(false, $originalPath, [], [], $errors);
    }
}
