<?php

namespace Package\FileUpload\Processing;

use Package\FileUpload\Contracts\ProcessorInterface;
use Package\FileUpload\Contracts\StorageDriverInterface;
use Package\FileUpload\Config\ConfigManager;
use Package\FileUpload\Exceptions\ProcessingException;

class ImageProcessor implements ProcessorInterface
{
    protected array $config;
    protected array $supportedMimeTypes = [
        'image/jpeg',
        'image/png',
        'image/gif',
        'image/webp',
        'image/svg+xml', // SVG support added but will be handled specially
    ];

    public function __construct(array $config = [])
    {
        $this->setConfig($config);
    }

    public function setConfig(array $config): void
    {
        $configManager = ConfigManager::getInstance();
        $defaultConfig = $configManager->getProcessingConfig();

        $this->config = array_merge($defaultConfig, $config);
    }

    public function process(string $filePath, array $options = []): ProcessingResult
    {
        try {
            $processedFiles = [];

            if (!$this->config['enabled']) {
                return ProcessingResult::success($filePath, $processedFiles);
            }

            // Check if this is an SVG file - skip processing for SVG files
            $mimeType = $this->getMimeType($filePath);
            if ($mimeType === 'image/svg+xml') {
                // SVG files don't need processing, just return success
                return ProcessingResult::success($filePath, $processedFiles);
            }

            // Generate thumbnails if enabled
            if ($this->config['thumbnails']['enabled'] ?? false) {
                $thumbnails = $this->generateThumbnails($filePath, $options);
                $processedFiles = array_merge($processedFiles, $thumbnails);
            }

            // Apply watermark if enabled
            if ($this->config['watermark']['enabled'] ?? false) {
                $watermarkedPath = $this->applyWatermark($filePath, $options);
                if ($watermarkedPath) {
                    $processedFiles['watermarked'] = [
                        'path' => $watermarkedPath,
                        'metadata' => ['type' => 'watermarked'],
                    ];
                }
            }

            // Compress original image if enabled
            if ($this->config['compress_images'] ?? true) {
                $this->compressOriginalImage($filePath, $options);
            }

            // Auto-orient image if enabled
            if ($this->config['auto_orient'] ?? false) {
                $this->autoOrientImage($filePath);
            }

            // Strip metadata if enabled
            if ($this->config['strip_metadata'] ?? false) {
                $this->stripMetadata($filePath);
            }

            return ProcessingResult::success($filePath, $processedFiles);
        } catch (\Exception $e) {
            return ProcessingResult::failure($filePath, [$e->getMessage()]);
        }
    }

    public function canProcess(string $mimeType): bool
    {
        return in_array($mimeType, $this->supportedMimeTypes);
    }

    public function getSupportedMimeTypes(): array
    {
        return $this->supportedMimeTypes;
    }

    protected function getMimeType(string $filePath): string
    {
        if (function_exists('finfo_file')) {
            $finfo = finfo_open(FILEINFO_MIME_TYPE);
            $mimeType = finfo_file($finfo, $filePath);
            finfo_close($finfo);

            if ($mimeType !== false) {
                return $mimeType;
            }
        }

        return 'application/octet-stream';
    }

    public function getName(): string
    {
        return 'image';
    }

    protected function generateThumbnails(string $filePath, array $options): array
    {
        $thumbnails = [];
        $sizes = $this->config['thumbnails']['sizes'] ?? [];
        $storageDriver = $options['storage_driver'] ?? null;
        $storedPath = $options['stored_path'] ?? '';

        foreach ($sizes as $sizeName => $sizeConfig) {
            try {
                $thumbnailPath = $this->createThumbnail(
                    $filePath,
                    $sizeName,
                    $sizeConfig,
                    $options
                );

                if ($thumbnailPath) {
                    // Generate relative path for thumbnail
                    $pathInfo = pathinfo($storedPath);
                    $thumbnailRelativePath = $pathInfo['dirname'] . '/' .
                        $pathInfo['filename'] . "_{$sizeName}." .
                        $pathInfo['extension'];

                    // Get URL for thumbnail if storage driver is available
                    $thumbnailUrl = $storageDriver ? $storageDriver->url($thumbnailRelativePath) : $thumbnailPath;

                    $thumbnails["thumbnail_{$sizeName}"] = [
                        'path' => $thumbnailRelativePath,
                        'url' => $thumbnailUrl,
                        'metadata' => [
                            'type' => 'thumbnail',
                            'size' => $sizeName,
                            'width' => $sizeConfig['width'],
                            'height' => $sizeConfig['height'],
                            'mode' => $sizeConfig['mode'] ?? 'crop',
                        ],
                    ];
                }
            } catch (\Exception $e) {
                // Log error but continue with other thumbnails
                error_log("Failed to create {$sizeName} thumbnail: " . $e->getMessage());
            }
        }

        return $thumbnails;
    }

    /**
     * Compress the original uploaded image based on configuration
     *
     * @param string $filePath Path to the original image file
     * @param array $options Processing options
     * @return void
     */
    protected function compressOriginalImage(string $filePath, array $options): void
    {
        // Get image info
        $imageInfo = getimagesize($filePath);
        if (!$imageInfo) {
            return; // Skip compression if we can't read the image
        }

        $imageType = $imageInfo[2];
        $quality = $this->config['quality'] ?? 85;

        // Skip compression for PNG images to preserve transparency
        // PNG compression is lossless and transparency issues can occur
        if ($imageType === IMAGETYPE_PNG) {
            return;
        }

        // Only compress JPEG and WebP images
        if (!in_array($imageType, [IMAGETYPE_JPEG, IMAGETYPE_WEBP])) {
            return;
        }

        // Create image resource from file
        $sourceImage = $this->createImageFromFile($filePath, $imageType);
        if (!$sourceImage) {
            return; // Skip compression if we can't create image resource
        }

        // Save compressed image back to the same file
        $this->saveImage($sourceImage, $filePath, $imageType, $quality);

        // Clean up
        imagedestroy($sourceImage);
    }

    protected function createThumbnail(string $filePath, string $sizeName, array $sizeConfig, array $options): ?string
    {
        $width = $sizeConfig['width'];
        $height = $sizeConfig['height'];
        $mode = $sizeConfig['mode'] ?? 'crop';
        $quality = $this->config['quality'] ?? 85;

        // Get image info
        $imageInfo = getimagesize($filePath);
        if (!$imageInfo) {
            throw new ProcessingException("Unable to get image information for: {$filePath}");
        }

        $originalWidth = $imageInfo[0];
        $originalHeight = $imageInfo[1];
        $imageType = $imageInfo[2];

        // Create source image
        $sourceImage = $this->createImageFromFile($filePath, $imageType);
        if (!$sourceImage) {
            throw new ProcessingException("Unable to create image resource from: {$filePath}");
        }

        // Calculate dimensions based on mode
        [$newWidth, $newHeight, $srcX, $srcY, $srcWidth, $srcHeight] =
            $this->calculateDimensions($originalWidth, $originalHeight, $width, $height, $mode);

        // Create thumbnail
        $thumbnail = imagecreatetruecolor($newWidth, $newHeight);

        // Preserve transparency for PNG and GIF
        if ($imageType === IMAGETYPE_PNG || $imageType === IMAGETYPE_GIF) {
            imagealphablending($thumbnail, false);
            imagesavealpha($thumbnail, true);
            $transparent = imagecolorallocatealpha($thumbnail, 255, 255, 255, 127);
            imagefill($thumbnail, 0, 0, $transparent);
        }

        // Resample the image
        imagecopyresampled(
            $thumbnail,
            $sourceImage,
            0,
            0,
            $srcX,
            $srcY,
            $newWidth,
            $newHeight,
            $srcWidth,
            $srcHeight
        );

        // Generate thumbnail path - save in same directory as original
        $pathInfo = pathinfo($filePath);
        $thumbnailDir = $pathInfo['dirname'];

        // Ensure thumbnail directory exists with proper permissions
        if (!is_dir($thumbnailDir)) {
            if (!mkdir($thumbnailDir, 0755, true)) {
                throw new ProcessingException("Failed to create thumbnail directory: {$thumbnailDir}");
            }
        }

        $thumbnailPath = $thumbnailDir . '/' .
            $pathInfo['filename'] . "_{$sizeName}." .
            $pathInfo['extension'];

        // Save thumbnail
        $saved = $this->saveImage($thumbnail, $thumbnailPath, $imageType, $quality);

        // Clean up
        imagedestroy($sourceImage);
        imagedestroy($thumbnail);

        return $saved ? $thumbnailPath : null;
    }

    protected function createImageFromFile(string $filePath, int $imageType)
    {
        switch ($imageType) {
            case IMAGETYPE_JPEG:
                return imagecreatefromjpeg($filePath);
            case IMAGETYPE_PNG:
                return imagecreatefrompng($filePath);
            case IMAGETYPE_GIF:
                return imagecreatefromgif($filePath);
            case IMAGETYPE_WEBP:
                return function_exists('imagecreatefromwebp') ? imagecreatefromwebp($filePath) : false;
            default:
                return false;
        }
    }

    protected function saveImage($image, string $filePath, int $imageType, int $quality): bool
    {
        switch ($imageType) {
            case IMAGETYPE_JPEG:
                return imagejpeg($image, $filePath, $quality);
            case IMAGETYPE_PNG:
                // PNG quality is 0-9, convert from 0-100
                $pngQuality = 9 - round(($quality / 100) * 9);
                return imagepng($image, $filePath, $pngQuality);
            case IMAGETYPE_GIF:
                return imagegif($image, $filePath);
            case IMAGETYPE_WEBP:
                return function_exists('imagewebp') ? imagewebp($image, $filePath, $quality) : false;
            default:
                return false;
        }
    }

    protected function calculateDimensions(int $originalWidth, int $originalHeight, int $targetWidth, int $targetHeight, string $mode): array
    {
        switch ($mode) {
            case 'fit':
                // Fit image within target dimensions, maintaining aspect ratio
                $ratio = min($targetWidth / $originalWidth, $targetHeight / $originalHeight);
                $newWidth = round($originalWidth * $ratio);
                $newHeight = round($originalHeight * $ratio);
                return [$newWidth, $newHeight, 0, 0, $originalWidth, $originalHeight];

            case 'stretch':
                // Stretch image to exact target dimensions
                return [$targetWidth, $targetHeight, 0, 0, $originalWidth, $originalHeight];

            case 'crop':
            default:
                // Crop image to exact target dimensions
                $ratio = max($targetWidth / $originalWidth, $targetHeight / $originalHeight);
                $srcWidth = round($targetWidth / $ratio);
                $srcHeight = round($targetHeight / $ratio);
                $srcX = round(($originalWidth - $srcWidth) / 2);
                $srcY = round(($originalHeight - $srcHeight) / 2);
                return [$targetWidth, $targetHeight, $srcX, $srcY, $srcWidth, $srcHeight];
        }
    }

    protected function applyWatermark(string $filePath, array $options): ?string
    {
        $watermarkConfig = $this->config['watermark'];
        $watermarkImage = $watermarkConfig['image'] ?? null;

        if (!$watermarkImage || !file_exists($watermarkImage)) {
            return null;
        }

        // This is a placeholder for watermark functionality
        // In a real implementation, you would overlay the watermark image
        return null;
    }

    protected function autoOrientImage(string $filePath): void
    {
        if (!function_exists('exif_read_data')) {
            return;
        }

        // Only try to read EXIF data from JPEG files
        $imageInfo = @getimagesize($filePath);
        if (!$imageInfo || $imageInfo[2] !== IMAGETYPE_JPEG) {
            return;
        }

        $exif = @exif_read_data($filePath);
        if (!$exif || !isset($exif['Orientation'])) {
            return;
        }

        // This is a placeholder for auto-orientation functionality
        // In a real implementation, you would rotate the image based on EXIF orientation
    }

    protected function stripMetadata(string $filePath): void
    {
        // This is a placeholder for metadata stripping functionality
        // In a real implementation, you would remove EXIF data and other metadata
    }
}
