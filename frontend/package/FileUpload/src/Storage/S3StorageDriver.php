<?php

namespace Package\FileUpload\Storage;

use Package\FileUpload\Contracts\StorageDriverInterface;
use Package\FileUpload\Exceptions\StorageException;
use Aws\S3\S3Client;
use Aws\Exception\AwsException;

class S3StorageDriver implements StorageDriverInterface
{
    protected array $config;
    protected S3Client $client;
    protected string $bucket;

    public function __construct(array $config = [])
    {
        $this->setConfig($config);
    }

    public function setConfig(array $config): void
    {
        $this->config = array_merge([
            'key' => '',
            'secret' => '',
            'region' => 'us-east-1',
            'bucket' => '',
            'url' => null,
            'endpoint' => null,
            'use_path_style_endpoint' => false,
            'visibility' => 'public',
            'options' => [],
        ], $config);

        $this->bucket = $this->config['bucket'];

        if (empty($this->bucket)) {
            throw new StorageException('S3 bucket name is required');
        }

        $clientConfig = [
            'version' => 'latest',
            'region' => $this->config['region'],
            'credentials' => [
                'key' => $this->config['key'],
                'secret' => $this->config['secret'],
            ],
        ];

        if (!empty($this->config['endpoint'])) {
            $clientConfig['endpoint'] = $this->config['endpoint'];
        }

        if ($this->config['use_path_style_endpoint']) {
            $clientConfig['use_path_style_endpoint'] = true;
        }

        try {
            $this->client = new S3Client($clientConfig);
        } catch (\Exception $e) {
            throw new StorageException("Failed to initialize S3 client: " . $e->getMessage());
        }
    }

    public function store(string $path, $contents, array $options = []): string
    {
        $params = array_merge([
            'Bucket' => $this->bucket,
            'Key' => $path,
            'Body' => $contents,
            'ACL' => $this->config['visibility'] === 'public' ? 'public-read' : 'private',
        ], $this->config['options'], $options);

        try {
            $result = $this->client->putObject($params);
            return $path;
        } catch (AwsException $e) {
            throw new StorageException("Failed to store file on S3: " . $e->getMessage());
        }
    }

    public function get(string $path): string
    {
        try {
            $result = $this->client->getObject([
                'Bucket' => $this->bucket,
                'Key' => $path,
            ]);

            return (string) $result['Body'];
        } catch (AwsException $e) {
            if ($e->getAwsErrorCode() === 'NoSuchKey') {
                throw new StorageException("File not found: {$path}");
            }
            throw new StorageException("Failed to get file from S3: " . $e->getMessage());
        }
    }

    public function exists(string $path): bool
    {
        try {
            $this->client->headObject([
                'Bucket' => $this->bucket,
                'Key' => $path,
            ]);
            return true;
        } catch (AwsException $e) {
            if ($e->getAwsErrorCode() === 'NotFound') {
                return false;
            }
            throw new StorageException("Failed to check file existence on S3: " . $e->getMessage());
        }
    }

    public function delete(string $path): bool
    {
        try {
            $this->client->deleteObject([
                'Bucket' => $this->bucket,
                'Key' => $path,
            ]);
            return true;
        } catch (AwsException $e) {
            throw new StorageException("Failed to delete file from S3: " . $e->getMessage());
        }
    }

    public function url(string $path): string
    {
        if (!empty($this->config['url'])) {
            return rtrim($this->config['url'], '/') . '/' . ltrim($path, '/');
        }

        try {
            return $this->client->getObjectUrl($this->bucket, $path);
        } catch (\Exception $e) {
            throw new StorageException("Failed to generate URL for S3 file: " . $e->getMessage());
        }
    }

    public function metadata(string $path): array
    {
        try {
            $result = $this->client->headObject([
                'Bucket' => $this->bucket,
                'Key' => $path,
            ]);

            return [
                'path' => $path,
                'size' => (int) $result['ContentLength'],
                'mime_type' => $result['ContentType'] ?? 'application/octet-stream',
                'last_modified' => $result['LastModified']->getTimestamp(),
                'etag' => trim($result['ETag'], '"'),
                'metadata' => $result['Metadata'] ?? [],
            ];
        } catch (AwsException $e) {
            if ($e->getAwsErrorCode() === 'NotFound') {
                throw new StorageException("File not found: {$path}");
            }
            throw new StorageException("Failed to get file metadata from S3: " . $e->getMessage());
        }
    }

    public function copy(string $from, string $to): bool
    {
        try {
            $this->client->copyObject([
                'Bucket' => $this->bucket,
                'Key' => $to,
                'CopySource' => $this->bucket . '/' . $from,
                'ACL' => $this->config['visibility'] === 'public' ? 'public-read' : 'private',
            ]);
            return true;
        } catch (AwsException $e) {
            throw new StorageException("Failed to copy file on S3: " . $e->getMessage());
        }
    }

    public function move(string $from, string $to): bool
    {
        if ($this->copy($from, $to)) {
            return $this->delete($from);
        }
        return false;
    }

    public function getName(): string
    {
        return 's3';
    }

    public function createDirectory(string $path, array $options = []): bool
    {
        // In S3, directories are virtual - they're created implicitly when files are uploaded
        // We can create an empty "directory marker" object to make the directory visible
        $directoryPath = rtrim($path, '/') . '/';

        try {
            $this->client->putObject([
                'Bucket' => $this->bucket,
                'Key' => $directoryPath,
                'Body' => '',
                'ACL' => $this->config['visibility'] === 'public' ? 'public-read' : 'private',
            ]);
            return true;
        } catch (AwsException $e) {
            throw new StorageException("Failed to create directory on S3: " . $e->getMessage());
        }
    }

    public function directoryExists(string $path): bool
    {
        $directoryPath = rtrim($path, '/') . '/';

        try {
            // Check if directory marker exists
            $this->client->headObject([
                'Bucket' => $this->bucket,
                'Key' => $directoryPath,
            ]);
            return true;
        } catch (AwsException $e) {
            if ($e->getAwsErrorCode() === 'NotFound') {
                // Check if any objects exist with this prefix
                try {
                    $result = $this->client->listObjectsV2([
                        'Bucket' => $this->bucket,
                        'Prefix' => $directoryPath,
                        'MaxKeys' => 1,
                    ]);
                    return !empty($result['Contents']);
                } catch (AwsException $listException) {
                    return false;
                }
            }
            return false;
        }
    }

    public function deleteDirectory(string $path, bool $recursive = false): bool
    {
        $directoryPath = rtrim($path, '/') . '/';

        try {
            if ($recursive) {
                // List all objects with this prefix and delete them
                $objects = $this->client->listObjectsV2([
                    'Bucket' => $this->bucket,
                    'Prefix' => $directoryPath,
                ]);

                if (!empty($objects['Contents'])) {
                    $deleteObjects = [];
                    foreach ($objects['Contents'] as $object) {
                        $deleteObjects[] = ['Key' => $object['Key']];
                    }

                    $this->client->deleteObjects([
                        'Bucket' => $this->bucket,
                        'Delete' => [
                            'Objects' => $deleteObjects,
                        ],
                    ]);
                }
            } else {
                // Only delete the directory marker if it exists
                $this->client->deleteObject([
                    'Bucket' => $this->bucket,
                    'Key' => $directoryPath,
                ]);
            }

            return true;
        } catch (AwsException $e) {
            throw new StorageException("Failed to delete directory from S3: " . $e->getMessage());
        }
    }

    public function listDirectory(string $path, bool $recursive = false): array
    {
        $directoryPath = rtrim($path, '/') . '/';

        try {
            $params = [
                'Bucket' => $this->bucket,
                'Prefix' => $directoryPath,
            ];

            if (!$recursive) {
                $params['Delimiter'] = '/';
            }

            $result = $this->client->listObjectsV2($params);
            $items = [];

            // Add files
            if (!empty($result['Contents'])) {
                foreach ($result['Contents'] as $object) {
                    // Skip the directory marker itself
                    if ($object['Key'] === $directoryPath) {
                        continue;
                    }

                    $items[] = [
                        'path' => $object['Key'],
                        'name' => basename($object['Key']),
                        'type' => 'file',
                        'size' => $object['Size'],
                        'last_modified' => $object['LastModified']->getTimestamp(),
                        'etag' => trim($object['ETag'], '"'),
                    ];
                }
            }

            // Add subdirectories (only for non-recursive listing)
            if (!$recursive && !empty($result['CommonPrefixes'])) {
                foreach ($result['CommonPrefixes'] as $prefix) {
                    $dirName = rtrim(str_replace($directoryPath, '', $prefix['Prefix']), '/');
                    $items[] = [
                        'path' => $prefix['Prefix'],
                        'name' => $dirName,
                        'type' => 'directory',
                        'size' => null,
                        'last_modified' => null,
                    ];
                }
            }

            return $items;
        } catch (AwsException $e) {
            throw new StorageException("Failed to list directory on S3: " . $e->getMessage());
        }
    }
}
