<?php

namespace Package\FileUpload\Storage;

use Package\FileUpload\Contracts\StorageDriverInterface;
use Package\FileUpload\Exceptions\StorageException;

class LocalStorageDriver implements StorageDriverInterface
{
    protected array $config;
    protected string $root;
    protected string $baseUrl;

    public function __construct(array $config = [])
    {
        $this->setConfig($config);
    }

    public function setConfig(array $config): void
    {
        $this->config = array_merge([
            'root' => $this->getDefaultRoot(),
            'url' => '/uploads',
            'permissions' => [
                'file' => ['public' => 0644, 'private' => 0600],
                'dir' => ['public' => 0755, 'private' => 0700],
            ],
        ], $config);

        $this->root = rtrim($this->config['root'], '/');
        $this->baseUrl = rtrim($this->config['url'], '/');

        $this->ensureDirectoryExists($this->root);
    }

    public function store(string $path, $contents, array $options = []): string
    {
        $fullPath = $this->getFullPath($path);
        $directory = dirname($fullPath);

        $this->ensureDirectoryExists($directory);

        if (is_resource($contents)) {
            $result = file_put_contents($fullPath, $contents);
        } else {
            $result = file_put_contents($fullPath, $contents);
        }

        if ($result === false) {
            throw new StorageException("Failed to store file at: {$path}");
        }

        // Set file permissions
        $visibility = $options['visibility'] ?? 'public';
        $permissions = $this->config['permissions']['file'][$visibility] ?? 0644;
        chmod($fullPath, $permissions);

        return $path;
    }

    public function get(string $path): string
    {
        $fullPath = $this->getFullPath($path);

        if (!file_exists($fullPath)) {
            throw new StorageException("File not found: {$path}");
        }

        $contents = file_get_contents($fullPath);

        if ($contents === false) {
            throw new StorageException("Failed to read file: {$path}");
        }

        return $contents;
    }

    public function exists(string $path): bool
    {
        return file_exists($this->getFullPath($path));
    }

    public function delete(string $path): bool
    {
        $fullPath = $this->getFullPath($path);

        if (!file_exists($fullPath)) {
            return true; // File doesn't exist, consider it deleted
        }

        return unlink($fullPath);
    }

    public function url(string $path): string
    {
        return $this->baseUrl . '/' . ltrim($path, '/');
    }

    public function metadata(string $path): array
    {
        $fullPath = $this->getFullPath($path);

        if (!file_exists($fullPath)) {
            throw new StorageException("File not found: {$path}");
        }

        $stat = stat($fullPath);
        $mimeType = $this->getMimeType($fullPath);

        return [
            'path' => $path,
            'size' => $stat['size'],
            'mime_type' => $mimeType,
            'last_modified' => $stat['mtime'],
            'permissions' => substr(sprintf('%o', fileperms($fullPath)), -4),
            'is_file' => is_file($fullPath),
            'is_dir' => is_dir($fullPath),
        ];
    }

    public function copy(string $from, string $to): bool
    {
        $fromPath = $this->getFullPath($from);
        $toPath = $this->getFullPath($to);

        if (!file_exists($fromPath)) {
            throw new StorageException("Source file not found: {$from}");
        }

        $this->ensureDirectoryExists(dirname($toPath));

        return copy($fromPath, $toPath);
    }

    public function move(string $from, string $to): bool
    {
        $fromPath = $this->getFullPath($from);
        $toPath = $this->getFullPath($to);

        if (!file_exists($fromPath)) {
            throw new StorageException("Source file not found: {$from}");
        }

        $this->ensureDirectoryExists(dirname($toPath));

        return rename($fromPath, $toPath);
    }

    public function getName(): string
    {
        return 'local';
    }

    public function createDirectory(string $path, array $options = []): bool
    {
        $fullPath = $this->getFullPath($path);

        if (is_dir($fullPath)) {
            return true; // Directory already exists
        }

        $visibility = $options['visibility'] ?? 'public';
        $permissions = $this->config['permissions']['dir'][$visibility] ?? 0755;

        if (!mkdir($fullPath, $permissions, true)) {
            throw new StorageException("Failed to create directory: {$path}");
        }

        return true;
    }

    public function directoryExists(string $path): bool
    {
        return is_dir($this->getFullPath($path));
    }

    public function deleteDirectory(string $path, bool $recursive = false): bool
    {
        $fullPath = $this->getFullPath($path);

        if (!is_dir($fullPath)) {
            return true; // Directory doesn't exist, consider it deleted
        }

        if ($recursive) {
            return $this->deleteDirectoryRecursive($fullPath);
        } else {
            // Only delete if empty
            return rmdir($fullPath);
        }
    }

    public function listDirectory(string $path, bool $recursive = false): array
    {
        $fullPath = $this->getFullPath($path);

        if (!is_dir($fullPath)) {
            throw new StorageException("Directory not found: {$path}");
        }

        $items = [];

        if ($recursive) {
            $iterator = new \RecursiveIteratorIterator(
                new \RecursiveDirectoryIterator($fullPath, \RecursiveDirectoryIterator::SKIP_DOTS),
                \RecursiveIteratorIterator::SELF_FIRST
            );
        } else {
            $iterator = new \DirectoryIterator($fullPath);
        }

        foreach ($iterator as $item) {
            // Skip dots for both iterator types
            if ($recursive) {
                // For RecursiveIteratorIterator, check if it's a dot directory
                if ($item->getFilename() === '.' || $item->getFilename() === '..') {
                    continue;
                }
                // Get the relative path from the iterator
                $subPath = str_replace($fullPath . '/', '', $item->getPathname());
                $relativePath = $path . '/' . $subPath;
            } else {
                // For DirectoryIterator, use isDot() method
                if ($item->isDot()) {
                    continue;
                }
                $relativePath = $path . '/' . $item->getFilename();
            }

            $items[] = [
                'path' => $relativePath,
                'name' => $item->getFilename(),
                'type' => $item->isDir() ? 'directory' : 'file',
                'size' => $item->isFile() ? $item->getSize() : null,
                'last_modified' => $item->getMTime(),
                'permissions' => substr(sprintf('%o', $item->getPerms()), -4),
            ];
        }

        return $items;
    }

    protected function getFullPath(string $path): string
    {
        return $this->root . '/' . ltrim($path, '/');
    }

    protected function ensureDirectoryExists(string $directory): void
    {
        if (!is_dir($directory)) {
            $permissions = $this->config['permissions']['dir']['public'] ?? 0755;

            if (!mkdir($directory, $permissions, true)) {
                throw new StorageException("Failed to create directory: {$directory}");
            }
        }
    }

    /**
     * Get default root path dynamically
     */
    protected function getDefaultRoot(): string
    {
        // Try to determine the best uploads path dynamically
        $possiblePaths = [
            // Current working directory + uploads
            getcwd() . '/uploads',
            // Script directory + uploads
            dirname($_SERVER['SCRIPT_FILENAME'] ?? __FILE__) . '/uploads',
            // Document root + uploads (for web environments)
            ($_SERVER['DOCUMENT_ROOT'] ?? '') . '/uploads',
            // Temp directory as last resort
            sys_get_temp_dir() . '/fileupload_uploads',
        ];

        foreach ($possiblePaths as $path) {
            $dir = dirname($path);
            if (is_dir($dir) && is_writable($dir)) {
                return $path;
            }
        }

        // Final fallback - use temp directory
        return sys_get_temp_dir() . '/fileupload_uploads';
    }

    protected function deleteDirectoryRecursive(string $directory): bool
    {
        $iterator = new \RecursiveIteratorIterator(
            new \RecursiveDirectoryIterator($directory, \RecursiveDirectoryIterator::SKIP_DOTS),
            \RecursiveIteratorIterator::CHILD_FIRST
        );

        foreach ($iterator as $file) {
            if ($file->isDir()) {
                if (!rmdir($file->getRealPath())) {
                    return false;
                }
            } else {
                if (!unlink($file->getRealPath())) {
                    return false;
                }
            }
        }

        return rmdir($directory);
    }

    protected function getMimeType(string $path): string
    {
        if (function_exists('finfo_file')) {
            $finfo = finfo_open(FILEINFO_MIME_TYPE);
            $mimeType = finfo_file($finfo, $path);
            finfo_close($finfo);

            if ($mimeType !== false) {
                return $mimeType;
            }
        }

        // Fallback to extension-based detection
        $extension = strtolower(pathinfo($path, PATHINFO_EXTENSION));
        $mimeTypes = [
            'jpg' => 'image/jpeg',
            'jpeg' => 'image/jpeg',
            'png' => 'image/png',
            'gif' => 'image/gif',
            'webp' => 'image/webp',
            'pdf' => 'application/pdf',
            'txt' => 'text/plain',
            'html' => 'text/html',
            'css' => 'text/css',
            'js' => 'application/javascript',
        ];

        return $mimeTypes[$extension] ?? 'application/octet-stream';
    }
}
