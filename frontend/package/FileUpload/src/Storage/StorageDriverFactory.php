<?php

namespace Package\FileUpload\Storage;

use Package\FileUpload\Contracts\StorageDriverInterface;
use Package\FileUpload\Exceptions\StorageException;
use Package\FileUpload\Config\ConfigManager;

class StorageDriverFactory
{
    protected static array $drivers = [
        'local' => LocalStorageDriver::class,
        's3' => S3StorageDriver::class,
        'gcs' => GoogleCloudStorageDriver::class,
        'azure' => AzureStorageDriver::class,
    ];

    protected static array $instances = [];

    public static function create(string $driver, array $config = []): StorageDriverInterface
    {
        $cacheKey = $driver . '_' . md5(serialize($config));

        if (isset(self::$instances[$cacheKey])) {
            return self::$instances[$cacheKey];
        }

        if (!isset(self::$drivers[$driver])) {
            throw new StorageException("Unsupported storage driver: {$driver}");
        }

        $driverClass = self::$drivers[$driver];

        if (!class_exists($driverClass)) {
            throw new StorageException("Storage driver class not found: {$driverClass}");
        }

        $instance = new $driverClass($config);

        if (!$instance instanceof StorageDriverInterface) {
            throw new StorageException("Storage driver must implement StorageDriverInterface");
        }

        self::$instances[$cacheKey] = $instance;

        return $instance;
    }

    public static function createFromConfig(string $driver = null): StorageDriverInterface
    {
        $configManager = ConfigManager::getInstance();
        
        if ($driver === null) {
            $driver = $configManager->get('default', 'local');
        }

        $config = $configManager->getDriverConfig($driver);

        if (empty($config)) {
            throw new StorageException("No configuration found for storage driver: {$driver}");
        }

        return self::create($driver, $config);
    }

    public static function registerDriver(string $name, string $className): void
    {
        if (!class_exists($className)) {
            throw new StorageException("Driver class not found: {$className}");
        }

        if (!is_subclass_of($className, StorageDriverInterface::class)) {
            throw new StorageException("Driver class must implement StorageDriverInterface");
        }

        self::$drivers[$name] = $className;
    }

    public static function getAvailableDrivers(): array
    {
        return array_keys(self::$drivers);
    }

    public static function hasDriver(string $driver): bool
    {
        return isset(self::$drivers[$driver]);
    }

    public static function clearInstances(): void
    {
        self::$instances = [];
    }
}
