<?php

use Core\Router;
use Middleware\Authenticate;
use Modules\Templates\Controllers\TemplatesController;
use Modules\Templates\Controllers\PreviewController;

// Remove the return and directly define routes
$router->group('templates', function($router) {
    $router->get('/', [TemplatesController::class, 'index'], [Authenticate::class]);
    $router->get('/dataTable', [TemplatesController::class, 'index'], [Authenticate::class]);

    $router->get('create', [TemplatesController::class,'create'], [Authenticate::class]);
    
    // Preview routes
    $router->get('/preview', [PreviewController::class, 'index']); // No auth for preview
    $router->get('/builder', [PreviewController::class, 'builder'], [Authenticate::class]);
    $router->post('/preview/ajax', [PreviewController::class, 'ajax'], [Authenticate::class]);
});