<?php

namespace Modules\Templates\Services;

use App\Services\ApiClient;
use Core\ServiceContainer;
use Core\Request;

class TemplatesService
{
    protected $api;

    public function __construct()
    {
        $this->api = ServiceContainer::resolve(ApiClient::class);
    }

    public function getData(Request $request)
    {

        $sort = $request->get('sort') ?? '-id';

        $itemsPerPage = $request->get('limit') ?? 10;

        $params = [
            'page' => $request->get('page') ?? 1,
            'sort' => $sort,
            'limit' => $itemsPerPage
        ];
        $response = $this->api->get('templates',$params, true);

        return $response;
    }

    public function getTemplateById($id){
        $response = $this->api->get('templates/' . $id, [], true);
        return $response;
    }
}
