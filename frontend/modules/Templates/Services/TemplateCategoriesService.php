<?php

namespace Modules\Templates\Services;

use Core\Service;
use Core\Request;

class TemplateCategoriesService  extends Service 
{
    
    /**
     * Summary of getData
     * @param mixed $params
     * @return array{data: mixed, success: bool}
     */
    public function getData($params = []):array {
        $response = $this->api->get('template-categories', $params);
        return [
            'success' => true,
            'data' => $response['data'] ?? []
        ];
    }

    /**
     * Summary of get
     * @param mixed $id
     * @return mixed
     */
    public function get($id):mixed {
        return $this->api->get('template-categories/' . $id);
    }

    /**
     * Summary of create
     * @param mixed $data
     * @return mixed
     */
    public function create($data): mixed {
        return $this->api->post('template-categories', $data);
    }

    /**
     * Summary of update
     * @param mixed $id
     * @param mixed $data
     * @return mixed
     */
    public function update($id, $data):mixed {
        return $this->api->put('template-categories/' . $id, $data);
    }

    /**
     * Summary of delete
     * @param mixed $id
     * @return mixed
     */
    public function delete($id):mixed {
        return $this->api->delete('template-categories/' . $id);
    }
}