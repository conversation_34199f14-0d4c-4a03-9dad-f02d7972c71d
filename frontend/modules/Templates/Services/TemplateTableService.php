<?php

namespace Modules\Templates\Services;

use Core\Request;
use Package\GridTable\Src\DataTable;
use Package\GridTable\Src\Helpers\TableHelper;

class TemplateTableService
{
    private array $templatesList;
    private array $pagination;
    private Request $request;

    public function __construct($templatesList, $pagination, Request $request)
    {
        $this->templatesList = $templatesList;
        $this->pagination = $pagination;
        $this->request = $request;
    }

    public function renderTable()
    {

        try {
            $default = $this->request->cookie('preferredView') ?? 'grid';
            $defaultView = $this->request->get('view', $default);

            $direction = $this->request->get('direction', 'desc');

            // Validate sort parameter 
            $sortColumns = ['id', 'name', 'pages_count', 'created_at', 'updated_at'];

            $table = new DataTable($this->templatesList, $this->getColumns(),  $this->request, $this->pagination, $sortColumns, $direction);

            $table->setAjaxUrl('templates/dataTable');

            $table->setTableId('templateTable')
                ->setBreadcrumbs('Templates', [
                    ['label' => 'Home', 'url' => url('/dashboard')],
                    ['label' => 'Templates']
                ])
                ->setSearchConfig(true, 'templateSearch')
                ->setActionButtons($this->getActionButtons())
                ->setRowActions($this->getRowActionButtons())
                ->setGridConfig($this->getGridConfig())
                ->setDefaultView($defaultView);

            return [
                'table' => $table->render(),
                'view' => $defaultView,
                'success' => true,
                'message' => ''
            ];
        } catch (\Throwable $th) {
            return [
                'table' => '',
                'view' => $defaultView,
                'success' => false,
                'message' => $th->getMessage()
            ];
        }
    }

    private function getColumns()
    {
        return [
            'name' => [
                'label' => 'Templates',
                'sortable' => true,
                'formatter' => function ($value, $row) {
                    return TableHelper::formatImageWithText($row, [
                        'imageKey' => 'preview_image',
                        'titleKey' => 'name',
                        'subtitleKey' => 'id',
                        'subtitlePrefix' => 'ID: TEMP-',
                        'placeholderText' => $row['name'] ?? 'Template'
                    ]);
                }
            ],
            'categories' => [
                'label' => 'Category',
                'sortable' => false,
                'cellClass' => 'categories',
                'formatter' => function ($value, $row) {
                    return TableHelper::formatCategoryBadges($row['categories'] ?? []);
                }
            ],
            'pages_count' => [
                'label' => 'Pages',
                'sortable' => true,
                'cellClass' => 'pages_count',
            ],
            'updated_at' => [
                'label' => 'Last Updated',
                'sortable' => true,
                'cellClass' => 'updated_at',
                'formatter' => function ($value) {
                    return TableHelper::formatDate($value);
                }
            ],
            'created_at' => [
                'label' => 'Created At',
                'cellClass' => 'created_at',
                'sortable' => true,
                'formatter' => function ($value) {
                    return TableHelper::formatDate($value);
                }
            ]
        ];
    }

    private function getRowActionButtons()
    {
        return [
            [
                'icon' => '<path d="M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0"></path><circle cx="12" cy="12" r="3"></circle>',
                'tooltip' => 'Preview',
                'hoverTextColor' => 'hover:text-green-700',
                'type' => 'button',
                'attributes' => [
                    'data-preview-id' => function ($row) {
                        return $row['id'] ?? '';
                    },
                    'data-modal-target' => 'templatePreviewModal',
                    'data-modal-toggle' => 'templatePreviewModal'
                ]
            ],
            [
                'icon' => '<path d="M21.174 6.812a1 1 0 0 0-3.986-3.987L3.842 16.174a2 2 0 0 0-.5.83l-1.321 4.352a.5.5 0 0 0 .623.622l4.353-1.32a2 2 0 0 0 .83-.497z"/><path d="m15 5 4 4"/>',
                'tooltip' => 'Edit',
                'hoverTextColor' => 'hover:text-blue-700',
                'urlFormatter' => function ($row) {
                    return url("/templates/form/" . $row['id']);
                }
            ],
            [
                'icon' => '<path d="m18 16 4-4-4-4"/><path d="m6 8-4 4 4 4"/><path d="m14.5 4-5 16"/>',
                'tooltip' => 'Template Builder',
                'hoverTextColor' => 'hover:text-purple-700',
                'type' => 'link',
                'urlFormatter' => function ($row) {
                    return url("/templates/builder?id=" . $row['id']);
                }
            ],
            [
                'icon' => '<path d="M3 6h18"/><path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"/><path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"/>',
                'tooltip' => 'Delete',
                'hoverTextColor' => 'hover:text-red-700',
                'type' => 'button',
                'attributes' => [
                    'data-id' => function ($row) {
                        return $row['id'] ?? '';
                    },
                    'data-modal-target' => 'deleteModal',
                    'data-modal-toggle' => 'deleteModal'
                ]
            ]
        ];
    }

    private function getActionButtons()
    {
        return [
            [
                'label' => 'Create New Template',
                'icon' => 'plus',
                'attributes' => [
                    'onclick' => "window.location.href='" . url('/templates/form') . "'"
                ]
            ]
        ];
    }

    private function getGridConfig()
    {
        return [
            'enabled' => true,
            'columns' => 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4',
            'imageKey' => 'preview_image',
            'titleKey' => 'name',
            'subtitleKey' => 'id',
            'descriptionKey' => 'detailed_description',
            'layout' => 'default',
            'cardTemplate' => function ($template, $config) {
                return $this->getCardTemplate($template, $config);
            }
        ];
    }

    private function getCardTemplate($template, $config)
    {
        $id = $template['id'] ?? '';
        $name = htmlspecialchars($template['name'] ?? 'Untitled Template');
        $previewImage = $template['preview_image'] ?? '';
        $pageCount = $template['page_count'] ?? 0;
        $updatedAt = $template['updated_at'] ?? '';
        $categories = $template['categories'] ?? [];
        $description = htmlspecialchars($template['detailed_description'] ?? '');

        // Get category names
        $categoryNames = [];
        if (is_array($categories)) {
            foreach ($categories as $category) {
                if (isset($category['name'])) {
                    $categoryNames[] = $category['name'];
                }
            }
        }
        $categoryText = !empty($categoryNames) ? implode(', ', $categoryNames) : 'Uncategorized';
        $firstCategory = !empty($categoryNames) ? $categoryNames[0] : 'Template';

        // Format time ago
        $timeAgo = '';
        if ($updatedAt) {
            $time = strtotime($updatedAt);
            $now = time();
            $diff = $now - $time;

            if ($diff < 3600) {
                $timeAgo = floor($diff / 60) . ' min ago';
            } elseif ($diff < 86400) {
                $timeAgo = floor($diff / 3600) . ' hours ago';
            } else {
                $timeAgo = floor($diff / 86400) . ' days ago';
            }
        }

        // Default image if no preview
        $imageUrl = !empty($previewImage) ? url($previewImage) : 'https://placehold.co/600x300/e5e7eb/9ca3af?text=' . urlencode($name);

        return '
        <div class="template-card group relative bg-white rounded-xl border border-gray-200 overflow-hidden transition-all duration-300 hover:scale-[1.02] hover:-translate-y-1" data-category="' . htmlspecialchars(strtolower($firstCategory)) . '" data-id="' . $id . '">
            <div class="relative aspect-[4/2] overflow-hidden">
                <img src="' . $imageUrl . '" alt="' . $name . '" class="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110">
                
                <!-- Hover Overlay with Description -->
                <div class="absolute inset-0 bg-gradient-to-t from-black/80 via-black/50 to-transparent opacity-0 group-hover:opacity-100 transition-all duration-300">
                    <div class="absolute bottom-0 left-0 right-0 p-4">
                        <div class="flex items-start justify-between gap-4">
                            <div class="flex-1 space-y-2">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                    ' . htmlspecialchars($firstCategory) . '
                                </span>
                                <p class="text-sm text-gray-100 line-clamp-2">' . (strlen($description) > 80 ? substr($description, 0, 80) . '...' : $description) . '</p>
                            </div>
                            <div class="flex items-center space-x-2 shrink-0 self-end">
                                <button data-preview-id="' . $id . '" class="previewBtn inline-flex items-center justify-center w-9 h-9 rounded-full bg-white/20 backdrop-blur-sm hover:bg-white/30 transition-colors" data-modal-target="templatePreviewModal" data-modal-toggle="templatePreviewModal">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-5 h-5 text-white">
                                        <path d="M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0"></path>
                                        <circle cx="12" cy="12" r="3"></circle>
                                    </svg>
                                </button>
                                <button onclick="window.location.href=\'' . url('/templates/form/' . $id) . '\'" class="useBtn inline-flex items-center justify-center w-9 h-9 rounded-full bg-white text-gray-700 hover:bg-gray-50 transition-colors">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <path d="M21.174 6.812a1 1 0 0 0-3.986-3.987L3.842 16.174a2 2 0 0 0-.5.83l-1.321 4.352a.5.5 0 0 0 .623.622l4.353-1.32a2 2 0 0 0 .83-.497z"></path>
                                        <path d="m15 5 4 4"></path>
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Card Content - Always Visible -->
            <div class="p-4">
                <!-- Title -->
                <h3 class="text-lg font-semibold text-gray-900 mb-2">' . $name . '</h3>
                <!-- Metadata -->
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <span class="flex items-center text-sm text-gray-500">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-4 h-4 mr-1">
                                <rect width="18" height="7" x="3" y="3" rx="1"></rect>
                                <rect width="9" height="7" x="3" y="14" rx="1"></rect>
                                <rect width="5" height="7" x="16" y="14" rx="1"></rect>
                            </svg>
                            ' . $pageCount . ' Pages
                        </span>
                        <span class="flex items-center text-sm text-gray-500">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-4 h-4 mr-1">
                                <circle cx="12" cy="12" r="10"></circle>
                                <polyline points="12 6 12 12 16 14"></polyline>
                            </svg>
                            ' . $timeAgo . '
                        </span>
                    </div>
                    <div class="flex -space-x-2">
                        <img class="w-6 h-6 rounded-full border-2 border-white" src="https://ui-avatars.com/api/?name=Admin&background=0C5BE2&color=ffffff" alt="User">
                    </div>
                </div>
            </div>
        </div>';
    }
}
