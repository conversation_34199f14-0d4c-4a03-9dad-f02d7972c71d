<!-- Delete Form -->
<form id="deleteForm" method="POST" style="display: none;">
    <input type="hidden" name="delete" value="1">
    <input type="hidden" name="id" id="deleteId">
</form>

<!-- Template Preview Modal -->
<div id="templatePreviewModal" tabindex="-1" class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
    <div class="relative w-full max-w-7xl max-h-full">
        <!-- Modal content -->
        <div class="relative bg-white rounded-lg shadow-sm">
            <!-- Modal header -->
            <div class="flex items-center justify-between p-4 md:p-5 border-b rounded-t border-gray-200">
                <h3 class="text-xl font-medium text-gray-900">Template Title</h3>
                <button type="button" class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center" onclick="closeTemplatePreview()">
                    <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"/>
                    </svg>
                    <span class="sr-only">Close modal</span>
                </button>
            </div>
            <!-- Modal body -->
            <div class="space-y-4">
                <!-- Two Column Layout -->
                <div class="flex flex-col md:flex-row">
                    <!-- Left Side - Image Gallery -->
                    <div class="w-full md:w-3/5 bg-gray-50">
                        <div class="h-[700px] overflow-y-auto scroll-smooth">
                            Template screenshots
                        </div>
                    </div>

                    <!-- Right Side - Template Info -->
                    <div class="w-full md:w-2/5">
                        <div class="h-[700px] overflow-y-auto">
                            <div class="p-8">
                                <!-- Template Header -->
                                <div class="space-y-4">
                                    <div class="flex flex-wrap gap-6 mb-4">
                                        <div class="inline-flex items-center px-3 py-1.5 bg-gray-50 rounded-md border border-gray-200">
                                            <svg class="w-4 h-4 mr-2 text-blue-600" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                <rect width="7" height="7" x="3" y="3" rx="1"/>
                                                <rect width="7" height="7" x="14" y="3" rx="1"/>
                                                <rect width="7" height="7" x="14" y="14" rx="1"/>
                                                <rect width="7" height="7" x="3" y="14" rx="1"/>
                                            </svg>
                                            <span class="text-sm text-gray-600">Category: <span class="preview-category font-semibold text-gray-900 ml-1"></span></span>
                                        </div>
                                        <div class="inline-flex items-center px-3 py-1.5 bg-gray-50 rounded-md border border-gray-200">
                                            <svg class="w-4 h-4 mr-2 text-blue-600" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                <path d="M20 7h-3a2 2 0 0 1-2-2V2"/>
                                                <path d="M9 18a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h7l4 4v10a2 2 0 0 1-2 2Z"/>
                                                <path d="M3 7.6v12.8A1.6 1.6 0 0 0 4.6 22h9.8"/>
                                            </svg>
                                            <span class="text-sm text-gray-600">Pages: <span class="preview-sections font-semibold text-gray-900 ml-1"></span></span>
                                        </div>
                                    </div>
                                    <p class="text-gray-600 leading-relaxed detailedDescription">
                                        Detailed Description
                                    </p>
                                </div>                          
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- Modal footer -->
            <div class="flex items-center p-4 md:p-5 border-t border-gray-200 rounded-b">
                <button data-modal-hide="templatePreviewModal" type="button" class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center">Use Template</button>
                <button type="button" onclick="closeTemplatePreview()" class="py-2.5 px-5 ms-3 text-sm font-medium text-gray-900 focus:outline-none bg-white rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-100">Close</button>
            </div>
        </div>
    </div>
</div>



<!-- Delete Modal -->
<!-- Delete Confirmation Modal -->
<div id="deleteModal" tabindex="-1" aria-hidden="true" class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
    <div class="relative p-4 w-full max-w-md max-h-full">
        <div class="relative bg-white rounded-lg shadow">
            <button type="button" class="absolute top-3 end-2.5 text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center" data-modal-hide="deleteModal">
                <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"/>
                </svg>
                <span class="sr-only">Close modal</span>
            </button>
            <div class="p-4 md:p-5 text-center">
                <svg class="mx-auto mb-4 text-gray-400 w-12 h-12" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 20">
                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 11V6m0 8h.01M19 10a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"/>
                </svg>
                <h3 class="mb-5 text-lg font-normal text-gray-500">Are you sure you want to delete this item?</h3>
                <div class="flex justify-center gap-4">
                    <button type="button" id="confirmDeleteBtn" class="text-white bg-red-600 hover:bg-red-800 focus:ring-4 focus:outline-none focus:ring-red-300 font-medium rounded-lg text-sm inline-flex items-center px-5 py-2.5 text-center">
                        Yes, I'm sure
                    </button>
                    <button type="button" data-modal-hide="deleteModal" class="text-gray-500 bg-white hover:bg-gray-100 focus:ring-4 focus:outline-none focus:ring-gray-200 rounded-lg border border-gray-200 text-sm font-medium px-5 py-2.5 hover:text-gray-900 focus:z-10">
                        No, cancel
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>