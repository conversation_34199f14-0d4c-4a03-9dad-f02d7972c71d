<?php

use Core\View;
    // Add view-specific scripts
    add_style(asset('modules/Templates/Assets/css/form.css'));
    add_script(asset('modules/Templates/Assets/js/form.js'));
?>


<div class="flex gap-8 px-6">
    <!-- Left Sidebar - Steps Navigation -->
    <div class="w-[275px] border-r border-gray-200 pt-6 pr-6 flex flex-col justify-between">
        <?php View::include('modules.Templates.Views.partials.sidebar', [
            'isEdit' => $isEdit,
        ]) ?>
    </div>

    <?php View::include('modules.Templates.Views.partials.content', [
        'isEdit' => $isEdit,
        'template' => $template,
        'globalAssetsList' => $globalAssetsList,
        'selectedGlobalAssets' => $selectedGlobalAssets,
        'categories' => $categories
    ]) ?>
</div>
