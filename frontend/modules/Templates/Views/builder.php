<?php
    add_style(asset('modules/Templates/Assets/css/builder.css'));
    add_script(asset('modules/Templates/Assets/js/builder.js'));
    
    // Generate blocks HTML
    $globalBlockList = '';
    if (!empty($blocks)) {
        ob_start();
        
        if (empty($blocks)) {
            echo '<div class="text-center py-4">';
            echo '<p class="text-gray-500">No blocks found</p>';
            echo '</div>';
        } else {
            foreach ($blocks as $block) {
                if (!isset($block['id']) || !isset($block['name'])) {
                    continue;
                }
                
                $blockType = isset($block['slug']) ? $block['slug'] : 'default-block';
                $blockName = isset($block['name']) ? $block['name'] : 'Unnamed Block';
                $blockImage = isset($block['image']) ? asset($block['image']) : 'https://placehold.co/600x300/e5e7eb/9ca3af?text=' . urlencode($blockName);
                $blockDesc = isset($block['description']) ? $block['description'] : '';
                $categoryName = isset($block['category_name']) ? $block['category_name'] : 'General';
                ?>
                <div class="block-item cursor-move bg-white border border-gray-200 rounded-lg mb-2 hover:border-blue-500 hover:shadow-sm overflow-hidden" 
                draggable="true" 
                data-block-type="<?= htmlspecialchars($blockType) ?>" 
                data-block-id="<?= htmlspecialchars($block['id']) ?>"
                data-category-id="<?= htmlspecialchars($block['category_id'] ?? '') ?>"
                data-category-name="<?= htmlspecialchars($categoryName) ?>">
                    <div class="flex items-start relative">
                        <img src="<?= htmlspecialchars($blockImage) ?>" alt="Block Image" class="w-full h-32 object-cover">
                        <p class="hidden"><?= htmlspecialchars($blockDesc) ?></p>
                        <div class="absolute top-2 right-2">
                            <span class="inline-flex items-center px-2 py-1 text-xs font-medium text-blue-800 bg-blue-100 rounded-full">
                                <?= htmlspecialchars($categoryName) ?>
                            </span>
                        </div>
                    </div>
                    <h3 class="text-sm font-medium text-gray-900 w-full px-3 py-2 bg-gradient-to-t from-[#f7f7f7] to-white border-t border-gray-100"><?= htmlspecialchars($blockName) ?></h3>
                </div>
                <?php
            }
        }
        
        $globalBlockList = ob_get_clean();
    }
?>

<div class="container-fluid">
    <!-- Main Content Area -->
    <div class="flex h-[calc(100dvh-110px)]">
        <!-- Sidebar -->
        <div id="builderSidebar" class="w-[320px] bg-white border-r border-gray-200 flex flex-col transition-all duration-300 ease-in-out">
            <!-- Sidebar Header -->
            <div class="border-b border-gray-200 p-4">
                <h2 class="text-lg font-semibold text-gray-900">Blocks</h2>
                <p class="text-sm text-gray-600">Drag blocks to build your template</p>
            </div>
            
            <!-- Search and Filter -->
            <div class="p-4 border-b border-gray-200">
                <!-- Search -->
                <div class="mb-3">
                    <input type="text" id="blockSearch" placeholder="Search blocks..." 
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                </div>
                
                <!-- Category Filter -->
                <div>
                    <select id="blockCategoryFilter" class="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <option value="">All Categories</option>
                        <?php if (!empty($blockCategories)): ?>
                            <?php foreach ($blockCategories as $category): ?>
                                <option value="<?= htmlspecialchars($category['id']) ?>" 
                                        data-block-count="<?= isset($category['blocks_count']) ? $category['blocks_count'] : 0 ?>">
                                    <?= htmlspecialchars($category['name']) ?>
                                    <?php if (isset($category['blocks_count'])): ?>
                                        (<?= $category['blocks_count'] ?> blocks)
                                    <?php endif; ?>
                                </option>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </select>
                </div>
            </div>
            
            <!-- Blocks List -->
            <div class="flex-1 overflow-hidden">
                <div id="blocksList" class="h-full overflow-y-auto p-4">
                    <?= $globalBlockList ?>
                </div>
            </div>
        </div>
        
        <!-- Canvas Area -->
        <div id="mainCanvasArea" class="flex-1 transition-all duration-300 ease-in-out relative">
            <!-- Canvas Header -->
            <div class="border-b border-gray-200 p-3">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <button id="toggleSidebarBtn" type="button" class="p-2 hover:bg-gray-100 rounded-lg">
                            <svg class="w-4 h-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <rect width="18" height="18" x="3" y="3" rx="2"/>
                                <path d="M9 3v18"/>
                            </svg>
                        </button>
                        
                        <div class="flex items-center space-x-2">
                            <label for="templateSelector" class="text-sm font-medium text-gray-700">Template:</label>
                            <select id="templateSelector" class="text-sm bg-white border border-gray-300 text-gray-900 rounded focus:ring-blue-500 focus:border-blue-500 p-2">
                                <option value="">Select a template</option>
                                <?php if (!empty($allTemplates)): ?>
                                    <?php foreach ($allTemplates as $template): ?>
                                        <option value="<?= htmlspecialchars($template['id']) ?>" <?= ($templateId == $template['id']) ? 'selected' : '' ?>>
                                            <?= htmlspecialchars($template['name']) ?>
                                        </option>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </select>
                        </div>
                        
                        <div class="flex items-center space-x-2">
                            <label for="pageSelector" class="text-sm font-medium text-gray-700">Page:</label>
                            <select id="pageSelector" class="text-sm bg-white border border-gray-300 text-gray-900 rounded focus:ring-blue-500 focus:border-blue-500 p-2">
                                <option value="">Select a page</option>
                                <?php if (!empty($templatePages)): ?>
                                    <?php foreach ($templatePages as $page): ?>
                                        <option value="<?= htmlspecialchars($page['id']) ?>">
                                            <?= htmlspecialchars($page['name']) ?>
                                        </option>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </select>
                        </div>
                    </div>
                    
                    <!-- Preview Controls -->
                    <div class="flex items-center space-x-2">
                        <div class="flex items-center bg-gray-100 rounded-lg p-1">
                            <button id="desktopView" class="preview-mode-btn active px-3 py-1 text-sm font-medium rounded-md" data-mode="desktop">
                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <rect x="2" y="4" width="20" height="16" rx="2"/>
                                    <path d="M6 20h12"/>
                                </svg>
                                Desktop
                            </button>
                            <button id="tabletView" class="preview-mode-btn px-3 py-1 text-sm font-medium rounded-md" data-mode="tablet">
                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <rect x="5" y="2" width="14" height="20" rx="2"/>
                                </svg>
                                Tablet
                            </button>
                            <button id="mobileView" class="preview-mode-btn px-3 py-1 text-sm font-medium rounded-md" data-mode="mobile">
                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <rect x="7" y="1" width="10" height="22" rx="2"/>
                                </svg>
                                Mobile
                            </button>
                        </div>
                        
                        <button id="refreshPreview" class="px-3 py-1 text-sm font-medium text-gray-700 hover:bg-gray-100 rounded-lg">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <polyline points="23 4 23 10 17 10"/>
                                <polyline points="1 20 1 14 7 14"/>
                                <path d="M20.49 9A9 9 0 0 0 5.64 5.64L1 10m22 4l-4.64 4.36A9 9 0 0 1 3.51 15"/>
                            </svg>
                            Refresh
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- Canvas Content -->
            <div class="flex-1 bg-gray-50 relative">
                <div id="previewContainer" class="h-full">
                    <div id="deviceFrame" class="device-frame desktop-frame bg-white shadow-xl rounded-lg overflow-hidden h-full">
                        <div class="device-content">
                            <iframe id="templateCanvas" 
                                    src="<?= htmlspecialchars($previewUrl) ?>" 
                                    class="w-full h-full border-0"
                                    data-template-id="<?= htmlspecialchars($templateId) ?>">
                            </iframe>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Floating Action Buttons -->
            <div class="absolute bottom-6 right-6 flex flex-col space-y-2">
                <button id="saveDraft" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg shadow-lg flex items-center space-x-2">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4"/>
                    </svg>
                    <span>Save Draft</span>
                </button>
                
                <button id="publishTemplate" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg shadow-lg flex items-center space-x-2">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                    <span>Publish</span>
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Success/Error Notifications -->
<div id="notification" class="fixed top-4 right-4 z-50 hidden"></div>

<script>
// Template ID for JavaScript
window.templateId = <?= json_encode($templateId) ?>;
window.previewUrl = <?= json_encode($previewUrl) ?>;
window.blocks = <?= json_encode($blocks) ?>;
window.blockCategories = <?= json_encode($blockCategories) ?>;
</script> 