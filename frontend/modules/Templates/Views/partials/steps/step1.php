<!-- Step 1 Content: Basic Information -->
<div id="step1Content" class="step-content">
    <!-- Header -->
    <div class="py-6 border-b">
        <div class="flex items-center justify-between">
            <div>
                <h2 class="text-xl font-semibold text-gray-800">Basic Information</h2>
                <p class="mt-1 text-sm text-gray-500">Enter the basic details of your template</p>
            </div>
            <span class="px-3 py-1 text-xs font-medium text-blue-700 bg-blue-50 rounded-full">Step 1 of 4</span>
        </div>
    </div>

    <div class="py-6">
        <div class="grid grid-cols-12 gap-8">
            <!-- Left Column - Form Fields -->
            <div class="col-span-7 space-y-6">
                <!-- Template Name -->
                <div class="form-group">
                    <div class="flex justify-between mb-2">
                        <label for="templateName" class="text-sm font-medium text-gray-800">
                            Template Name <span class="text-red-500">*</span>
                        </label>
                        <span class="text-xs text-gray-500">50 characters max</span>
                    </div>
                    <div class="relative rounded-lg">
                        <input type="text" id="templateName" name="name"
                            class="block w-full pl-4 pr-4 py-2.5 text-sm text-gray-900 bg-white border border-gray-300 rounded-lg focus:border-blue-500 focus:ring-1 focus:ring-blue-500" 
                            placeholder="Enter template name"
                            value="<?= e($template['name'] ?? ''); ?>" required>
                    </div>
                </div>

                <!-- Template Category -->
                <div class="form-group relative">
                    <label class="text-sm font-medium text-gray-800 mb-2 block">
                        Category <span class="text-red-500">*</span>
                    </label>
                    <!-- Category List -->
                    <div class="relative flex-1 templateCategoryWrap">
                        <select id="templateCategory" name="categories[]" 
                            class="select2 w-full text-sm" required multiple="multiple">
                            <?php foreach ($categories as $category): ?>
                                <option value="<?= e($category['id']); ?>" 
                                    <?php 
                                    if (isset($template['categories']) && is_array($template['categories'])) {
                                        foreach ($template['categories'] as $templateCategory) {
                                            if ($templateCategory['id'] == $category['id']) {
                                                echo 'selected';
                                                break;
                                            }
                                        }
                                    }
                                    ?>
                                >
                                    <?= e($category['name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <label for="showNewCategory" class="text-xs flex items-center absolute top-0 right-0 cursor-pointer bg-[#0C5BE2]/10 text-[#0C5BE2] hover:bg-[#0C5BE2] hover:text-white font-medium rounded px-2 py-1">
                        <input type="checkbox" id="showNewCategory" class="w-3 h-3 text-[#0C5BE2] border-gray-300 visible-hidden opacity-0 w-0 h-0 absolute">
                        Add new category
                    </label>
                    
                    <div id="newCategoryInput" class="w-full bg-white hidden mt-2">
                        <div class="flex space-x-2">
                            <input type="text" id="newCategoryName" class="w-full py-2 px-3 text-sm border border-gray-300 rounded-lg focus:border-blue-500 focus:ring-1" placeholder="New category name">
                            <button type="button" onclick="addNewCategory()" class="px-3 py-2 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700">Add</button>
                        </div>
                    </div>
                </div>

                <!-- Short Description -->
                <div class="form-group">
                    <div class="flex justify-between mb-2">
                        <label for="templateShortDescription" class="text-sm font-medium text-gray-800">
                            Short Description <span class="text-red-500">*</span>
                        </label>
                        <span class="text-xs text-gray-500">160 characters max</span>
                    </div>
                    <div class="relative rounded-lg">
                        <input type="text" id="templateShortDescription" name="short_description"
                            class="block w-full pl-4 pr-4 py-2.5 text-sm text-gray-900 bg-white border border-gray-300 rounded-lg focus:border-blue-500 focus:ring-1 focus:ring-blue-500" 
                            placeholder="Brief description of the template"
                            value="<?= e($template['short_description'] ?? ''); ?>" required>
                    </div>
                </div>

                <!-- Tags Section -->
                <div class="form-group">
                    <label class="flex items-center text-sm font-medium text-gray-700 mb-2">
                        Tags
                        <button type="button" class="ml-1 group relative text-gray-400 hover:text-gray-500">
                            <svg xmlns="http://www.w3.org/2000/svg" class="w-3.5 h-3.5" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"/><path d="M12 16v-4"/><path d="M12 8h.01"/></svg>
                            <span class="hidden group-hover:block absolute left-full ml-2 px-2 py-1 text-xs bg-gray-800 text-white rounded z-10 whitespace-nowrap">Press Enter or comma to add tags</span>
                        </button>
                    </label>
                    <div class="relative">
                        <div class="flex flex-wrap p-1 bg-white border border-gray-300 rounded-lg focus-within:border-[#0C5BE2] focus-within:ring-1">
                            <div id="tags-container" class="flex flex-wrap gap-1 w-full"></div>
                            <input type="text" id="tagInput" class="flex-1 min-w-[100px] py-1 px-2 text-sm border-0 focus:outline-none focus:ring-0" placeholder="Add tags (Enter or comma)">
                        </div>
                        <!-- Suggestions dropdown -->
                        <div id="tagSuggestions" class="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg hidden">
                            <!-- Suggestions will be populated here -->
                        </div>
                    </div>
                </div>

                <!-- Detailed Description -->
                <div class="form-group">
                    <div class="flex justify-between mb-2">
                        <label for="templateDetailedDescription" class="text-sm font-medium text-gray-800">
                            Detailed Description
                        </label>
                    </div>
                    <div class="relative rounded-lg">
                        <style>
                            .ck-editor__editable_inline { height: 200px; font-size: 14px !important; line-height: 1.6 !important;}
                        </style>
                        <textarea id="detailed_description" name="detailed_description" 
                            class="w-full py-2 px-3 text-sm border border-gray-300 rounded-lg focus:border-blue-500 focus:ring-1" 
                            rows="5" placeholder="Comprehensive description of features and use cases"><?= e($template['detailed_description'] ?? ''); ?></textarea>
                    </div>
                </div>
            </div>

            <!-- Right Column - Image Upload -->
            <div class="col-span-5">
                <!-- Template Screenshots -->
                <div class="form-group mb-4">
                    <div class="flex justify-between items-center mb-2">
                        <label class="text-sm font-medium text-gray-800">
                            Template Screenshots
                        </label>
                        <span class="text-xs text-gray-500">800x400px recommended</span>
                    </div>
                    
                    <!-- Compact Screenshot Area -->
                    <div class="border-2 border-dashed border-gray-300 rounded-lg hover:border-gray-400 transition-colors bg-gray-50 p-4" id="screenshotDropZone">
                        <!-- Existing Screenshots (if any) -->
                        <div id="screenshotPreviews" class="mb-3">
                            <?php
                            if (!empty($template['multiple_template_screenshot'])) {
                                $screenshots = is_string($template['multiple_template_screenshot']) 
                                    ? json_decode($template['multiple_template_screenshot'], true) 
                                    : $template['multiple_template_screenshot'];
                                
                                if (is_array($screenshots)) {
                                    echo '<div class="grid grid-cols-3 gap-2 mb-3">';
                                    foreach ($screenshots as $screenshot) {
                                        $screenshot = str_replace(['\/', '\"'], ['/', ''], $screenshot);
                                        echo '<div class="relative group">';
                                        echo '<img src="' . BASE_URL . $screenshot . '" class="w-full h-16 object-contain rounded border border-gray-200" alt="Screenshot">';
                                        echo '<div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-40 transition-all rounded flex items-center justify-center">';
                                        echo '<button type="button" class="opacity-0 group-hover:opacity-100 text-white hover:text-red-400" onclick="removeScreenshot(this)">';
                                        echo '<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" /></svg>';
                                        echo '</button>';
                                        echo '</div>';
                                        echo '</div>';
                                    }
                                    echo '</div>';
                                }
                            }
                            ?>
                        </div>
                        
                        <!-- Upload Area -->
                        <div class="text-center">
                            <input type="file" name="template_screenshots[]" id="screenshotUpload" multiple accept="image/*" class="hidden">
                            <button type="button" onclick="document.getElementById('screenshotUpload').click()" 
                                class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                </svg>
                                Add Screenshots
                            </button>
                            <p class="text-xs text-gray-500 mt-1">Drag and drop or click to upload</p>
                        </div>
                    </div>
                </div>

                <!-- Logo and Favicon Row -->
                <div class="grid grid-cols-2 gap-4 mb-4">
                    <!-- Logo Image -->
                    <div class="form-group">
                        <label class="block text-sm font-medium text-gray-800 mb-2">Logo</label>
                        <div id="logoPreview" class="w-full h-24 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center bg-gray-50 hover:bg-gray-100 transition-colors cursor-pointer overflow-hidden">
                            <?php if (!empty($template['logo_image'])): ?>
                                <img src="<?= BASE_URL . $template['logo_image']; ?>" alt="Logo Preview" class="max-w-full max-h-full object-contain">
                            <?php else: ?>
                                <div class="text-center">
                                    <svg class="w-6 h-6 text-gray-400 mx-auto mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                    </svg>
                                    <p class="text-xs text-gray-500">Upload Logo</p>
                                </div>
                            <?php endif; ?>
                        </div>
                        <input type="file" name="logo_image" id="logo_image" class="hidden" accept="image/*">
                        <button type="button" onclick="document.getElementById('logo_image').click()" 
                            class="w-full mt-2 px-3 py-1.5 text-xs font-medium text-gray-700 bg-white border border-gray-300 rounded hover:bg-gray-50 transition-colors">
                            <?= !empty($template['logo_image']) ? 'Change Logo' : 'Choose File'; ?>
                        </button>
                    </div>

                    <!-- Favicon Image -->
                    <div class="form-group">
                        <label class="block text-sm font-medium text-gray-800 mb-2">Favicon</label>
                        <div id="faviconPreview" class="w-full h-24 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center bg-gray-50 hover:bg-gray-100 transition-colors cursor-pointer overflow-hidden">
                            <?php if (!empty($template['favicon_image'])): ?>
                                <img src="<?= BASE_URL . $template['favicon_image']; ?>" alt="Favicon Preview" class="max-w-full max-h-full object-contain">
                            <?php else: ?>
                                <div class="text-center">
                                    <svg class="w-5 h-5 text-gray-400 mx-auto mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                    </svg>
                                    <p class="text-xs text-gray-500">Favicon</p>
                                </div>
                            <?php endif; ?>
                        </div>
                        <input type="file" name="favicon_image" id="favicon_image" class="hidden" accept="image/*">
                        <button type="button" onclick="document.getElementById('favicon_image').click()" 
                            class="w-full mt-2 px-3 py-1.5 text-xs font-medium text-gray-700 bg-white border border-gray-300 rounded hover:bg-gray-50 transition-colors">
                            <?= !empty($template['favicon_image']) ? 'Change Favicon' : 'Choose File'; ?>
                        </button>
                    </div>
                </div>

                <!-- Compact Guidelines -->
                <div class="bg-blue-50 border border-blue-200 rounded p-2">
                    <div class="flex items-center text-xs text-blue-800">
                        <svg class="w-3 h-3 text-blue-600 mr-1.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <span class="font-medium">Guidelines:</span>
                        <span class="ml-1">Screenshots: 800x400px • Logo: Square • Favicon: 32x32px • Max: 2MB</span>
                    </div>
                </div>

                <!-- Template Status -->
                <div class="form-group mt-5">
                    <label class="block text-sm font-medium text-gray-800 mb-2">Template Status</label>
                    <div class="radio-button-group mt-1">
                        <input type="radio" name="template_status" value="0" id="status_draft" <?= (!isset($template['template_status']) || $template['template_status'] == 0) ? 'checked' : ''; ?>>
                        <label for="status_draft">Draft</label>
                        
                        <input type="radio" name="template_status" value="1" id="status_published" <?= (isset($template['template_status']) && $template['template_status'] == 1) ? 'checked' : ''; ?>>
                        <label for="status_published">Published</label>
                        
                        <input type="radio" name="template_status" value="2" id="status_archived" <?= (isset($template['template_status']) && $template['template_status'] == 2) ? 'checked' : ''; ?>>
                        <label for="status_archived">Archived</label>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>