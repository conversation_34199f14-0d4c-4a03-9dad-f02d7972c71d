<div id="step4Content" class="step-content hidden">
    <!-- Header -->
    <div class="py-6 border-b">
        <div class="flex items-center justify-between">
            <div>
                <h2 class="text-lg font-semibold text-gray-800">Advanced</h2>
                <p class="mt-1 text-sm text-gray-500">Configure advanced settings and custom code</p>
            </div>
            <span class="px-3 py-1 text-xs font-medium text-blue-700 bg-blue-50 rounded-full">Step 4 of 4</span>
        </div>
    </div>

    <div class="py-6">
        <div class="grid grid-cols-1 gap-6">
            <!-- Global Assets Section -->
            <div class="bg-gray-50 p-6 rounded-lg">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Global Assets</h3>
                <p class="text-sm text-gray-600 mb-4">Select global CSS and JavaScript assets to include with this template.</p>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- CSS Assets -->
                    <div>
                        <div class="flex items-center justify-between mb-3">
                            <h4 class="text-md font-medium text-gray-800 flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-blue-600" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                                    <polyline points="14,2 14,8 20,8"/>
                                    <line x1="16" y1="13" x2="8" y2="13"/>
                                    <line x1="16" y1="17" x2="8" y2="17"/>
                                    <polyline points="10,9 9,9 8,9"/>
                                </svg>
                                CSS Assets
                            </h4>
                            <button type="button" id="selectAllCSS" class="text-xs px-2 py-1 bg-blue-100 text-blue-700 rounded hover:bg-blue-200">
                                Select All CSS
                            </button>
                        </div>
                        <div class="space-y-2 max-h-64 overflow-y-auto">
                            <?php
                            $cssAssets = array_filter($globalAssetsList, function($asset) {
                                return $asset['asset_type'] === 'css';
                            });
                            
                            if (empty($cssAssets)): ?>
                                <p class="text-sm text-gray-500 italic">No CSS assets available</p>
                            <?php else: ?>
                                <?php foreach ($cssAssets as $asset): ?>
                                    <label class="flex items-start space-x-3 p-3 bg-white border border-gray-200 rounded-lg hover:bg-blue-50 cursor-pointer transition-colors asset-label">
                                        <input type="checkbox" 
                                            name="global_assets[]" 
                                            value="<?= $asset['id']; ?>"
                                            class="mt-1 rounded border-gray-300 text-blue-600 shadow-sm asset-checkbox css-asset"
                                            <?= in_array($asset['id'], $selectedGlobalAssets) ? 'checked' : ''; ?>>
                                        <div class="flex-1 min-w-0">
                                            <div class="flex items-center justify-between">
                                                <span class="font-medium text-gray-900 text-sm">CSS Asset #<?= $asset['id']; ?></span>
                                                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">CSS</span>
                                            </div>
                                            <p class="text-xs text-gray-600 mt-1 break-all"><?= htmlspecialchars($asset['asset_value']); ?></p>
                                            <?php if (!empty($asset['templates']) && is_array($asset['templates'])): ?>
                                                <p class="text-xs text-gray-400 mt-1">Used by <?= count($asset['templates']); ?> template(s)</p>
                                            <?php endif; ?>
                                        </div>
                                    </label>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <!-- JS Assets -->
                    <div>
                        <div class="flex items-center justify-between mb-3">
                            <h4 class="text-md font-medium text-gray-800 flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-yellow-600" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                                    <polyline points="14,2 14,8 20,8"/>
                                    <line x1="16" y1="13" x2="8" y2="13"/>
                                    <line x1="16" y1="17" x2="8" y2="17"/>
                                    <polyline points="10,9 9,9 8,9"/>
                                </svg>
                                JavaScript Assets
                            </h4>
                            <button type="button" id="selectAllJS" class="text-xs px-2 py-1 bg-yellow-100 text-yellow-700 rounded hover:bg-yellow-200">
                                Select All JS
                            </button>
                        </div>
                        <div class="space-y-2 max-h-64 overflow-y-auto">
                            <?php
                            $jsAssets = array_filter($globalAssetsList, function($asset) {
                                return $asset['asset_type'] === 'js';
                            });
                            
                            if (empty($jsAssets)): ?>
                                <p class="text-sm text-gray-500 italic">No JavaScript assets available</p>
                            <?php else: ?>
                                <?php foreach ($jsAssets as $asset): ?>
                                    <label class="flex items-start space-x-3 p-3 bg-white border border-gray-200 rounded-lg hover:bg-yellow-50 cursor-pointer transition-colors asset-label">
                                        <input type="checkbox" 
                                            name="global_assets[]" 
                                            value="<?= $asset['id']; ?>"
                                            class="mt-1 rounded border-gray-300 text-yellow-600 shadow-sm asset-checkbox js-asset"
                                            <?= in_array($asset['id'], $selectedGlobalAssets) ? 'checked' : ''; ?>>
                                        <div class="flex-1 min-w-0">
                                            <div class="flex items-center justify-between">
                                                <span class="font-medium text-gray-900 text-sm">JS Asset #<?= $asset['id']; ?></span>
                                                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-yellow-100 text-yellow-800">JS</span>
                                            </div>
                                            <p class="text-xs text-gray-600 mt-1 break-all"><?= htmlspecialchars($asset['asset_value']); ?></p>
                                            <?php if (!empty($asset['templates']) && is_array($asset['templates'])): ?>
                                                <p class="text-xs text-gray-400 mt-1">Used by <?= count($asset['templates']); ?> template(s)</p>
                                            <?php endif; ?>
                                        </div>
                                    </label>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                
                <!-- Summary -->
                <div class="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                    <div class="flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-blue-600 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <circle cx="12" cy="12" r="10"/>
                            <path d="M12 16v-4"/>
                            <path d="M12 8h.01"/>
                        </svg>
                        <span class="text-sm font-medium text-blue-800">
                            Selected: <span id="selectedAssetsCount"><?= count($selectedGlobalAssets); ?></span> assets
                            (<span id="selectedCSSCount">0</span> CSS, <span id="selectedJSCount">0</span> JS)
                        </span>
                    </div>
                    <p class="text-xs text-blue-600 mt-1">These assets will be automatically included in all pages of this template.</p>
                </div>
            </div>
            
            <!-- Custom CSS/JS Section -->
            <div class="grid grid-cols-2 gap-6">
                <div class="form-group">
                    <label class="block text-sm font-medium text-gray-700 mb-1">Custom CSS</label>
                    <textarea name="custom_css" rows="10" class="w-full py-2 px-3 text-sm font-mono border border-gray-300 rounded-lg focus:border-[#0C5BE2] focus:ring-1 focus:ring-[#0C5BE2]/20"><?= htmlspecialchars($template['custom_css'] ?? ''); ?></textarea>
                </div>
                <div class="form-group">
                    <label class="block text-sm font-medium text-gray-700 mb-1">Custom JavaScript</label>
                    <textarea name="custom_js" rows="10" class="w-full py-2 px-3 text-sm font-mono border border-gray-300 rounded-lg focus:border-[#0C5BE2] focus:ring-1 focus:ring-[#0C5BE2]/20"><?= htmlspecialchars($template['custom_js'] ?? ''); ?></textarea>
                </div>
            </div>
        </div>
    </div>
</div>