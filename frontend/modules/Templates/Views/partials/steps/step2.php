<div id="step2Content" class="step-content hidden">
    <!-- Header -->
    <div class="py-6 border-b">
        <div class="flex items-center justify-between">
            <div>
                <h2 class="text-lg font-semibold text-gray-800">Site Settings</h2>
                <p class="mt-1 text-sm text-gray-500">Configure your site settings and metadata</p>
            </div>
            <span class="px-3 py-1 text-xs font-medium text-blue-700 bg-blue-50 rounded-full">Step 2 of 4</span>
        </div>
    </div>

    <div class="py-6">
        <div class="grid gap-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Left Column -->
                <div class="space-y-4">
                    <!-- Site Title -->
                    <div class="form-group">
                        <label class="block text-sm font-medium text-gray-800 mb-2">
                            Site Title <span class="text-red-500">*</span>
                        </label>
                        <input type="text" name="site_title" value="<?= e($template['site_title'] ?? ''); ?>" required 
                            class="w-full py-2 px-3 text-sm border border-gray-300 rounded-lg focus:border-blue-500 focus:ring-1 focus:ring-blue-500" 
                            placeholder="Enter site title">
                        <p class="mt-1 text-xs text-gray-500">Enter your website's main title or brand name</p>
                    </div>

                    <!-- Site Description -->
                    <div class="form-group">
                        <label class="block text-sm font-medium text-gray-800 mb-2">
                            Site Description
                        </label>
                        <textarea name="site_description" rows="3" 
                            class="w-full py-2 px-3 text-sm border border-gray-300 rounded-lg focus:border-blue-500 focus:ring-1" 
                            placeholder="Enter site description for SEO"><?= e($template['site_description'] ?? ''); ?></textarea>
                        <p class="mt-1 text-xs text-gray-500">Recommended: 150-160 characters</p>
                    </div>
                </div>
                
                <!-- Right Column -->
                <div class="space-y-4">
                    <!-- Meta Keywords -->
                    <div class="form-group">
                        <label class="block text-sm font-medium text-gray-800 mb-2">
                            Meta Keywords
                        </label>
                        <input type="text" name="meta_keywords" value="<?= e($template['meta_keywords'] ?? ''); ?>" 
                            class="w-full py-2 px-3 text-sm border border-gray-300 rounded-lg focus:border-blue-500 focus:ring-1" 
                            placeholder="keyword1, keyword2, keyword3">
                        <p class="mt-1 text-xs text-gray-500">Separate keywords with commas</p>
                    </div>
                    
                    <!-- Meta Description -->
                    <div class="form-group">
                        <label class="block text-sm font-medium text-gray-800 mb-2">
                            Meta Description
                        </label>
                        <textarea name="meta_description" rows="3" 
                            class="w-full py-2 px-3 text-sm border border-gray-300 rounded-lg focus:border-blue-500 focus:ring-1" 
                            placeholder="Enter meta description"><?= e($template['meta_description'] ?? ''); ?></textarea>
                        <p class="mt-1 text-xs text-gray-500">Recommended: 150-160 characters</p>
                    </div>
                </div>
            </div>

            <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-6">
                <!-- Body Font Family -->
                <div class="form-group">
                    <label class="block text-sm font-medium text-gray-800 mb-2">
                        Body Font
                    </label>
                    <div class="relative">
                        <select name="body_font_family" class="w-full py-2 px-3 text-sm border border-gray-300 rounded-lg appearance-none focus:border-blue-500 focus:ring-1">
                            <option value="">Select Font Family</option>
                            <option value="inter" <?= ($template['body_font_family'] ?? '') === 'inter' ? 'selected' : ''; ?>>Inter</option>
                            <option value="roboto" <?= ($template['body_font_family'] ?? '') === 'roboto' ? 'selected' : ''; ?>>Roboto</option>
                            <option value="opensans" <?= ($template['body_font_family'] ?? '') === 'opensans' ? 'selected' : ''; ?>>Open Sans</option>
                            <option value="lato" <?= ($template['body_font_family'] ?? '') === 'lato' ? 'selected' : ''; ?>>Lato</option>
                            <option value="poppins" <?= ($template['body_font_family'] ?? '') === 'poppins' ? 'selected' : ''; ?>>Poppins</option>
                            <option value="noto-sans" <?= ($template['body_font_family'] ?? '') === 'noto-sans' ? 'selected' : ''; ?>>Noto Sans</option>
                            <option value="montserrat" <?= ($template['body_font_family'] ?? '') === 'montserrat' ? 'selected' : ''; ?>>Montserrat</option>
                            <option value="source-sans-pro" <?= ($template['body_font_family'] ?? '') === 'source-sans-pro' ? 'selected' : ''; ?>>Source Sans Pro</option>
                        </select>
                        <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-gray-500" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M6 9l6 6 6-6"/></svg>
                        </div>
                    </div>
                </div>

                <!-- Body Font Size -->
                <div class="form-group">
                    <label class="block text-sm font-medium text-gray-800 mb-2">
                        Base Font Size
                    </label>
                    <div class="flex items-center space-x-2">
                        <input type="number" name="body_font_size" value="<?= e($template['body_font_size'] ?? '16'); ?>" 
                            min="12" max="24" step="1" class="w-full py-2 px-3 text-sm border border-gray-300 rounded-lg focus:border-blue-500 focus:ring-1" placeholder="16">
                        <span class="text-sm text-gray-500">px</span>
                    </div>
                </div>
                
                <!-- Heading Font Family -->
                <div class="form-group">
                    <label class="block text-sm font-medium text-gray-800 mb-2">
                        Heading Font
                    </label>
                    <div class="relative">
                        <select name="heading_font_family" class="w-full py-2 px-3 text-sm border border-gray-300 rounded-lg appearance-none focus:border-blue-500 focus:ring-1">
                            <option value="">Select Font Family</option>
                            <option value="Playfair Display" <?= ($template['heading_font_family'] ?? '') === 'Playfair Display' ? 'selected' : ''; ?>>Playfair Display</option>
                            <option value="Merriweather" <?= ($template['heading_font_family'] ?? '') === 'Merriweather' ? 'selected' : ''; ?>>Merriweather</option>
                            <option value="Roboto Slab" <?= ($template['heading_font_family'] ?? '') === 'Roboto Slab' ? 'selected' : ''; ?>>Roboto Slab</option>
                            <option value="Montserrat" <?= ($template['heading_font_family'] ?? '') === 'Montserrat' ? 'selected' : ''; ?>>Montserrat</option>
                            <option value="Oswald" <?= ($template['heading_font_family'] ?? '') === 'Oswald' ? 'selected' : ''; ?>>Oswald</option>
                            <option value="Lora" <?= ($template['heading_font_family'] ?? '') === 'Lora' ? 'selected' : ''; ?>>Lora</option>
                            <option value="Source Serif Pro" <?= ($template['heading_font_family'] ?? '') === 'Source Serif Pro' ? 'selected' : ''; ?>>Source Serif Pro</option>
                            <option value="PT Serif" <?= ($template['heading_font_family'] ?? '') === 'PT Serif' ? 'selected' : ''; ?>>PT Serif</option>
                            <option value="Libre Baskerville" <?= ($template['heading_font_family'] ?? '') === 'Libre Baskerville' ? 'selected' : ''; ?>>Libre Baskerville</option>
                            <option value="Crimson Text" <?= ($template['heading_font_family'] ?? '') === 'Crimson Text' ? 'selected' : ''; ?>>Crimson Text</option>
                        </select>
                        <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-gray-500" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M6 9l6 6 6-6"/></svg>
                        </div>
                    </div>
                </div>

                <!-- Line Heights -->
                <div class="form-group">
                    <label class="block text-sm font-medium text-gray-800 mb-2">
                        Line Height
                    </label>
                    <input type="number" name="line_height" value="<?= e($template['line_height'] ?? '1.5'); ?>" 
                        min="1" max="2" step="0.1" class="w-full py-2 px-3 text-sm border border-gray-300 rounded-lg focus:border-blue-500 focus:ring-1" placeholder="1.5">
                </div>
            </div>
        </div>
    </div>
</div>