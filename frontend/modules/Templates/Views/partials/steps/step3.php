
<?php
    use Core\View;
?>
<div id="step3Content" class="step-content hidden">
    <!-- Header -->
    <div class="py-6 border-b">
        <div class="flex items-center justify-between">
            <div>
                <h2 class="text-lg font-semibold text-gray-800">Appearance</h2>
                <p class="mt-1 text-sm text-gray-500">Customize the look and feel of your template</p>
            </div>
            <span class="px-3 py-1 text-xs font-medium text-blue-700 bg-blue-50 rounded-full">Step 3 of 4</span>
        </div>
    </div>

    <div class="py-6">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-10">
            <div class="space-y-4">
                <h3 class="text-base font-medium text-gray-900 mb-2">Color Theme</h3>
                
                <!-- Theme Selection Pills -->
                <div class="flex flex-wrap gap-2 mb-3">
                    <button type="button" class="bg-white px-3 py-1.5 rounded-full border border-[#0C5BE2] text-[#0C5BE2] text-xs font-medium shadow-sm" data-theme="corporate-light" data-color-code="#0C5BE2,#F3F4F6,#4F46E5">
                        <span class="flex items-center">
                            <span class="w-3 h-3 rounded-full bg-[#0C5BE2] mr-1.5"></span>
                            Corporate Light
                        </span>
                    </button>
                    <button type="button" class="bg-white px-3 py-1.5 rounded-full border border-gray-200 text-gray-700 text-xs font-medium hover:border-gray-300" data-theme="corporate-dark" data-color-code="#1F2937,#374151,#6366F1">
                        <span class="flex items-center">
                            <span class="w-3 h-3 rounded-full bg-gray-900 mr-1.5"></span>
                            Corporate Dark
                        </span>
                    </button>
                    <button type="button" class="bg-white px-3 py-1.5 rounded-full border border-gray-200 text-gray-700 text-xs font-medium hover:border-gray-300" data-theme="nature" data-color-code="#059669,#D1FAE5,#047857">
                        <span class="flex items-center">
                            <span class="w-3 h-3 rounded-full bg-[#059669] mr-1.5"></span>
                            Nature
                        </span>
                    </button>
                    <button type="button" class="bg-white px-3 py-1.5 rounded-full border border-gray-200 text-gray-700 text-xs font-medium hover:border-gray-300" data-theme="ocean" data-color-code="#0EA5E9,#E0F2FE,#0284C7">
                        <span class="flex items-center">
                            <span class="w-3 h-3 rounded-full bg-[#0EA5E9] mr-1.5"></span>
                            Ocean
                        </span>
                    </button>
                    <button type="button" class="bg-white px-3 py-1.5 rounded-full border border-gray-200 text-gray-700 text-xs font-medium hover:border-gray-300" data-theme="warm" data-color-code="#F97316,#FFF7ED,#EA580C">
                        <span class="flex items-center">
                            <span class="w-3 h-3 rounded-full bg-[#F97316] mr-1.5"></span>
                            Warm
                        </span>
                    </button>
                    <button type="button" class="bg-white px-3 py-1.5 rounded-full border border-gray-200 text-gray-700 text-xs font-medium hover:border-gray-300" data-theme="custom">
                        <span class="flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1.5 text-gray-500" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"/><line x1="12" y1="8" x2="12" y2="16"/><line x1="8" y1="12" x2="16" y2="12"/></svg>
                            Custom
                        </span>
                    </button>
                </div>
                
                <!-- Color Grid -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-3 hidden" id="customColorGrid">
                    <!-- Primary Color -->
                    <div class="p-2 border border-gray-200 rounded-lg bg-white">
                        <label class="block text-xs text-gray-700 mb-1">Primary Color</label>
                        <div class="flex items-center space-x-1">
                            <input type="color" id="primaryColor" value="#0C5BE2" class="w-8 h-8 rounded p-0 cursor-pointer">
                            <input type="text" id="primaryColorHex" value="#0C5BE2" class="w-full h-8 px-2 text-xs border border-gray-300 rounded-md bg-white focus:outline-none focus:border-[#0C5BE2] focus:ring-1">
                        </div>
                        <div class="mt-1 text-xs text-gray-500">Buttons, links, accents</div>
                    </div>
                    
                    <!-- Secondary Color -->
                    <div class="p-2 border border-gray-200 rounded-lg bg-white">
                        <label class="block text-xs text-gray-700 mb-1">Secondary Color</label>
                        <div class="flex items-center space-x-1">
                            <input type="color" id="secondaryColor" value="#F3F4F6" class="w-8 h-8 rounded p-0 cursor-pointer">
                            <input type="text" id="secondaryColorHex" value="#F3F4F6" class="w-full h-8 px-2 text-xs border border-gray-300 rounded-md bg-white focus:outline-none focus:border-[#0C5BE2] focus:ring-1">
                        </div>
                        <div class="mt-1 text-xs text-gray-500">Backgrounds, inactive states</div>
                    </div>
                    
                    <!-- Accent Color -->
                    <div class="p-2 border border-gray-200 rounded-lg bg-white">
                        <label class="block text-xs text-gray-700 mb-1">Accent Color</label>
                        <div class="flex items-center space-x-1">
                            <input type="color" id="accentColor" value="#4F46E5" class="w-8 h-8 rounded p-0 cursor-pointer">
                            <input type="text" id="accentColorHex" value="#4F46E5" class="w-full h-8 px-2 text-xs border border-gray-300 rounded-md bg-white focus:outline-none focus:border-[#0C5BE2] focus:ring-1">
                        </div>
                        <div class="mt-1 text-xs text-gray-500">Highlights, call-to-actions</div>
                    </div>
                </div>

                <!-- Tailwind Configuration -->
                <div class="mt-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Tailwind Configuration</label>
                    <div class="relative">
                        <textarea id="tailwindConfig" name="tailwind_configuration_code" rows="12" class="w-full font-mono text-sm bg-gray-50 p-3 border border-gray-200 rounded-lg" readonly><?= e($template['tailwind_configuration_code'] ?? ''); ?></textarea>
                        <button type="button" onclick="copyConfig()" class="absolute top-2 right-2 p-1.5 text-gray-500 hover:text-gray-700 bg-white border border-gray-200 rounded-lg shadow-sm">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <rect x="9" y="9" width="13" height="13" rx="2" ry="2"/>
                                <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"/>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Color Theme Preview -->
            <div class="border border-gray-200 rounded-lg p-4 bg-white">
                <h4 class="text-sm font-medium text-gray-900 mb-4">Color Theme Preview</h4>
                <?php View::include('modules.Templates.Views.partials.theme'); ?>
            </div>
        </div>
    </div>
</div>