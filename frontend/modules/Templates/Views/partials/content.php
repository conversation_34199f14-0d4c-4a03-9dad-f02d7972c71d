<?php 
    use Core\View;
?>
<div class="w-[calc(100%-275px)]">
    <form id="templateForm" action="<?= isset($template) && $isEdit ? url("templates/update/{$template['id']}") : url('templates/store') ?>" method="POST" enctype="multipart/form-data" data-ajax-form data-reset-on-success data-table-id="templatesTable" data-ajax-url="templates">
    <input type="hidden" name="id" value="<?= isset($template) && !empty($template) ? $template['id'] : '' ?>">

        <!-- Step 1 Content: Basic Information -->
        <?php View::include('modules.Templates.Views.partials.steps.step1', [
            'isEdit' => $isEdit,
            'template' => $template,
            'categories' => $categories
        ]); ?>

        <!-- Step 2 Content: Site Settings -->
        <?php View::include('modules.Templates.Views.partials.steps.step2', [
            'isEdit' => $isEdit,
            'template' => $template,
        ]); ?>

        <!-- Step 3 Content: Appearance -->
        <?php View::include('modules.Templates.Views.partials.steps.step3', [
            'isEdit' => $isEdit,
            'template' => $template,
        ]); ?>
        
        <!-- Step 4 Content: Advanced -->
        <?php View::include('modules.Templates.Views.partials.steps.step4', [
            'isEdit' => $isEdit,
            'template' => $template,
            'globalAssetsList' => $globalAssetsList,
            'selectedGlobalAssets' => $selectedGlobalAssets
        ]); ?>
        
        <?php View::include('modules.Templates.Views.partials.navigation', [
            'isEdit'=> $isEdit,
        ]); ?>
    </form>
</div>