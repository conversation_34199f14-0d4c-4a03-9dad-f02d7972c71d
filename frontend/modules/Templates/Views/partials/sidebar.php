<!-- Steps List -->
<div class="relative">
    <!-- Step 1: Basic Information -->
    <div class="step-item relative pb-8 <?= $isEdit ? 'cursor-pointer' : ''; ?>" 
        data-step="1" <?= $isEdit ? 'title="Click to jump to Basic Information"' : ''; ?>>
        <div class="flex items-center">
            <div class="relative z-40">
                <!-- Outer circle for border effect -->
                <div class="absolute -inset-0.5 bg-blue-100 rounded-full <?= $isEdit ? 'group-hover:bg-blue-200' : ''; ?>"></div>
                <!-- Inner circle for step number -->
                <div class="relative flex items-center justify-center w-8 h-8 bg-blue-600 rounded-full <?= $isEdit ? 'group-hover:bg-blue-700' : ''; ?>">
                    <span class="text-white text-sm">1</span>
                </div>
            </div>
            <div class="ml-4 flex-1">
                <h3 class="text-base font-semibold text-gray-900 <?= $isEdit ? 'group-hover:text-blue-600' : ''; ?>">
                    Basic Information
                </h3>
                <p class="text-sm text-gray-500">Enter the basic details of your template</p>
            </div>
        </div>
        <div class="absolute left-4 top-10 h-full w-0.5 bg-blue-100 -ml-px"></div>
    </div>

    <!-- Step 2: Site Settings -->
    <div class="step-item relative pb-8 <?= $isEdit ? 'cursor-pointer' : ''; ?>" 
        data-step="2" <?= $isEdit ? 'title="Click to jump to Site Settings"' : ''; ?>>
        <div class="flex items-center">
            <div class="relative z-40">
                <!-- Outer circle for border effect -->
                <div class="absolute -inset-0.5 bg-gray-200 rounded-full <?= $isEdit ? 'group-hover:bg-blue-100' : ''; ?>"></div>
                <!-- Inner circle for step number -->
                <div class="relative flex items-center justify-center w-8 h-8 bg-gray-100 rounded-full <?= $isEdit ? 'group-hover:bg-blue-100' : ''; ?>">
                    <span class="text-gray-500 text-sm <?= $isEdit ? 'group-hover:text-blue-600' : ''; ?>">2</span>
                </div>
            </div>
            <div class="ml-4 flex-1">
                <h3 class="text-base font-semibold text-gray-400 <?= $isEdit ? 'group-hover:text-blue-600' : ''; ?>">
                    Site Settings
                </h3>
                <p class="text-sm text-gray-400 <?= $isEdit ? 'group-hover:text-blue-500' : ''; ?>">Configure your site settings and metadata</p>
            </div>
        </div>
        <div class="absolute left-4 top-10 h-full w-0.5 bg-gray-200 -ml-px"></div>
    </div>

    <!-- Step 3: Appearance -->
    <div class="step-item relative pb-8 <?= $isEdit ? 'cursor-pointer' : ''; ?>" 
        data-step="3" <?= $isEdit ? 'title="Click to jump to Appearance"' : ''; ?>>
        <div class="flex items-center">
            <div class="relative z-40">
                <!-- Outer circle for border effect -->
                <div class="absolute -inset-0.5 bg-gray-200 rounded-full <?= $isEdit ? 'group-hover:bg-blue-100' : ''; ?>"></div>
                <!-- Inner circle for step number -->
                <div class="relative flex items-center justify-center w-8 h-8 bg-gray-100 rounded-full <?= $isEdit ? 'group-hover:bg-blue-100' : ''; ?>">
                    <span class="text-gray-500 text-sm <?= $isEdit ? 'group-hover:text-blue-600' : ''; ?>">3</span>
                </div>
            </div>
            <div class="ml-4 flex-1">
                <h3 class="text-base font-semibold text-gray-400 <?= $isEdit ? 'group-hover:text-blue-600' : ''; ?>">
                    Appearance
                </h3>
                <p class="text-sm text-gray-400 <?= $isEdit ? 'group-hover:text-blue-500' : ''; ?>">Customize the look and feel of your template</p>
            </div>
        </div>
        <div class="absolute left-4 top-10 h-full w-0.5 bg-gray-200 -ml-px"></div>
    </div>

    <!-- Step 4: Advanced -->
    <div class="step-item relative <?= $isEdit ? 'cursor-pointer' : ''; ?>" 
        data-step="4" <?= $isEdit ? 'title="Click to jump to Advanced"' : ''; ?>>
        <div class="flex items-center">
            <div class="relative z-40">
                <!-- Outer circle for border effect -->
                <div class="absolute -inset-0.5 bg-gray-200 rounded-full <?= $isEdit ? 'group-hover:bg-blue-100' : ''; ?>"></div>
                <!-- Inner circle for step number -->
                <div class="relative flex items-center justify-center w-8 h-8 bg-gray-100 rounded-full <?= $isEdit ? 'group-hover:bg-blue-100' : ''; ?>">
                    <span class="text-gray-500 text-sm <?= $isEdit ? 'group-hover:text-blue-600' : ''; ?>">4</span>
                </div>
            </div>
            <div class="ml-4 flex-1">
                <h3 class="text-base font-semibold text-gray-400 <?= $isEdit ? 'group-hover:text-blue-600' : ''; ?>">
                    Advanced
                </h3>
                <p class="text-sm text-gray-400 <?= $isEdit ? 'group-hover:text-blue-500' : ''; ?>">Configure advanced settings and custom code</p>
            </div>
        </div>
    </div>
</div>

<!-- Bottom Navigation -->
<div class="flex items-center space-x-3">
    <a href="<?= url("templates") ?>" class="inline-flex items-center text-[14px] px-2.5 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 transition-all">
        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
        </svg>
        <span>Back to Templates</span>
    </a>
</div>