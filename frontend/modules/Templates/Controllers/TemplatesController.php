<?php

namespace Modules\Templates\Controllers;

use App\Services\Auth;
use Core\Controller;
use Core\Request;
use Dotenv\Exception\ExceptionInterface;
use Modules\Tag\Services\TagService;
use Modules\Templates\Services\TemplatesService;
use Modules\Templates\Services\TemplateTableService;

class Templates<PERSON>ontroller extends Controller
{
    protected $templatesService;
    protected $tagService;

    public function __construct()
    {
        $this->templatesService = new TemplatesService();
        $this->tagService = new TagService();
    }

    public function index(Request $request)
    {
        $this->setTitle('Templates');
        
        // Wrap the service call in try-catch
        $result = $this->templatesService->getData($request);
        
        $templatesList = $result['status'] === 'success' ? $result['data']['data'] : [];
        $pagination = $result['status'] === 'success' 
            ? array_merge($this->defaultPagination, $result['data']) 
            : $this->defaultPagination;

        // Try to create the table service
        $templateTable = new TemplateTableService($templatesList, $pagination, $request);

        $tableRender = $templateTable->renderTable();
        if ($request->isAjax()) {
            if ($tableRender['success']) {
                return $this->jsonResponse([
                    'html' => $tableRender['table'],
                    'view' => $tableRender['view'],
                    'pagination' => $pagination,
                    'success' => true,
                    'message' => ''
                ]);
            }

            return $this->jsonResponse([
                'html' => '',
                'view' => 'table',
                'pagination' => $pagination,
                'success' => true,
                'message' => $tableRender['message']
            ]);
        }

        // Non-AJAX response
        return $this->view('modules/Templates/Views/index', [
            'templateTable' => $tableRender['table'] ?? 'Error loading table'
        ]);
    }


    public function preview()
    {
        dd('Here');
    }
}