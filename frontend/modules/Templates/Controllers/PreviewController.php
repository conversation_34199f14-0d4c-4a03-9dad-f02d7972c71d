<?php

namespace Modules\Templates\Controllers;

use Core\Controller;
use Core\Request;
use Core\Services\PreviewService;
use Exception;

class PreviewController extends Controller
{
    private $previewService;
    
    public function __construct()
    {
        $this->previewService = new PreviewService();
    }
    
    /**
     * Show template preview
     */
    public function index(Request $request)
    {
        try {
            $templateId = $request->get('id');
            $pageId = $request->get('page_id');
            $type = $request->get('type', 'template');
            $previewMode = $request->get('preview') === 'true';
            $pageSlug = $request->get('page');
            
            // If page_id is provided, render specific page
            if ($pageId) {
                $options = [
                    'preview_mode' => $previewMode,
                    'hide_controls' => $previewMode
                ];
                
                $html = $this->previewService->renderPreview('page', $pageId, $options);
                
                if (isset($html['error'])) {
                    return $this->renderError($html['error']);
                }
                
                echo $html;
                exit;
            }
            
            // If template_id is provided but no page_id, render template with all pages
            if ($templateId) {
                $options = [
                    'preview_mode' => $previewMode,
                    'hide_controls' => $previewMode,
                    'page_slug' => $pageSlug,
                    'is_template_page' => !$pageSlug
                ];
                
                $html = $this->previewService->renderPreview('template', $templateId, $options);
                
                if (isset($html['error'])) {
                    return $this->renderError($html['error']);
                }
                
                echo $html;
                exit;
            }
            
            return $this->renderError('Template ID or Page ID is required');
            
        } catch (Exception $e) {
            return $this->renderError($e->getMessage());
        }
    }
    
    /**
     * Show template builder with preview
     */
    public function builder(Request $request)
    {
        try {
            $templateId = $request->get('id');
            
            if (!$templateId) {
                return $this->renderError('Template ID is required');
            }
            
            $this->setTitle('Template Builder');
            
            // Get blocks data for the builder
            $blocksData = $this->getBlocksForBuilder();
            
            // Get all templates for the dropdown
            $allTemplates = $this->getAllTemplates();
            
            // Get pages for the current template
            $templatePages = $this->getTemplatePages($templateId);
            
            return $this->view('modules/Templates/Views/builder', [
                'templateId' => $templateId,
                'previewUrl' => url("/templates/preview?id={$templateId}"),
                'blocks' => $blocksData['blocks'],
                'blockCategories' => $blocksData['categories'],
                'allTemplates' => $allTemplates,
                'templatePages' => $templatePages
            ]);
            
        } catch (Exception $e) {
            return $this->renderError($e->getMessage());
        }
    }
    
    /**
     * Get blocks data for the builder
     */
    private function getBlocksForBuilder()
    {
        try {
            // Get block categories
            $blockCategoryService = new \Modules\Blocks\Services\BlockCategoryService();
            $categoriesResult = $blockCategoryService->getAllCategories();
            $categories = $categoriesResult['status'] == 'success' ? $categoriesResult['data']['data'] : [];
            
            // Get all blocks (you might want to paginate this in a real application)
            $blocks = [];
            foreach ($categories as $category) {
                $blockService = new \Modules\Blocks\Services\BlockService();
                $request = new Request();
                $request->setGetParams(['limit' => 50]); // Get up to 50 blocks per category
                
                $blocksResult = $blockService->getBlocksByCategory($category['id'], $request);
                if ($blocksResult['status'] == 'success' && !empty($blocksResult['data']['data'])) {
                    foreach ($blocksResult['data']['data'] as $block) {
                        $block['category_name'] = $category['name'];
                        $blocks[] = $block;
                    }
                }
            }
            
            return [
                'blocks' => $blocks,
                'categories' => $categories
            ];
            
        } catch (Exception $e) {
            error_log('Error getting blocks for builder: ' . $e->getMessage());
            return [
                'blocks' => [],
                'categories' => []
            ];
        }
    }

    /**
     * Get all templates for the dropdown
     */
    private function getAllTemplates()
    {
        try {
            $templatesService = new \Modules\Templates\Services\TemplatesService();
            $request = new Request();
            $request->setGetParams(['limit' => 100]); // Get up to 100 templates
            
            $result = $templatesService->getData($request);
            return $result['status'] === 'success' ? $result['data']['data'] : [];
            
        } catch (Exception $e) {
            error_log('Error getting all templates: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Get pages for a specific template
     */
    private function getTemplatePages($templateId)
    {
        try {
            $pagesService = new \Modules\Pages\Services\PageService();
            $request = new Request();
            $request->setGetParams(['template_id' => $templateId, 'limit' => 100]);
            
            $result = $pagesService->getData($request);
            return $result['status'] === 'success' ? $result['data']['data'] : [];
            
        } catch (Exception $e) {
            error_log('Error getting template pages: ' . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Handle AJAX requests for template preview
     */
    public function ajax(Request $request)
    {
        try {
            $result = $this->previewService->handleAjaxRequest($request);
            return $this->json($result);
        } catch (Exception $e) {
            return $this->json([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * Render error page
     */
    private function renderError($message)
    {
        $html = '<!DOCTYPE html>';
        $html .= '<html lang="en">';
        $html .= '<head>';
        $html .= '<meta charset="UTF-8">';
        $html .= '<meta name="viewport" content="width=device-width, initial-scale=1.0">';
        $html .= '<title>Preview Error</title>';
        $html .= '<script src="https://cdn.tailwindcss.com"></script>';
        $html .= '</head>';
        $html .= '<body class="bg-gray-100">';
        $html .= '<div class="flex items-center justify-center min-h-screen">';
        $html .= '<div class="text-center">';
        $html .= '<div class="inline-flex items-center justify-center w-16 h-16 rounded-full bg-red-100 mb-4">';
        $html .= '<svg class="w-8 h-8 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">';
        $html .= '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.864-.833-2.634 0L4.183 16.5c-.77.833.192 2.5 1.732 2.5z"></path>';
        $html .= '</svg>';
        $html .= '</div>';
        $html .= '<h1 class="text-2xl font-bold text-gray-900 mb-2">Preview Error</h1>';
        $html .= '<p class="text-gray-600">' . htmlspecialchars($message) . '</p>';
        $html .= '<button onclick="history.back()" class="mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">';
        $html .= 'Go Back';
        $html .= '</button>';
        $html .= '</div>';
        $html .= '</div>';
        $html .= '</body>';
        $html .= '</html>';
        
        echo $html;
        exit;
    }
} 