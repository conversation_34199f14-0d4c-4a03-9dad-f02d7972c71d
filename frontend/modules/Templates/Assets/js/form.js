 // Pass PHP data to JavaScript
 window.BASE_URL = '<?php echo addslashes(BASE_URL); ?>';
 window.TEMPLATE_DATA = `{
     isEdit: <?php echo $isEdit ? 'true' : 'false'; ?>,
     selectedGlobalAssets: <?php echo json_encode($selectedGlobalAssets ?? []); ?>,
     <?php if ($isEdit && !empty($template['tags'])): ?>
     existingTags: <?php echo json_encode($template['tags']); ?>,
     <?php else: ?>
     existingTags: [],
     <?php endif; ?>
     template: <?php echo json_encode($template ?? []); ?>
 }`;


/**
 * Template Form JavaScript
 * Enhanced Step Navigation and Form Management
 * Author: Your Team
 * Version: 1.0.0
 */

// ============================================================================
// GLOBAL VARIABLES AND CONFIGURATION
// ============================================================================

let templateTagsManager;
let currentStep = 1;
const totalSteps = 4;

// Color Theme Configuration
const colorThemes = {
    'corporate-light': {
        primary: '#0C5BE2',
        secondary: '#F3F4F6',
        accent: '#4F46E5'
    },
    'corporate-dark': {
        primary: '#1F2937',
        secondary: '#374151',
        accent: '#6366F1'
    },
    'nature': {
        primary: '#059669',
        secondary: '#D1FAE5',
        accent: '#047857'
    },
    'ocean': {
        primary: '#0EA5E9',
        secondary: '#E0F2FE',
        accent: '#0284C7'
    },
    'warm': {
        primary: '#F97316',
        secondary: '#FFF7ED',
        accent: '#EA580C'
    }
};

// ============================================================================
// TEMPLATE TAGS MANAGEMENT CLASS
// ============================================================================

class TemplateTagsManager {
    constructor() {
        this.selectedTags = new Set();
        this.debounceTimer = null;
        this.tagInput = document.getElementById('tagInput');
        this.tagContainer = document.getElementById('tags-container');
        this.tagSuggestions = document.getElementById('tagSuggestions');
        
        this.init();
    }
    
    init() {
        if (!this.tagInput) return;
        
        this.setupEventListeners();
        this.loadExistingTags();
    }
    
    setupEventListeners() {
        // Input event listeners
        this.tagInput.addEventListener('input', (e) => {
            clearTimeout(this.debounceTimer);
            const query = e.target.value.trim();
            
            if (query.length >= 1) {
                this.debounceTimer = setTimeout(() => {
                    this.searchTags(query);
                }, 300);
            } else {
                this.hideSuggestions();
            }
        });

        this.tagInput.addEventListener('keydown', async (e) => {
            if ((e.key === 'Enter' || e.key === ',') && e.target.value.trim()) {
                e.preventDefault();
                const tagName = e.target.value.trim().replace(/,/g, '');
                if (tagName) {
                    await this.addTag(tagName);
                }
            } else if (e.key === 'Escape') {
                this.hideSuggestions();
            }
        });

        // Close suggestions when clicking outside
        document.addEventListener('click', (e) => {
            if (!this.tagInput.contains(e.target) && !this.tagSuggestions.contains(e.target)) {
                this.hideSuggestions();
            }
        });
        
        // Form submission handler
        const templateForm = document.querySelector('.template-form');
        if (templateForm) {
            templateForm.addEventListener('submit', (e) => {
                this.ensureTagInputs();
            });
        }
    }
    
    async loadExistingTags() {
        // This will be populated by PHP if editing a template
        // Implementation depends on your PHP template data
    }
    
    async searchTags(query) {
        try {
            const response = await fetch(`${window.BASE_URL}/modules/tags/tags_search.php?search=${encodeURIComponent(query)}`);
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const data = await response.json();
            
            if (data.success && data.data && Array.isArray(data.data)) {
                const suggestions = data.data.map(tag => ({
                    id: tag.id,
                    tag: tag.tag,
                    created_at: tag.created_at,
                    updated_at: tag.updated_at
                }));
                
                this.showSuggestions(suggestions);
            }
        } catch (error) {
            console.error('Error searching tags:', error);
        }
    }
    
    async createTag(name) {
        try {
            const response = await fetch(`${window.BASE_URL}/modules/tags/tags_create.php`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ tag: name.trim() })
            });
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const data = await response.json();
            
            if (data.success && data.data) {
                return {
                    id: data.data.id || data.data.last_insert_id,
                    tag: name.trim()
                };
            }
            
            throw new Error(data.message || 'Failed to create tag');
        } catch (error) {
            console.error('Error creating tag:', error);
            throw error;
        }
    }
    
    async addTag(tagInput) {
        let tagData;
        
        try {
            if (typeof tagInput === 'string') {
                tagData = await this.createTag(tagInput);
                if (!tagData) return;
            } else {
                tagData = tagInput;
            }
            
            if (this.selectedTags.has(tagData.id.toString())) {
                return;
            }
            
            this.selectedTags.add(tagData.id.toString());
            this.tagContainer.appendChild(this.createTagElement(tagData));
            
            const form = document.querySelector('.template-form');
            form.appendChild(this.createHiddenTagInput(tagData.id));
            
            this.tagInput.value = '';
            this.hideSuggestions();
            
        } catch (error) {
            showNotification('Failed to add tag: ' + error.message, 'error');
        }
    }
    
    removeTag(tagId) {
        this.selectedTags.delete(tagId);
        
        const tagElement = document.querySelector(`span[data-tag-id="${tagId}"]`);
        if (tagElement) {
            tagElement.remove();
        }
        
        const hiddenInput = document.querySelector(`input[data-tag-id="${tagId}"]`);
        if (hiddenInput) {
            hiddenInput.remove();
        }
    }
    
    createTagElement(tagData) {
        const tag = document.createElement('span');
        tag.className = 'inline-flex items-center px-2 py-1 bg-[#0C5BE2]/10 text-[#0C5BE2] text-xs font-medium rounded';
        
        const tagText = tagData.tag || tagData.tag_name || 'Unknown Tag';
        
        tag.innerHTML = `
            ${tagText}
            <button type="button" class="ml-1 tag-remove-btn" onclick="templateTagsManager.removeTag('${tagData.id}')">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                </svg>
            </button>
        `;
        tag.dataset.tagId = tagData.id;
        return tag;
    }
    
    createHiddenTagInput(tagId) {
        const hiddenInput = document.createElement('input');
        hiddenInput.type = 'hidden';
        hiddenInput.name = 'tags[]';
        hiddenInput.value = tagId;
        hiddenInput.dataset.tagId = tagId;
        return hiddenInput;
    }
    
    showSuggestions(suggestions) {
        this.tagSuggestions.innerHTML = '';
        
        const filteredSuggestions = suggestions.filter(tag => 
            !this.selectedTags.has(tag.id.toString())
        );
        
        if (filteredSuggestions.length === 0 && this.tagInput.value.trim()) {
            const div = document.createElement('div');
            div.className = 'px-3 py-2 text-sm text-gray-500 italic cursor-pointer hover:bg-gray-50';
            div.innerHTML = `Create new tag: "${this.tagInput.value.trim()}"`;
            div.onclick = () => this.addTag(this.tagInput.value.trim());
            this.tagSuggestions.appendChild(div);
        } else {
            filteredSuggestions.forEach(tag => {
                const div = document.createElement('div');
                div.className = 'px-3 py-2 hover:bg-gray-50 cursor-pointer text-sm';
                div.textContent = tag.tag;
                div.onclick = () => this.addTag(tag);
                this.tagSuggestions.appendChild(div);
            });
            
            if (this.tagInput.value.trim() && !filteredSuggestions.some(tag => 
                tag.tag.toLowerCase() === this.tagInput.value.trim().toLowerCase()
            )) {
                const div = document.createElement('div');
                div.className = 'px-3 py-2 hover:bg-gray-50 cursor-pointer text-sm text-[#0C5BE2] border-t border-gray-200';
                div.innerHTML = `Create: "${this.tagInput.value.trim()}"`;
                div.onclick = () => this.addTag(this.tagInput.value.trim());
                this.tagSuggestions.appendChild(div);
            }
        }

        this.tagSuggestions.classList.remove('hidden');
    }
    
    hideSuggestions() {
        this.tagSuggestions.classList.add('hidden');
    }
    
    ensureTagInputs() {
        this.selectedTags.forEach(tagId => {
            const existingInput = document.querySelector(`input[data-tag-id="${tagId}"]`);
            if (!existingInput) {
                const hiddenInput = this.createHiddenTagInput(tagId);
                const form = document.querySelector('.template-form');
                form.appendChild(hiddenInput);
            }
        });
        
        console.log('Submitting template with tags:', Array.from(this.selectedTags));
    }
}

// ============================================================================
// ENHANCED STEP NAVIGATION FUNCTIONS
// ============================================================================

let isEditMode = false;
let allowDirectNavigation = false;

function initializeStepNavigation() {
    // Check if we're in edit mode
    isEditMode = window.TEMPLATE_DATA?.isEdit || false;
    allowDirectNavigation = isEditMode;
    
    updateSteps();
    setupStepClickHandlers();

    // Button Event Listeners for Steps
    document.getElementById('nextBtn')?.addEventListener('click', () => {
        if (currentStep < totalSteps) {
            if (validateCurrentStep() || allowDirectNavigation) {
                currentStep++;
                updateSteps();
            }
        }
    });

    document.getElementById('prevBtn')?.addEventListener('click', () => {
        if (currentStep > 1) {
            currentStep--;
            updateSteps();
        }
    });
    
    // Initialize keyboard shortcuts and enhanced features
    initializeStepShortcuts();
    if (allowDirectNavigation) {
        //createStepBreadcrumb();
        //showEditModeIndicator();
    }
}

function setupStepClickHandlers() {
    document.querySelectorAll('.step-item').forEach((item, index) => {
        const stepNum = index + 1;
        
        if (allowDirectNavigation) {
            // Make step clickable in edit mode
            item.style.cursor = 'pointer';
            item.classList.add('step-clickable');
            
            item.addEventListener('click', function() {
                // Only allow navigation if it's edit mode or step is completed/current
                if (allowDirectNavigation || stepNum <= currentStep) {
                    navigateToStep(stepNum);
                }
            });

            // Add hover effects
            item.addEventListener('mouseenter', function() {
                if (stepNum !== currentStep) {
                    this.classList.add('step-hover');
                }
            });

            item.addEventListener('mouseleave', function() {
                this.classList.remove('step-hover');
            });
        }
    });
    
    // Add click handlers for breadcrumb buttons
    document.querySelectorAll('.step-breadcrumb-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const targetStep = parseInt(this.getAttribute('data-target'));
            navigateToStep(targetStep);
            
            // Update active breadcrumb
            document.querySelectorAll('.step-breadcrumb-btn').forEach(b => {
                b.classList.remove('active');
            });
            this.classList.add('active');
        });
    });
}

function navigateToStep(targetStep) {
    if (targetStep < 1 || targetStep > totalSteps) return;
    
    // Add transition effect
    const currentContent = document.getElementById(`step${currentStep}Content`);
    const targetContent = document.getElementById(`step${targetStep}Content`);
    
    if (currentContent && targetContent) {
        // Fade out current step
        currentContent.style.opacity = '0.5';
        
        setTimeout(() => {
            currentStep = targetStep;
            updateSteps();
            
            // Fade in target step
            setTimeout(() => {
                targetContent.style.opacity = '1';
            }, 150);
        }, 150);
    } else {
        currentStep = targetStep;
        updateSteps();
    }
    
    // Show notification about step change
    if (allowDirectNavigation) {
        showStepChangeNotification(targetStep);
    }
    
    // Dispatch custom event
    dispatchStepChange(targetStep);
}

function showStepChangeNotification(stepNum) {
    const stepNames = {
        1: 'Basic Information',
        2: 'Site Settings', 
        3: 'Appearance',
        4: 'Advanced'
    };
    
    const notification = document.createElement('div');
    notification.className = 'fixed top-20 right-4 bg-blue-500 text-white px-4 py-2 rounded-lg text-sm z-50 transform transition-all duration-300 translate-x-full shadow-lg';
    notification.innerHTML = `
        <div class="flex items-center">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5-5 5M6 12h12"/>
            </svg>
            Jumped to: ${stepNames[stepNum]}
        </div>
    `;
    
    document.body.appendChild(notification);
    
    // Trigger animation
    setTimeout(() => {
        notification.classList.remove('translate-x-full');
    }, 10);
    
    // Remove notification
    setTimeout(() => {
        notification.classList.add('translate-x-full');
        setTimeout(() => notification.remove(), 300);
    }, 2500);
}

function validateCurrentStep() {
    if (allowDirectNavigation) {
        return true; // Skip validation in edit mode for direct navigation
    }
    
    if (currentStep === 1) {
        let hasError = false;
        
        ['templateName', 'templateShortDescription', 'templateCategory'].forEach(fieldId => {
            const field = document.getElementById(fieldId);
            if (field) clearFieldError(field);
        });

        const templateNameField = document.getElementById('templateName');
        const templateName = templateNameField.value.trim();
        if (!templateName) {
            showFieldError(templateNameField, 'Template Name is required');
            hasError = true;
        }

        const templateShortDescField = document.getElementById('templateShortDescription');
        const templateShortDescription = templateShortDescField.value.trim();
        if (!templateShortDescription) {
            showFieldError(templateShortDescField, 'Short Description is required');
            hasError = true;
        }

        const templateCategoriesField = document.getElementById('templateCategory');
        const templateCategories = Array.from(templateCategoriesField.selectedOptions).map(opt => opt.value);
        if (templateCategories.length === 0) {
            showFieldError(templateCategoriesField, 'Please select at least one category');
            hasError = true;
        }
        
        if (hasError) {
            showNotification('Please fill in all required fields before continuing.', 'error');
            return false;
        }
    } else if (currentStep === 2) {
        const siteTitleField = document.querySelector('input[name="site_title"]');
        if (siteTitleField && !siteTitleField.value.trim()) {
            showFieldError(siteTitleField, 'Site Title is required');
            showNotification('Please fill in all required fields before continuing.', 'error');
            return false;
        }
    }
    
    return true;
}

function updateSteps() {
    document.querySelectorAll('.step-item').forEach((item, index) => {
        const stepNum = index + 1;
        const outerCircle = item.querySelector('[class*="absolute -inset"]');
        const innerCircle = item.querySelector('[class*="relative flex"]');
        const numberSpan = innerCircle.querySelector('span');
        const title = item.querySelector('h3');
        const desc = item.querySelector('p');
        const connector = item.querySelector('[class*="absolute left-4"]');

        // Remove previous classes
        item.classList.remove('step-accessible', 'step-current', 'step-completed', 'step-disabled');

        if (stepNum < currentStep) {
            // Completed state
            outerCircle.className = 'absolute -inset-0.5 bg-green-100 rounded-full';
            innerCircle.className = 'relative flex items-center justify-center w-8 h-8 bg-green-500 rounded-full';
            numberSpan.innerHTML = '<svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>';
            title.className = 'text-base font-semibold text-gray-900';
            desc.className = 'text-sm text-gray-500';
            if (connector) connector.className = 'absolute left-4 top-8 h-full w-0.5 bg-green-200 -ml-px';
            item.classList.add('step-completed');
            
            // Add clickable indicator in edit mode
            if (allowDirectNavigation) {
                item.classList.add('step-accessible');
                title.classList.add('hover:text-green-600');
            }
            
        } else if (stepNum === currentStep) {
            // Active state
            outerCircle.className = 'absolute -inset-0.5 bg-blue-100 rounded-full';
            innerCircle.className = 'relative flex items-center justify-center w-8 h-8 bg-blue-600 rounded-full';
            numberSpan.innerHTML = stepNum;
            numberSpan.className = 'text-white text-sm';
            title.className = 'text-base font-semibold text-gray-900';
            desc.className = 'text-sm text-gray-500';
            if (connector) connector.className = 'absolute left-4 top-8 h-full w-0.5 bg-gray-200 -ml-px';
            item.classList.add('step-current');
            
        } else {
            // Future/Disabled state
            if (allowDirectNavigation) {
                // In edit mode, make future steps accessible
                outerCircle.className = 'absolute -inset-0.5 bg-gray-200 hover:bg-blue-100 rounded-full transition-colors';
                innerCircle.className = 'relative flex items-center justify-center w-8 h-8 bg-gray-100 hover:bg-blue-100 rounded-full transition-colors';
                numberSpan.innerHTML = stepNum;
                numberSpan.className = 'text-gray-500 hover:text-blue-600 text-sm transition-colors';
                title.className = 'text-base font-semibold text-gray-400 hover:text-blue-600 transition-colors';
                desc.className = 'text-sm text-gray-400 hover:text-blue-500 transition-colors';
                item.classList.add('step-accessible');
            } else {
                // In create mode, keep future steps disabled
                outerCircle.className = 'absolute -inset-0.5 bg-gray-200 rounded-full';
                innerCircle.className = 'relative flex items-center justify-center w-8 h-8 bg-gray-100 rounded-full';
                numberSpan.innerHTML = stepNum;
                numberSpan.className = 'text-gray-500 text-sm';
                title.className = 'text-base font-semibold text-gray-400';
                desc.className = 'text-sm text-gray-400';
                item.classList.add('step-disabled');
            }
            if (connector) connector.className = 'absolute left-4 top-8 h-full w-0.5 bg-gray-200 -ml-px';
        }
    });

    // Show/hide content
    document.querySelectorAll('.step-content').forEach((content, index) => {
        if (index + 1 === currentStep) {
            content.classList.remove('hidden');
            content.style.opacity = '1';
        } else {
            content.classList.add('hidden');
            content.style.opacity = '0';
        }
    });

    // Update buttons
    const prevBtn = document.getElementById('prevBtn');
    const nextBtn = document.getElementById('nextBtn');
    const submitBtn = document.getElementById('submitBtn');
    
    if (prevBtn && nextBtn) {
        prevBtn.disabled = currentStep === 1;
        prevBtn.style.opacity = currentStep === 1 ? '0.5' : '1';
        nextBtn.textContent = currentStep === totalSteps ? 'Finish' : 'Next Step';
        
        if (submitBtn) {
            if (currentStep === totalSteps) {
                submitBtn.classList.remove('hidden');
                nextBtn.classList.add('hidden');
            } else {
                submitBtn.classList.add('hidden');
                nextBtn.classList.remove('hidden');
            }
        }
    }
    
}

function initializeStepShortcuts() {
    if (!allowDirectNavigation) return;
    
    document.addEventListener('keydown', function(e) {
        // Ctrl/Cmd + Number to jump to step
        if ((e.ctrlKey || e.metaKey) && e.key >= '1' && e.key <= '4') {
            e.preventDefault();
            const targetStep = parseInt(e.key);
            navigateToStep(targetStep);
        }
    });
}

function createStepBreadcrumb() {
    if (!allowDirectNavigation) return;
    
    const breadcrumb = document.createElement('div');
    breadcrumb.id = 'stepBreadcrumb';
    breadcrumb.className = 'flex items-center space-x-2 text-xs text-gray-500 mb-4 pt-4 border-t';
    
    const stepNames = ['Basic', 'Settings', 'Appearance', 'Advanced'];
    
    stepNames.forEach((name, index) => {
        const stepNum = index + 1;
        
        if (index > 0) {
            const separator = document.createElement('span');
            separator.textContent = '›';
            separator.className = 'text-gray-300';
            breadcrumb.appendChild(separator);
        }
        
        const stepLink = document.createElement('button');
        stepLink.type = 'button';
        stepLink.textContent = name;
        stepLink.className = stepNum === currentStep 
            ? 'text-blue-600 font-medium step-breadcrumb-btn active' 
            : 'text-gray-500 hover:text-blue-600 transition-colors step-breadcrumb-btn';
        
        stepLink.setAttribute('data-target', stepNum);
        stepLink.addEventListener('click', () => navigateToStep(stepNum));
        breadcrumb.appendChild(stepLink);
    });
    
    // Insert breadcrumb
    const stepNavigation = document.querySelector('.w-\\[275px\\] .relative');
    if (stepNavigation) {
        stepNavigation.appendChild(breadcrumb);
    }
}

function showEditModeIndicator() {
    // This will be handled in PHP template
}

function dispatchStepChange(stepNumber) {
    const event = new CustomEvent('stepChanged', {
        detail: { step: stepNumber }
    });
    document.dispatchEvent(event);
}

// Make navigateToStep globally available
window.navigateToStep = navigateToStep;

// ============================================================================
// FORM VALIDATION FUNCTIONS
// ============================================================================

function showFieldError(field, message) {
    const existingError = field.parentElement.querySelector('.error-message');
    if (existingError) {
        existingError.remove();
    }

    field.classList.add('border-red-500');

    const errorDiv = document.createElement('div');
    errorDiv.className = 'error-message text-red-500 text-sm mt-1';
    errorDiv.textContent = message;
    field.parentElement.appendChild(errorDiv);
}

function clearFieldError(field) {
    field.classList.remove('border-red-500');

    const errorMessage = field.parentElement.querySelector('.error-message');
    if (errorMessage) {
        errorMessage.remove();
    }
}

function showNotification(message, type = 'success') {
    const notification = document.createElement('div');
    notification.style.opacity = '0';
    notification.className = `fixed top-4 right-4 p-4 rounded-lg text-white z-50 transition-all duration-300 ${type === 'success' ? 'bg-green-500' : 'bg-red-500'}`;
    notification.textContent = message;
    document.body.appendChild(notification);

    notification.offsetHeight;
    notification.style.opacity = '1';
    
    setTimeout(() => {
        notification.style.opacity = '0';
        setTimeout(() => notification.remove(), 300);
    }, 3000);
}

// ============================================================================
// CATEGORY MANAGEMENT FUNCTIONS
// ============================================================================

function initializeCategoryManagement() {
    const showNewCategoryCheckbox = document.getElementById('showNewCategory');
    if (showNewCategoryCheckbox) {
        showNewCategoryCheckbox.addEventListener('change', function() {
            const newCategoryInput = document.getElementById('newCategoryInput');
            const templateCategoryWrap = document.querySelector('.templateCategoryWrap');
            
            if (this.checked) {
                newCategoryInput.classList.remove('hidden');
                templateCategoryWrap.classList.add('hidden');
            } else {
                newCategoryInput.classList.add('hidden');
                templateCategoryWrap.classList.remove('hidden');
            }
        });
    }

    document.addEventListener('click', function(event) {
        const newCategoryInput = document.getElementById('newCategoryInput');
        const showNewCategoryLabel = document.querySelector('label[for="showNewCategory"]');
        const showNewCategoryCheckbox = document.getElementById('showNewCategory');
        
        if (!newCategoryInput.contains(event.target) && 
            !showNewCategoryLabel.contains(event.target) && 
            event.target !== showNewCategoryCheckbox &&
            !newCategoryInput.classList.contains('hidden')) {
            
            hideNewTemplateCategoryInput();
        }
    });
}

function hideNewTemplateCategoryInput() {
    const newCategoryInput = document.getElementById('newCategoryInput');
    const templateCategoryWrap = document.querySelector('.templateCategoryWrap');
    const showNewCategoryCheckbox = document.getElementById('showNewCategory');
    
    newCategoryInput.classList.add('hidden');
    templateCategoryWrap.classList.remove('hidden');
    showNewCategoryCheckbox.checked = false;
}

window.addNewCategory = async function() {
    const newCategoryName = document.getElementById('newCategoryName').value.trim();
    if (!newCategoryName) {
        showNotification('Please enter a category name', 'error');
        return;
    }
    
    try {
        const response = await fetch(`${window.BASE_URL}/modules/template_categories/template_categories.php`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                name: newCategoryName
            })
        });
        
        const data = await response.json();
        
        if (data.status === 'success' && data.data) {
            const categoryId = data.data.last_insert_id || data.data.id;
            
            if (categoryId) {
                const select = $('#templateCategory');
                const newOption = new Option(newCategoryName, categoryId, true, true);
                select.append(newOption).trigger('change');
                
                document.getElementById('newCategoryName').value = '';
                hideNewTemplateCategoryInput();
                
                showNotification('Category added successfully');
            } else {
                showNotification('Failed to create category: Category ID not found', 'error');
            }
        } else {
            showNotification('Failed to create category: ' + (data.message || 'Unknown error'), 'error');
        }
    } catch (error) {
        console.error('Error creating category:', error);
        showNotification('Failed to create category. Please try again.', 'error');
    }
};

// ============================================================================
// COLOR THEME MANAGEMENT
// ============================================================================

function updateTailwindConfig(colors) {
    const config = {
        theme: {
            extend: {
                colors: {
                    primary: colors.primary,
                    secondary: colors.secondary,
                    accent: colors.accent
                }
            }
        }
    };
    
    const configElement = document.getElementById('tailwindConfig');
    if (configElement) {
        configElement.value = 'tailwind.config = ' + JSON.stringify(config, null, 4);
    }
}

function initColorPickers() {
    ['primary', 'secondary', 'accent'].forEach(type => {
        const colorInput = document.getElementById(type + 'Color');
        const hexInput = document.getElementById(type + 'ColorHex');
        
        if (colorInput && hexInput) {
            colorInput.addEventListener('input', (e) => {
                hexInput.value = e.target.value.toUpperCase();
                updateCustomColors();
            });

            hexInput.addEventListener('input', (e) => {
                const hex = e.target.value;
                if (/^#[0-9A-F]{6}$/i.test(hex)) {
                    colorInput.value = hex;
                    updateCustomColors();
                }
            });
        }
    });
}

function updateCustomColors() {
    const primaryHex = document.getElementById('primaryColorHex');
    const secondaryHex = document.getElementById('secondaryColorHex');
    const accentHex = document.getElementById('accentColorHex');
    
    if (primaryHex && secondaryHex && accentHex) {
        const colors = {
            primary: primaryHex.value,
            secondary: secondaryHex.value,
            accent: accentHex.value
        };
        updateTailwindConfig(colors);
    }
}

function initializeThemeButtons() {
    document.querySelectorAll('[data-theme]').forEach(button => {
        button.addEventListener('click', (e) => {
            document.querySelectorAll('[data-theme]').forEach(btn => {
                btn.classList.remove('border-[#0C5BE2]', 'text-[#0C5BE2]');
                btn.classList.add('border-gray-200', 'text-gray-700');
            });

            const theme = e.currentTarget.dataset.theme;
            
            e.currentTarget.classList.remove('border-gray-200', 'text-gray-700');
            e.currentTarget.classList.add('border-[#0C5BE2]', 'text-[#0C5BE2]');

            const customColorGrid = document.getElementById('customColorGrid');
            if (theme === 'custom') {
                customColorGrid?.classList.remove('hidden');
                updateCustomColors();
            } else {
                customColorGrid?.classList.add('hidden');
                updateTailwindConfig(colorThemes[theme]);
            }

            const themeData = e.currentTarget.getAttribute('data-color-code');
            if (themeData) {
                const colors = themeData.split(',');
                if (colors.length === 3) {
                    document.documentElement.style.setProperty('--prev-primary-color', colors[0].trim());
                    document.documentElement.style.setProperty('--prev-secondary-color', colors[1].trim());
                    document.documentElement.style.setProperty('--prev-accent-color', colors[2].trim());
                }
            }
        });
    });
}

// ============================================================================
// IMAGE UPLOAD HANDLERS
// ============================================================================

function initializeImageUploads() {
    // Logo preview
    const logoInput = document.getElementById('logo_image');
    const logoPreview = document.getElementById('logoPreview');
    
    if (logoInput && logoPreview) {
        logoInput.addEventListener('change', function(e) {
            if (this.files && this.files[0]) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    logoPreview.innerHTML = `<img src="${e.target.result}" alt="Logo Preview" class="max-w-full max-h-full object-contain">`;
                    logoPreview.classList.remove('border-2', 'border-dashed');
                };
                reader.readAsDataURL(this.files[0]);
            }
        });
    }

    // Favicon preview
    const faviconInput = document.getElementById('favicon_image');
    const faviconPreview = document.getElementById('faviconPreview');
    
    if (faviconInput && faviconPreview) {
        faviconInput.addEventListener('change', function(e) {
            if (this.files && this.files[0]) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    faviconPreview.innerHTML = `<img src="${e.target.result}" alt="Favicon Preview" class="max-w-full max-h-full object-contain">`;
                    faviconPreview.classList.remove('border-2', 'border-dashed');
                };
                reader.readAsDataURL(this.files[0]);
            }
        });
    }

    // Screenshot upload
    const screenshotInput = document.getElementById('screenshotUpload');
    if (screenshotInput) {
        screenshotInput.addEventListener('change', function(e) {
            if (this.files) {
                Array.from(this.files).forEach(createScreenshotPreview);
            }
        });
    }
}

function createScreenshotPreview(file) {
    const reader = new FileReader();
    reader.onload = function(e) {
        const previewDiv = document.createElement('div');
        previewDiv.className = 'relative aspect-video group';
        previewDiv.innerHTML = `
            <img src="${e.target.result}" class="w-full h-full object-cover rounded-lg" alt="Screenshot">
            <div class="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity rounded-lg flex items-center justify-center">
                <button type="button" class="text-white hover:text-red-500" onclick="removeScreenshot(this)">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                </button>
            </div>
        `;
        const screenshotPreviews = document.getElementById('screenshotPreviews');
        if (screenshotPreviews) {
            screenshotPreviews.appendChild(previewDiv);
        }
    };
    reader.readAsDataURL(file);
}

window.removeScreenshot = function(button) {
    const previewDiv = button.closest('.relative');
    if (previewDiv) {
        previewDiv.remove();
    }
};

// ============================================================================
// GLOBAL ASSETS MANAGEMENT
// ============================================================================

function initializeGlobalAssets() {
    function updateAssetsCount() {
        const countElement = document.getElementById('selectedAssetsCount');
        const cssCountElement = document.getElementById('selectedCSSCount');
        const jsCountElement = document.getElementById('selectedJSCount');
        
        if (!countElement || !cssCountElement || !jsCountElement) {
            return;
        }
        
        const checkedCount = document.querySelectorAll('input[name="global_assets[]"]:checked').length;
        const checkedCSSCount = document.querySelectorAll('input.css-asset:checked').length;
        const checkedJSCount = document.querySelectorAll('input.js-asset:checked').length;
        
        countElement.textContent = checkedCount;
        cssCountElement.textContent = checkedCSSCount;
        jsCountElement.textContent = checkedJSCount;
    }

    document.querySelectorAll('input[name="global_assets[]"]').forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            const label = this.closest('.asset-label');
            if (label) {
                if (this.checked) {
                    if (this.classList.contains('css-asset')) {
                        label.classList.add('bg-blue-50', 'border-blue-300');
                    } else {
                        label.classList.add('bg-yellow-50', 'border-yellow-300');
                    }
                } else {
                    label.classList.remove('bg-blue-50', 'border-blue-300', 'bg-yellow-50', 'border-yellow-300');
                }
            }
            updateAssetsCount();
        });
    });

    // Select All buttons
    const selectAllCSSBtn = document.getElementById('selectAllCSS');
    const selectAllJSBtn = document.getElementById('selectAllJS');

    if (selectAllCSSBtn) {
        selectAllCSSBtn.addEventListener('click', function() {
            const cssCheckboxes = document.querySelectorAll('input.css-asset');
            const allChecked = Array.from(cssCheckboxes).every(cb => cb.checked);
            
            cssCheckboxes.forEach(checkbox => {
                checkbox.checked = !allChecked;
                checkbox.dispatchEvent(new Event('change'));
            });
            
            this.textContent = allChecked ? 'Select All CSS' : 'Deselect All CSS';
        });
    }

    if (selectAllJSBtn) {
        selectAllJSBtn.addEventListener('click', function() {
            const jsCheckboxes = document.querySelectorAll('input.js-asset');
            const allChecked = Array.from(jsCheckboxes).every(cb => cb.checked);
            
            jsCheckboxes.forEach(checkbox => {
                checkbox.checked = !allChecked;
                checkbox.dispatchEvent(new Event('change'));
            });
            
            this.textContent = allChecked ? 'Select All JS' : 'Deselect All JS';
        });
    }

    // Initial count update
    updateAssetsCount();
}

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

window.copyConfig = function() {
    const configTextarea = document.getElementById('tailwindConfig');
    if (configTextarea) {
        configTextarea.select();
        document.execCommand('copy');
        
        const button = document.querySelector('button[onclick="copyConfig()"]');
        if (button) {
            const originalSvg = button.innerHTML;
            button.innerHTML = `
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-green-500" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <polyline points="20 6 9 17 4 12"/>
                </svg>
            `;
            setTimeout(() => {
                button.innerHTML = originalSvg;
            }, 1500);
        }
        
        showNotification('Configuration copied to clipboard!');
    }
};

// ============================================================================
// INITIALIZATION FUNCTIONS
// ============================================================================

function initializeSelect2() {
    if (typeof $ !== 'undefined' && $.fn.select2) {
        $('#templateCategory').select2({
            theme: 'default',
            placeholder: 'Select categories',
            allowClear: true,
            width: '100%',
            closeOnSelect: false,
            selectionCssClass: 'select2--large',
            templateResult: function(data) {
                if (!data.id) return data.text;
                return $('<span class="text-sm">' + data.text + '</span>');
            },
            templateSelection: function(data) {
                if (!data.id) return data.text;
                return $('<span class="text-sm">' + data.text + '</span>');
            }
        });
    } else {
        console.error('Select2 is not loaded');
    }
}

function initializeCKEditor() {
    if (typeof ClassicEditor !== 'undefined') {
        ClassicEditor
            .create(document.querySelector('#detailed_description'), {
                toolbar: {
                    items: ['heading', '|', 'bold', 'italic', 'link', 'bulletedList', 'numberedList', '|', 'undo', 'redo', '|', 'sourceEditing']
                },
                placeholder: 'Comprehensive description of features and use cases',
                removePlugins: [],
                htmlSupport: {
                    allow: [
                        {
                            name: /.*/,
                            attributes: true,
                            classes: true,
                            styles: true
                        }
                    ]
                }
            })
            .then(editor => {
                console.log('CKEditor initialized successfully');
                window.templateCKEditor = editor; // Store reference for form submission
            })
            .catch(error => {
                console.error('CKEditor initialization error:', error);
            });
    } else {
        console.error('CKEditor is not loaded');
    }
}

function initializeFormSubmission() {
    const templateForm = document.querySelector('.template-form');
    if (templateForm) {
        templateForm.addEventListener('submit', function(e) {
            // Update CKEditor content before submission
            if (window.templateCKEditor) {
                const detailedDescTextarea = document.getElementById('detailed_description');
                if (detailedDescTextarea) {
                    detailedDescTextarea.value = window.templateCKEditor.getData();
                }
            }
            
            // Ensure tags are properly included
            if (templateTagsManager) {
                templateTagsManager.ensureTagInputs();
            }
            
            console.log('Template form submitted');
        });
    }
}

function initializeTemplateStatusHandlers() {
    document.querySelectorAll('input[name="template_status"]').forEach(radio => {
        radio.addEventListener('change', function() {
            const status = parseInt(this.value);
            console.log('Template status changed to:', status);
            
            // Add visual feedback
            const labels = document.querySelectorAll('input[name="template_status"] + label');
            labels.forEach(label => {
                label.classList.remove('bg-blue-50', 'text-blue-600', 'border-blue-200');
            });
            
            const selectedLabel = this.nextElementSibling;
            if (selectedLabel) {
                selectedLabel.classList.add('bg-blue-50', 'text-blue-600', 'border-blue-200');
            }
        });
    });
}

function initializeGlobalAssetsObserver() {
    const step4Observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.type === 'attributes' && 
                mutation.attributeName === 'class') {
                const step4Element = document.getElementById('step4Content');
                if (step4Element && !step4Element.classList.contains('hidden')) {
                    console.log('Step 4 became visible, initializing global assets...');
                    initializeGlobalAssets();
                    step4Observer.disconnect(); // Only initialize once
                }
            }
        });
    });
    
    const step4Element = document.getElementById('step4Content');
    if (step4Element) {
        step4Observer.observe(step4Element, { attributes: true });
        
        // Also initialize immediately if we're already on step 4
        if (!step4Element.classList.contains('hidden')) {
            console.log('Already on Step 4, initializing global assets immediately');
            initializeGlobalAssets();
        }
    }
}

// ============================================================================
// DRAG AND DROP FUNCTIONALITY
// ============================================================================

function initializeDragAndDrop() {
    const dropZone = document.getElementById('screenshotDropZone');
    
    if (!dropZone) return;

    // Prevent default drag behaviors
    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
        dropZone.addEventListener(eventName, preventDefaults, false);
        document.body.addEventListener(eventName, preventDefaults, false);
    });

    // Highlight drop zone when item is dragged over it
    ['dragenter', 'dragover'].forEach(eventName => {
        dropZone.addEventListener(eventName, highlight, false);
    });

    ['dragleave', 'drop'].forEach(eventName => {
        dropZone.addEventListener(eventName, unhighlight, false);
    });

    // Handle dropped files
    dropZone.addEventListener('drop', handleDrop, false);

    function preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }

    function highlight() {
        dropZone.classList.add('border-blue-500', 'bg-blue-50');
    }

    function unhighlight() {
        dropZone.classList.remove('border-blue-500', 'bg-blue-50');
    }

    function handleDrop(e) {
        const dt = e.dataTransfer;
        const files = dt.files;

        if (files.length > 0) {
            const screenshotInput = document.getElementById('screenshotUpload');
            
            // Create a new DataTransfer object
            const dataTransfer = new DataTransfer();
            
            // Add existing files
            if (screenshotInput.files) {
                Array.from(screenshotInput.files).forEach(file => dataTransfer.items.add(file));
            }
            
            // Add new dropped files
            Array.from(files).forEach(file => {
                if (file.type.startsWith('image/')) {
                    dataTransfer.items.add(file);
                    createScreenshotPreview(file);
                }
            });
            
            // Update the file input
            screenshotInput.files = dataTransfer.files;
        }
    }
}

// ============================================================================
// KEYBOARD SHORTCUTS
// ============================================================================

function initializeKeyboardShortcuts() {
    document.addEventListener('keydown', function(e) {
        // Ctrl/Cmd + S to save form
        if ((e.ctrlKey || e.metaKey) && e.key === 's') {
            e.preventDefault();
            const submitBtn = document.getElementById('submitBtn');
            if (submitBtn && !submitBtn.classList.contains('hidden')) {
                submitBtn.click();
            }
        }
        
        // Ctrl/Cmd + Right Arrow to go to next step
        if ((e.ctrlKey || e.metaKey) && e.key === 'ArrowRight') {
            e.preventDefault();
            const nextBtn = document.getElementById('nextBtn');
            if (nextBtn && !nextBtn.classList.contains('hidden')) {
                nextBtn.click();
            }
        }
        
        // Ctrl/Cmd + Left Arrow to go to previous step
        if ((e.ctrlKey || e.metaKey) && e.key === 'ArrowLeft') {
            e.preventDefault();
            const prevBtn = document.getElementById('prevBtn');
            if (prevBtn && !prevBtn.disabled) {
                prevBtn.click();
            }
        }
        
        // Escape to close any open modals or dropdowns
        if (e.key === 'Escape') {
            // Close tag suggestions
            if (templateTagsManager) {
                templateTagsManager.hideSuggestions();
            }
            
            // Close new category input
            hideNewTemplateCategoryInput();
        }
    });
}

// ============================================================================
// ERROR HANDLING AND DEBUGGING
// ============================================================================

function setupErrorHandling() {
    window.addEventListener('error', function(e) {
        console.error('Template Form Error:', e.error);
        
        // Show user-friendly error message for critical errors
        if (e.error && e.error.message) {
            showNotification('An error occurred. Please try again.', 'error');
        }
    });
    
    // Handle unhandled promise rejections
    window.addEventListener('unhandledrejection', function(e) {
        console.error('Unhandled Promise Rejection:', e.reason);
        showNotification('An error occurred while processing your request.', 'error');
    });
}

function enableDebugMode() {
    if (window.location.search.includes('debug=true')) {
        console.log('Template Form Debug Mode Enabled');
        
        // Add debug information to global scope
        window.templateFormDebug = {
            currentStep,
            templateTagsManager,
            colorThemes,
            totalSteps
        };
        
        // Add debug panel
        const debugPanel = document.createElement('div');
        debugPanel.id = 'debug-panel';
        debugPanel.className = 'fixed bottom-4 left-4 bg-black text-white p-4 rounded-lg text-xs z-50';
        debugPanel.innerHTML = `
            <div>Debug Panel</div>
            <div>Current Step: <span id="debug-step">${currentStep}</span></div>
            <div>Total Steps: ${totalSteps}</div>
            <div>Tags Selected: <span id="debug-tags">0</span></div>
        `;
        document.body.appendChild(debugPanel);
        
        // Update debug info when step changes
        const originalUpdateSteps = updateSteps;
        updateSteps = function() {
            originalUpdateSteps();
            document.getElementById('debug-step').textContent = currentStep;
        };
    }
}

// ============================================================================
// MAIN INITIALIZATION
// ============================================================================

document.addEventListener('DOMContentLoaded', function() {
    console.log('Initializing Template Form...');
    
    // Set BASE_URL for JavaScript
    window.BASE_URL = window.BASE_URL || '';
    
    try {
        // Core functionality
        templateTagsManager = new TemplateTagsManager();
        initializeStepNavigation(); // This now includes enhanced navigation
        initializeCategoryManagement();
        
        // UI Components
        initializeSelect2();
        initializeCKEditor();
        initColorPickers();
        initializeThemeButtons();
        initializeImageUploads();
        initializeDragAndDrop();
        
        // Form handling
        initializeFormSubmission();
        initializeTemplateStatusHandlers();
        
        // Advanced features
        initializeGlobalAssetsObserver();
        initializeKeyboardShortcuts();
        
        // Error handling and debugging
        setupErrorHandling();
        enableDebugMode();
        
        // Enhanced Step Navigation Features
        if (window.TEMPLATE_DATA?.isEdit) {
            // Additional edit mode setup
            console.log('Edit mode detected - Enhanced navigation enabled');
            
            // Add step click handlers that might not be in PHP
            setTimeout(() => {
                document.querySelectorAll('.step-item[data-step]').forEach(item => {
                    item.addEventListener('click', function() {
                        const targetStep = parseInt(this.getAttribute('data-step'));
                        if (window.navigateToStep) {
                            window.navigateToStep(targetStep);
                        }
                    });
                });
            }, 100);
        }
        
        console.log('Template Form initialized successfully');
        
    } catch (error) {
        console.error('Error initializing Template Form:', error);
        showNotification('Failed to initialize form. Please refresh the page.', 'error');
    }
});

// ============================================================================
// EXPORT FOR TESTING (if needed)
// ============================================================================

if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        TemplateTagsManager,
        initializeStepNavigation,
        validateCurrentStep,
        updateSteps,
        navigateToStep,
        showNotification,
        colorThemes
    };
}