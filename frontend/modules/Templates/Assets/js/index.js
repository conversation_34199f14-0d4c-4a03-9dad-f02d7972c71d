// In modules/Templates/Assets/js/index.js
document.addEventListener('DOMContentLoaded', function () {
    window.addEventListener('load', function () {
        const initFlowbite = window.initFlowbite || (() => {});
        
        // Initialize Flowbite globally (this binds data attributes)
        initFlowbite();
    
        // List of modal IDs you expect to conditionally initialize
        const modalIds = ['deleteModal', 'templatePreviewModal']; // Replace with your actual modal IDs
    
        modalIds.forEach(modalId => {
            const modalElement = document.getElementById(modalId);
            const triggerButton = document.querySelector(`[data-modal-target="${modalId}"]`);
    
            // Only initialize the modal if both modal element and its trigger button exist
            if (modalElement && triggerButton) {
                try {
                    FlowbiteInstances.getInstance('Modal', modalId);
                } catch (error) {
                }
            } else {
                console.log(`Skipped modal "${modalId}" — missing element or trigger.`);
            }
        });
    });
    
    // Initialize template preview modal
    const templatePreviewModalElement = document.getElementById('templatePreviewModal');
    const templatePreviewModal = templatePreviewModalElement
        ? new Modal(templatePreviewModalElement, {
            placement: 'center',
            backdrop: 'dynamic',
            closable: true
        })
        : null;

    // Initialize delete modal
    const deleteModalElement = document.getElementById('deleteModal');
    const deleteModal = deleteModalElement
        ? new Modal(deleteModalElement, {
            placement: 'center',
            backdrop: 'dynamic',
            closable: true
        })
        : null;

    let currentDeleteId = null;

    // Delegated event listeners
    document.addEventListener('click', async function (e) {
        // Show template preview modal
        const previewButton = e.target.closest('[data-preview-id]');
        if (previewButton) {
            const templateId = previewButton.getAttribute('data-preview-id');
            if (templateId) {
                try {
                    const response = await fetch(`${baseUrl}templates/preview?id=${templateId}`, {
                        headers: { 'X-CSRF-TOKEN': csrfToken }
                    });
                    const result = await response.json();
                    
                    if (result.success && result.data) {
                        const data = result.data;
                        if (templatePreviewModal) templatePreviewModal.show();

                        const titleEl = document.querySelector('#templatePreviewModal h3.text-xl');
                        if (titleEl) titleEl.textContent = data.name || 'Template Preview';

                        const screenshotsContainer = document.querySelector('#templatePreviewModal .h-\\[700px\\].overflow-y-auto.scroll-smooth');
                        if (screenshotsContainer) {
                            if (data.multiple_template_screenshot && data.multiple_template_screenshot !== 'null') {
                                try {
                                    const screenshots = JSON.parse(data.multiple_template_screenshot);
                                    screenshotsContainer.innerHTML = screenshots.map(s =>
                                        `<div class="mb-4 p-4">
                                            <img src="${baseUrl}${s}" class="w-full h-auto rounded-lg shadow-sm" alt="Template screenshot">
                                        </div>`).join('');
                                } catch {
                                    screenshotsContainer.innerHTML = '<div class="p-4 text-gray-500">Error loading screenshots</div>';
                                }
                            } else if (data.preview_image) {
                                screenshotsContainer.innerHTML = `
                                    <div class="mb-4 p-4">
                                        <img src="${baseUrl}${data.preview_image}" class="w-full h-auto rounded-lg shadow-sm" alt="Template preview">
                                    </div>`;
                            } else {
                                screenshotsContainer.innerHTML = '<div class="p-4 text-gray-500">No screenshots available</div>';
                            }
                        }

                        const categorySpan = document.querySelector('#templatePreviewModal .preview-category');
                        if (categorySpan && data.categories) {
                            categorySpan.textContent = data.categories.map(cat => cat.name).join(', ') || 'Uncategorized';
                        }

                        const pagesSpan = document.querySelector('#templatePreviewModal .preview-sections');
                        if (pagesSpan && data.pages) {
                            pagesSpan.textContent = data.pages.length.toString();
                        }

                        const descEl = document.querySelector('#templatePreviewModal .text-gray-600.leading-relaxed.detailedDescription');
                        if (descEl) {
                            descEl.innerHTML = data.detailed_description || 'No description available';
                        }
                    } else {
                        console.error('Failed to fetch template data:', result.error || 'Unknown error');
                    }
                } catch (err) {
                    console.error('Error fetching template data:', err);
                }
            }
        }

        // Handle delete button
        const deleteButton = e.target.closest('[data-modal-target="deleteModal"]');
        if (deleteButton) {
            currentDeleteId = deleteButton.getAttribute('data-id');
            const deleteIdInput = document.getElementById('deleteId');
            if (deleteIdInput) deleteIdInput.value = currentDeleteId;
        }
    });

    // Handle delete confirmation
    const confirmDeleteButton = document.querySelector('#deleteModal button[type="submit"]');
    if (confirmDeleteButton) {
        confirmDeleteButton.addEventListener('click', function (e) {
            e.preventDefault();
            if (currentDeleteId) {
                const deleteForm = document.getElementById('deleteForm');
                if (deleteForm) {
                    document.getElementById('deleteId').value = currentDeleteId;
                    deleteForm.submit();
                }
            }
        });
    }
});
