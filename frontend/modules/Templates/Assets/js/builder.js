// Template Builder JavaScript Implementation
// Similar to old structure but adapted for new frontend architecture

let currentPreviewMode = 'desktop';
let sidebarVisible = true;
let currentBlocks = [];
let currentCategories = [];

document.addEventListener('DOMContentLoaded', function() {
    // Initialize the template builder
    initializeBuilder();
});

function initializeBuilder() {
    // Set data from PHP
    currentBlocks = window.blocks || [];
    currentCategories = window.blockCategories || [];
    
    // Initialize components
    setupSidebarToggle();
    setupPreviewModeControls();
    setupBlockSearch();
    setupCategoryFilter();
    setupDragAndDrop();
    setupActionButtons();
    setupNotificationSystem();
    setupTemplatePageSelectors();
    
    // Load blocks initially
    renderBlocks(currentBlocks);
    
    // Initialize preview mode to desktop
    changePreviewMode('desktop');
    
    console.log('Template Builder initialized');
}

// Sidebar Toggle
function setupSidebarToggle() {
    const toggleBtn = document.getElementById('toggleSidebarBtn');
    const sidebar = document.getElementById('builderSidebar');
    const canvasArea = document.getElementById('mainCanvasArea');
    
    if (toggleBtn && sidebar && canvasArea) {
        toggleBtn.addEventListener('click', function() {
            sidebarVisible = !sidebarVisible;
            
            if (sidebarVisible) {
                sidebar.style.marginLeft = '0';
                canvasArea.style.marginLeft = '0';
                sidebar.style.width = '320px';
            } else {
                sidebar.style.marginLeft = '-320px';
                canvasArea.style.marginLeft = '0';
                sidebar.style.width = '0';
            }
        });
    }
}

// Preview Mode Controls
function setupPreviewModeControls() {
    const previewModeButtons = document.querySelectorAll('.preview-mode-btn');
    const deviceFrame = document.getElementById('deviceFrame');
    
    previewModeButtons.forEach(button => {
        button.addEventListener('click', function() {
            const mode = this.dataset.mode;
            changePreviewMode(mode);
            
            // Update active button
            previewModeButtons.forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');
        });
    });
    
    // Refresh preview button
    const refreshBtn = document.getElementById('refreshPreview');
    if (refreshBtn) {
        refreshBtn.addEventListener('click', refreshPreview);
    }
}

function changePreviewMode(mode) {
    currentPreviewMode = mode;
    const deviceFrame = document.getElementById('deviceFrame');
    const previewContainer = document.getElementById('previewContainer');
    
    if (deviceFrame && previewContainer) {
        // Remove existing mode classes from device frame
        deviceFrame.className = deviceFrame.className.replace(/\b(desktop|tablet|mobile)-frame\b/g, '');
        
        // Add new mode class to device frame
        deviceFrame.classList.add(mode + '-frame');
        
        // Update preview container classes
        previewContainer.className = previewContainer.className.replace(/\b(desktop|tablet|mobile)-view\b/g, '');
        previewContainer.classList.add(mode + '-view');
        
        // Update iframe dimensions based on mode
        updateIframeDimensions(mode);
    }
}

function updateIframeDimensions(mode) {
    const deviceFrame = document.getElementById('deviceFrame');
    if (!deviceFrame) return;
    
    switch(mode) {
        case 'desktop':
            deviceFrame.style.width = '100%';
            deviceFrame.style.height = '100%';
            deviceFrame.style.maxWidth = 'none';
            break;
        case 'tablet':
            deviceFrame.style.width = '768px';
            deviceFrame.style.height = '1024px';
            deviceFrame.style.maxWidth = '90%';
            break;
        case 'mobile':
            deviceFrame.style.width = '375px';
            deviceFrame.style.height = '667px';
            deviceFrame.style.maxWidth = '90%';
            break;
    }
}

// Block Search Functionality
function setupBlockSearch() {
    const searchInput = document.getElementById('blockSearch');
    
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            filterBlocks(searchTerm);
        });
    }
}

// Category Filter
function setupCategoryFilter() {
    const categoryFilter = document.getElementById('blockCategoryFilter');
    
    if (categoryFilter) {
        categoryFilter.addEventListener('change', function() {
            const categoryId = this.value;
            filterBlocksByCategory(categoryId);
        });
    }
}

// Filter blocks by search term
function filterBlocks(searchTerm) {
    const blockItems = document.querySelectorAll('.block-item');
    let visibleCount = 0;
    
    blockItems.forEach(item => {
        const blockName = item.querySelector('h3').textContent.toLowerCase();
        const blockDesc = item.querySelector('p').textContent.toLowerCase();
        const categoryName = item.dataset.categoryName ? item.dataset.categoryName.toLowerCase() : '';
        
        const isVisible = !searchTerm || 
                         blockName.includes(searchTerm) || 
                         blockDesc.includes(searchTerm) || 
                         categoryName.includes(searchTerm);
        
        item.style.display = isVisible ? 'block' : 'none';
        if (isVisible) visibleCount++;
    });
    
    // Show no results message
    showNoResultsMessage(visibleCount === 0);
}

// Filter blocks by category
function filterBlocksByCategory(categoryId) {
    const blockItems = document.querySelectorAll('.block-item');
    let visibleCount = 0;
    
    blockItems.forEach(item => {
        const blockCategoryId = item.dataset.categoryId;
        const isVisible = !categoryId || blockCategoryId === categoryId;
        
        item.style.display = isVisible ? 'block' : 'none';
        if (isVisible) visibleCount++;
    });
    
    // Show no results message
    showNoResultsMessage(visibleCount === 0);
    
    // Clear search when category changes
    const searchInput = document.getElementById('blockSearch');
    if (searchInput) {
        searchInput.value = '';
    }
}

// Show/hide no results message
function showNoResultsMessage(show) {
    const blocksList = document.getElementById('blocksList');
    let noResultsMsg = document.getElementById('noResultsMessage');
    
    if (show) {
        if (!noResultsMsg) {
            noResultsMsg = document.createElement('div');
            noResultsMsg.id = 'noResultsMessage';
            noResultsMsg.className = 'text-center py-8';
            noResultsMsg.innerHTML = `
                <div class="text-gray-400">
                    <svg class="w-12 h-12 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    <p class="text-sm text-gray-500">No blocks found matching your criteria</p>
                </div>
            `;
            blocksList.appendChild(noResultsMsg);
        }
        noResultsMsg.style.display = 'block';
    } else {
        if (noResultsMsg) {
            noResultsMsg.style.display = 'none';
        }
    }
}

// Render blocks in the sidebar
function renderBlocks(blocks) {
    const blocksList = document.getElementById('blocksList');
    if (!blocksList) return;
    
    if (blocks.length === 0) {
        blocksList.innerHTML = `
            <div class="text-center py-8">
                <div class="text-gray-400">
                    <svg class="w-12 h-12 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                    </svg>
                    <p class="text-sm text-gray-500">No blocks available</p>
                </div>
            </div>
        `;
        return;
    }
    
    // Blocks are already rendered by PHP, just setup interactions
    setupBlockInteractions();
}

// Setup block interactions
function setupBlockInteractions() {
    const blockItems = document.querySelectorAll('.block-item');
    
    blockItems.forEach(item => {
        // Add click handler
        item.addEventListener('click', function() {
            selectBlock(this);
        });
        
        // Add drag start handler
        item.addEventListener('dragstart', function(e) {
            handleDragStart(e, this);
        });
        
        // Add drag end handler
        item.addEventListener('dragend', function(e) {
            handleDragEnd(e, this);
        });
    });
}

// Select block functionality
function selectBlock(blockElement) {
    // Remove previous selections
    document.querySelectorAll('.block-item.selected').forEach(item => {
        item.classList.remove('selected');
    });
    
    // Add selection to current block
    blockElement.classList.add('selected');
    
    const blockId = blockElement.dataset.blockId;
    const blockName = blockElement.querySelector('h3').textContent;
    console.log('Selected block:', blockId, blockName);
}

// Drag and Drop Setup
function setupDragAndDrop() {
    const templateCanvas = document.getElementById('templateCanvas');
    const canvasArea = document.getElementById('mainCanvasArea');
    const blockItems = document.querySelectorAll('.block-item');
    
    // Setup drag events for block items
    blockItems.forEach(item => {
        item.addEventListener('dragstart', function(e) {
            const blockId = item.dataset.blockId;
            const blockType = item.dataset.blockType;
            const categoryId = item.dataset.categoryId;
            const categoryName = item.dataset.categoryName;
            
            // Store block data for drag operation
            const blockData = {
                id: blockId,
                type: blockType,
                category_id: categoryId,
                category_name: categoryName,
                name: item.querySelector('h3').textContent
            };
            
            e.dataTransfer.setData('application/json', JSON.stringify(blockData));
            item.classList.add('dragging');
            
            // Notify iframe about drag start
            if (templateCanvas && templateCanvas.contentWindow) {
                templateCanvas.contentWindow.postMessage({
                    type: 'dragStart',
                    blockData: blockData
                }, '*');
            }
        });
        
        item.addEventListener('dragend', function(e) {
            item.classList.remove('dragging');
            
            // Notify iframe about drag end
            if (templateCanvas && templateCanvas.contentWindow) {
                templateCanvas.contentWindow.postMessage({
                    type: 'dragEnd'
                }, '*');
            }
        });
    });
    
    // Setup canvas area drag events
    if (canvasArea) {
        canvasArea.addEventListener('dragover', function(e) {
            e.preventDefault();
            canvasArea.style.backgroundColor = 'rgba(59, 130, 246, 0.1)';
        });
        
        canvasArea.addEventListener('dragleave', function(e) {
            canvasArea.style.backgroundColor = '';
        });
        
        canvasArea.addEventListener('drop', function(e) {
            e.preventDefault();
            canvasArea.style.backgroundColor = '';
            
            try {
                const data = JSON.parse(e.dataTransfer.getData('application/json'));
                
                // Send block data to iframe for positioning
                if (templateCanvas && templateCanvas.contentWindow) {
                    templateCanvas.contentWindow.postMessage({
                        type: 'addBlock',
                        blockData: data
                    }, '*');
                }
                
                addBlockToTemplate(data.id, data.name);
            } catch (error) {
                console.error('Error parsing drag data:', error);
                showNotification('Error adding block', 'error');
            }
        });
    }
    
    // Setup iframe drag events
    if (templateCanvas) {
        templateCanvas.addEventListener('dragover', function(e) {
            e.preventDefault();
        });
        
        templateCanvas.addEventListener('drop', function(e) {
            e.preventDefault();
            
            try {
                const blockData = JSON.parse(e.dataTransfer.getData('application/json'));
                console.log('Block dropped on iframe:', blockData);
                
                // Send block data to iframe
                if (templateCanvas.contentWindow) {
                    templateCanvas.contentWindow.postMessage({
                        type: 'addBlock',
                        blockData: blockData
                    }, '*');
                }
                
                addBlockToTemplate(blockData.id, blockData.name);
            } catch (error) {
                console.error('Error handling block drop:', error);
            }
        });
    }
}

// Handle drag start
function handleDragStart(e, blockElement) {
    const blockId = blockElement.dataset.blockId;
    const blockName = blockElement.querySelector('h3').textContent;
    
    e.dataTransfer.setData('application/json', JSON.stringify({
        blockId: blockId,
        blockName: blockName
    }));
    
    blockElement.style.opacity = '0.5';
    
    // Add visual feedback
    blockElement.classList.add('dragging');
}

// Handle drag end
function handleDragEnd(e, blockElement) {
    blockElement.style.opacity = '1';
    blockElement.classList.remove('dragging');
}

// Add block to template
function addBlockToTemplate(blockId, blockName) {
    console.log('Adding block to template:', blockId, blockName);
    
    // Show loading notification
    showNotification('Adding block to template...', 'info');
    
    // Make API call to add block to template
    fetch('/api/templates/add-block', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: JSON.stringify({
            template_id: window.templateId,
            block_id: blockId
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(`Block "${blockName}" added successfully!`, 'success');
            refreshPreview();
        } else {
            showNotification(data.message || 'Error adding block', 'error');
        }
    })
    .catch(error => {
        console.error('Error adding block:', error);
        showNotification('Error adding block to template', 'error');
    });
}

// Action Buttons Setup
function setupActionButtons() {
    const saveDraftBtn = document.getElementById('saveDraft');
    const publishBtn = document.getElementById('publishTemplate');
    
    if (saveDraftBtn) {
        saveDraftBtn.addEventListener('click', saveTemplateDraft);
    }
    
    if (publishBtn) {
        publishBtn.addEventListener('click', publishTemplate);
    }
}

// Save template as draft
function saveTemplateDraft() {
    showNotification('Saving template as draft...', 'info');
    
    fetch('/api/templates/save-draft', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: JSON.stringify({
            template_id: window.templateId
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Template saved as draft!', 'success');
        } else {
            showNotification(data.message || 'Error saving template', 'error');
        }
    })
    .catch(error => {
        console.error('Error saving template:', error);
        showNotification('Error saving template', 'error');
    });
}

// Publish template
function publishTemplate() {
    showNotification('Publishing template...', 'info');
    
    fetch('/api/templates/publish', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: JSON.stringify({
            template_id: window.templateId
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Template published successfully!', 'success');
            refreshPreview();
        } else {
            showNotification(data.message || 'Error publishing template', 'error');
        }
    })
    .catch(error => {
        console.error('Error publishing template:', error);
        showNotification('Error publishing template', 'error');
    });
}

// Refresh preview
function refreshPreview() {
    const templateCanvas = document.getElementById('templateCanvas');
    if (templateCanvas) {
        const currentSrc = templateCanvas.src;
        const url = new URL(currentSrc);
        url.searchParams.set('_t', Date.now()); // Add timestamp to force reload
        templateCanvas.src = url.toString();
    }
}

// Notification System
function setupNotificationSystem() {
    // Create notification container if it doesn't exist
    if (!document.getElementById('notification')) {
        const notificationContainer = document.createElement('div');
        notificationContainer.id = 'notification';
        notificationContainer.className = 'fixed top-4 right-4 z-50 hidden';
        document.body.appendChild(notificationContainer);
    }
}

// Show notification
function showNotification(message, type = 'info') {
    const notificationContainer = document.getElementById('notification');
    if (!notificationContainer) return;
    
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `mb-4 p-4 rounded-lg shadow-lg max-w-sm transform transition-all duration-300 translate-x-full opacity-0`;
    
    // Set notification style based on type
    switch(type) {
        case 'success':
            notification.classList.add('bg-green-500', 'text-white');
            break;
        case 'error':
            notification.classList.add('bg-red-500', 'text-white');
            break;
        case 'warning':
            notification.classList.add('bg-yellow-500', 'text-white');
            break;
        default:
            notification.classList.add('bg-blue-500', 'text-white');
    }
    
    notification.innerHTML = `
        <div class="flex items-center">
            <div class="flex-shrink-0">
                ${getNotificationIcon(type)}
            </div>
            <div class="ml-3">
                <p class="text-sm font-medium">${message}</p>
            </div>
            <div class="ml-4 flex-shrink-0">
                <button class="inline-flex text-white hover:text-gray-200 focus:outline-none" onclick="this.parentElement.parentElement.parentElement.remove()">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
        </div>
    `;
    
    // Add to container
    notificationContainer.appendChild(notification);
    notificationContainer.classList.remove('hidden');
    
    // Animate in
    setTimeout(() => {
        notification.classList.remove('translate-x-full', 'opacity-0');
    }, 10);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
        notification.classList.add('translate-x-full', 'opacity-0');
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
                
                // Hide container if no more notifications
                if (notificationContainer.children.length === 0) {
                    notificationContainer.classList.add('hidden');
                }
            }
        }, 300);
    }, 5000);
}

// Get notification icon
function getNotificationIcon(type) {
    switch(type) {
        case 'success':
            return `<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>`;
        case 'error':
            return `<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>`;
        case 'warning':
            return `<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.864-.833-2.634 0L4.183 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
            </svg>`;
        default:
            return `<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>`;
    }
}

// Utility functions
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Global variables
let currentTemplatePages = [];
let currentPageId = null;
let currentTemplateId = null;

// Template and Page Selectors
function setupTemplatePageSelectors() {
    const templateSelector = document.getElementById('templateSelector');
    const pageSelector = document.getElementById('pageSelector');
    
    if (!templateSelector || !pageSelector) {
        console.error('Template or page selector not found');
        return;
    }
    
    // Template selector change handler
    templateSelector.addEventListener('change', async function() {
        const templateId = this.value;
        currentTemplateId = templateId;
        currentPageId = null;
        
        console.log('Template changed to:', templateId);
        
        if (templateId) {
            try {
                // Show loading state
                pageSelector.innerHTML = '<option value="">Loading pages...</option>';
                pageSelector.disabled = true;
                
                showLoadingInCanvas('Loading template...');
                
                // Fetch pages for this template using the correct API endpoint
                const pagesResponse = await fetch(`/pages/by-template?template_id=${templateId}`);
                const pagesData = await pagesResponse.json();
                
                console.log('Pages API Response:', pagesData);
                console.log('Pages Data Structure:', pagesData.data);
                
                if (!pagesData.success) {
                    throw new Error('Failed to fetch pages');
                }
                
                // Handle different API response formats
                let pages = [];
                if (pagesData.data && Array.isArray(pagesData.data)) {
                    pages = pagesData.data;
                } else if (pagesData.data && pagesData.data.data && Array.isArray(pagesData.data.data)) {
                    pages = pagesData.data.data;
                } else if (pagesData.data && typeof pagesData.data === 'object') {
                    pages = Object.values(pagesData.data);
                }
                
                console.log('Processed pages:', pages);
                
                currentTemplatePages = pages;
                
                // Handle template data
                handleTemplateData(templateId, currentTemplatePages);
                
            } catch (error) {
                console.error('Error loading template:', error);
                pageSelector.innerHTML = '<option value="">Error loading pages</option>';
                showErrorInCanvas('Error loading template', error.message);
            }
        } else {
            pageSelector.innerHTML = '<option value="">Select a template first</option>';
            pageSelector.disabled = true;
            currentTemplatePages = [];
            showDefaultCanvas();
        }
    });
    
    // Page selector change handler
    pageSelector.addEventListener('change', function() {
        const templateId = templateSelector.value;
        const pageId = this.value;
        
        console.log('Page changed to:', pageId);
        
        if (templateId && pageId) {
            currentPageId = pageId;
            loadTemplatePage(templateId, pageId);
        } else {
            currentPageId = null;
        }
    });
    
    // Auto-load template if already selected
    if (templateSelector.value) {
        console.log('Auto-loading template:', templateSelector.value);
        setTimeout(() => {
            templateSelector.dispatchEvent(new Event('change'));
        }, 100);
    }
}

// Handle template data (similar to old structure)
function handleTemplateData(templateId, pages) {
    const pageSelector = document.getElementById('pageSelector');
    
    console.log('Handling template data:', { templateId, pagesCount: pages ? pages.length : 0 });
    console.log('Pages data:', pages);
    console.log('Page selector element:', pageSelector);
    
    if (!pageSelector) {
        console.error('Page selector element not found!');
        return;
    }
    
    // Populate page selector
    pageSelector.innerHTML = '<option value="">Select a page</option>';
    
    if (pages && Array.isArray(pages) && pages.length > 0) {
        console.log('Adding pages to dropdown...');
        pages.forEach((page, index) => {
            console.log(`Adding page ${index}:`, page);
            const option = document.createElement('option');
            option.value = page.id;
            option.textContent = page.name || `Page ${page.id}`;
            option.setAttribute('data-page-id', page.id);
            option.setAttribute('data-page-slug', page.slug || 'page');
            option.setAttribute('data-page-name', page.name || 'Untitled Page');
            pageSelector.appendChild(option);
        });
        
        pageSelector.disabled = false;
        console.log('Pages added to dropdown, total options:', pageSelector.options.length);
        
        // Auto-select default page
        autoSelectDefaultPage(templateId, pages);
    } else {
        console.log('No pages found or pages is not an array');
        pageSelector.innerHTML = '<option value="">No pages found</option>';
        showNoPagesCanvas();
    }
}

// Auto-select default page (from old structure)
function autoSelectDefaultPage(templateId, pages) {
    const pageSelector = document.getElementById('pageSelector');
    const defaultPageNames = ['home', 'index', 'default'];
    let defaultPageId = null;
    
    // Look for default pages
    for (const page of pages) {
        // Check if page has is_default flag
        if (page.is_default === true || page.is_default === '1' || page.is_default === 1) {
            defaultPageId = page.id;
            break;
        }
        
        const pageName = (page.name || '').toLowerCase();
        const pageSlug = (page.slug || '').toLowerCase();
        
        if (defaultPageNames.includes(pageSlug) || defaultPageNames.includes(pageName)) {
            defaultPageId = page.id;
            break;
        }
    }
    
    // If no default found, use the first page
    if (!defaultPageId && pages.length > 0) {
        defaultPageId = pages[0].id;
    }
    
    // Select the default page in dropdown and load it
    if (defaultPageId) {
        pageSelector.value = defaultPageId;
        currentPageId = defaultPageId;
        loadTemplatePage(templateId, defaultPageId);
        console.log('Default page loaded:', defaultPageId);
    } else {
        console.warn('No valid page found to auto-select');
    }
}

// Load template page (based on old structure)
function loadTemplatePage(templateId, pageId) {
    const canvasContainer = document.getElementById('canvasContainer');
    
    if (templateId && pageId && canvasContainer) {
        console.log('Loading page in iframe - Template ID:', templateId, 'Page ID:', pageId);
        
        // Use page_id parameter to load specific page
        const iframeSrc = `/templates/preview?page_id=${pageId}`;
        
        canvasContainer.innerHTML = `
            <iframe id="templateCanvas" 
                    src="${iframeSrc}" 
                    class="w-full h-full border-0 transition-all duration-300"
                    data-page-id="${pageId}"
                    data-template-id="${templateId}">
            </iframe>
        `;
        
        // Re-setup drag and drop after iframe loads
        setTimeout(() => {
            const iframe = document.getElementById('templateCanvas');
            if (iframe) {
                iframe.addEventListener('load', function() {
                    console.log('Template canvas iframe loaded for page ID:', pageId);
                    setupDragAndDrop();
                    
                    // Send page context to iframe after it loads
                    try {
                        iframe.contentWindow.postMessage({
                            type: 'pageContext',
                            pageId: pageId,
                            templateId: templateId
                        }, '*');
                        console.log('Sent page context to iframe - Page ID:', pageId);
                    } catch (error) {
                        console.warn('Could not send page context to iframe:', error);
                    }
                });
            }
        }, 100);
    }
}

// Canvas helper functions (from old structure)
function showLoadingInCanvas(message = 'Loading...') {
    const canvasContainer = document.getElementById('canvasContainer');
    if (canvasContainer) {
        canvasContainer.innerHTML = `
            <div class="flex items-center justify-center h-full">
                <div class="text-center">
                    <div class="inline-flex items-center justify-center w-16 h-16 rounded-full bg-gray-200 mb-4">
                        <svg class="w-8 h-8 text-gray-500 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                        </svg>
                    </div>
                    <p class="text-lg font-medium text-gray-900">${message}</p>
                </div>
            </div>
        `;
    }
}

function showErrorInCanvas(title, message) {
    const canvasContainer = document.getElementById('canvasContainer');
    if (canvasContainer) {
        canvasContainer.innerHTML = `
            <div class="flex items-center justify-center h-full">
                <div class="text-center">
                    <div class="inline-flex items-center justify-center w-16 h-16 rounded-full bg-red-100 mb-4">
                        <svg class="w-8 h-8 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <p class="text-lg font-medium text-gray-900">${title}</p>
                    <p class="text-sm text-gray-500">${message}</p>
                </div>
            </div>
        `;
    }
}

function showDefaultCanvas() {
    const canvasContainer = document.getElementById('canvasContainer');
    if (canvasContainer) {
        canvasContainer.innerHTML = `
            <div class="flex items-center justify-center h-full">
                <div class="text-center">
                    <div class="inline-flex items-center justify-center w-16 h-16 rounded-full bg-gray-200 mb-4">
                        <svg class="w-8 h-8 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                    </div>
                    <p class="text-lg font-medium text-gray-900">Select a Template & Page</p>
                    <p class="text-sm text-gray-500">Choose a template and page to start building</p>
                </div>
            </div>
        `;
    }
}

function showNoPagesCanvas() {
    const canvasContainer = document.getElementById('canvasContainer');
    if (canvasContainer) {
        canvasContainer.innerHTML = `
            <div class="flex items-center justify-center h-full">
                <div class="text-center">
                    <div class="inline-flex items-center justify-center w-16 h-16 rounded-full bg-gray-100 mb-4">
                        <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                    </div>
                    <p class="text-lg font-medium text-gray-900">No Pages Found</p>
                    <p class="text-sm text-gray-500 mb-4">This template doesn't have any pages yet</p>
                </div>
            </div>
        `;
    }
}

// Export functions for global access
window.templateBuilder = {
    addBlockToTemplate,
    refreshPreview,
    changePreviewMode,
    showNotification,
    filterBlocks,
    filterBlocksByCategory
}; 