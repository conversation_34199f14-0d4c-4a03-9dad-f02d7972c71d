/* Radio Button Group Styles */
.radio-button-group {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.radio-button-group input[type="radio"] {
    appearance: none;
    width: 0;
    height: 0;
    margin: 0;
}

.radio-button-group label {
    display: inline-flex;
    align-items: center;
    padding: 0.5rem 1rem;
    background-color: #f9fafb;
    border: 1px solid #d1d5db;
    border-radius: 0.5rem;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.875rem;
    font-weight: 500;
}

.radio-button-group input[type="radio"]:checked + label {
    background-color: #0C5BE2;
    color: white;
    border-color: #0C5BE2;
}

.radio-button-group label:hover {
    background-color: #f3f4f6;
    border-color: #9ca3af;
}

.radio-button-group input[type="radio"]:checked + label:hover {
    background-color: #0C5BE2;
    border-color: #0C5BE2;
}

/* Step Navigation Styles */
.step-item {
    transition: all 0.3s ease;
}

.step-content {
    transition: opacity 0.3s ease;
}

/* Tag Styles */
.tag-input-container {
    position: relative;
}

.tag-suggestions {
    max-height: 200px;
    overflow-y: auto;
    z-index: 1000;
}

.tag-item {
    transition: all 0.2s ease;
}

.tag-item:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.tag-remove-btn {
    transition: color 0.2s ease;
}

.tag-remove-btn:hover {
    color: #dc2626;
}