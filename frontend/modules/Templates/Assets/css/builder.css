/* Template Builder CSS - Modern Implementation */

/* Base Styles */
.container-fluid {
    padding: 0;
    margin: 0;
    height: 100vh;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
}

/* Layout Styles */
.flex {
    display: flex;
}

.flex-col {
    flex-direction: column;
}

.flex-1 {
    flex: 1;
}

.h-full {
    height: 100%;
}

.w-full {
    width: 100%;
}

.relative {
    position: relative;
}

.absolute {
    position: absolute;
}

.fixed {
    position: fixed;
}

.overflow-hidden {
    overflow: hidden;
}

.overflow-y-auto {
    overflow-y: auto;
}

/* Sidebar Styles */
#builderSidebar {
    background: #ffffff;
    border-right: 1px solid #e5e7eb;
    transition: all 0.3s ease;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

#builderSidebar.collapsed {
    margin-left: -320px;
    width: 0;
}

.sidebar-header {
    border-bottom: 1px solid #e5e7eb;
    padding: 1rem;
    background: #f9fafb;
}

.sidebar-header h2 {
    font-size: 1.125rem;
    font-weight: 600;
    color: #111827;
    margin: 0;
}

.sidebar-header p {
    font-size: 0.875rem;
    color: #6b7280;
    margin: 0.25rem 0 0 0;
}

/* Search and Filter Styles */
#blockSearch, #blockCategoryFilter {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    transition: all 0.2s ease;
    background: #ffffff;
}

#blockSearch:focus, #blockCategoryFilter:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

#blockSearch::placeholder {
    color: #9ca3af;
}

/* Blocks List Styles */
#blocksList {
    scrollbar-width: thin;
    scrollbar-color: #d1d5db #f9fafb;
}

#blocksList::-webkit-scrollbar {
    width: 6px;
}

#blocksList::-webkit-scrollbar-track {
    background: #f9fafb;
}

#blocksList::-webkit-scrollbar-thumb {
    background: #d1d5db;
    border-radius: 3px;
}

#blocksList::-webkit-scrollbar-thumb:hover {
    background: #9ca3af;
}

/* Block Item Styles */
.block-item {
    background: #ffffff;
    border: 1px solid #e5e7eb;
    border-radius: 0.5rem;
    margin-bottom: 0.5rem;
    cursor: move;
    transition: all 0.2s ease;
    overflow: hidden;
    user-select: none;
}

.block-item:hover {
    border-color: #3b82f6;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
}

.block-item.selected {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    background: #eff6ff;
}

.block-item.dragging {
    opacity: 0.5;
    transform: rotate(5deg);
}

.block-item img {
    width: 100%;
    height: 8rem;
    object-fit: cover;
    display: block;
}

.block-item h3 {
    font-size: 0.875rem;
    font-weight: 500;
    color: #111827;
    margin: 0;
    padding: 0.75rem;
    background: linear-gradient(to top, #f7f7f7, #ffffff);
    border-top: 1px solid #e5e7eb;
}

.block-item .badge {
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    font-weight: 500;
    color: #1e40af;
    background: #dbeafe;
    border-radius: 9999px;
}

/* Canvas Area Styles */
#mainCanvasArea {
    background: #f9fafb;
    transition: all 0.3s ease;
}

#mainCanvasArea.drag-over {
    background: rgba(59, 130, 246, 0.1);
}

/* Canvas Header Styles */
.canvas-header {
    background: #ffffff;
    border-bottom: 1px solid #e5e7eb;
    padding: 0.75rem;
}

#toggleSidebarBtn {
    padding: 0.5rem;
    border: none;
    background: none;
    color: #6b7280;
    cursor: pointer;
    border-radius: 0.375rem;
    transition: all 0.2s ease;
}

#toggleSidebarBtn:hover {
    background: #f3f4f6;
    color: #111827;
}

/* Preview Mode Controls */
.preview-mode-btn {
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
    font-weight: 500;
    color: #6b7280;
    background: transparent;
    border: none;
    border-radius: 0.375rem;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.preview-mode-btn:hover {
    color: #111827;
    background: #e5e7eb;
}

.preview-mode-btn.active {
    color: #ffffff;
    background: #3b82f6;
}

.preview-mode-btn svg {
    width: 1rem;
    height: 1rem;
}

#refreshPreview {
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
    font-weight: 500;
    color: #6b7280;
    background: transparent;
    border: none;
    border-radius: 0.375rem;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

#refreshPreview:hover {
    color: #111827;
    background: #f3f4f6;
}

/* Preview Container Styles */
#previewContainer {
    padding: 0;
    background: #f3f4f6;
    background-image: 
        radial-gradient(circle at 25px 25px, #e5e7eb 2px, transparent 2px),
        radial-gradient(circle at 75px 75px, #e5e7eb 2px, transparent 2px);
    background-size: 100px 100px;
    background-position: 0 0, 50px 50px;
    height: 100%;
}

#previewContainer.desktop-view {
    display: block;
    padding: 0;
}

#previewContainer.tablet-view,
#previewContainer.mobile-view {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
}

/* Device Frame Styles */
.device-frame {
    background: #ffffff;
    border-radius: 0.5rem;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    overflow: hidden;
    transition: all 0.3s ease;
    margin: 0 auto;
}

.device-frame.desktop-frame {
    width: 100%;
    height: 100%;
    max-width: none;
    max-height: none;
    border-radius: 0;
    box-shadow: none;
}

.device-frame.tablet-frame {
    width: 768px;
    height: 1024px;
    max-width: 90%;
    max-height: 90%;
}

.device-frame.mobile-frame {
    width: 375px;
    height: 667px;
    max-width: 90%;
    max-height: 90%;
}

.device-content {
    width: 100%;
    height: 100%;
    overflow: hidden;
}

#templateCanvas {
    width: 100%;
    height: 100%;
    border: none;
    background: #ffffff;
    min-height: 100vh;
}

/* Floating Action Buttons */
.floating-actions {
    position: absolute;
    bottom: 1.5rem;
    right: 1.5rem;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.floating-actions button {
    padding: 0.75rem 1rem;
    border: none;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    color: #ffffff;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.floating-actions button:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

#saveDraft {
    background: #6b7280;
}

#saveDraft:hover {
    background: #4b5563;
}

#publishTemplate {
    background: #3b82f6;
}

#publishTemplate:hover {
    background: #2563eb;
}

/* Notification Styles */
#notification {
    z-index: 1000;
}

.notification-item {
    background: #ffffff;
    border-left: 4px solid #3b82f6;
    border-radius: 0.5rem;
    padding: 1rem;
    margin-bottom: 0.5rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    transform: translateX(100%);
    transition: all 0.3s ease;
    max-width: 24rem;
}

.notification-item.show {
    transform: translateX(0);
}

.notification-item.success {
    background: #10b981;
    border-left-color: #059669;
}

.notification-item.error {
    background: #ef4444;
    border-left-color: #dc2626;
}

.notification-item.warning {
    background: #f59e0b;
    border-left-color: #d97706;
}

.notification-item.info {
    background: #3b82f6;
    border-left-color: #2563eb;
}

/* No Results Message */
#noResultsMessage {
    padding: 2rem;
    text-align: center;
    color: #6b7280;
}

#noResultsMessage svg {
    width: 3rem;
    height: 3rem;
    margin: 0 auto 1rem auto;
    display: block;
}

/* Responsive Design */
@media (max-width: 768px) {
    #builderSidebar {
        width: 100%;
        position: fixed;
        top: 0;
        left: 0;
        height: 100vh;
        z-index: 1000;
        transform: translateX(-100%);
    }
    
    #builderSidebar.show {
        transform: translateX(0);
    }
    
    #mainCanvasArea {
        width: 100%;
    }
    
    .device-frame.tablet-frame,
    .device-frame.mobile-frame {
        width: 100%;
        height: 100%;
    }
    
    #previewContainer {
        padding: 1rem;
    }
    
    .floating-actions {
        bottom: 1rem;
        right: 1rem;
    }
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #3b82f6;
    border-top: 2px solid transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Drag and Drop Visual Effects */
.drag-over {
    background: rgba(59, 130, 246, 0.1);
    border: 2px dashed #3b82f6;
}

.drag-placeholder {
    height: 100px;
    background: rgba(59, 130, 246, 0.1);
    border: 2px dashed #3b82f6;
    border-radius: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #3b82f6;
    font-size: 0.875rem;
    font-weight: 500;
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes slideIn {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

.fade-in {
    animation: fadeIn 0.3s ease;
}

.slide-in {
    animation: slideIn 0.3s ease;
}

/* Utility Classes */
.text-center {
    text-align: center;
}

.text-left {
    text-align: left;
}

.text-right {
    text-align: right;
}

.font-semibold {
    font-weight: 600;
}

.font-medium {
    font-weight: 500;
}

.text-sm {
    font-size: 0.875rem;
}

.text-xs {
    font-size: 0.75rem;
}

.text-lg {
    font-size: 1.125rem;
}

.text-gray-500 {
    color: #6b7280;
}

.text-gray-600 {
    color: #4b5563;
}

.text-gray-700 {
    color: #374151;
}

.text-gray-900 {
    color: #111827;
}

.text-blue-600 {
    color: #2563eb;
}

.text-blue-800 {
    color: #1e40af;
}

.bg-gray-50 {
    background-color: #f9fafb;
}

.bg-gray-100 {
    background-color: #f3f4f6;
}

.bg-white {
    background-color: #ffffff;
}

.bg-blue-100 {
    background-color: #dbeafe;
}

.border-gray-200 {
    border-color: #e5e7eb;
}

.border-gray-300 {
    border-color: #d1d5db;
}

.border-b {
    border-bottom-width: 1px;
}

.border-r {
    border-right-width: 1px;
}

.border-t {
    border-top-width: 1px;
}

.rounded-lg {
    border-radius: 0.5rem;
}

.rounded-md {
    border-radius: 0.375rem;
}

.rounded-full {
    border-radius: 9999px;
}

.shadow-sm {
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.shadow-lg {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.shadow-xl {
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.transition-all {
    transition-property: all;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
}

.duration-300 {
    transition-duration: 300ms;
}

.ease-in-out {
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

.cursor-move {
    cursor: move;
}

.cursor-pointer {
    cursor: pointer;
}

.hidden {
    display: none;
}

.block {
    display: block;
}

.inline-flex {
    display: inline-flex;
}

.items-center {
    align-items: center;
}

.justify-center {
    justify-content: center;
}

.justify-between {
    justify-content: space-between;
}

.space-x-2 > * + * {
    margin-left: 0.5rem;
}

.space-x-4 > * + * {
    margin-left: 1rem;
}

.space-y-2 > * + * {
    margin-top: 0.5rem;
}

.mb-2 {
    margin-bottom: 0.5rem;
}

.mb-3 {
    margin-bottom: 0.75rem;
}

.mb-4 {
    margin-bottom: 1rem;
}

.mr-1 {
    margin-right: 0.25rem;
}

.ml-3 {
    margin-left: 0.75rem;
}

.ml-4 {
    margin-left: 1rem;
}

.mt-4 {
    margin-top: 1rem;
}

.p-1 {
    padding: 0.25rem;
}

.p-2 {
    padding: 0.5rem;
}

.p-3 {
    padding: 0.75rem;
}

.p-4 {
    padding: 1rem;
}

.px-2 {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
}

.px-3 {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
}

.px-4 {
    padding-left: 1rem;
    padding-right: 1rem;
}

.py-1 {
    padding-top: 0.25rem;
    padding-bottom: 0.25rem;
}

.py-2 {
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
}

.py-4 {
    padding-top: 1rem;
    padding-bottom: 1rem;
}

.py-8 {
    padding-top: 2rem;
    padding-bottom: 2rem;
}

.pt-6 {
    padding-top: 1.5rem;
}

.pr-6 {
    padding-right: 1.5rem;
}

.top-2 {
    top: 0.5rem;
}

.right-2 {
    right: 0.5rem;
}

.top-4 {
    top: 1rem;
}

.right-4 {
    right: 1rem;
}

.bottom-6 {
    bottom: 1.5rem;
}

.right-6 {
    right: 1.5rem;
}

.z-50 {
    z-index: 50;
}

.w-4 {
    width: 1rem;
}

.h-4 {
    height: 1rem;
}

.w-5 {
    width: 1.25rem;
}

.h-5 {
    height: 1.25rem;
}

.w-12 {
    width: 3rem;
}

.h-12 {
    height: 3rem;
}

.max-w-sm {
    max-width: 24rem;
}

.object-cover {
    object-fit: cover;
}

.overflow-hidden {
    overflow: hidden;
}

.transform {
    transform: translateX(var(--transform-translate-x, 0)) translateY(var(--transform-translate-y, 0)) rotate(var(--transform-rotate, 0)) skewX(var(--transform-skew-x, 0)) skewY(var(--transform-skew-y, 0)) scaleX(var(--transform-scale-x, 1)) scaleY(var(--transform-scale-y, 1));
}

.translate-x-full {
    --transform-translate-x: 100%;
}

.opacity-0 {
    opacity: 0;
}

.focus\:outline-none:focus {
    outline: none;
}

.focus\:ring-2:focus {
    --ring-offset-shadow: var(--ring-inset) 0 0 0 var(--ring-offset-width) var(--ring-offset-color);
    --ring-shadow: var(--ring-inset) 0 0 0 calc(2px + var(--ring-offset-width)) var(--ring-color);
    box-shadow: var(--ring-offset-shadow), var(--ring-shadow), var(--shadow, 0 0 #0000);
}

.focus\:ring-blue-500:focus {
    --ring-opacity: 1;
    --ring-color: rgba(59, 130, 246, var(--ring-opacity));
}

.focus\:border-transparent:focus {
    border-color: transparent;
}

.hover\:bg-gray-100:hover {
    background-color: #f3f4f6;
}

.hover\:bg-gray-700:hover {
    background-color: #374151;
}

.hover\:bg-blue-700:hover {
    background-color: #1d4ed8;
}

.hover\:border-blue-500:hover {
    border-color: #3b82f6;
}

.hover\:shadow-sm:hover {
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.hover\:text-gray-200:hover {
    color: #e5e7eb;
} 