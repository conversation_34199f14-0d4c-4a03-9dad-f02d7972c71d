<?php

namespace Modules\Auth\Controllers;

use App\Services\Auth;
use Core\Controller;
use Core\Request;
use Modules\Auth\Services\AuthService;
class ProfileController extends Controller
{
    protected $authService;

    public function __construct()
    {
        $this->authService = new AuthService();
    }

    public function showProfileForm()
    {
        $this->setTitle('Profile');
        $user = Auth::user();
        
        return $this->view('modules/Auth/Views/profile',[
            'profile'=> $user
        ]);
    }

    public function update(){
        try {
            $request = new Request();
            $validated = $request->only(['name', 'email']);
            $validated['id'] = Auth::id();

            $result = $this->authService->updateProfile($validated);

            if ($result['success']) {
                return $this->jsonResponse([
                    'success' => true,
                    'message' => $result['message'],
                    'data' => $result['data']
                ]);
            }

            return $this->jsonResponse([
                'success' => false,
                'errors' => $result['message'],
                'data' => $result['data']
            ], 422);
        } catch (\Throwable $th) {
            //throw $th;  
            return $this->jsonResponse([
                'success' => false,
                'errors' => $th->getMessage()
            ], 500);
        }
    }


    public function changePassword(){
        try {
            $request = new Request();
            $validated = $request->only(['current_password', 'new_password', 'password_confirmation']);
            $validated['id'] = Auth::id();

            if ($validated['new_password'] !== $validated['password_confirmation']) {
                return $this->jsonResponse([
                    'success' => false,
                    'errors' => 'New passwords do not match',
                    'data' => $validated
                ], 422);
            }

            // Validate password length
            if (strlen($validated['new_password']) < 8) {
                return $this->jsonResponse([
                    'success' => false,
                    'errors' => 'Password must be at least 8 characters long',
                    'data' => $validated
                ], 422);
            }

            $result = $this->authService->changePassword($validated);

            if ($result['success']) {
                return $this->jsonResponse([
                    'success' => true,
                    'message' => $result['message'],
                ]);
            }

            return $this->jsonResponse([
                'success' => false,
                'errors' => $result['message'],
            ], 422);
        } catch (\Throwable $th) {
            //throw $th;  
            return $this->jsonResponse([
                'success' => false,
                'errors' => $th->getMessage()
            ], 500);
        }
    }

}