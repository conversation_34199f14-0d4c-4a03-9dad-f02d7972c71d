<?php

namespace Modules\Auth\Controllers;

use Core\Controller;
use Core\Request;
use Modules\Auth\Services\AuthService;

class LoginController extends Controller
{
    protected $authService;

    public function __construct()
    {
        $this->authService = new AuthService();
    }

    public function showLoginForm()
    {

        $this->setTitle('Login');
        
        // If user is already logged in, redirect to dashboard
        if ($this->authService->check()) {
            return $this->redirect('/dashboard');
        }

        return $this->view('modules/Auth/Views/login');
    }

    public function login()
    {
        try {

            $request = new Request();
            $validated = $request->only(['email', 'password', 'is_admin']);

            if ($validated['is_admin'] == 1) {
                $result = $this->authService->adminLogin($validated['email'], $validated['password']);
            } else {
                $result = $this->authService->userLogin($validated['email'], $validated['password']);
            }

            if ($result['success']) {
                return $this->jsonResponse([
                    'success' => true,
                    'redirect' => url('dashboard')
                ]);
            }

            return $this->jsonResponse([
                'success' => false,
                'errors' => $result['message']
            ], 422);
        } catch (\Throwable $th) {
            // Handle validation errors
            return $this->jsonResponse([
                'success' => false,
                'errors' => $th->getMessage()
            ], 500);
        }
    }

    public function forgotPassword()
    {
        $email = $this->request->post('email');
        $result = $this->authService->forgotPassword($email);

        if ($result['success']) {
            return $this->jsonResponse([
                'success' => true,
                'message' => $result['message'],
                'redirect' => url('login')
            ]);
        }

        return $this->view('modules/Auth/Views/login', [
            'error' => $result['message'],
            'forgotSuccess' => ''
        ]);
    }

    public function logout()
    {
        $this->authService->logout('user');
        return $this->redirect('/login');
    }
}