<?php

namespace Modules\Auth\Services;

use App\Services\ApiClient;
use App\Services\Auth;
use Core\Service;
use App\Services\Auth as BaseAuth;

class AuthService extends Service
{
    protected $auth;

    public function __construct()
    {
        parent::__construct();
        $this->auth = new BaseAuth();
    }

    public function check(): bool
    {
        return $this->auth->check();
    }

    public function userLogin(string $email, string $password): array
    {
        return $this->attemptLogin($email, $password, 'user');
    }

    public function adminLogin(string $email, string $password): array
    {
        return $this->attemptLogin($email, $password, 'admin');
    }

    protected function attemptLogin(string $email, string $password, string $type): array
    {
        if (empty($email) || empty($password)) {
            return [
                'success' => false,
                'message' => 'Email and password are required.'
            ];
        }

        $this->auth->isAdmin($type == 'admin' ? 1 : 0);

        $response = $this->api->post($type . '/login', [
            'email' => $email,
            'password' => $password
        ], true);

        if (isset($response['user']) && isset($response['token'])) {
            $this->auth->login($response['user'], $response['token']);
            return ['success' => true, 'user' => $response['user'], 'token' => $response['token']];
        }

        return [
            'success' => false,
            'message' => $response['message'] ?? 'Invalid credentials.'
        ];
    }

    public function forgotPassword(string $email): array
    {
        if (empty($email)) {
            return [
                'success' => false,
                'message' => 'Email address is required.'
            ];
        }


        $response = $this->api->post('password/forgot', ['email' => $email]);

        if ($response['success'] ?? false) {
            return [
                'success' => true,
                'message' => 'If an account exists with this email, you will receive a password reset link.'
            ];
        }


        return [
            'success' => false,
            'message' => $response['message'] ?? 'Failed to send password reset link.'
        ];
    }

    public function updateProfile($profileData)
    {
        $requestUrl = $this->auth->isAdmin() ? 'admins/' : 'users/';

        $response = $this->api->put($requestUrl.$this->auth->id(), $profileData, true);

        if (isset($response['errors']) && $response['errors'] !== []) {
            return [
                'success' => false,
                'message' => $response['errors'] ?? 'Failed to update profile.'
            ];
        }

        $user = $response['admin'] ?? $response['user'];
        Auth::user($user);
        return [
            'success' => true,
            'message' => 'Profile updated successfully.',
            'data' => $user
        ];
    }
    public function changePassword(array $passwordData)
    {
        $requestUrl = $this->auth->isAdmin() ? 'admins/changePassword/' : 'users/changePassword/';
        $response = $this->api->put($requestUrl.$this->auth->id(), $passwordData);

        if (empty($response)) {
            return [
                'success' => false,
                'message' => 'Failed to change password.'
            ];
        }

        // Check if there's an error in the response
        if (isset($response['error']) && $response['error']) {
            return [
                'success' => false,
                'message' => $response['message'] ?? 'Failed to change password.'
            ];
        }

        // If we get here, the request was successful
        return [
            'success' => true,
            'message' => 'Password changed successfully'
        ];
    }

    
    public function logout($type)
    {
        $this->api->post("{$type}/logout");
        $this->auth->logout();
    }

}
