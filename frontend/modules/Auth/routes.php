<?php

use Core\Router;
use Middleware\Authenticate;
use Modules\Auth\Controllers\LoginController;
use Modules\Auth\Controllers\ProfileController;
// use Modules\Auth\Controllers\RegisterController;
// use Modules\Auth\Controllers\ForgotPasswordController;



// Route definitions (local to module)

$router->group('', function(Router $router) {
    $router->get('login', [LoginController::class, 'showLoginForm']);
    $router->post('login', [LoginController::class, 'login']);
    $router->get('logout', [LoginController::class, 'logout']);

    // Profile Routes
    $router->get('profile', [ProfileController::class, 'showProfileForm'], [Authenticate::class]);
    $router->post('profile/update', [ProfileController::class, 'update']);
    $router->post('profile/change-password', [ProfileController::class, 'changePassword'], [Authenticate::class]);
});
