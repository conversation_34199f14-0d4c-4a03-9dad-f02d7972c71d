<div class="p-6">
    <h1 class="text-2xl font-bold mb-6">Profile Settings</h1>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-10">
        <!-- Profile Information -->
        <div>
            <h2 class="text-xl font-semibold">Profile Information</h2>
            <p class="text-xs text-gray-600 mb-6">Update your account profile information and email address.</p>

            <?php if ($profile): ?>
            <form id="profile-form" method="POST" class="space-y-4" action="<?= url('profile/update') ?>" data-ajax-form data-reset-on-success>
                
                <!-- Name -->
                <div>
                    <label for="name" class="block text-sm font-bold text-gray-700 mb-2">Name</label>
                    <input type="text" id="name" name="name" value="<?= htmlspecialchars($profile['name']) ?>" required
                        class="block w-full pr-4 py-2.5 text-sm text-gray-900 bg-white border border-gray-300 rounded-lg focus:border-blue-500 focus:ring-1 focus:ring-blue-500">
                </div>

                <!-- Email -->
                <div>
                    <label for="email" class="block text-sm font-bold text-gray-700 mb-2">Email</label>
                    <input type="email" id="email" name="email" value="<?= htmlspecialchars($profile['email']) ?>" required
                        class="block w-full pr-4 py-2.5 text-sm text-gray-900 bg-white border border-gray-300 rounded-lg focus:border-blue-500 focus:ring-1 focus:ring-blue-500">
                </div>

                <div class="flex justify-end">
                    <button type="submit"
                        class="btn-primary py-2.5 px-4 border border-transparent rounded-lg text-sm font-medium text-white bg-[#0C5BE2] hover:bg-[#114bbc] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#1375fd]" disabled>
                        Save
                    </button>
                </div>
            </form>
            <?php else: ?>
            <div class="p-4 text-sm text-yellow-800 rounded-lg bg-yellow-50">
                Unable to load profile information. Please try again later.
            </div>
            <?php endif; ?>
        </div>

        <!-- Update Password -->
        <div>
            <h2 class="text-xl font-semibold">Update Password</h2>
            <p class="text-xs text-gray-600 mb-6">Ensure your account is using a long, random password to stay secure.</p>

            <form id="password-form" method="POST" action="<?= url('profile/change-password') ?>" class="space-y-4" data-ajax-form data-callback="handleCustomResponse">
                <!-- Current Password -->
                <div>
                    <label for="current_password" class="block text-sm font-bold text-gray-700 mb-2">Current Password</label>
                    <div class="relative">
                        <input type="password" id="current_password" name="current_password" required
                            class="block w-full pr-4 py-2.5 text-sm text-gray-900 bg-white border border-gray-300 rounded-lg focus:border-blue-500 focus:ring-1 focus:ring-blue-500">
                        <button type="button" class="absolute inset-y-0 right-0 pr-3 flex items-center" onclick="togglePasswordVisibility('current_password')">
                            <svg class="h-5 w-5 text-gray-400 hover:text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                            </svg>
                        </button>
                    </div>
                </div>

                <!-- New Password -->
                <div>
                    <label for="new_password" class="block text-sm font-bold text-gray-700 mb-2">New Password</label>
                    <div class="relative">
                        <input type="password" id="new_password" name="new_password" required
                            class="block w-full pr-4 py-2.5 text-sm text-gray-900 bg-white border border-gray-300 rounded-lg focus:border-blue-500 focus:ring-1 focus:ring-blue-500">
                        <button type="button" class="absolute inset-y-0 right-0 pr-3 flex items-center" onclick="togglePasswordVisibility('new_password')">
                            <svg class="h-5 w-5 text-gray-400 hover:text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                            </svg>
                        </button>
                    </div>
                </div>

                <!-- Confirm Password -->
                <div>
                    <label for="password_confirmation" class="block text-sm font-bold text-gray-700 mb-2">Confirm Password</label>
                    <div class="relative">
                        <input type="password" id="password_confirmation" name="password_confirmation" required
                            class="block w-full pr-4 py-2.5 text-sm text-gray-900 bg-white border border-gray-300 rounded-lg focus:border-blue-500 focus:ring-1 focus:ring-blue-500">
                        <button type="button" class="absolute inset-y-0 right-0 pr-3 flex items-center" onclick="togglePasswordVisibility('password_confirmation')">
                            <svg class="h-5 w-5 text-gray-400 hover:text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                            </svg>
                        </button>
                    </div>
                </div>

                <div class="flex justify-end">
                    <button type="submit"
                        class="btn-primary py-2.5 px-4 border border-transparent rounded-lg text-sm font-medium text-white bg-[#0C5BE2] hover:bg-[#114bbc] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#1375fd]" disabled>
                        Update Password
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>


<?php
// Add view-specific scripts
    add_script(asset('modules/Auth/Assets/js/profile.js'), ['defer' => true]);

    add_inline_script("
            function handleCustomResponse(response) {
                console.log(response);
            }
    ");
?>

