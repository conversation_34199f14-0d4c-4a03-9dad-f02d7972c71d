<?php
    add_style(asset('modules/Auth/Assets/css/auth.css'));
?>

<!-- Main Container -->
<section class="relative overflow-hidden min-h-screen w-full">
  <div class="grid grid-cols-2 h-full">
    <!-- Left Panel -->
    <div class="bg-purple-800 p-16 relative flex flex-col justify-center items-center">
        <div class="absolute top-0 left-0 -translate-x-[60%] -translate-y-3/4 z-10">
            <div class="w-80 h-80 rounded-full border-8 border-white opacity-20"></div>
        </div>
        
        <div class="max-w-xl text-center">
            <h1 class="text-5xl font-bold text-white mb-2">Welcome to Echo<span class="font-thin">CMS</span></h1>
            <p class="text-purple-100 text-lg mb-12 font-light">EchoCMS empowers you to build modular websites using reusable blocks and AI-assisted design tools.</p>
    
            <!-- Testimonial -->
            <div class="mb-8 w-full">
                <!-- Image Slider Start -->
                <div id="slider" class="relative w-full h-80 overflow-hidden rounded-lg">
                    <div class="slide flex w-full h-full absolute top-0 left-0 transition-opacity duration-700 opacity-100 z-10">
                        <img src="<?= asset('images/hero.png') ?>" class="object-contain w-full h-full" alt="Slide 1">
                    </div>
                    <div class="slide flex w-full h-full absolute top-0 left-0 transition-opacity duration-700 opacity-0 z-0">
                        <img src="<?= asset('images/logo-cloud.png') ?>" class="object-contain w-full h-full" alt="Slide 2">
                    </div>
                    <div class="slide flex w-full h-full absolute top-0 left-0 transition-opacity duration-700 opacity-0 z-0">
                        <img src="<?= asset('images/call-to-action.png') ?>" class="object-contain w-full h-full" alt="Slide 3">
                    </div>
                    <div class="slide flex w-full h-full absolute top-0 left-0 transition-opacity duration-700 opacity-0 z-0">
                        <img src="<?= asset('images/testimonail.png') ?>" class="object-contain w-full h-full" alt="Slide 4">
                    </div>
                </div>
                <!-- Image Slider End -->
            </div>
            <blockquote class="text-white text-lg mb-8 font-light">
                "Using EchoCMS, we reduced development time and created stunning websites without writing redundant code."
            </blockquote>
        </div>
        
        <div class="absolute bottom-0 right-0 translate-x-1/4 translate-y-3/4">
            <div class="w-96 h-96 rounded-full border-8 border-white opacity-20 lg:opacity-100"></div>
        </div>
    </div>

    <!-- Right Panel -->
    <div class="bg-white p-16 flex flex-col justify-center items-center">
      <div class="max-w-md">
        <?php include __DIR__ . '/partials/loginForm.php'; ?> 
        <?php include __DIR__ . '/partials/forgotPasswordSuccess.php'; ?>
      </div>
    </div>
  </div>
</section>
<?php
// Add view-specific scripts
    add_script(asset('modules/Auth/Assets/js/auth.js'));
    add_inline_script("
        function handleCustomResponse(response) {
            // Handle the AJAX response here
            if (response.success) {
                $('#loginCardContainer').hide();
                $('#forgotPasswordSuccess').show();
            } else {
                $('#loginCardContainer').show();
                $('#forgotPasswordSuccess').hide();
            }
        }
        // Image Slider JS (Fade Effect, No Controls)
        (function() {
            var slides = document.querySelectorAll('#slider .slide');
            var current = 0;
            var interval = null;
            function showSlide(index) {
                slides[current].style.opacity = 0;
                slides[current].style.zIndex = 0;
                current = (index + slides.length) % slides.length;
                slides[current].style.opacity = 1;
                slides[current].style.zIndex = 10;
            }
            function nextSlide() {
                showSlide(current + 1);
            }
            function startInterval() {
                interval = setInterval(nextSlide, 3000);
            }
            // Initialize all slides except the first to opacity 0
            for (var i = 1; i < slides.length; i++) {
                slides[i].style.opacity = 0;
                slides[i].style.zIndex = 0;
            }
            slides[0].style.opacity = 1;
            slides[0].style.zIndex = 10;
            startInterval();
        })();
    ");
?>