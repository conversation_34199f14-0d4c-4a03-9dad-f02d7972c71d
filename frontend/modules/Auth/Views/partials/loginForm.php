<!-- Login Card -->
<div id="loginCardContainer">
    <div id="loginCard" class="bg-white rounded-xl shadow-lg p-8 slide-in">
        <!-- Logo -->
        <div class="flex justify-center mb-8">
            <div class="relative">
                <svg width="220px" viewBox="0 0 220 37.045">
                    <path id="a" d="m40.559 18.773c0-1.8537 0.36963-3.4977 1.1089-4.9321 0.73926-1.4344 1.7488-2.5377 3.0287-3.3101 1.2799-0.77236 2.7033-1.1585 4.27-1.1585 1.1916 0 2.3281 0.25929 3.4094 0.77788 1.0813 0.51858 1.9419 1.2082 2.5819 2.0688v-8.7056h4.7004v24.495h-4.7004v-2.7143c-0.57375 0.90476-1.3792 1.633-2.4164 2.1847-1.0372 0.55168-2.2398 0.82753-3.608 0.82753-1.5447 0-2.957-0.39721-4.2369-1.1916-1.2799-0.79443-2.2895-1.9143-3.0287-3.3598s-1.1089-3.106-1.1089-4.9817zm14.432 0.066202c0-1.1254-0.22067-2.0909-0.66202-2.8963-0.44135-0.80546-1.0372-1.4233-1.7875-1.8537-0.75029-0.43031-1.5558-0.64547-2.4164-0.64547s-1.6551 0.20964-2.3833 0.62892c-0.72822 0.41928-1.3185 1.0317-1.7709 1.8371-0.45238 0.80546-0.67857 1.7599-0.67857 2.8632s0.22619 2.0688 0.67857 2.8963c0.45238 0.82753 1.0482 1.462 1.7875 1.9033 0.73926 0.44135 1.5282 0.66202 2.3667 0.66202 0.86063 0 1.6661-0.21516 2.4164-0.64547 0.75029-0.43031 1.3461-1.0482 1.7875-1.8537 0.44135-0.80546 0.66202-1.7709 0.66202-2.8963z" fill="#0C5BE2"></path>
                    <path d="m28.009 7.6387h-8.9118c-1.4068 0-2.5462-1.1394-2.5462-2.5462v-2.5462c0-1.4068 1.1394-2.5462 2.5462-2.5462h6.3656c2.8123 0 5.0925 2.2802 5.0925 5.0925 0 1.4068-1.1394 2.5462-2.5462 2.5462z" fill="#E65100"></path>
                    <path d="m8.9118 10.185v-5.0925c0-1.4068-1.1394-2.5462-2.5462-2.5462h-1.2731c-2.8123 0-5.0925 2.2802-5.0925 5.0925v2.5462h8.9118zm1.2731 20.37h10.185v-7.6387h-10.185v7.6387zm12.731-7.6387v7.6387h2.5462c2.8123 0 5.0925-2.2802 5.0925-5.0925v-2.5462h-7.6387zm-15.277 0h-7.6387v2.5462c0 2.8123 2.2802 5.0925 5.0925 5.0925h2.5462v-7.6387zm6.3656-10.185h-14.004v7.6387h14.004v-7.6387zm2.5462 0v7.6387h14.004v-5.0925c0-1.4068-1.1394-2.5462-2.5462-2.5462h-11.458z" fill="#0C5BE2"></path>
                </svg>
            </div>
        </div>

        <!-- Welcome Text -->
        <div class="text-center mb-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-2">Welcome back!</h2>
            <p class="text-gray-600">Please sign in to your account</p>
        </div>

        <?php if (isset($error) && !empty($error)): ?>
            <div class="mb-4 p-4 text-sm text-red-800 rounded-lg bg-red-50" role="alert">
                <?php echo htmlspecialchars($error); ?>
            </div>
        <?php endif; ?>

        <!-- Login Form -->
        <form id="loginForm" class="space-y-5" method="POST" action="<?= url('login') ?>" data-ajax-form data-reset-on-success>
            <!-- Email -->
            <div>
                <label for="login-email" class="block text-sm font-medium text-gray-700 mb-1">Email</label>
                <div class="relative">
                    <input type="email" id="login-email" name="email" required
                        class="input-focus block w-full px-4 py-2.5 border border-gray-300 rounded-lg text-gray-900 placeholder-gray-400 focus:ring-2 focus:ring-[#1375fd] focus:border-[#1375fd]"
                        placeholder="Enter your email" value="<?php echo isset($_POST['email']) ? htmlspecialchars($_POST['email']) : ''; ?>">
                </div>
                <p class="mt-1 text-sm text-red-600 hidden" id="email-error"></p>
            </div>

            <!-- Password -->
            <div>
                <label for="login-password" class="block text-sm font-medium text-gray-700 mb-1">Password</label>
                <div class="relative">
                    <input type="password" id="login-password" name="password" required
                        class="input-focus block w-full px-4 py-2.5 border border-gray-300 rounded-lg text-gray-900 placeholder-gray-400 focus:ring-2 focus:ring-[#1375fd] focus:border-[#1375fd]"
                        placeholder="Enter your password">
                    <button type="button" class="absolute inset-y-0 right-0 pr-3 flex items-center" onclick="togglePasswordVisibility('login-password')">
                        <svg class="h-5 w-5 text-gray-400 hover:text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                        </svg>
                    </button>
                </div>
                <p class="mt-1 text-sm text-red-600 hidden" id="password-error"></p>
            </div>

            <!-- User Type (Admin or Regular User) -->
            <div class="flex items-center space-x-4">
                <div class="flex items-center">
                    <input type="radio" id="user-type-regular" name="is_admin" value="0"
                        class="h-4 w-4 text-[#0C5BE2] focus:ring-[#1375fd] border-gray-300" <?php echo (!isset($_POST['is_admin']) || $_POST['is_admin'] == '0') ? 'checked' : ''; ?>>
                    <label for="user-type-regular" class="ml-2 block text-sm text-gray-700">Regular User</label>
                </div>
                <div class="flex items-center">
                    <input type="radio" id="user-type-admin" name="is_admin" value="1"
                        class="h-4 w-4 text-[#0C5BE2] focus:ring-[#1375fd] border-gray-300" <?php echo (isset($_POST['is_admin']) && $_POST['is_admin'] == '1') ? 'checked' : ''; ?>>
                    <label for="user-type-admin" class="ml-2 block text-sm text-gray-700">Admin</label>
                </div>
            </div>

            <!-- Remember Me & Forgot Password -->
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <input type="checkbox" id="remember-me" name="remember_me"
                        class="h-4 w-4 text-[#0C5BE2] focus:ring-[#1375fd] border-gray-300 rounded" <?php echo (isset($_POST['remember_me'])) ? 'checked' : ''; ?>>
                    <label for="remember-me" class="ml-2 block text-sm text-gray-700">Remember me</label>
                </div>
                <button type="button" onclick="showForgotPassword()"
                    class="text-sm font-medium text-[#0C5BE2] hover:text-[#1375fd]">
                    Forgot password?
                </button>
            </div>

            <!-- Login Button -->
            <button type="submit"
                class="btn-primary w-full flex justify-center py-2.5 px-4 border border-transparent rounded-lg text-sm font-medium text-white bg-[#0C5BE2] hover:bg-[#114bbc] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#1375fd]">
                Sign in
            </button>

            <!-- Register Link -->
            <div class="text-center mt-4">
                <p class="text-sm text-gray-600">
                    Don't have an account? <a href="<?= config('app_url'); ?>/register" class="text-[#0C5BE2] hover:underline font-medium">Sign up</a>
                </p>
            </div>
        </form>
    </div>

    <!-- Forgot Password Card -->
    <div id="forgotCard" class="bg-white rounded-xl shadow-lg p-8 hidden">
        <!-- Back Button -->
        <div class="mb-6">
            <button onclick="showLoginCard()"
                class="inline-flex items-center text-sm font-medium text-[#0C5BE2] hover:text-[#1375fd]">
                <svg class="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                </svg>
                Back to login
            </button>
        </div>

        <!-- Title -->
        <div class="text-center mb-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-2">Forgot Password?</h2>
            <p class="text-gray-600">Enter your email to reset your password</p>
        </div>

        <!-- Forgot Password Form -->
        <form id="forgotForm" class="space-y-5" method="POST" action="<?= url('forgot-password') ?>" data-ajax-form data-callback="handleCustomResponse()">
            <div>
                <label for="forgot-email" class="block text-sm font-medium text-gray-700 mb-1">Email address</label>
                <div class="relative">
                    <input type="email" id="forgot-email" name="email" required
                        class="input-focus block w-full px-4 py-2.5 border border-gray-300 rounded-lg text-gray-900 placeholder-gray-400 focus:ring-2 focus:ring-[#1375fd] focus:border-[#1375fd]"
                        placeholder="Enter your email">
                </div>
                <p class="mt-1 text-sm text-red-600 hidden" id="forgot-email-error"></p>
            </div>

            <button type="submit"
                class="btn-primary w-full flex justify-center py-2.5 px-4 border border-transparent rounded-lg text-sm font-medium text-white bg-[#0C5BE2] hover:bg-[#114bbc] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#1375fd]">
                Send Reset Link
            </button>
        </form>
    </div>
</div>