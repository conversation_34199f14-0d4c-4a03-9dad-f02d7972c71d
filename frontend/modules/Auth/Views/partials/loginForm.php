<!-- Login Card -->
<div id="loginCardContainer">
    <div id="loginCard" class="slide-in">
        <!-- Logo -->
        <div class="flex justify-center mb-8">
            <div class="relative">
                <svg width="220" viewBox="0 0 220 32.1119432" version="1.1">
                    <g id="Page-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                        <g id="logo-copy" fill-rule="nonzero">
                            <g id="Logo-variation-1" fill="#6B21A8">
                                <path d="M0,15.9677733 C0,7.14901896 7.14902162,0 15.9677493,0 L15.9677493,31.9355865 C7.14902162,31.9355865 0,24.786501 0,15.9677733 Z" id="Path"></path>
                                <path d="M17.0322666,0 L33,0 L33,15.4355146 L32.4677413,15.4355146 C23.9429801,15.4355146 17.0322666,8.52480116 17.0322666,0 L17.0322666,0 Z" id="Path"></path>
                                <path d="M17.0322666,16.5000319 L33,16.5000319 L33,23.9516266 C33,28.3609373 29.4256169,31.9355865 25.0161466,31.9355865 L25.0161466,31.9355865 C20.6067828,31.9355865 17.0322666,28.3609373 17.0322666,23.9516533 L17.0322666,16.5000319 Z" id="Path"></path>
                            </g>
                            <path d="M65.5955062,1.50918052 L65.5955062,6.98996101 L54.7775721,6.98996101 L54.7775721,13.3560909 L64.3180428,13.3560909 L64.3180428,18.8368845 L54.7775721,18.8368845 L54.7775721,25.7089161 L65.5955062,25.7089161 L65.5955062,31.1897097 L47.4945548,31.1897097 L47.4945548,1.50918052 L65.5955062,1.50918052 Z M69.8690231,28.3942459 C67.6906118,26.1952049 66.6010782,23.2632378 66.6010782,19.5981805 C66.6010782,15.933156 67.6974993,13.0082076 69.8903415,10.8232696 C72.0828557,8.63833158 74.9191853,7.54591177 78.3990022,7.54591177 C81.284856,7.54591177 83.7322069,8.25774915 85.7410551,9.68148951 C87.7499032,11.1051971 89.0795146,13.1420871 89.7302174,15.7921923 L81.879147,15.7921923 C81.1999104,14.3825549 80.0897143,13.677769 78.5475749,13.677769 C77.0057635,13.677769 75.8598182,14.2204695 75.1100668,15.3058707 C74.3603155,16.3913046 73.9854399,17.8220636 73.9854399,19.5981805 C73.9854399,21.3743302 74.3603155,22.8050893 75.1100668,23.8904904 C75.8598182,24.9758915 76.9562393,25.5185921 78.3990022,25.5185921 C79.3045417,25.5185921 80.0116562,25.3635582 80.5210017,25.0534249 C81.0303472,24.7433243 81.482953,24.1935723 81.879147,23.4041688 L89.7302174,23.4041688 C89.0795146,25.9415161 87.7354723,27.9572517 85.6984183,29.4514739 C83.6613643,30.9456634 81.2284442,31.6927581 78.3990022,31.6927581 C74.9191853,31.6927581 72.0759682,30.593254 69.8690231,28.3942459 Z M97.5140529,0 L97.5140529,11.1271386 C99.0188031,8.65443516 101.417286,7.41809984 104.710157,7.41809984 C107.435303,7.41809984 109.642248,8.31724385 111.33132,10.1155647 C113.020393,11.9139183 113.864929,14.3584815 113.864929,17.4493854 L113.864929,31.1897097 L106.626188,31.1897097 L106.626188,18.4187817 C106.626188,16.9014372 106.221795,15.7283355 105.41268,14.899411 C104.603565,14.0704865 103.489433,13.6560242 102.070284,13.6560242 C100.650808,13.6560242 99.536676,14.0704865 98.7275612,14.899411 C97.9187743,15.7283355 97.5140529,16.9014372 97.5140529,18.4187817 L97.5140529,31.1897097 L90.2330034,31.1897097 L90.2330034,0 L97.5140529,0 Z M130.510982,23.6600222 C131.480805,22.6647159 131.96588,21.2271022 131.96588,19.3470499 C131.96588,17.4670305 131.466374,16.0363042 130.468017,15.0548384 C129.469661,14.0733398 128.28567,13.5826233 126.916374,13.5826233 C125.547077,13.5826233 124.370302,14.0733398 123.386049,15.0548384 C122.401795,16.0363042 121.909832,17.4670305 121.909832,19.3470499 C121.909832,21.2271022 122.415898,22.6647159 123.428686,23.6600222 C124.441473,24.6553286 125.632679,25.1529981 127.001975,25.1529981 C128.371272,25.1529981 129.540831,24.6553286 130.510982,23.6600222 Z M118.420176,28.3519371 C116.053835,26.1247231 114.870501,23.1927559 114.870501,19.5559045 C114.870501,15.9190531 116.053835,13.0082076 118.420176,10.8232696 C120.786845,8.63833158 123.716648,7.54591177 127.21024,7.54591177 C130.703832,7.54591177 133.626747,8.63833158 135.978985,10.8232696 C138.331551,13.0082076 139.50767,15.9190531 139.50767,19.5559045 C139.50767,23.1927559 138.338766,26.1247231 136.000303,28.3519371 C133.66184,30.5791839 130.74614,31.6927581 127.252548,31.6927581 C123.758957,31.6927581 120.815051,30.5791839 118.420176,28.3519371 Z" id="Shape" fill="#1F2937"></path>
                            <path d="M217.557569,24.6458829 C217.557569,23.5853095 217.319459,22.6794748 216.843567,21.9283787 C216.367347,21.1772499 215.559872,20.5086069 214.421142,19.9224168 C213.282412,19.3362267 211.719282,18.763418 209.731752,18.2039579 C208.296533,17.8029755 207.003655,17.3592906 205.853118,16.8728706 C204.702909,16.3864834 203.719967,15.8278432 202.903965,15.1969829 C202.087962,14.5660898 201.463825,13.8281784 201.031226,12.9832159 C200.598955,12.1382862 200.382491,11.1467843 200.382491,10.008776 C200.382491,8.52914863 200.792132,7.23390896 201.611414,6.12312255 C202.430696,5.01230333 203.562867,4.1464488 205.007598,3.52555894 C206.452656,2.90463301 208.111883,2.59417496 209.985277,2.59417496 C211.995437,2.59417496 213.734362,2.97698106 215.201723,3.74258014 C216.669084,4.50820545 217.804206,5.53867074 218.607417,6.8340416 C219.410301,8.12941246 219.811742,9.57673423 219.811742,11.1759741 L217.379478,11.1759741 C217.379478,9.92448633 217.088892,8.80802595 216.507392,7.82662575 C215.925892,6.84519275 215.08562,6.07287675 213.985919,5.50961213 C212.886546,4.94634752 211.552999,4.66471521 209.985277,4.66471521 C208.416244,4.66471521 207.098439,4.90518663 206.031536,5.38612947 C204.964633,5.86707231 204.161749,6.50760788 203.622886,7.30776898 C203.084351,8.10789728 202.814755,8.99179045 202.814755,9.95944849 C202.814755,10.8683006 203.054177,11.6866643 203.532693,12.4145725 C204.011537,13.1424806 204.810156,13.8028915 205.92888,14.3958707 C207.047931,14.988817 208.569408,15.5454893 210.493311,16.0659204 C212.516918,16.6109824 214.236492,17.2514852 215.652361,17.9873959 C217.067902,18.7233067 218.146612,19.6221883 218.887836,20.6840736 C219.629388,21.7459589 220,23.0533993 220,24.6063947 C220,26.1651624 219.57396,27.506745 218.721881,28.6311096 C217.869473,29.7554742 216.702865,30.6167042 215.222057,31.2147998 C213.740921,31.8128954 212.051193,32.1119432 210.151889,32.1119432 C208.881313,32.1119432 207.615328,31.947726 206.353936,31.6192916 C205.092543,31.2908573 203.942334,30.7799375 202.903965,30.0865651 C201.865267,29.3931927 201.032866,28.5022153 200.407089,27.4136328 C199.780985,26.3250175 199.468096,25.0206601 199.468096,23.5005279 L201.890849,23.5005279 C201.890849,24.7131834 202.133879,25.7378107 202.619938,26.5744427 C203.105997,27.4110418 203.751453,28.0844733 204.555648,28.5947043 C205.360171,29.1049353 206.24964,29.4752521 207.224382,29.7056547 C208.199124,29.9360573 209.17485,30.051275 210.151889,30.051275 C211.654671,30.051275 212.959028,29.8255953 214.065289,29.3742358 C215.171877,28.9229091 216.030516,28.292016 216.641206,27.4815893 C217.252224,26.6711953 217.557569,25.725938 217.557569,24.6458829 Z" id="Path" fill="#75787A"></path>
                            <path d="M163.480689,22.761239 C163.284232,24.6963255 162.75488,26.3640794 161.892961,27.7644679 C161.031043,29.1648564 159.851316,30.2395328 158.354109,30.9884969 C156.85723,31.7374611 155.054678,32.1119432 152.946782,32.1119432 C150.77821,32.1119432 148.886777,31.5727192 147.272155,30.4943039 C145.657533,29.4158558 144.404668,27.9035622 143.513887,25.9573573 C142.623106,24.0111853 142.177716,21.7461229 142.177716,19.1621703 L142.177716,15.5535247 C142.177716,12.9631766 142.624746,10.6965728 143.518807,8.75368046 C144.412539,6.81078815 145.680163,5.2984617 147.320695,4.21676668 C148.961555,3.13503559 150.894969,2.59417496 153.121921,2.59417496 C155.158319,2.59417496 156.91561,2.9637735 158.393138,3.70296073 C159.870666,4.44218404 161.038914,5.51197355 161.897553,6.91236207 C162.75652,8.31275059 163.284232,9.99992063 163.480689,11.9738722 L161.057936,11.9738722 C160.769646,9.64865919 159.989721,7.84919044 158.718817,6.57540035 C157.447585,5.30161026 155.582063,4.66471521 153.121921,4.66471521 C151.35151,4.66471521 149.831345,5.11548433 148.562081,6.01705537 C147.292817,6.9186264 146.316763,8.18224926 145.633919,9.80789115 C144.951402,11.4335658 144.60998,13.3356252 144.60998,15.5140693 L144.60998,19.1621703 C144.60998,21.2626875 144.936643,23.1339828 145.590298,24.7759578 C146.243625,26.4179657 147.190489,27.7076954 148.430891,28.6451141 C149.671293,29.5825656 151.176372,30.051275 152.946782,30.051275 C154.63815,30.051275 156.046803,29.7798099 157.172742,29.2369126 C158.298681,28.6939824 159.176999,27.8808991 159.807367,26.7976298 C160.438063,25.7143933 160.85492,24.3689078 161.057936,22.761239 L163.480689,22.761239 Z" id="Path" fill="#75787A"></path>
                            <path d="M171.483284,2.98876849 L182.054253,28.3340296 L192.644572,2.98876849 L195.078476,2.98876849 L182.982423,31.7172906 L181.125755,31.7172906 L169.039541,2.98876849 L171.483284,2.98876849 Z M170.438355,2.98876849 L170.700735,19.1538069 L170.700735,31.7172906 L168.287494,31.7172906 L168.287494,2.98876849 L170.438355,2.98876849 Z M195.860369,2.98876849 L195.860369,31.7172906 L193.447456,31.7172906 L193.447456,19.1538069 L193.709836,2.98876849 L195.860369,2.98876849 Z" id="Shape" fill="#75787A"></path>
                        </g>
                    </g>
                </svg>
            </div>
        </div>

        <!-- Welcome Text -->
        <div class="text-center mb-8">
            <h2 class="text-3xl font-bold text-gray-900 mb-4 hidden">Sign in to Echo<span class="font-thin">CMS</span></h2>
            <p class="text-gray-600 text-md font-light mb-8">Manage your blocks, pages, and templates with full API control and a user-friendly builder interface.</p>
        </div>

        <?php if (isset($error) && !empty($error)): ?>
            <div class="mb-4 p-4 text-sm text-red-800 rounded-lg bg-red-50" role="alert">
                <?php echo htmlspecialchars($error); ?>
            </div>
        <?php endif; ?>

        <!-- Login Form -->
        <form id="loginForm" class="space-y-5" method="POST" action="<?= url('login') ?>" data-ajax-form data-reset-on-success>
            <!-- Email -->
            <div>
                <label for="login-email" class="block text-sm font-medium text-gray-700 mb-1">Email</label>
                <div class="relative">
                    <input type="email" id="login-email" name="email" required
                        class="input-focus block w-full px-4 py-2.5 border border-gray-300 rounded-lg text-gray-900 placeholder-gray-400 focus:ring-2 focus:ring-purple-950 focus:border-purple-950"
                        placeholder="Enter your email" value="<?php echo isset($_POST['email']) ? htmlspecialchars($_POST['email']) : ''; ?>">
                </div>
                <p class="mt-1 text-sm text-red-600 hidden" id="email-error"></p>
            </div>

            <!-- Password -->
            <div>
                <label for="login-password" class="block text-sm font-medium text-gray-700 mb-1">Password</label>
                <div class="relative">
                    <input type="password" id="login-password" name="password" required
                        class="input-focus block w-full px-4 py-2.5 border border-gray-300 rounded-lg text-gray-900 placeholder-gray-400 focus:ring-2 focus:ring-purple-950 focus:border-purple-950"
                        placeholder="Enter your password">
                    <button type="button" class="absolute inset-y-0 right-0 pr-3 flex items-center" onclick="togglePasswordVisibility('login-password')">
                        <svg class="h-5 w-5 text-gray-400 hover:text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                        </svg>
                    </button>
                </div>
                <p class="mt-1 text-sm text-red-600 hidden" id="password-error"></p>
            </div>

            <!-- User Type (Admin or Regular User) -->
            <div class="flex items-center space-x-4">
                <div class="flex items-center">
                    <input type="radio" id="user-type-regular" name="is_admin" value="0"
                        class="h-4 w-4 text-purple-800 focus:ring-purple-950 border-gray-300" <?php echo (!isset($_POST['is_admin']) || $_POST['is_admin'] == '0') ? 'checked' : ''; ?>>
                    <label for="user-type-regular" class="ml-2 block text-sm text-gray-700">Regular User</label>
                </div>
                <div class="flex items-center">
                    <input type="radio" id="user-type-admin" name="is_admin" value="1"
                        class="h-4 w-4 text-purple-800 focus:ring-purple-950 border-gray-300" <?php echo (isset($_POST['is_admin']) && $_POST['is_admin'] == '1') ? 'checked' : ''; ?>>
                    <label for="user-type-admin" class="ml-2 block text-sm text-gray-700">Admin</label>
                </div>
            </div>

            <!-- Remember Me & Forgot Password -->
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <input type="checkbox" id="remember-me" name="remember_me"
                        class="h-4 w-4 text-purple-800 focus:ring-purple-950 border-gray-300 rounded" <?php echo (isset($_POST['remember_me'])) ? 'checked' : ''; ?>>
                    <label for="remember-me" class="ml-2 block text-sm text-gray-700">Remember me</label>
                </div>
                <button type="button" onclick="showForgotPassword()"
                    class="text-sm font-medium text-purple-800 hover:text-purple-950">
                    Forgot password?
                </button>
            </div>

            <!-- Login Button -->
            <button type="submit"
                class="btn-primary w-full flex justify-center py-2.5 px-4 border border-transparent rounded-lg text-sm font-medium text-white bg-purple-800 hover:bg-purple-950 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-950">
                Sign in
            </button>

            <!-- Register Link -->
            <div class="text-center mt-4">
                <p class="text-sm text-gray-600">
                    Don't have an account? <a href="<?= config('app_url'); ?>/register" class="text-purple-800 hover:underline font-medium">Sign up</a>
                </p>
            </div>
        </form>
    </div>

    <!-- Forgot Password Card -->
    <div id="forgotCard" class="hidden">
        <!-- Back Button -->
        <div class="mb-6">
            <button onclick="showLoginCard()"
                class="inline-flex items-center text-sm font-medium text-purple-800 hover:text-purple-950">
                <svg class="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                </svg>
                Back to login
            </button>
        </div>

        <!-- Title -->
        <div class="text-center mb-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-2">Forgot Password?</h2>
            <p class="text-gray-600">Enter your email to reset your password</p>
        </div>

        <!-- Forgot Password Form -->
        <form id="forgotForm" class="space-y-5" method="POST" action="<?= url('forgot-password') ?>" data-ajax-form data-callback="handleCustomResponse()">
            <div>
                <label for="forgot-email" class="block text-sm font-medium text-gray-700 mb-1">Email address</label>
                <div class="relative">
                    <input type="email" id="forgot-email" name="email" required
                        class="input-focus block w-full px-4 py-2.5 border border-gray-300 rounded-lg text-gray-900 placeholder-gray-400 focus:ring-2 focus:ring-purple-950 focus:border-purple-950"
                        placeholder="Enter your email">
                </div>
                <p class="mt-1 text-sm text-red-600 hidden" id="forgot-email-error"></p>
            </div>

            <button type="submit"
                class="btn-primary w-full flex justify-center py-2.5 px-4 border border-transparent rounded-lg text-sm font-medium text-white bg-purple-800 hover:bg-[#114bbc] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-950">
                Send Reset Link
            </button>
        </form>
    </div>
</div>