<!-- Success Message Card -->
<div id="forgotPasswordSuccess" class="bg-white rounded-xl shadow-lg p-8 slide-in" style="display: none;">
    <!-- Back Button -->
    <div class="mb-6">
        <button onclick="window.location.href='<?= config('app_url'); ?>/login'"
            class="inline-flex items-center text-sm font-medium text-[#0C5BE2] hover:text-[#1375fd]">
            <svg class="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
            </svg>
            Back to login
        </button>
    </div>

    <!-- Success Icon -->
    <div class="flex justify-center mb-6">
        <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
            <svg class="w-10 h-10 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
            </svg>
        </div>
    </div>

    <!-- Success Message -->
    <div class="text-center">
        <h2 class="text-2xl font-bold text-gray-900 mb-2">Check your email</h2>
        <p class="text-gray-600 mb-6">
            <?php echo isset($forgotSuccess) ? htmlspecialchars($forgotSuccess) : 'If an account exists with this email, you will receive a password reset link.'; ?>
        </p>

        <div class="bg-blue-50 p-4 rounded-lg text-sm text-blue-700 mb-6 text-left">
            <p class="font-medium">Didn't receive an email?</p>
            <ul class="list-disc list-inside mt-1 space-y-1">
                <li>Check your spam or junk folder</li>
                <li>Make sure you entered the correct email address</li>
                <li>Try again in a few minutes</li>
            </ul>
        </div>

        <button onclick="window.location.href='<?= config('app_url'); ?>/login'"
            class="btn-primary w-full flex justify-center py-2.5 px-4 border border-transparent rounded-lg text-sm font-medium text-white bg-[#0C5BE2] hover:bg-[#114bbc] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#1375fd]">
            Return to Login
        </button>
    </div>
</div>