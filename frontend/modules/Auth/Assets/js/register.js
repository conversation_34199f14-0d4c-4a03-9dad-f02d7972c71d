document.addEventListener('DOMContentLoaded', () => {
    const registerForm = document.getElementById('registerForm');
    
    if (registerForm) {
        // Initialize password visibility toggles
        initPasswordToggles();
        
        // Add form validation on submit
        registerForm.addEventListener('submit', (e) => {
            if (!validateRegisterForm()) {
                e.preventDefault();
                if (typeof Auth !== 'undefined' && Auth.showToast) {
                    Auth.showToast('Please fix form errors');
                }
            }
        });
    }
});

function initPasswordToggles() {
    // Password visibility toggle
    const toggleButtons = document.querySelectorAll('.password-toggle');
    
    toggleButtons.forEach(button => {
        button.addEventListener('click', function() {
            const targetId = this.getAttribute('data-target');
            const input = document.getElementById(targetId);
            const svg = this.querySelector('svg');
            
            if (input.type === 'password') {
                input.type = 'text';
                svg.innerHTML = '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.88 9.88l-3.29-3.29m7.532 7.532l3.29 3.29M3 3l3.59 3.59m0 0A9.953 9.953 0 0112 5c4.478 0 8.268 2.943 9.543 7a10.025 10.025 0 01-4.132 5.411m0 0L21 21" />';
            } else {
                input.type = 'password';
                svg.innerHTML = '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" /><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />';
            }
        });
    });
}

function validateRegisterForm() {
    let isValid = true;
    
    // Reset errors
    document.querySelectorAll('.text-red-600').forEach(el => el.classList.add('hidden'));
    
    // Name validation
    const name = document.getElementById('name');
    if (!name.value.trim()) {
        document.getElementById('name-error').textContent = 'Name is required';
        document.getElementById('name-error').classList.remove('hidden');
        isValid = false;
    }
    
    // Email validation
    const email = document.getElementById('email');
    if (!email.value.trim()) {
        document.getElementById('email-error').textContent = 'Email is required';
        document.getElementById('email-error').classList.remove('hidden');
        isValid = false;
    } else if (!/^\S+@\S+\.\S+$/.test(email.value)) {
        document.getElementById('email-error').textContent = 'Please enter a valid email address';
        document.getElementById('email-error').classList.remove('hidden');
        isValid = false;
    }
    
    // Password validation
    const password = document.getElementById('password');
    if (!password.value.trim()) {
        document.getElementById('password-error').textContent = 'Password is required';
        document.getElementById('password-error').classList.remove('hidden');
        isValid = false;
    } else if (password.value.length < 8) {
        document.getElementById('password-error').textContent = 'Password must be at least 8 characters';
        document.getElementById('password-error').classList.remove('hidden');
        isValid = false;
    }
    
    // Confirm password validation
    const passwordConfirm = document.getElementById('password_confirmation');
    if (!passwordConfirm.value.trim()) {
        document.getElementById('password-confirm-error').textContent = 'Please confirm your password';
        document.getElementById('password-confirm-error').classList.remove('hidden');
        isValid = false;
    } else if (password.value !== passwordConfirm.value) {
        document.getElementById('password-confirm-error').textContent = 'Passwords do not match';
        document.getElementById('password-confirm-error').classList.remove('hidden');
        isValid = false;
    }
    
    // Terms and conditions validation
    const terms = document.getElementById('terms');
    if (!terms.checked) {
        const termsError = document.getElementById('terms-error') || createTermsErrorElement();
        termsError.textContent = 'You must accept the terms and conditions';
        termsError.classList.remove('hidden');
        isValid = false;
    }
    
    return isValid;
}

function createTermsErrorElement() {
    const errorElement = document.createElement('p');
    errorElement.id = 'terms-error';
    errorElement.className = 'mt-1 text-sm text-red-600';
    document.querySelector('input[name="terms"]').parentNode.parentNode.appendChild(errorElement);
    return errorElement;
}

// Make functions available globally if needed
if (typeof window.Auth === 'undefined') {
    window.Auth = {};
}

window.Auth.validateRegisterForm = validateRegisterForm;
