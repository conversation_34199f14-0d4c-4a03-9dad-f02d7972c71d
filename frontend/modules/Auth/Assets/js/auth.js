// Card Management
function showLoginCard() {
    const loginCard = document.getElementById('loginCard');
    const forgotCard = document.getElementById('forgotCard');

    forgotCard.classList.add('hidden');
    loginCard.classList.remove('hidden');
    loginCard.classList.add('slide-in');

    // Clear forgot password form
    document.getElementById('forgotForm')?.reset();
}

function showForgotPassword() {
    const loginCard = document.getElementById('loginCard');
    const forgotCard = document.getElementById('forgotCard');

    loginCard.classList.add('hidden');
    forgotCard.classList.remove('hidden');
    forgotCard.classList.add('slide-in');

    // Clear login form
    document.getElementById('loginForm').reset();
}

// Password Visibility Toggle
function togglePasswordVisibility(inputId) {
    const input = document.getElementById(inputId);
    const type = input.getAttribute('type') === 'password' ? 'text' : 'password';
    input.setAttribute('type', type);

    const button = input.nextElementSibling;
    const svg = button.querySelector('svg');
    if (type === 'password') {
        svg.innerHTML = '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" /><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />';
    } else {
        svg.innerHTML = '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.88 9.88l-3.29-3.29m7.532 7.532l3.29 3.29M3 3l3.59 3.59m0 0A9.953 9.953 0 0112 5c4.478 0 8.268 2.943 9.543 7a10.025 10.025 0 01-4.132 5.411m0 0L21 21" />';
    }
}

// Form validation for login
document.getElementById('loginForm')?.addEventListener('submit', function(event) {
    let isValid = true;
    
    // Reset errors
    document.querySelectorAll('.text-red-600').forEach(el => el.classList.add('hidden'));
    
    // Validate email
    const email = document.getElementById('login-email');
    if (email.value.trim() === '') {
        document.getElementById('email-error').textContent = 'Email is required';
        document.getElementById('email-error').classList.remove('hidden');
        isValid = false;
    } else if (!/^\S+@\S+\.\S+$/.test(email.value)) {
        document.getElementById('email-error').textContent = 'Please enter a valid email address';
        document.getElementById('email-error').classList.remove('hidden');
        isValid = false;
    }
    
    // Validate password
    const password = document.getElementById('login-password');
    if (password.value.trim() === '') {
        document.getElementById('password-error').textContent = 'Password is required';
        document.getElementById('password-error').classList.remove('hidden');
        isValid = false;
    }
    
    if (!isValid) {
        event.preventDefault();
    }
});

// Form validation for forgot password
document.getElementById('forgotForm')?.addEventListener('submit', function(event) {
    let isValid = true;
    
    // Reset errors
    document.querySelectorAll('.text-red-600').forEach(el => el.classList.add('hidden'));
    
    // Validate email
    const email = document.getElementById('forgot-email');
    if (email.value.trim() === '') {
        document.getElementById('forgot-email-error').textContent = 'Email is required';
        document.getElementById('forgot-email-error').classList.remove('hidden');
        isValid = false;
    } else if (!/^\S+@\S+\.\S+$/.test(email.value)) {
        document.getElementById('forgot-email-error').textContent = 'Please enter a valid email address';
        document.getElementById('forgot-email-error').classList.remove('hidden');
        isValid = false;
    }
    
    if (!isValid) {
        event.preventDefault();
    }
});

// Initialize
document.addEventListener('DOMContentLoaded', function() {
    // Clear any existing form data
    document.getElementById('loginForm')?.reset();
    document.getElementById('forgotForm')?.reset();
});