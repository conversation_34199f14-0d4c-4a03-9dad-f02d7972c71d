body {
    background-color: #f8fafc;
}

.card-shadow {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 
                0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.input-focus {
    transition: all 0.3s ease;
}

.input-focus:focus {
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.btn-primary {
    transition: all 0.3s ease;
}

.btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
}

.slide-in {
    animation: slideIn 0.3s ease-out forwards;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Toast Notification */
#toast {
    position: fixed;
    top: 1rem;
    right: 1rem;
    z-index: 50;
    transform: translateX(100%);
    transition: all 0.3s;
}

#toast .toast-content {
    display: flex;
    align-items: center;
    padding: 1rem;
    margin-bottom: 1rem;
    color: #6b7280;
    background-color: white;
    border-radius: 0.5rem;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}