<?php

namespace Modules\CommonTags\Services;

use App\Services\ApiClient;
use Core\ServiceContainer;

class CommonTagService
{
    protected $api;
    protected $endpoint = 'common_tags';

    public function __construct()
    {
        $this->api = ServiceContainer::resolve(ApiClient::class);
    }

    public function getByModel($modelType, $modelId)
    {
        try {
            $response = $this->api->get($this->endpoint, [
                'model_type' => $modelType,
                'model_id' => $modelId
            ]);

            $data = $response['data'] ?? [];
            if (!is_array($data)) {
                $data = [];
            }

            return [
                'success' => true,
                'data' => $data
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    public function create($data)
    {
        try {
            $response = $this->api->post($this->endpoint, $data);
            
            return [
                'success' => true,
                'data' => $response['data'] ?? null
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    public function removeAllForModel($modelType, $modelId)
    {
        try {
            $response = $this->api->delete($this->endpoint, [
                'model_type' => $modelType,
                'model_id' => $modelId
            ]);
            
            return [
                'success' => true,
                'data' => $response['data'] ?? null
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }
}