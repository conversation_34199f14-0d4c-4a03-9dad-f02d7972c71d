document.addEventListener('DOMContentLoaded', function() {
    // Initialize Flowbite
    const initFlowbite = window.initFlowbite || (() => {});
    initFlowbite();
    
    // Handle edit button clicks
    document.addEventListener('click', function(e) {
        const editButton = e.target.closest('[data-modal-target="editCategoryModal"]');
        if (editButton) {
            const id = editButton.dataset.id;
            const name = editButton.dataset.name;
            const image = editButton.dataset.image;
            const parent = editButton.dataset.parent;
            
            // Set form values
            document.getElementById('editCategoryId').value = id;
            document.getElementById('editCategoryName').value = name;
            
            // Set parent category dropdown value
            const parentSelect = document.getElementById('editCategoryParent');
            if (parent && parentSelect) {
                const option = Array.from(parentSelect.options).find(opt => opt.value === parent);
                if (option) {
                    option.selected = true;
                }
            } else if (parentSelect) {
                parentSelect.value = '';
            }

            // Handle image preview
            const editImagePreview = document.getElementById('editImagePreview');
            if (image && editImagePreview) {
                editImagePreview.innerHTML = `<img src="${image}" alt="Category Image" class="w-full h-full object-contain">`;
            }
        }
    });

    // Handle delete button clicks
    document.addEventListener('click', function(e) {
        const deleteButton = e.target.closest('[data-modal-target="deleteCategoryModal"]');
        if (deleteButton) {
            const id = deleteButton.dataset.id;
            const blockCount = parseInt(deleteButton.dataset.blockCount || 0);
            
            // Set the category ID in the delete confirmation modal
            document.getElementById('deleteCategoryId').value = id;
            document.getElementById('deleteCategoryBlockCount').value = blockCount;
            
            // Show appropriate message based on block count
            const deleteMessageWithBlocks = document.getElementById('deleteMessageWithBlocks');
            const deleteMessageNoBlocks = document.getElementById('deleteMessageNoBlocks');
            const deleteCategoryBtn = document.getElementById('deleteCategoryBtn');
            const blockCountText = document.getElementById('blockCountText');
            
            if (blockCount > 0) {
                deleteMessageWithBlocks.classList.remove('hidden');
                deleteMessageNoBlocks.classList.add('hidden');
                deleteCategoryBtn.disabled = true;
                blockCountText.textContent = `${blockCount} block${blockCount > 1 ? 's' : ''}`;
            } else {
                deleteMessageWithBlocks.classList.add('hidden');
                deleteMessageNoBlocks.classList.remove('hidden');
                deleteCategoryBtn.disabled = false;
            }
        }
    });
    
    // Handle image uploads
    const createImageInput = document.getElementById('createCategoryImage');
    const editImageInput = document.getElementById('editCategoryImage');
    
    if (createImageInput) {
        createImageInput.addEventListener('change', function(e) {
            handleImagePreview(e, 'createImagePreview');
        });
    }
    
    if (editImageInput) {
        editImageInput.addEventListener('change', function(e) {
            handleImagePreview(e, 'editImagePreview');
        });
    }
    
    function handleImagePreview(event, previewId) {
        const file = event.target.files[0];
        const preview = document.getElementById(previewId);
        
        if (file && preview) {
            const reader = new FileReader();
            
            reader.onload = function(e) {
                preview.innerHTML = `<img src="${e.target.result}" alt="Preview" class="w-full h-full object-contain">`;
            };
            
            reader.readAsDataURL(file);
        }
    }
    
    // Initialize Lucide icons
    if (typeof lucide !== "undefined" && typeof lucide.createIcons === "function") {
        lucide.createIcons();
    }
});