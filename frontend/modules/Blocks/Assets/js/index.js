document.addEventListener('DOMContentLoaded', function() {
    // Initialize Flowbite
    const initFlowbite = window.initFlowbite || (() => {});
    initFlowbite();
    
    // Check localStorage for category info
    const storedCategoryId = localStorage.getItem('selectedCategoryId');
    const storedCategoryName = localStorage.getItem('selectedCategoryName');
    
    if (!window.location.search.includes('category_id') && storedCategoryId) {
        window.location.href = `${window.location.pathname}?category_id=${storedCategoryId}`;
    }
    
    // Handle delete button clicks
    document.addEventListener('click', function(e) {
        const deleteButton = e.target.closest('[data-modal-target="deleteBlockModal"]');
        if (deleteButton) {
            const id = deleteButton.dataset.id;
            
            // Set the block ID in the delete confirmation modal
            const deleteBlockId = document.getElementById('deleteBlockId');
            if (deleteBlockId) {
                deleteBlockId.value = id;
            }
        }
    });
    
    // Initialize Lucide icons
    if (typeof lucide !== "undefined" && typeof lucide.createIcons === "function") {
        lucide.createIcons();
    }
});