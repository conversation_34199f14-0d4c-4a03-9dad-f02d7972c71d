// Global variables
let currentStep = 1;
let editors = {};

document.addEventListener('DOMContentLoaded', function() {
    // Initialize components
    initializeStepNavigation();
    initializeEditors();
    initializeSelect2();
    initializeImageUpload();
    initializeTagsManager();
    initializeDependencies();
    initializeAIAssistant();
    initializeFlowbite();

    // Initialize tabs when step 2 becomes visible
    // This will be called from the step navigation logic

    // Add event listener to reset preview button
    const resetPreviewBtn = document.getElementById('resetPreview');
    if (resetPreviewBtn) {
        resetPreviewBtn.addEventListener('click', updatePreview);
    }

    // Initialize theme controls
    initializeThemeControls();

    // Hide preview controls by default (they will be shown when preview tab is clicked)
    hidePreviewControls();
    
    // Auto-generate slug from name
    const nameInput = document.getElementById('blockName');
    const slugInput = document.getElementById('blockSlug');
    
    if (nameInput && slugInput) {
        nameInput.addEventListener('input', function() {
            const slug = generateSlug(this.value);
            slugInput.value = slug;
        });
    }
});

// Initialize step navigation
function initializeStepNavigation() {
    const prevBtn = document.getElementById('prevBtn');
    const nextBtn = document.getElementById('nextBtn');
    const submitBtn = document.getElementById('submitBtn');
    
    prevBtn.addEventListener('click', function() {
        if (currentStep > 1) {
            currentStep--;
            updateSteps();
        }
    });
    
    nextBtn.addEventListener('click', function() {
        if (validateCurrentStep()) {
            if (currentStep < 2) {
                currentStep++;
                updateSteps();
            }
        }
    });
    
    updateSteps();
}

// Update step display
function updateSteps() {
    // Hide all step contents
    document.querySelectorAll('.step-content').forEach(content => {
        content.classList.add('hidden');
    });
    
    // Show current step content
    document.getElementById(`step${currentStep}Content`).classList.remove('hidden');

    // Initialize tabs when reaching step 2
    if (currentStep === 2) {
        setTimeout(() => {
            initializeEditorTabs();
        }, 100);
    }

    // Update step indicators
    document.querySelectorAll('.step-item').forEach((item, index) => {
        const stepNum = index + 1;
        const indicator = item.querySelector('.relative.z-40');
        const outerCircle = indicator.querySelector('.absolute');
        const innerCircle = indicator.querySelector('.relative');
        const text = innerCircle.querySelector('span');
        const title = item.querySelector('h3');
        const desc = item.querySelector('p');
        
        if (stepNum === currentStep) {
            outerCircle.className = 'absolute -inset-0.5 bg-blue-100 rounded-full';
            innerCircle.className = 'relative flex items-center justify-center w-8 h-8 bg-blue-600 rounded-full';
            text.className = 'text-white text-sm';
            title.className = 'text-base font-semibold text-gray-900';
            desc.className = 'text-sm text-gray-500';
        } else if (stepNum < currentStep) {
            outerCircle.className = 'absolute -inset-0.5 bg-green-100 rounded-full';
            innerCircle.className = 'relative flex items-center justify-center w-8 h-8 bg-green-600 rounded-full';
            text.className = 'text-white text-sm';
            title.className = 'text-base font-semibold text-gray-900';
            desc.className = 'text-sm text-gray-500';
        } else {
            outerCircle.className = 'absolute -inset-0.5 bg-gray-200 rounded-full';
            innerCircle.className = 'relative flex items-center justify-center w-8 h-8 bg-gray-100 rounded-full';
            text.className = 'text-gray-500 text-sm';
            title.className = 'text-base font-semibold text-gray-400';
            desc.className = 'text-sm text-gray-400';
        }
    });
    
    // Update navigation buttons
    const prevBtn = document.getElementById('prevBtn');
    const nextBtn = document.getElementById('nextBtn');
    const submitBtn = document.getElementById('submitBtn');
    
    prevBtn.style.display = currentStep === 1 ? 'none' : 'block';
    
    if (currentStep === 2) {
        nextBtn.classList.add('hidden');
        submitBtn.classList.remove('hidden');
    } else {
        nextBtn.classList.remove('hidden');
        submitBtn.classList.add('hidden');
    }
}

// Validate current step
function validateCurrentStep() {
    if (currentStep === 1) {
        const name = document.getElementById('blockName').value.trim();
        const categories = $('#blockCategories').val();
        
        if (!name) {
            alert('Please enter a block name');
            return false;
        }
        
        if (!categories || categories.length === 0) {
            alert('Please select at least one category');
            return false;
        }
    }
    
    return true;
}

// Initialize CodeMirror editors
function initializeEditors() {
    console.log('Initializing CodeMirror editors...');

    const editorConfig = {
        lineNumbers: true,
        theme: 'default',
        lineWrapping: true,
        autoCloseBrackets: true,
        matchBrackets: true,
        indentUnit: 2,
        tabSize: 2,
        height: '300px'
    };

    // Template editor
    const templateTextarea = document.getElementById('blockTmplCode');
    if (templateTextarea) {
        console.log('Creating template editor...');
        editors.template = CodeMirror.fromTextArea(templateTextarea, {
            ...editorConfig,
            mode: 'htmlmixed'
        });

        // Replace the textarea with the editor in the correct container
        const templateContainer = document.getElementById('templateEditor');
        if (templateContainer) {
            templateContainer.innerHTML = '';
            templateContainer.appendChild(editors.template.getWrapperElement());
        }

        editors.template.on('change', function() {
            templateTextarea.value = editors.template.getValue();
        });

        console.log('Template editor created successfully');
    }

    // Content editor
    const contentTextarea = document.getElementById('blockJsonCode');
    if (contentTextarea) {
        console.log('Creating content editor...');
        editors.content = CodeMirror.fromTextArea(contentTextarea, {
            ...editorConfig,
            mode: 'application/json'
        });

        // Replace the textarea with the editor in the correct container
        const contentContainer = document.getElementById('contentEditor');
        if (contentContainer) {
            contentContainer.innerHTML = '';
            contentContainer.appendChild(editors.content.getWrapperElement());
        }

        editors.content.on('change', function() {
            contentTextarea.value = editors.content.getValue();
        });

        console.log('Content editor created successfully');
    }

    // CSS editor
    const cssTextarea = document.getElementById('blockCssCode');
    if (cssTextarea) {
        console.log('Creating CSS editor...');
        editors.css = CodeMirror.fromTextArea(cssTextarea, {
            ...editorConfig,
            mode: 'css'
        });

        // Replace the textarea with the editor in the correct container
        const cssContainer = document.getElementById('cssEditor');
        if (cssContainer) {
            cssContainer.innerHTML = '';
            cssContainer.appendChild(editors.css.getWrapperElement());
        }

        editors.css.on('change', function() {
            cssTextarea.value = editors.css.getValue();
        });

        console.log('CSS editor created successfully');
    }

    // JavaScript editor
    const jsTextarea = document.getElementById('blockJsCode');
    if (jsTextarea) {
        console.log('Creating JavaScript editor...');
        editors.javascript = CodeMirror.fromTextArea(jsTextarea, {
            ...editorConfig,
            mode: 'javascript'
        });

        // Replace the textarea with the editor in the correct container
        const jsContainer = document.getElementById('javascriptEditor');
        if (jsContainer) {
            jsContainer.innerHTML = '';
            jsContainer.appendChild(editors.javascript.getWrapperElement());
        }

        editors.javascript.on('change', function() {
            jsTextarea.value = editors.javascript.getValue();
        });

        console.log('JavaScript editor created successfully');
    }

    window.editors = editors;
    console.log('All editors initialized:', editors);
}

// Initialize Select2
function initializeSelect2() {
    if (typeof $ !== 'undefined' && $.fn.select2) {
        $('#blockCategories').select2({
            theme: 'default',
            placeholder: 'Select categories',
            allowClear: true
        });
    }
}

// Initialize image upload
function initializeImageUpload() {
    const fileInput = document.getElementById('blockPoster');
    const browseButton = document.getElementById('browseButton');
    const uploadPlaceholder = document.getElementById('uploadPlaceholder');
    const imagePreview = document.getElementById('imagePreview');
    const previewImg = document.getElementById('preview');
    const removeButton = document.getElementById('removeImage');
    const hiddenInput = document.getElementById('blockImage');
    
    if (browseButton) {
        browseButton.addEventListener('click', () => fileInput.click());
    }
    
    if (uploadPlaceholder) {
        uploadPlaceholder.addEventListener('click', () => fileInput.click());
    }
    
    if (fileInput) {
        fileInput.addEventListener('change', async function(e) {
            const file = e.target.files[0];
            if (file) {
                const formData = new FormData();
                formData.append('action', 'upload_block_image');
                formData.append('image', file);
                
                try {
                    const response = await fetch(window.location.pathname + '/upload-image', {
                        method: 'POST',
                        body: formData
                    });
                    
                    const result = await response.json();
                    
                    if (result.success) {
                        previewImg.src = result.path;
                        hiddenInput.value = result.path;
                        uploadPlaceholder.style.display = 'none';
                        imagePreview.classList.remove('hidden');
                    } else {
                        alert('Failed to upload image: ' + result.message);
                    }
                } catch (error) {
                    alert('Error uploading image');
                    console.error(error);
                }
            }
        });
    }
    
    if (removeButton) {
        removeButton.addEventListener('click', function() {
            fileInput.value = '';
            hiddenInput.value = '';
            previewImg.src = '';
            uploadPlaceholder.style.display = 'flex';
            imagePreview.classList.add('hidden');
        });
    }
}

// Initialize tags manager
function initializeTagsManager() {
    window.blockTagsManager = new BlockTagsManager();
}

// Block Tags Manager Class
class BlockTagsManager {
    constructor() {
        this.selectedTags = new Set();
        this.debounceTimer = null;
        this.tagInput = document.getElementById('tagInput');
        this.tagContainer = document.getElementById('tags-container');
        this.tagSuggestions = document.getElementById('tagSuggestions');
        
        this.init();
    }
    
    init() {
        if (!this.tagInput) return;
        
        this.setupEventListeners();
        this.loadExistingTags();
    }
    
    setupEventListeners() {
        // Input event listeners
        this.tagInput.addEventListener('input', (e) => {
            clearTimeout(this.debounceTimer);
            const query = e.target.value.trim();
            
            if (query.length >= 1) {
                this.debounceTimer = setTimeout(() => {
                    this.searchTags(query);
                }, 300);
            } else {
                this.hideSuggestions();
            }
        });

        this.tagInput.addEventListener('keydown', async (e) => {
            if ((e.key === 'Enter' || e.key === ',') && e.target.value.trim()) {
                e.preventDefault();
                const tagName = e.target.value.trim().replace(/,/g, '');
                if (tagName) {
                    await this.addTag(tagName);
                }
            } else if (e.key === 'Escape') {
                this.hideSuggestions();
            }
        });

        // Close suggestions when clicking outside
        document.addEventListener('click', (e) => {
            if (!this.tagInput.contains(e.target) && !this.tagSuggestions.contains(e.target)) {
                this.hideSuggestions();
            }
        });
    }
    
    loadExistingTags() {
        // Tags are already loaded from PHP
        const existingTags = this.tagContainer.querySelectorAll('[data-tag-id]');
        existingTags.forEach(tag => {
            this.selectedTags.add(tag.dataset.tagId);
        });
    }
    
    async searchTags(query) {
        try {
            const response = await fetch(`/tags/search?search=${encodeURIComponent(query)}`);
            const data = await response.json();
            
            if (data.success && data.data) {
                this.showSuggestions(data.data);
            }
        } catch (error) {
            console.error('Error searching tags:', error);
        }
    }
    
    async createTag(name) {
        try {
            const response = await fetch('/tags/create', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify({ tag: name.trim() })
            });
            
            const data = await response.json();
            
            if (data.success && data.data) {
                return {
                    id: data.data.id || data.data.last_insert_id,
                    tag: name.trim()
                };
            }
            
            throw new Error(data.message || 'Failed to create tag');
        } catch (error) {
            console.error('Error creating tag:', error);
            throw error;
        }
    }
    
    async addTag(tagInput) {
        let tagData;
        
        try {
            if (typeof tagInput === 'string') {
                tagData = await this.createTag(tagInput);
                if (!tagData) return;
            } else {
                tagData = tagInput;
            }
            
            if (this.selectedTags.has(tagData.id.toString())) {
                return;
            }
            
            this.selectedTags.add(tagData.id.toString());
            
            this.tagContainer.appendChild(this.createTagElement(tagData));
            
            const form = document.querySelector('.block-form');
            form.appendChild(this.createHiddenTagInput(tagData.id));
            
            this.tagInput.value = '';
            this.hideSuggestions();
            
        } catch (error) {
            alert('Failed to add tag: ' + error.message);
        }
    }
    
    removeTag(tagId) {
        this.selectedTags.delete(tagId);
        
        const tagElement = document.querySelector(`span[data-tag-id="${tagId}"]`);
        if (tagElement) {
            tagElement.remove();
        }
        
        const hiddenInput = document.querySelector(`input[data-tag-id="${tagId}"]`);
        if (hiddenInput) {
            hiddenInput.remove();
        }
    }
    
    createTagElement(tagData) {
        const tag = document.createElement('span');
        tag.className = 'inline-flex items-center px-2 py-1 bg-blue-100 text-blue-800 text-xs font-medium rounded';
        tag.dataset.tagId = tagData.id;
        
        tag.innerHTML = `
            ${tagData.tag}
            <button type="button" class="ml-1 tag-remove-btn" onclick="blockTagsManager.removeTag('${tagData.id}')">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                </svg>
            </button>
        `;
        
        return tag;
    }
    
    createHiddenTagInput(tagId) {
        const hiddenInput = document.createElement('input');
        hiddenInput.type = 'hidden';
        hiddenInput.name = 'tags[]';
        hiddenInput.value = tagId;
        hiddenInput.dataset.tagId = tagId;
        return hiddenInput;
    }
    
    showSuggestions(suggestions) {
        this.tagSuggestions.innerHTML = '';
        
        const filteredSuggestions = suggestions.filter(tag => 
            !this.selectedTags.has(tag.id.toString())
        );
        
        if (filteredSuggestions.length === 0 && this.tagInput.value.trim()) {
            const div = document.createElement('div');
            div.className = 'px-3 py-2 text-sm text-gray-500 italic cursor-pointer hover:bg-gray-50';
            div.innerHTML = `Create new tag: "${this.tagInput.value.trim()}"`;
            div.onclick = () => this.addTag(this.tagInput.value.trim());
            this.tagSuggestions.appendChild(div);
        } else {
            filteredSuggestions.forEach(tag => {
                const div = document.createElement('div');
                div.className = 'px-3 py-2 hover:bg-gray-50 cursor-pointer text-sm';
                div.textContent = tag.tag;
                div.onclick = () => this.addTag(tag);
                this.tagSuggestions.appendChild(div);
            });
            
            if (this.tagInput.value.trim() && !filteredSuggestions.some(tag => 
                tag.tag.toLowerCase() === this.tagInput.value.trim().toLowerCase()
            )) {
                const div = document.createElement('div');
                div.className = 'px-3 py-2 hover:bg-gray-50 cursor-pointer text-sm text-blue-600 border-t border-gray-200';
                div.innerHTML = `Create: "${this.tagInput.value.trim()}"`;
                div.onclick = () => this.addTag(this.tagInput.value.trim());
                this.tagSuggestions.appendChild(div);
            }
        }

        this.tagSuggestions.classList.remove('hidden');
    }
    
    hideSuggestions() {
        this.tagSuggestions.classList.add('hidden');
    }
}

// Initialize dependencies
function initializeDependencies() {
    const addJsBtn = document.getElementById('addJsDepBtn');
    const addCssBtn = document.getElementById('addCssDepBtn');
    const jsDependencies = document.getElementById('jsDependencies');
    const cssDependencies = document.getElementById('cssDependencies');
    
    if (addJsBtn) {
        addJsBtn.addEventListener('click', () => {
            addDependencyRow(jsDependencies, 'js');
        });
    }
    
    if (addCssBtn) {
        addCssBtn.addEventListener('click', () => {
            addDependencyRow(cssDependencies, 'css');
        });
    }
    
    // Initialize remove buttons for existing dependencies
    document.querySelectorAll('#jsDependencies .cursor-pointer, #cssDependencies .cursor-pointer').forEach(btn => {
        btn.addEventListener('click', function() {
            this.closest('.flex').remove();
            updateDependenciesJson();
        });
    });
    
    // Update dependencies JSON when inputs change
    document.addEventListener('input', function(e) {
        if (e.target.matches('#jsDependencies input, #cssDependencies input')) {
            updateDependenciesJson();
        }
    });
}

// Add dependency row
function addDependencyRow(container, type) {
    const row = document.createElement('div');
    row.className = 'flex items-center gap-2';
    row.innerHTML = `
        <input type="text" class="flex-1 px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="Enter ${type.toUpperCase()} CDN URL">
        <span class="p-1.5 text-red-500 hover:text-red-600 rounded-md hover:bg-red-50 cursor-pointer" title="Remove">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
            </svg>
        </span>
    `;
    
    container.appendChild(row);
    
    // Add remove event listener
    row.querySelector('.cursor-pointer').addEventListener('click', function() {
        row.remove();
        updateDependenciesJson();
    });
    
    // Add input event listener
    row.querySelector('input').addEventListener('input', updateDependenciesJson);
}

// Update dependencies JSON
function updateDependenciesJson() {
    const dependencies = {
        js: [],
        css: []
    };
    
    // Collect JS dependencies
    document.querySelectorAll('#jsDependencies input').forEach(input => {
        if (input.value.trim()) {
            dependencies.js.push(input.value.trim());
        }
    });
    
    // Collect CSS dependencies
    document.querySelectorAll('#cssDependencies input').forEach(input => {
        if (input.value.trim()) {
            dependencies.css.push(input.value.trim());
        }
    });
    
    // Update hidden textarea
    document.getElementById('blockDependencies').value = JSON.stringify(dependencies, null, 2);
}

// Initialize AI Assistant
function initializeAIAssistant() {
    const generateBtn = document.getElementById('generateSmartBlockBtn');
    const smartInput = document.getElementById('smartInput');
    
    if (!generateBtn || !smartInput) return;
    
    // Auto-detect input type
    smartInput.addEventListener('input', function() {
        const inputValue = this.value.trim();
        const detectedType = detectInputType(inputValue);
        updateInputTypeIndicator(detectedType);
    });
    
    // Handle generate button click
    generateBtn.addEventListener('click', async function() {
        const inputValue = smartInput.value.trim();
        if (!inputValue) {
            alert('Please enter some content to generate a block.');
            return;
        }
        
        await generateBlock();
    });
    
    // Keyboard shortcut
    smartInput.addEventListener('keydown', function(e) {
        if (e.ctrlKey && e.key === 'Enter') {
            e.preventDefault();
            generateBtn.click();
        }
    });
}

// Generate block with AI
async function generateBlock() {
    const generateBtn = document.getElementById('generateSmartBlockBtn');
    const smartInput = document.getElementById('smartInput');
    const aiModel = document.getElementById('aiModel');
    const customInstructions = document.getElementById('customInstructions');
    
    const inputValue = smartInput.value.trim();
    const detectedType = detectInputType(inputValue);
    const selectedModel = aiModel.value;
    const customInstructionsValue = customInstructions.value.trim();
    
    // Show loading state
    generateBtn.disabled = true;
    generateBtn.innerHTML = `
        <svg class="animate-spin -ml-1 mr-3 h-4 w-4 text-white inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        Generating...`;
    
    try {
        const response = await fetch('/blocks/generate', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                input: inputValue,
                type: detectedType,
                model: selectedModel,
                custom_instructions: customInstructionsValue
            })
        });
        
        const data = await response.json();
        
        if (data.success) {
            // Update editors with generated content
            updateEditorsWithContent(data.data);
            
            // Switch to template tab
            document.getElementById('template-tab').click();
            
            alert('Block generated successfully! Check the Template and Content tabs.');
        } else {
            alert('Error generating block: ' + data.error);
        }
    } catch (error) {
        console.error('Error:', error);
        alert('Failed to generate block. Please try again.');
    } finally {
        // Reset button
        generateBtn.disabled = false;
        generateBtn.innerHTML = `
            <svg class="w-4 h-4 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M12 8V4H8"/>
                <rect width="16" height="12" x="4" y="8" rx="2"/>
                <path d="M2 14h2"/>
                <path d="M20 14h2"/>
                <path d="M15 13v2"/>
                <path d="M9 13v2"/>
            </svg>
            Generate Block`;
    }
}

// Update editors with content
function updateEditorsWithContent(data) {
    if (data.template && editors.template) {
        editors.template.setValue(data.template);
    }
    
    if (data.json && editors.content) {
        const jsonString = typeof data.json === 'string' 
            ? data.json 
            : JSON.stringify(data.json, null, 2);
        editors.content.setValue(jsonString);
    }
    
    if (data.css && editors.css) {
        editors.css.setValue(data.css);
    }
    
    if (data.js && editors.javascript) {
        editors.javascript.setValue(data.js);
    }
}

// Detect input type
function detectInputType(input) {
    if (!input) return 'empty';
    
    if (input.includes('<') && input.includes('>')) {
        return 'html';
    }
    
    const imageExtensions = /\.(jpg|jpeg|png|gif|svg|webp)(\?.*)?$/i;
    if (imageExtensions.test(input) || input.startsWith('data:image/')) {
        return 'image';
    }
    
    return 'message';
}

// Update input type indicator
function updateInputTypeIndicator(type) {
    const typeLabels = {
        'html': '📝 HTML Code',
        'image': '🖼️ Image URL',
        'message': '💬 Instruction',
        'empty': 'Empty'
    };
    
    const indicator = document.getElementById('inputTypeIndicator');
    const detectedType = document.getElementById('detectedType');
    
    if (indicator && detectedType) {
        detectedType.textContent = typeLabels[type] || type;
        
        if (type !== 'empty') {
            indicator.classList.remove('hidden');
        } else {
            indicator.classList.add('hidden');
        }
    }
}

// Initialize tab functionality
function initializeEditorTabs() {
    const tabs = document.querySelectorAll('[role="tab"]');
    const tabPanels = document.querySelectorAll('[role="tabpanel"]');

    tabs.forEach(tab => {
        tab.addEventListener('click', function() {
            const targetId = this.getAttribute('data-editor-target');
            if (!targetId) return;

            const targetPanelId = targetId.substring(1); // Remove # from target

            // Update tab states
            tabs.forEach(t => {
                const isSelected = t === this;
                if (isSelected) {
                    t.className = 'inline-flex items-center gap-1.5 px-3 py-1.5 text-sm font-medium rounded-full bg-blue-50 text-blue-600';
                    t.setAttribute('aria-selected', 'true');
                } else {
                    t.className = 'inline-flex items-center gap-1.5 px-3 py-1.5 text-sm font-medium rounded-full text-gray-500 hover:bg-gray-100';
                    t.setAttribute('aria-selected', 'false');
                }
            });

            // Show selected panel and hide others
            tabPanels.forEach(panel => {
                if (panel.id === targetPanelId) {
                    panel.classList.remove('hidden');
                } else {
                    panel.classList.add('hidden');
                }
            });

            // Refresh editor if exists and give it time to render
            const editor = editors[targetPanelId];
            if (editor) {
                setTimeout(() => {
                    editor.refresh();
                }, 100);
            }

            // Handle preview tab
            if (targetPanelId === 'preview') {
                setTimeout(() => updatePreview(), 100);
                // Show theme controls for preview
                showPreviewControls();
                // Initialize responsive controls
                setTimeout(() => setupResponsiveControls(), 200);
            } else {
                // Hide theme controls for other tabs
                hidePreviewControls();
            }

            // Update editor header text based on active tab
            updateEditorHeaderText(this.id);
        });
    });

    // Activate first tab by default
    if (tabs.length > 0) {
        setTimeout(() => {
            tabs[0].click();
        }, 200);
    }
}

// Update preview
function updatePreview() {
    const templateCode = editors.template ? editors.template.getValue() : '';
    const jsonCode = editors.content ? editors.content.getValue() : '{}';
    const cssCode = editors.css ? editors.css.getValue() : '';
    const jsCode = editors.javascript ? editors.javascript.getValue() : '';
    const dependencies = JSON.parse(document.getElementById('blockDependencies').value || '{"js":[],"css":[]}');

    // Show loading state
    const previewContent = document.getElementById('previewContent');
    const previewFrame = document.getElementById('previewFrame');

    // Create loading indicator
    previewContent.insertAdjacentHTML('beforeend',
        '<div id="previewLoader" class="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center">' +
        '<div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>' +
        '</div>'
    );

    // Process template using FormData (like the old code)
    const formData = new FormData();
    formData.append('template', templateCode);
    formData.append('json_data', jsonCode);

    fetch('/blocks/process-template', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        const iframeDoc = previewFrame.contentDocument || previewFrame.contentWindow.document;

        if (data.success) {
            // Create CSS links
            const cssLinks = dependencies.css.map(url =>
                `<link rel="stylesheet" href="${url}">`
            ).join('\n');

            // Create JS scripts
            const jsScripts = dependencies.js.map(url =>
                `<script src="${url}"><\/script>`
            ).join('\n');

            const htmlContent = `
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset="UTF-8">
                    <meta name="viewport" content="width=device-width, initial-scale=1.0">
                    <script src="https://cdn.tailwindcss.com"><\/script>
                     <script>
                        // tailwind.config.js
                        tailwind.config = {
                            darkMode: 'class',
                            theme: {
                                extend: {
                                    colors: {
                                        primary: {
                                            50: 'var(--primary-50)',
                                            100: 'var(--primary-100)',
                                            200: 'var(--primary-200)',
                                            300: 'var(--primary-300)',
                                            400: 'var(--primary-400)',
                                            500: 'var(--primary-500)',
                                            600: 'var(--primary-600)',
                                            700: 'var(--primary-700)',
                                            800: 'var(--primary-800)',
                                            900: 'var(--primary-900)',
                                            950: 'var(--primary-950)',
                                            DEFAULT: 'var(--primary-500)',
                                            dark: 'var(--primary-500)'
                                        },
                                        secondary: {
                                            50: 'var(--secondary-50)',
                                            100: 'var(--secondary-100)',
                                            200: 'var(--secondary-200)',
                                            300: 'var(--secondary-300)',
                                            400: 'var(--secondary-400)',
                                            500: 'var(--secondary-500)',
                                            600: 'var(--secondary-600)',
                                            700: 'var(--secondary-700)',
                                            800: 'var(--secondary-800)',
                                            900: 'var(--secondary-900)',
                                            950: 'var(--secondary-950)',
                                            DEFAULT: 'var(--secondary-500)',
                                            dark: 'var(--secondary-500)'
                                        },
                                        tertiary: {
                                            50: 'var(--tertiary-50)',
                                            100: 'var(--tertiary-100)',
                                            200: 'var(--tertiary-200)',
                                            300: 'var(--tertiary-300)',
                                            400: 'var(--tertiary-400)',
                                            500: 'var(--tertiary-500)',
                                            600: 'var(--tertiary-600)',
                                            700: 'var(--tertiary-700)',
                                            800: 'var(--tertiary-800)',
                                            900: 'var(--tertiary-900)',
                                            950: 'var(--tertiary-950)',
                                            DEFAULT: 'var(--tertiary-500)',
                                            dark: 'var(--tertiary-500)'
                                        },
                                        quaternary: {
                                            50: 'var(--quaternary-50)',
                                            100: 'var(--quaternary-100)',
                                            200: 'var(--quaternary-200)',
                                            300: 'var(--quaternary-300)',
                                            400: 'var(--quaternary-400)',
                                            500: 'var(--quaternary-500)',
                                            600: 'var(--quaternary-600)',
                                            700: 'var(--quaternary-700)',
                                            800: 'var(--quaternary-800)',
                                            900: 'var(--quaternary-900)',
                                            950: 'var(--quaternary-950)',
                                            DEFAULT: 'var(--quaternary-500)',
                                            dark: 'var(--quaternary-500)'
                                        },
                                        quinary: {
                                            50: 'var(--quinary-50)',
                                            100: 'var(--quinary-100)',
                                            200: 'var(--quinary-200)',
                                            300: 'var(--quinary-300)',
                                            400: 'var(--quinary-400)',
                                            500: 'var(--quinary-500)',
                                            600: 'var(--quinary-600)',
                                            700: 'var(--quinary-700)',
                                            800: 'var(--quinary-800)',
                                            900: 'var(--quinary-900)',
                                            950: 'var(--quinary-950)',
                                            DEFAULT: 'var(--quinary-500)',
                                            dark: 'var(--quinary-500)'
                                        }
                                    }
                                }
                            }
                        }
                    <\/script>
                    ${cssLinks}
                    <style>
                        /* Reset iframe specific styles */
                        body { margin: 0; padding: 0; }
                        :root[data-theme="theme1"]{
                            --primary-50: #ECEAF1;
                            --primary-100: #D9D4E3;
                            --primary-200: #B2A9C6;
                            --primary-300: #8C7EAA;
                            --primary-400: #685987;
                            --primary-500: #473D5C;
                            --primary-600: #39314A;
                            --primary-700: #2A2537;
                            --primary-800: #1C1825;
                            --primary-900: #0E0C12;
                            --primary-950: #070609;

                            --secondary-50: #F9F9F6;
                            --secondary-100: #F3F2ED;
                            --secondary-200: #E5E3D7;
                            --secondary-300: #D8D7C5;
                            --secondary-400: #CAC8AF;
                            --secondary-500: #BEBB9D;
                            --secondary-600: #A19D72;
                            --secondary-700: #7E7A53;
                            --secondary-800: #535037;
                            --secondary-900: #2B2A1C;
                            --secondary-950: #15150E;

                            --tertiary-50: #F1F5F0;
                            --tertiary-100: #DFE9DD;
                            --tertiary-200: #BFD2BC;
                            --tertiary-300: #A3BE9D;
                            --tertiary-400: #83A77B;
                            --tertiary-500: #668D5E;
                            --tertiary-600: #52714B;
                            --tertiary-700: #3E5639;
                            --tertiary-800: #283725;
                            --tertiary-900: #141C12;
                            --tertiary-950: #0B0F0A;

                            --quaternary-50: #FFFFFF;
                            --quaternary-100: #FCFCFD;
                            --quaternary-200: #F9F9FB;
                            --quaternary-300: #F6F6F9;
                            --quaternary-400: #F4F3F7;
                            --quaternary-500: #F1F0F5;
                            --quaternary-600: #BAB6CE;
                            --quaternary-700: #847BA7;
                            --quaternary-800: #554E74;
                            --quaternary-900: #2B273A;
                            --quaternary-950: #16141F;

                            --quinary-50: #E2E0EB;
                            --quinary-100: #C9C5D8;
                            --quinary-200: #928BB1;
                            --quinary-300: #605884;
                            --quinary-400: #363149;
                            --quinary-500: #0B0A0F;
                            --quinary-600: #09080C;
                            --quinary-700: #070609;
                            --quinary-800: #040406;
                            --quinary-900: #020203;
                            --quinary-950: #000000;
                        }
                        .dark[data-theme="theme1"]  {
                            --primary-50: #F7F6F9;
                            --primary-100: #EFEDF3;
                            --primary-200: #DEDAE7;
                            --primary-300: #CEC8DA;
                            --primary-400: #BDB5CE;
                            --primary-500: #ADA3C2;
                            --primary-600: #8678A6;
                            --primary-700: #635581;
                            --primary-800: #423956;
                            --primary-900: #211C2B;
                            --primary-950: #110E15;

                            --secondary-50: #F1F0EA;
                            --secondary-100: #E3E1D4;
                            --secondary-200: #C8C6AC;
                            --secondary-300: #ACA881;
                            --secondary-400: #8D895E;
                            --secondary-500: #625F41;
                            --secondary-600: #504D35;
                            --secondary-700: #3A3927;
                            --secondary-800: #28271A;
                            --secondary-900: #12120C;
                            --secondary-950: #090906;

                            --tertiary-50: #F1F5F0;
                            --tertiary-100: #E5EDE3;
                            --tertiary-200: #CBDAC8;
                            --tertiary-300: #AEC6A9;
                            --tertiary-400: #94B48E;
                            --tertiary-500: #7AA172;
                            --tertiary-600: #5F8458;
                            --tertiary-700: #476241;
                            --tertiary-800: #31432D;
                            --tertiary-900: #182216;
                            --tertiary-950: #0B0F0A;

                            --quaternary-50: #E2E0EB;
                            --quaternary-100: #C9C5D8;
                            --quaternary-200: #928BB1;
                            --quaternary-300: #605884;
                            --quaternary-400: #363149;
                            --quaternary-500: #0B0A0F;
                            --quaternary-600: #09080C;
                            --quaternary-700: #070609;
                            --quaternary-800: #040406;
                            --quaternary-900: #020203;
                            --quaternary-950: #000000;

                            --quinary-50: #FFFFFF;
                            --quinary-100: #FCFCFD;
                            --quinary-200: #F9F9FB;
                            --quinary-300: #F6F6F9;
                            --quinary-400: #F4F3F7;
                            --quinary-500: #F1F0F5;
                            --quinary-600: #BAB6CE;
                            --quinary-700: #847BA7;
                            --quinary-800: #554E74;
                            --quinary-900: #2B273A;
                            --quinary-950: #16141F;
                        }

                        :root[data-theme="theme2"]  {
                            --primary-50: #E9F1EB;
                            --primary-100: #D4E3D6;
                            --primary-200: #A8C7AE;
                            --primary-300: #7AA982;
                            --primary-400: #56855F;
                            --primary-500: #3A5A40;
                            --primary-600: #2E4733;
                            --primary-700: #223525;
                            --primary-800: #18251A;
                            --primary-900: #0C130D;
                            --primary-950: #060907;

                            --secondary-50: #F6F8F9;
                            --secondary-100: #EDF1F2;
                            --secondary-200: #DEE6E7;
                            --secondary-300: #CDD8DB;
                            --secondary-400: #BECDD0;
                            --secondary-500: #ACBFC3;
                            --secondary-600: #839FA5;
                            --secondary-700: #5C797F;
                            --secondary-800: #3E5256;
                            --secondary-900: #1E2829;
                            --secondary-950: #0F1415;

                            --tertiary-50: #F3F5F6;
                            --tertiary-100: #EAEDF0;
                            --tertiary-200: #D6DCE1;
                            --tertiary-300: #C1CAD2;
                            --tertiary-400: #ACB9C3;
                            --tertiary-500: #97A7B4;
                            --tertiary-600: #718798;
                            --tertiary-700: #536574;
                            --tertiary-800: #38434D;
                            --tertiary-900: #1C2227;
                            --tertiary-950: #0D1012;

                            --quaternary-50: #FFFFFF;
                            --quaternary-100: #FFFFFF;
                            --quaternary-200: #FCFDFC;
                            --quaternary-300: #FCFDFC;
                            --quaternary-400: #F9FBF9;
                            --quaternary-500: #F9FBF9;
                            --quaternary-600: #BCD2BC;
                            --quaternary-700: #82AB82;
                            --quaternary-800: #507750;
                            --quaternary-900: #293D29;
                            --quaternary-950: #141F14;

                            --quinary-50: #E2E9E2;
                            --quinary-100: #C8D5C8;
                            --quinary-200: #8FA88F;
                            --quinary-300: #5E785E;
                            --quinary-400: #313F31;
                            --quinary-500: #070907;
                            --quinary-600: #040604;
                            --quinary-700: #040604;
                            --quinary-800: #020302;
                            --quinary-900: #020302;
                            --quinary-950: #000000;
                        }
                        .dark[data-theme="theme2"]  {
                            --primary-50: #F6F9F6;
                            --primary-100: #ECF3EE;
                            --primary-200: #DAE7DC;
                            --primary-300: #CADDCE;
                            --primary-400: #B8D1BC;
                            --primary-500: #A5C5AB;
                            --primary-600: #7AA982;
                            --primary-700: #56855F;
                            --primary-800: #38573E;
                            --primary-900: #1C2B1F;
                            --primary-950: #0E160F;

                            --secondary-50: #EAEFF0;
                            --secondary-100: #D6DFE1;
                            --secondary-200: #A9BDC1;
                            --secondary-300: #809DA3;
                            --secondary-400: #5A777C;
                            --secondary-500: #3C4F53;
                            --secondary-600: #2F3E41;
                            --secondary-700: #243032;
                            --secondary-800: #181F21;
                            --secondary-900: #0D1112;
                            --secondary-950: #060809;

                            --tertiary-50: #EDF0F2;
                            --tertiary-100: #D8DEE3;
                            --tertiary-200: #B2BEC7;
                            --tertiary-300: #8B9DAC;
                            --tertiary-400: #677C8E;
                            --tertiary-500: #4B5B68;
                            --tertiary-600: #3C4953;
                            --tertiary-700: #2D363E;
                            --tertiary-800: #1E2429;
                            --tertiary-900: #0F1215;
                            --tertiary-950: #090A0C;

                            --quaternary-50: #E0EBE0;
                            --quaternary-100: #C2D6C2;
                            --quaternary-200: #88AF88;
                            --quaternary-300: #547D54;
                            --quaternary-400: #2D432D;
                            --quaternary-500: #040604;
                            --quaternary-600: #040604;
                            --quaternary-700: #020302;
                            --quaternary-800: #020302;
                            --quaternary-900: #000000;
                            --quaternary-950: #000000;

                            --quinary-50: #FFFFFF;
                            --quinary-100: #FCFDFC;
                            --quinary-200: #FCFDFC;
                            --quinary-300: #F9FBF9;
                            --quinary-400: #F9FBF9;
                            --quinary-500: #F6F8F6;
                            --quinary-600: #C0CEC0;
                            --quinary-700: #87A187;
                            --quinary-800: #577057;
                            --quinary-900: #2A372A;
                            --quinary-950: #161D16;
                        }

                        :root[data-theme="theme3"]  {
                            --primary-50: #E4EDF6;
                            --primary-100: #C9DAED;
                            --primary-200: #93B5DC;
                            --primary-300: #6293CB;
                            --primary-400: #396FAD;
                            --primary-500: #274C77;
                            --primary-600: #1F3E60;
                            --primary-700: #182F49;
                            --primary-800: #0F1E2E;
                            --primary-900: #080F17;
                            --primary-950: #04070C;

                            --secondary-50: #EDF3F7;
                            --secondary-100: #DFEAF1;
                            --secondary-200: #BFD5E3;
                            --secondary-300: #9FC0D5;
                            --secondary-400: #7FAAC7;
                            --secondary-500: #5F95B9;
                            --secondary-600: #44799C;
                            --secondary-700: #335B75;
                            --secondary-800: #223C4E;
                            --secondary-900: #111E27;
                            --secondary-950: #080E12;

                            --tertiary-50: #F8F3F2;
                            --tertiary-100: #F1E7E4;
                            --tertiary-200: #E3CFCA;
                            --tertiary-300: #D6BAB2;
                            --tertiary-400: #C8A298;
                            --tertiary-500: #BA8A7D;
                            --tertiary-600: #A36757;
                            --tertiary-700: #7B4E41;
                            --tertiary-800: #50332A;
                            --tertiary-900: #281915;
                            --tertiary-950: #140D0B;

                            --quaternary-50: #FFFFFF;
                            --quaternary-100: #FFFFFF;
                            --quaternary-200: #FCFCFD;
                            --quaternary-300: #FCFCFD;
                            --quaternary-400: #F9FAFB;
                            --quaternary-500: #F9FAFB;
                            --quaternary-600: #BCC7D2;
                            --quaternary-700: #8296AB;
                            --quaternary-800: #506377;
                            --quaternary-900: #29333D;
                            --quaternary-950: #141A1F;

                            --quinary-50: #E1E8EF;
                            --quinary-100: #C0CFDD;
                            --quinary-200: #809EBC;
                            --quinary-300: #4C6E8F;
                            --quinary-400: #2A3D50;
                            --quinary-500: #090D11;
                            --quinary-600: #070A0D;
                            --quinary-700: #05080A;
                            --quinary-800: #040507;
                            --quinary-900: #020303;
                            --quinary-950: #020303;

                        }
                        .dark[data-theme="theme3"]  {
                            --primary-50: #F3F7FB;
                            --primary-100: #E8EFF7;
                            --primary-200: #D1DFF0;
                            --primary-300: #B6CDE7;
                            --primary-400: #9FBDE0;
                            --primary-500: #88ADD8;
                            --primary-600: #5288C6;
                            --primary-700: #34659D;
                            --primary-800: #23456C;
                            --primary-900: #122236;
                            --primary-950: #09111B;

                            --secondary-50: #EDF3F7;
                            --secondary-100: #D8E5EE;
                            --secondary-200: #B1CBDD;
                            --secondary-300: #8AB1CC;
                            --secondary-400: #6398BB;
                            --secondary-500: #467CA0;
                            --secondary-600: #386380;
                            --secondary-700: #2A4A60;
                            --secondary-800: #1C3140;
                            --secondary-900: #0E1920;
                            --secondary-950: #080E12;

                            --tertiary-50: #F4EDEB;
                            --tertiary-100: #EADBD7;
                            --tertiary-200: #D5B7AF;
                            --tertiary-300: #BE9084;
                            --tertiary-400: #A86C5C;
                            --tertiary-500: #825245;
                            --tertiary-600: #674137;
                            --tertiary-700: #4D3029;
                            --tertiary-800: #35221C;
                            --tertiary-900: #1B110E;
                            --tertiary-950: #0D0807;

                            --quaternary-50: #E0E6EB;
                            --quaternary-100: #C2CCD6;
                            --quaternary-200: #889CAF;
                            --quaternary-300: #54697D;
                            --quaternary-400: #2D3843;
                            --quaternary-500: #040506;
                            --quaternary-600: #040506;
                            --quaternary-700: #020303;
                            --quaternary-800: #020303;
                            --quaternary-900: #000000;
                            --quaternary-950: #000000;

                            --quinary-50: #FCFCFD;
                            --quinary-100: #FCFCFD;
                            --quinary-200: #F8FAFB;
                            --quinary-300: #F5F7FA;
                            --quinary-400: #F2F5F8;
                            --quinary-500: #EEF2F6;
                            --quinary-600: #AFC2D5;
                            --quinary-700: #7091B3;
                            --quinary-800: #43617F;
                            --quinary-900: #22303F;
                            --quinary-950: #10171E;
                        }

                        :root[data-theme="theme4"]  {
                            --primary-50: #FBF1EF;
                            --primary-100: #F7E4DE;
                            --primary-200: #EFC8BE;
                            --primary-300: #E7B0A1;
                            --primary-400: #DF9581;
                            --primary-500: #D77A60;
                            --primary-600: #C85332;
                            --primary-700: #973E26;
                            --primary-800: #622819;
                            --primary-900: #31140C;
                            --primary-950: #180A06;

                            --secondary-50: #FBF6F4;
                            --secondary-100: #F8F0ED;
                            --secondary-200: #F0E1DB;
                            --secondary-300: #E7CFC5;
                            --secondary-400: #E0C0B3;
                            --secondary-500: #D9B2A1;
                            --secondary-600: #C3856A;
                            --secondary-700: #A05D41;
                            --secondary-800: #6D402C;
                            --secondary-900: #362016;
                            --secondary-950: #190F0A;

                            --tertiary-50: #F5F4EA;
                            --tertiary-100: #EBEAD5;
                            --tertiary-200: #D8D5AC;
                            --tertiary-300: #C6C286;
                            --tertiary-400: #B2AC5C;
                            --tertiary-500: #8E8943;
                            --tertiary-600: #726E36;
                            --tertiary-700: #575429;
                            --tertiary-800: #37351A;
                            --tertiary-900: #1C1B0D;
                            --tertiary-950: #0E0D07;

                            --quaternary-50: #FFFFFF;
                            --quaternary-100: #FCFCFC;
                            --quaternary-200: #FAFAFA;
                            --quaternary-300: #FAFAFA;
                            --quaternary-400: #F7F7F7;
                            --quaternary-500: #F5F5F5;
                            --quaternary-600: #C4C4C4;
                            --quaternary-700: #949494;
                            --quaternary-800: #616161;
                            --quaternary-900: #303030;
                            --quaternary-950: #1A1A1A;

                            --quinary-50: #EFDEDC;
                            --quinary-100: #E0C1BD;
                            --quinary-200: #C1837B;
                            --quinary-300: #954F46;
                            --quinary-400: #532C27;
                            --quinary-500: #110908;
                            --quinary-600: #0E0707;
                            --quinary-700: #0A0605;
                            --quinary-800: #070403;
                            --quinary-900: #030202;
                            --quinary-950: #000000;

                        }
                        .dark[data-theme="theme4"]  {
                            --primary-50: #F9EBE7;
                            --primary-100: #F3D6CE;
                            --primary-200: #E6AD9D;
                            --primary-300: #D98168;
                            --primary-400: #CD5837;
                            --primary-500: #9F4228;
                            --primary-600: #7E3420;
                            --primary-700: #5E2718;
                            --primary-800: #411B10;
                            --primary-900: #210D08;
                            --primary-950: #100704;

                            --secondary-50: #FBF6F4;
                            --secondary-100: #F8F0ED;
                            --secondary-200: #F0E1DB;
                            --secondary-300: #E7CFC5;
                            --secondary-400: #E0C0B3;
                            --secondary-500: #D9B2A1;
                            --secondary-600: #C3856A;
                            --secondary-700: #A05D41;
                            --secondary-800: #6D402C;
                            --secondary-900: #362016;
                            --secondary-950: #190F0A;

                            --tertiary-50: #F8F8F1;
                            --tertiary-100: #F2F1E3;
                            --tertiary-200: #E5E3C8;
                            --tertiary-300: #D6D3A8;
                            --tertiary-400: #C9C58D;
                            --tertiary-500: #BCB771;
                            --tertiary-600: #A39D4D;
                            --tertiary-700: #797539;
                            --tertiary-800: #535027;
                            --tertiary-900: #2A2814;
                            --tertiary-950: #15140A;

                            --quaternary-50: #E6E6E6;
                            --quaternary-100: #CFCFCF;
                            --quaternary-200: #9E9E9E;
                            --quaternary-300: #6B6B6B;
                            --quaternary-400: #3B3B3B;
                            --quaternary-500: #0A0A0A;
                            --quaternary-600: #080808;
                            --quaternary-700: #050505;
                            --quaternary-800: #050505;
                            --quaternary-900: #030303;
                            --quaternary-950: #000000;

                            --quinary-50: #FFFFFF;
                            --quinary-100: #FDFCFC;
                            --quinary-200: #FCF8F8;
                            --quinary-300: #FAF5F5;
                            --quinary-400: #F8F2F1;
                            --quinary-500: #F7EFEE;
                            --quinary-600: #D8B1AC;
                            --quinary-700: #B9736A;
                            --quinary-800: #84463E;
                            --quinary-900: #42231F;
                            --quinary-950: #231210;

                        }
                        ${cssCode}
                    </style>
                </head>
                <body>
                    ${data.html}
                    ${jsScripts}
                    <script>${jsCode}<\/script>
                </body>
                </html>`;

            iframeDoc.open();
            iframeDoc.write(htmlContent);
            iframeDoc.close();
        } else {
            const errorContent = `
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset="UTF-8">
                    <style>
                        body {
                            font-family: system-ui, -apple-system, sans-serif;
                            padding: 1rem;
                            margin: 0;
                        }
                        .error-box {
                            padding: 1rem;
                            background-color: #FEF2F2;
                            color: #DC2626;
                            border: 1px solid #FEE2E2;
                            border-radius: 0.5rem;
                        }
                        .error-title { font-weight: 500; margin: 0; }
                        .error-message { font-size: 0.875rem; margin-top: 0.25rem; }
                    </style>
                </head>
                <body>
                    <div class="error-box">
                        <p class="error-title">Template Error</p>
                        <p class="error-message">${data.message || 'Unknown error occurred'}</p>
                    </div>
                </body>
                </html>`;

            iframeDoc.open();
            iframeDoc.write(errorContent);
            iframeDoc.close();
        }
    })
    .catch(error => {
        console.error('Error processing template:', error);
        const iframeDoc = previewFrame.contentDocument || previewFrame.contentWindow.document;

        const errorContent = `
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <style>
                    body {
                        font-family: system-ui, -apple-system, sans-serif;
                        padding: 1rem;
                        margin: 0;
                    }
                    .error-box {
                        padding: 1rem;
                        background-color: #FEF2F2;
                        color: #DC2626;
                        border: 1px solid #FEE2E2;
                        border-radius: 0.5rem;
                    }
                    .error-title { font-weight: 500; margin: 0; }
                    .error-message { font-size: 0.875rem; margin-top: 0.25rem; }
                </style>
            </head>
            <body>
                <div class="error-box">
                    <p class="error-title">Error</p>
                    <p class="error-message">Failed to process template. Please try again.</p>
                </div>
            </body>
            </html>`;

        iframeDoc.open();
        iframeDoc.write(errorContent);
        iframeDoc.close();
    })
    .finally(() => {
        // Remove loading indicator
        document.getElementById('previewLoader')?.remove();
    });
}

// Update editor header text based on active tab
function updateEditorHeaderText(tabId) {
    const editorHeaderText = document.getElementById('editorHeaderText');
    if (!editorHeaderText) return;

    const headerTexts = {
        'template-tab': 'HTML Editor',
        'content-tab': 'JSON Editor',
        'css-tab': 'CSS Editor',
        'javascript-tab': 'JavaScript Editor',
        'dependencies-tab': 'Dependencies Manager',
        'preview-tab': 'Preview',
        'aiassistant-tab': 'AI Assistant'
    };

    editorHeaderText.textContent = headerTexts[tabId] || 'Editor';
}

// Show preview controls when preview tab is active
function showPreviewControls() {
    const previewControls = [
        document.getElementById('themeToggle'),
        document.getElementById('resetPreview'),
        document.getElementById('darkModeToggle'),
        document.getElementById('desktopBtn'),
        document.getElementById('tabletBtn'),
        document.getElementById('mobileBtn')
    ];

    previewControls.forEach(control => {
        if (control) control.classList.remove('hidden');
    });
}

// Hide preview controls when other tabs are active
function hidePreviewControls() {
    const previewControls = [
        document.getElementById('themeToggle'),
        document.getElementById('resetPreview'),
        document.getElementById('darkModeToggle'),
        document.getElementById('desktopBtn'),
        document.getElementById('tabletBtn'),
        document.getElementById('mobileBtn')
    ];

    previewControls.forEach(control => {
        if (control) control.classList.add('hidden');
    });
}

// Initialize theme controls event listeners
function initializeThemeControls() {
    // Theme buttons
    document.querySelectorAll('.theme-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const theme = this.getAttribute('data-theme');
            setThemeInIframe(theme);
        });
    });

    // Dark mode toggle
    const darkModeToggle = document.getElementById('darkModeToggle');
    if (darkModeToggle) {
        darkModeToggle.addEventListener('click', toggleDarkModeInIframe);
    }
}

// Function to set theme in the iframe
function setThemeInIframe(themeName) {
    const iframe = document.getElementById('previewFrame');
    if (iframe && iframe.contentDocument) {
        const iframeDocument = iframe.contentDocument;
        iframeDocument.documentElement.setAttribute('data-theme', themeName);

        // If the iframe has local storage, also save the theme there
        try {
            iframe.contentWindow.localStorage.setItem('theme', themeName);
        } catch (e) {
            console.log('Could not save theme to iframe localStorage:', e);
        }
    }
}

// Function to toggle dark mode in the iframe
function toggleDarkModeInIframe() {
    const iframe = document.getElementById('previewFrame');

    if (iframe && iframe.contentDocument) {
        const iframeDocument = iframe.contentDocument;
        const isDark = iframeDocument.documentElement.classList.toggle('dark');

        // If the iframe has local storage, also save the preference there
        try {
            iframe.contentWindow.localStorage.setItem('darkMode', isDark);
        } catch (e) {
            console.log('Could not save dark mode to iframe localStorage:', e);
        }
    }
}

// Setup responsive controls for preview
function setupResponsiveControls() {
    const previewFrame = document.getElementById('previewFrame');
    const desktopBtn = document.getElementById('desktopBtn');
    const tabletBtn = document.getElementById('tabletBtn');
    const mobileBtn = document.getElementById('mobileBtn');

    if (!previewFrame || !desktopBtn || !tabletBtn || !mobileBtn) return;

    // Set active button function
    function setActiveButton(activeBtn) {
        [desktopBtn, tabletBtn, mobileBtn].forEach(btn => {
            btn.classList.remove('active');
        });
        activeBtn.classList.add('active');
    }

    // Desktop view
    desktopBtn.addEventListener('click', function() {
        previewFrame.style.width = '100%';
        previewFrame.style.margin = '0';
        setActiveButton(desktopBtn);
    });

    // Tablet view
    tabletBtn.addEventListener('click', function() {
        previewFrame.style.width = '768px';
        previewFrame.style.margin = '0 auto';
        setActiveButton(tabletBtn);
    });

    // Mobile view
    mobileBtn.addEventListener('click', function() {
        previewFrame.style.width = '375px';
        previewFrame.style.margin = '0 auto';
        setActiveButton(mobileBtn);
    });

    // Set desktop as default
    setActiveButton(desktopBtn);
}

// Initialize Flowbite
function initializeFlowbite() {
    if (typeof initFlowbite === 'function') {
        initFlowbite();
    }
}

// Helper function to generate slug
function generateSlug(text) {
    return text
        .toLowerCase()
        .trim()
        .replace(/[^\w\s-]/g, '') // Remove special characters
        .replace(/[\s_-]+/g, '-') // Replace spaces, underscores, hyphens with single hyphen
        .replace(/^-+|-+$/g, ''); // Remove leading/trailing hyphens
}

// Fullscreen functionality
document.getElementById('editorFullscreenBtn')?.addEventListener('click', function() {
    const editorContent = document.querySelector('.editor-content');
    const leftColumn = document.querySelector('.leftColumnfullscreen');
    
    if (!document.fullscreenElement) {
        leftColumn.requestFullscreen().then(() => {
            editorContent.classList.add('fullscreen-mode');
            this.innerHTML = `
                <svg class="w-4 h-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M9 9 3 3m0 0v6m0-6h6"/>
                    <path d="M15 9l6-6m0 0v6m0-6h-6"/>
                    <path d="M9 15l-6 6m0 0v-6m0 6h6"/>
                    <path d="M15 15l6 6m0 0v-6m0 6h-6"/>
                </svg>`;
        });
    } else {
        document.exitFullscreen().then(() => {
            editorContent.classList.remove('fullscreen-mode');
            this.innerHTML = `
                <svg class="w-4 h-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="m15 15 6 6"/>
                    <path d="m15 9 6-6"/>
                    <path d="M21 16.2V21h-4.8"/>
                    <path d="M21 7.8V3h-4.8"/>
                    <path d="M3 16.2V21h4.8"/>
                    <path d="m3 21 6-6"/>
                    <path d="M3 7.8V3h4.8"/>
                    <path d="M9 9 3 3"/>
                </svg>`;
        });
    }
});

// Add fullscreen styles
const style = document.createElement('style');
style.textContent = `
    .fullscreen-mode {
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        width: 100vw !important;
        height: 100vh !important;
        z-index: 9999 !important;
        background: white !important;
        padding: 20px !important;
    }
    
    .fullscreen-mode .tab-content {
        height: calc(100vh - 120px) !important;
    }
    
    .fullscreen-mode .CodeMirror {
        height: calc(100vh - 200px) !important;
    }
`;
document.head.appendChild(style);