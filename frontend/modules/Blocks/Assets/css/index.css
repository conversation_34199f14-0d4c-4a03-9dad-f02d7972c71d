/* Blocks Module Custom Styles */

/* Category Card Styles */
.category-card {
    transition: all 0.3s ease;
}

.category-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

/* Block Card Styles */
.block-card {
    transition: all 0.3s ease;
}

.block-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

/* Image Placeholder Styles */
.image-placeholder {
    background-color: #f3f4f6;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
}

.image-container {
    overflow: hidden;
    background-color: #f3f4f6;
}

.image-container img {
    transition: transform 0.3s ease;
}

.image-container:hover img {
    transform: scale(1.05);
}

/* Form Styles */
.select2-container--default .select2-selection--multiple {
    border: 1px solid #d1d5db;
    border-radius: 0.5rem;
    min-height: 42px;
    padding: 0.25rem;
}

.select2-container--default.select2-container--focus .select2-selection--multiple {
    border-color: #0C5BE2;
    box-shadow: 0 0 0 1px #0C5BE2;
}

/* Step Navigation Styles */
.step-item::after {
    content: '';
    position: absolute;
    left: 1rem;
    top: 2.5rem;
    bottom: -2rem;
    width: 2px;
    background-color: #e5e7eb;
}

.step-item:last-child::after {
    display: none;
}

/* Editor Styles */
.CodeMirror {
    height: 400px;
    font-size: 14px;
}

.editor-tab-content {
    min-height: 500px;
}

/* Tag Input Styles */
.tag-input-container {
    position: relative;
}

.tag-suggestions {
    max-height: 200px;
    overflow-y: auto;
}

/* Modal Styles */
.modal-backdrop {
    background-color: rgba(0, 0, 0, 0.5);
}

/* Responsive Styles */
@media (max-width: 768px) {
    .leftColumnfullscreen {
        width: 100% !important;
    }
    
    .grid-cols-12 > .col-span-7 {
        grid-column: span 12 / span 12;
    }
    
    .grid-cols-12 > .col-span-5 {
        grid-column: span 12 / span 12;
    }
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-radius: 50%;
    border-top: 2px solid #0C5BE2;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Preview Frame Styles */
#previewFrame {
    min-height: 500px;
    background: #fff;
}

/* Dependency Row Styles */
#jsDependencies input,
#cssDependencies input {
    font-family: monospace;
    font-size: 13px;
}

/* AI Assistant Styles */
#smartInput {
    font-family: monospace;
}

#inputTypeIndicator {
    transition: all 0.2s ease;
}

/* Block Count Badge */
.block_count span {
    min-width: 24px;
    text-align: center;
}

/* Cell Styles */
.cell_imagewrapper {
    width: 120px;
}

.cell_id__name {
    min-width: 200px;
}

.cell_slug {
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.cell_description {
    max-width: 300px;
}

.cell_created_at,
.cell_updated_at {
    width: 150px;
}