<?php
    add_style('https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/codemirror.min.css');
    add_style('https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/theme/monokai.min.css');
    add_script('https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/codemirror.min.js');
    add_script('https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/mode/xml/xml.min.js');
    add_script('https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/mode/javascript/javascript.min.js');
    add_script('https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/mode/css/css.min.js');
    add_script('https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/mode/htmlmixed/htmlmixed.min.js');
    add_script(asset('modules/Blocks/Assets/js/form.js'));
?>

<div class="flex gap-8 px-6">
    <!-- Left Sidebar - Steps Navigation -->
    <div class="w-[275px] border-r border-gray-200 pt-6 pr-6 flex flex-col justify-between">
        <!-- Steps List -->
        <div class="relative">
            <!-- Step 1 -->
            <div class="step-item relative pb-8">
                <div class="flex items-center">
                    <div class="relative z-40">
                        <div class="absolute -inset-0.5 bg-blue-100 rounded-full"></div>
                        <div class="relative flex items-center justify-center w-8 h-8 bg-blue-600 rounded-full">
                            <span class="text-white text-sm">1</span>
                        </div>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-base font-semibold text-gray-900">Block Configuration</h3>
                        <p class="text-sm text-gray-500">Configure basic block settings</p>
                    </div>
                </div>
                <div class="absolute left-4 top-10 h-full w-0.5 bg-blue-100 -ml-px"></div>
            </div>

            <!-- Step 2 -->
            <div class="step-item relative pb-8">
                <div class="flex items-center">
                    <div class="relative z-40">
                        <div class="absolute -inset-0.5 bg-gray-200 rounded-full"></div>
                        <div class="relative flex items-center justify-center w-8 h-8 bg-gray-100 rounded-full">
                            <span class="text-gray-500 text-sm">2</span>
                        </div>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-base font-semibold text-gray-400">Block Content</h3>
                        <p class="text-sm text-gray-400">Add and customize content</p>
                    </div>
                </div>
            </div>
        </div>
        <div class="flex items-center space-x-3">
            <a href="<?= url('blocks/list?category_id=' . ($selectedCategoryId ?? '')) ?>" class="inline-flex items-center text-[14px] px-2.5 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 transition-all">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                <span>Back to Blocks</span>
            </a>
            <?php if (!$isNewBlock): ?>
            <button data-modal-target="deleteBlockModal" data-modal-toggle="deleteBlockModal" type="button" class="inline-flex items-center text-[14px] px-2.5 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-all">
                <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M3 6h18"/><path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"/><path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"/><line x1="10" x2="10" y1="11" y2="17"/><line x1="14" x2="14" y1="11" y2="17"/></svg>
                <span>Delete</span>
            </button>
            <?php endif; ?>
        </div>
    </div>

    <!-- Right Content Area -->
    <div class="w-[calc(100%-275px)]">
        <form method="POST" action="<?= url($isNewBlock ? 'blocks/create' : 'blocks/update') ?>" class="block-form">
            <input type="hidden" name="action" value="<?= $isNewBlock ? 'add' : 'edit' ?>">
            <?php if (!$isNewBlock && isset($block['id'])): ?>
                <input type="hidden" name="id" value="<?= htmlspecialchars($block['id']) ?>">
            <?php endif; ?>
            <?php if (isset($selectedCategoryId)): ?>
                <input type="hidden" name="category_id" value="<?= htmlspecialchars($selectedCategoryId) ?>">
            <?php endif; ?>
            
            <!-- Step 1 Content: Block Configuration -->
            <div id="step1Content" class="step-content">
                <div class="py-6">
                    <div class="grid grid-cols-12 gap-8">
                        <!-- Left Column - Form Fields -->
                        <div class="col-span-7 space-y-6">
                            <!-- Block Name -->
                            <div class="form-group">
                                <div class="flex justify-between mb-2">
                                    <label for="blockName" class="text-sm font-medium text-gray-800">
                                        Block Name <span class="text-red-500">*</span>
                                    </label>
                                    <span class="text-xs text-gray-500">50 characters max</span>
                                </div>
                                <div class="relative rounded-lg">
                                    <input type="text" id="blockName" name="name"
                                        class="block w-full pl-4 pr-4 py-2.5 text-sm text-gray-900 bg-white border border-gray-300 rounded-lg focus:border-blue-500 focus:ring-1 focus:ring-blue-500" 
                                        placeholder="Enter block name" 
                                        value="<?= htmlspecialchars($block['name'] ?? '') ?>" 
                                        required>
                                </div>
                            </div>
                            
                            <!-- Block Description -->
                            <div class="form-group">
                                <div class="flex justify-between mb-2">
                                    <label for="blockDescription" class="text-sm font-medium text-gray-800">
                                        Block Description
                                    </label>
                                </div>
                                <div class="relative rounded-lg">
                                    <textarea rows="2" id="blockDescription" name="description"
                                        class="block w-full pl-4 pr-4 py-2.5 text-sm text-gray-900 bg-white border border-gray-300 rounded-lg focus:border-blue-500 focus:ring-1 focus:ring-blue-500" 
                                        placeholder="Enter block description"><?= htmlspecialchars($block['description'] ?? '') ?></textarea>
                                </div>
                            </div>
                            
                            <!-- Block Slug -->
                            <div class="form-group">
                                <div class="flex justify-between mb-2">
                                    <label for="blockSlug" class="text-sm font-medium text-gray-800">
                                        Block Slug <span class="text-red-500">*</span>
                                    </label>
                                    <span class="text-xs text-gray-500">Auto-generated</span>
                                </div>
                                <div class="relative rounded-lg">
                                    <input type="text" id="blockSlug" name="slug"
                                        class="block w-full pl-4 pr-4 py-2.5 text-sm text-gray-500 bg-gray-50 border border-gray-300 rounded-lg cursor-not-allowed" 
                                        value="<?= htmlspecialchars($block['slug'] ?? '') ?>"
                                        readonly>
                                </div>
                            </div>

                            <!-- Block Category -->
                            <div class="form-group relative">
                                <label class="text-sm font-medium text-gray-800 mb-2 block">
                                    Block Categories <span class="text-red-500">*</span>
                                </label>
                                <div class="relative flex-1 blockCategoriesWrap">
                                    <select id="blockCategories" name="categories[]" class="select2 w-full text-sm" required multiple>
                                        <?php foreach ($categories as $category): ?>
                                            <option value="<?= htmlspecialchars($category['id'] ?? '') ?>" 
                                                <?php
                                                    // Check if category is selected
                                                    $isSelected = false;

                                                    // For existing blocks, check if category is assigned to the block
                                                    if (!$isNewBlock && isset($block['categories']) && is_array($block['categories'])) {
                                                        foreach ($block['categories'] as $blockCategory) {
                                                            if ($blockCategory['id'] == $category['id']) {
                                                                $isSelected = true;
                                                                break;
                                                            }
                                                        }
                                                    }

                                                    // Also select the category user came from (for both new and edit)
                                                    if (isset($selectedCategoryId) && $category['id'] == $selectedCategoryId) {
                                                        $isSelected = true;
                                                    }

                                                    echo $isSelected ? 'selected' : '';
                                                ?>>
                                                <?= htmlspecialchars($category['name'] ?? '') ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>

                            <!-- Tags Section -->
                            <div class="form-group">
                                <label class="flex items-center text-sm font-medium text-gray-700 mb-1">
                                    Tags
                                </label>
                                <div class="relative">
                                    <div class="flex flex-wrap p-1 bg-white border border-gray-300 rounded-lg focus-within:border-[#0C5BE2] focus-within:ring-1">
                                        <div id="tags-container" class="flex flex-wrap gap-1 w-full">
                                            <?php if (!$isNewBlock && isset($block['tags']) && is_array($block['tags'])): ?>
                                                <?php foreach ($block['tags'] as $tag): ?>
                                                    <span class="tag-item inline-flex items-center px-2 py-1 rounded bg-blue-50 text-blue-700 text-sm">
                                                        <?= htmlspecialchars($tag['tag'] ?? '') ?>
                                                        <input type="hidden" name="tags[]" value="<?= htmlspecialchars($tag['id'] ?? '') ?>">
                                                        <button type="button" class="tag-remove-btn ml-1 text-blue-500 hover:text-red-500" onclick="this.parentElement.remove()">
                                                            <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                                            </svg>
                                                        </button>
                                                    </span>
                                                <?php endforeach; ?>
                                            <?php endif; ?>
                                        </div>
                                        <input type="text" id="tagInput" 
                                            class="flex-1 min-w-[100px] py-1 px-2 text-sm border-0 focus:outline-none focus:ring-0" 
                                            placeholder="Add tags (Enter or comma)">
                                    </div>
                                    <div id="tagSuggestions" class="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg hidden"></div>
                                </div>
                            </div>

                            <!-- Block Classes -->
                            <div class="form-group">
                                <div class="flex justify-between mb-2">
                                    <label for="blockClasses" class="text-sm font-medium text-gray-800">
                                        Block Classes
                                    </label>
                                    <span class="text-xs text-gray-500">Optional</span>
                                </div>
                                <div class="relative rounded-lg">
                                    <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                                        <svg class="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
                                        </svg>
                                    </div>
                                    <input type="text" id="blockClasses" name="classes"
                                        class="block w-full pl-11 pr-4 py-2.5 text-sm text-gray-900 bg-white border border-gray-300 rounded-lg focus:border-blue-500 focus:ring-1 focus:ring-blue-500" 
                                        placeholder="Enter CSS classes" 
                                        value="<?= htmlspecialchars($block['classes'] ?? '') ?>">
                                </div>
                                <p class="mt-1.5 text-xs text-gray-500">Add custom CSS classes (space-separated)</p>
                            </div>
                        </div>

                        <!-- Right Column - Image Upload -->
                        <div class="col-span-5">
                            <div class="form-group">
                                <div class="flex justify-between mb-2">
                                    <label class="text-sm font-medium text-gray-800">
                                        Block Poster Image
                                    </label>
                                    <span class="text-xs text-gray-500">800x400px recommended</span>
                                </div>
                                <div class="w-full">
                                    <div class="relative">
                                        <div class="flex flex-col items-center justify-center w-full h-[495px] border-2 border-dashed border-gray-300 rounded-lg cursor-pointer bg-gray-50 hover:bg-gray-100/50 transition-all group">
                                            <!-- Upload Placeholder -->
                                            <div class="flex flex-col items-center justify-center p-6" id="uploadPlaceholder" 
                                                style="<?= (!$isNewBlock && !empty($block['image'])) ? 'display:none;' : 'display:flex;' ?>">
                                                <div class="mb-4 rounded-full bg-blue-50 p-3 group-hover:bg-blue-100/80 transition-colors">
                                                    <svg class="w-6 h-6 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                                    </svg>
                                                </div>
                                                <p class="mb-1 text-sm text-blue-600 font-medium">Drop your image here, or 
                                                    <span class="underline cursor-pointer" id="browseButton">browse</span>
                                                </p>
                                                <p class="text-xs text-gray-500">SVG, PNG, JPG or GIF (MAX. 800x400px)</p>
                                            </div>

                                            <!-- Image Preview -->
                                            <div id="imagePreview" class="absolute inset-0 flex items-center justify-center <?= (!$isNewBlock && !empty($block['image'])) ? '' : 'hidden' ?>">
                                                <img id="preview" class="max-w-full max-h-full object-contain rounded-lg" 
                                                    src="<?= htmlspecialchars($block['image'] ?? '') ?>">
                                                <div class="absolute inset-0 bg-black/40 opacity-0 hover:opacity-100 transition-opacity rounded-lg">
                                                    <button type="button" id="removeImage" class="absolute top-4 right-4 p-2 bg-white/90 backdrop-blur-sm rounded-lg text-gray-600 hover:text-red-500 shadow-sm transition-colors">
                                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                                        </svg>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                        <!-- File Input -->
                                        <input type="file" id="blockPoster" name="blockPoster" class="hidden" accept="image/*">
                                        <!-- Hidden input to store image path -->
                                        <input type="hidden" id="blockImage" name="image" value="<?= htmlspecialchars($block['image'] ?? '') ?>">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- Step 2 Content: Block Content -->
            <div id="step2Content" class="step-content hidden">
                <div class="py-6">
                    <div class="grid grid-cols-12 gap-6">
                        <!-- Left Column - Code Editor -->
                        <div class="col-span-12 leftColumnfullscreen">
                            <!-- Editor content wrapper -->
                            <div class="editor-content">
                                <!-- Tab Navigation -->
                                <div class="flex items-center gap-2 mb-4" data-tabs-group="editor-tabs">
                                    <button class="inline-flex items-center gap-1.5 px-3 py-1.5 text-sm font-medium rounded-full bg-blue-50 text-blue-600" id="template-tab" data-editor-target="#template" type="button" role="tab">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                        </svg>
                                        Template
                                        <span class="text-xs text-gray-400">.tmpl</span>
                                    </button>
                                
                                    <button class="inline-flex items-center gap-1.5 px-3 py-1.5 text-sm font-medium rounded-full text-gray-500 hover:bg-gray-100" id="content-tab" data-editor-target="#content" type="button" role="tab">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16m-7 6h7"></path>
                                        </svg>
                                        Content
                                        <span class="text-xs text-gray-400">.json</span>
                                    </button>
                                
                                    <button class="inline-flex items-center gap-1.5 px-3 py-1.5 text-sm font-medium rounded-full text-gray-500 hover:bg-gray-100" id="css-tab" data-editor-target="#css" type="button" role="tab">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                        </svg>
                                        CSS
                                    </button>
                                
                                    <button class="inline-flex items-center gap-1.5 px-3 py-1.5 text-sm font-medium rounded-full text-gray-500 hover:bg-gray-100" id="javascript-tab" data-editor-target="#javascript" type="button" role="tab">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 9l3 3-3 3m5 0h3M5 20h14a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                        </svg>
                                        JavaScript
                                    </button>

                                    <button class="inline-flex items-center gap-1.5 px-3 py-1.5 text-sm font-medium rounded-full text-gray-500 hover:bg-gray-100" id="dependencies-tab" data-editor-target="#dependencies" type="button" role="tab">
                                        <svg class="w-4 h-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M17 21v-2a1 1 0 0 1-1-1v-1a2 2 0 0 1 2-2h2a2 2 0 0 1 2 2v1a1 1 0 0 1-1 1"/><path d="M19 15V6.5a1 1 0 0 0-7 0v11a1 1 0 0 1-7 0V9"/><path d="M21 21v-2h-4"/><path d="M3 5h4V3"/><path d="M7 5a1 1 0 0 1 1 1v1a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V6a1 1 0 0 1 1-1V3"/></svg>
                                        Dependencies
                                    </button>

                                    <button class="inline-flex items-center gap-1.5 px-3 py-1.5 text-sm font-medium rounded-full text-gray-500 hover:bg-gray-100" id="preview-tab" data-editor-target="#preview" type="button" role="tab">
                                        <svg class="w-4 h-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M3 7V5a2 2 0 0 1 2-2h2"/><path d="M17 3h2a2 2 0 0 1 2 2v2"/><path d="M21 17v2a2 2 0 0 1-2 2h-2"/><path d="M7 21H5a2 2 0 0 1-2-2v-2"/><circle cx="12" cy="12" r="1"/><path d="M18.944 12.33a1 1 0 0 0 0-.66 7.5 7.5 0 0 0-13.888 0 1 1 0 0 0 0 .66 7.5 7.5 0 0 0 13.888 0"/></svg>
                                        Preview
                                    </button>

                                    <button class="inline-flex items-center gap-1.5 px-3 py-1.5 text-sm font-medium rounded-full text-gray-500 hover:bg-gray-100" id="aiassistant-tab" data-editor-target="#aiassistant" type="button" role="tab">
                                        <svg class="w-4 h-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M12 8V4H8"/><rect width="16" height="12" x="4" y="8" rx="2"/><path d="M2 14h2"/><path d="M20 14h2"/><path d="M15 13v2"/><path d="M9 13v2"/></svg>
                                        AI Assistant
                                    </button>
                                </div>

                                <!-- Editor Container -->
                                <div class="tab-content border rounded-lg bg-gray-50 editor-tab-content flex flex-col">
                                    <!-- Editor Header/Toolbar -->
                                    <div class="flex items-center rounded-t-lg justify-between p-3 border-b bg-white sticky top-0 z-50">
                                        <div class="flex items-center gap-3 mr-auto">
                                            <span id="editorHeaderText" class="text-sm text-gray-600 font-medium pl-2">HTML Editor</span>
                                        </div>
                                        <div class="flex items-center gap-2">
                                            <!-- Theme Toggle -->
                                            <div id="themeToggle" class="flex gap-1.5 pr-1.5">
                                                <span data-theme="theme1"
                                                        class="theme-btn cursor-pointer w-6 h-6 rounded-lg hover:scale-110 transition-transform border border-gray-200 bg-[linear-gradient(45deg,#473d5c_0%,#473d5c_40%,#bebb9d_33%,#bebb9d_60%,#668d5e_50%,#668d5e_100%)]">
                                                </span>
                                                <span data-theme="theme2"
                                                        class="theme-btn cursor-pointer w-6 h-6 rounded-lg hover:scale-110 transition-transform border border-gray-200 bg-[linear-gradient(45deg,#3a5a40_0%,#3a5a40_40%,#acbfc3_33%,#acbfc3_60%,#97a7b4_50%,#97a7b4_100%)]">
                                                </span>
                                                <span data-theme="theme3"
                                                        class="theme-btn cursor-pointer w-6 h-6 rounded-lg hover:scale-110 transition-transform border border-gray-200 bg-[linear-gradient(45deg,#274c77_0%,#274c77_40%,#5f95b9_33%,#5f95b9_60%,#ba8a7d_50%,#ba8a7d_100%)]">
                                                </span>
                                                <span data-theme="theme4"
                                                        class="theme-btn cursor-pointer w-6 h-6 rounded-lg hover:scale-110 transition-transform border border-gray-200 bg-[linear-gradient(45deg,#d77a60_0%,#d77a60_40%,#d9b2a1_33%,#d9b2a1_60%,#8e8943_50%,#8e8943_100%)]">
                                                </span>
                                            </div>
                                            <span class="cursor-pointer p-1.5 text-gray-400 hover:text-gray-600 rounded-md hover:bg-gray-100 hidden" id="resetPreview">
                                                <svg class="w-4 h-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 12a9 9 0 0 0-9-9 9.75 9.75 0 0 0-6.74 2.74L3 8"/><path d="M3 3v5h5"/><path d="M3 12a9 9 0 0 0 9 9 9.75 9.75 0 0 0 6.74-2.74L21 16"/><path d="M16 16h5v5"/></svg>
                                            </span>
                                            <!-- Dark Mode Toggle -->
                                            <span id="darkModeToggle" class="cursor-pointer p-1.5 text-gray-400 hover:text-gray-600 rounded-md hover:bg-gray-100 hidden">
                                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"/>
                                                </svg>
                                            </span>
                                            <span id="desktopBtn" class="responsive-btn cursor-pointer p-1.5 text-gray-400 hover:text-gray-600 rounded-md hover:bg-gray-100 hidden" data-device="desktop" title="Desktop">
                                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
                                                </svg>
                                            </span>
                                            <span id="tabletBtn" class="responsive-btn cursor-pointer p-1.5 text-gray-400 hover:text-gray-600 rounded-md hover:bg-gray-100 hidden" data-device="tablet" title="Tablet">
                                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M12 18h.01M8 21h8a1 1 0 001-1V4a1 1 0 00-1-1H8a1 1 0 00-1 1v16a1 1 0 001 1z"/>
                                                </svg>
                                            </span>
                                            <span id="mobileBtn" class="responsive-btn cursor-pointer p-1.5 text-gray-400 hover:text-gray-600 rounded-md hover:bg-gray-100 hidden" data-device="mobile" title="Mobile">
                                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M12 18h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"/>
                                                </svg>
                                            </span>
                                            <span id="editorFullscreenBtn" class="cursor-pointer p-1.5 text-gray-500 hover:text-blue-600 rounded-md hover:bg-gray-100" title="Fullscreen">
                                                <svg class="w-4 h-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m15 15 6 6"/><path d="m15 9 6-6"/><path d="M21 16.2V21h-4.8"/><path d="M21 7.8V3h-4.8"/><path d="M3 16.2V21h4.8"/><path d="m3 21 6-6"/><path d="M3 7.8V3h4.8"/><path d="M9 9 3 3"/></svg>
                                            </span>
                                        </div>
                                    </div>
                                
                                    <!-- Editor Content -->
                                    <div class="relative flex-1">
                                        <!-- Template Panel -->
                                        <div class="hidden" id="template" role="tabpanel">
                                            <div class="absolute top-2 right-3 px-2 py-1 text-xs text-gray-500 bg-gray-200 rounded">HTML</div>
                                            <div id="templateEditor" class="min-h-[300px] p-4"></div>
                                            <textarea id="blockTmplCode" name="tmpl_code" rows="15" class="w-full p-2 border border-gray-300 rounded-md font-mono hidden"><?= htmlspecialchars($block['tmpl_code'] ?? '<div class="p-4 bg-blue-100 rounded-lg"><h2 class="text-xl font-bold text-blue-800">{{title}}</h2><p class="text-blue-600">{{description}}</p></div>') ?></textarea>
                                        </div>
                                
                                        <!-- Content Panel -->
                                        <div class="hidden" id="content" role="tabpanel">
                                            <div class="absolute top-2 right-3 px-2 py-1 text-xs text-gray-500 bg-gray-200 rounded">JSON</div>
                                            <div id="contentEditor" class="min-h-[300px] p-4"></div>
                                            <textarea id="blockJsonCode" name="json_code" rows="10" class="w-full p-2 border border-gray-300 rounded-md font-mono hidden"><?= htmlspecialchars($block['json_code'] ?? '{"title": "Sample Block", "description": "This is a sample block with Handlebars template processing."}') ?></textarea>
                                        </div>
                                
                                        <!-- CSS Panel -->
                                        <div class="hidden" id="css" role="tabpanel">
                                            <div class="absolute top-2 right-3 px-2 py-1 text-xs text-gray-500 bg-gray-200 rounded">CSS</div>
                                            <div id="cssEditor" class="min-h-[300px] p-4"></div>
                                            <textarea id="blockCssCode" name="css_code" rows="15" class="w-full p-2 border border-gray-300 rounded-md font-mono hidden"><?= htmlspecialchars($block['css_code'] ?? '') ?></textarea>
                                        </div>
                                
                                        <!-- JavaScript Panel -->
                                        <div class="hidden" id="javascript" role="tabpanel">
                                            <div class="absolute top-2 right-3 px-2 py-1 text-xs text-gray-500 bg-gray-200 rounded">JS</div>
                                            <div id="javascriptEditor" class="min-h-[300px] p-4"></div>
                                            <textarea id="blockJsCode" name="js_code" rows="15" class="w-full p-2 border border-gray-300 rounded-md font-mono hidden"><?= htmlspecialchars($block['js_code'] ?? '') ?></textarea>
                                        </div>

                                        <!-- Dependencies Panel -->
                                        <div class="hidden" id="dependencies" role="tabpanel">
                                            <div class="absolute top-2 right-3 px-2 py-1 text-xs text-gray-500 bg-gray-200 rounded">CDN</div>
                                            <div class="p-4 space-y-6">
                                                <!-- Introduction Section -->
                                                <div class="border-b pb-4">
                                                    <div class="flex items-start gap-3">
                                                        <div class="p-2 rounded-lg bg-gradient-to-br from-blue-50 to-indigo-50">
                                                            <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                                            </svg>
                                                        </div>
                                                        <div>
                                                            <h2 class="text-base font-medium text-gray-900 mb-1">External Dependencies</h2>
                                                            <p class="text-sm text-gray-500 leading-relaxed">
                                                                Add external JavaScript and CSS resources to enhance your block's functionality. 
                                                                <span class="inline-flex items-center gap-1 text-blue-600">
                                                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                                    </svg>
                                                                    Use CDN URLs for better performance
                                                                </span>
                                                            </p>
                                                        </div>
                                                    </div>
                                                </div>
                                                
                                                <!-- JavaScript Dependencies -->
                                                <div class="py-4">
                                                    <div class="flex items-center justify-between">
                                                        <h3 class="text-sm font-medium text-gray-700">JavaScript Dependencies</h3>
                                                        <span id="addJsDepBtn" class="p-1.5 text-gray-500 hover:text-blue-600 rounded-md hover:bg-gray-100 cursor-pointer" title="Add JavaScript Dependency">
                                                            <svg class="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect width="18" height="18" x="3" y="3" rx="2"/><path d="M8 12h8"/><path d="M12 8v8"/></svg>
                                                        </span>
                                                    </div>
                                                    <div id="jsDependencies" class="space-y-2">
                                                        <?php 
                                                        $dependencies = json_decode($block['dependencies'] ?? '{"js":[],"css":[]}', true);
                                                        if (isset($dependencies['js']) && is_array($dependencies['js'])):
                                                            foreach ($dependencies['js'] as $jsDep):
                                                        ?>
                                                            <div class="dependency-item flex items-center gap-2">
                                                                <input type="text" value="<?= htmlspecialchars($jsDep) ?>" 
                                                                    class="flex-1 px-3 py-1.5 text-sm border border-gray-300 rounded-md focus:border-blue-500 focus:ring-1 focus:ring-blue-500" 
                                                                    placeholder="https://cdn.example.com/library.js">
                                                                <button type="button" class="remove-dep-btn p-1.5 text-red-500 hover:bg-red-50 rounded-md">
                                                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                                                    </svg>
                                                                </button>
                                                            </div>
                                                        <?php 
                                                            endforeach;
                                                        endif;
                                                        ?>
                                                    </div>
                                                </div>

                                                <!-- CSS Dependencies -->
                                                <div class="border-t pt-4">
                                                    <div class="flex items-center justify-between">
                                                        <h3 class="text-sm font-medium text-gray-700">CSS Dependencies</h3>
                                                        <span id="addCssDepBtn" class="p-1.5 text-gray-500 hover:text-blue-600 rounded-md hover:bg-gray-100 cursor-pointer" title="Add CSS Dependency">
                                                            <svg class="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect width="18" height="18" x="3" y="3" rx="2"/><path d="M8 12h8"/><path d="M12 8v8"/></svg>
                                                        </span>
                                                    </div>
                                                    <div id="cssDependencies" class="space-y-2">
                                                        <?php 
                                                        if (isset($dependencies['css']) && is_array($dependencies['css'])):
                                                            foreach ($dependencies['css'] as $cssDep):
                                                        ?>
                                                            <div class="dependency-item flex items-center gap-2">
                                                                <input type="text" value="<?= htmlspecialchars($cssDep) ?>" 
                                                                    class="flex-1 px-3 py-1.5 text-sm border border-gray-300 rounded-md focus:border-blue-500 focus:ring-1 focus:ring-blue-500" 
                                                                    placeholder="https://cdn.example.com/styles.css">
                                                                <button type="button" class="remove-dep-btn p-1.5 text-red-500 hover:bg-red-50 rounded-md">
                                                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                                                    </svg>
                                                                </button>
                                                            </div>
                                                        <?php 
                                                            endforeach;
                                                        endif;
                                                        ?>
                                                    </div>
                                                </div>
                                            </div>
                                            <textarea id="blockDependencies" name="dependencies" rows="5" class="w-full p-2 border border-gray-300 rounded-md font-mono hidden"><?= htmlspecialchars($block['dependencies'] ?? '{"js":[],"css":[]}') ?></textarea>
                                        </div>

                                        <!-- Preview Panel -->
                                        <div class="hidden" id="preview" role="tabpanel">
                                            <div id="previewContent" class="relative min-h-[400px]">
                                                <iframe id="previewFrame" class="w-full h-full border-2 border-dashed border-gray-300 rounded-lg" frameborder="0"></iframe>
                                            </div>
                                        </div>

                                        <!-- AI Assistant Panel -->
                                        <div class="hidden" id="aiassistant" role="tabpanel">
                                            <div class="p-6 space-y-6">
                                                <!-- Model Selection -->
                                                <div class="form-group">
                                                    <label for="aiModel" class="text-sm font-medium text-gray-700 mb-2 block">
                                                        AI Model
                                                        <span class="text-red-500 ml-1">*</span>
                                                    </label>
                                                    <select id="aiModel" name="ai_model" class="w-full px-4 py-2.5 border border-gray-300 rounded-lg focus:border-purple-500 focus:ring-2 focus:ring-purple-500 focus:ring-opacity-20 text-sm">
                                                        <option value="claude-3-5-sonnet-20241022">Claude 3.5 Sonnet (Recommended)</option>
                                                        <option value="claude-3-opus-20240229">Claude 3 Opus (Most Capable)</option>
                                                        <option value="claude-3-haiku-20240307">Claude 3 Haiku (Fastest)</option>
                                                    </select>
                                                    <p class="mt-1 text-xs text-gray-500">Choose the AI model for block generation</p>
                                                </div>

                                                <!-- Custom Instructions -->
                                                <div class="form-group">
                                                    <label for="customInstructions" class="text-sm font-medium text-gray-700 mb-2 block">
                                                        Custom Instructions
                                                    </label>
                                                    <textarea id="customInstructions" name="custom_instructions" rows="3" 
                                                        class="w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:border-purple-500 focus:ring-2 focus:ring-purple-500 focus:ring-opacity-20 text-sm resize-y" 
                                                        placeholder="Add custom instructions for the AI (optional) Example: Use modern design, include animations, make it responsive, use specific colors..."></textarea>
                                                    <p class="mt-1 text-xs text-gray-500">Optional: Add specific requirements or preferences for the generated block</p>
                                                </div>

                                                <!-- Smart Input Area -->
                                                <div class="form-group">
                                                    <div class="flex items-center justify-between mb-3">
                                                        <label for="smartInput" class="text-sm font-medium text-gray-700">
                                                            Input Content
                                                            <span class="text-red-500 ml-1">*</span>
                                                        </label>
                                                        <div class="flex items-center gap-2">
                                                            <span id="inputTypeIndicator" class="text-xs px-2 py-1 bg-gray-100 text-gray-600 rounded hidden">
                                                                Type: <span id="detectedType">Auto</span>
                                                            </span>
                                                            <span class="text-xs text-gray-500">AI will auto-detect input type</span>
                                                        </div>
                                                    </div>
                                                    <div class="relative">
                                                        <textarea id="smartInput" name="smart_input" rows="5" 
                                                            class="w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:border-purple-500 focus:ring-2 focus:ring-purple-500 focus:ring-opacity-20 font-mono text-sm resize-y transition-all" 
                                                            placeholder="💡 Paste HTML, Image URL, or describe what you want"></textarea>
                                                        
                                                        <div class="absolute bottom-3 right-3 text-xs text-gray-400 bg-white px-2 py-1 rounded border">
                                                            Ctrl+Enter to generate
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- Generate Button -->
                                                <div class="flex justify-between items-center pt-4 border-t">
                                                    <div class="flex items-center gap-2 text-sm text-gray-500">
                                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                                        </svg>
                                                        <span>Powered by Anthropic Claude</span>
                                                        <span id="aiStatusIndicator" class="w-2 h-2 bg-green-400 rounded-full animate-pulse" title="AI Ready"></span>
                                                    </div>
                                                    <button type="button" id="generateSmartBlockBtn" 
                                                        class="inline-flex items-center px-6 py-3 border border-transparent text-sm font-medium rounded-lg shadow-sm text-white bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-all duration-200 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none">
                                                        <svg class="w-4 h-4 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                            <path d="M12 8V4H8"></path>
                                                            <rect width="16" height="12" x="4" y="8" rx="2"></rect>
                                                            <path d="M2 14h2"></path>
                                                            <path d="M20 14h2"></path>
                                                            <path d="M15 13v2"></path>
                                                            <path d="M9 13v2"></path>
                                                        </svg>
                                                        Generate Block
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>                            
            </div>
            
            <!-- Navigation Buttons -->
            <div class="flex justify-end gap-4 mt-6 pt-6 border-t">
                <a href="javascript:void(0);" id="prevBtn" class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 cursor-pointer" style="display: none;">
                    Back
                </a>
                <a href="javascript:void(0);" id="nextBtn" class="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700 cursor-pointer">
                    Continue
                </a>
                <button type="submit" id="submitBtn" class="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700 hidden cursor-pointer">
                    <?= $isNewBlock ? 'Create Block' : 'Update Block' ?>
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div id="deleteBlockModal" tabindex="-1" aria-hidden="true" class="fixed top-0 left-0 right-0 z-50 hidden w-full p-4 overflow-x-hidden overflow-y-auto md:inset-0 h-[calc(100%-1rem)] max-h-full">
    <div class="relative w-full max-w-md max-h-full">
        <div class="relative bg-white rounded-lg shadow">
            <form method="POST" action="<?= url('blocks/delete') ?>" class="p-6 text-center" data-ajax-form>
                <div class="p-6">
                    <input type="hidden" name="id" value="<?= htmlspecialchars($block['id'] ?? '') ?>">
                    <input type="hidden" name="action" value="delete">
                    <?php if (isset($selectedCategoryId)): ?>
                        <input type="hidden" name="category_id" value="<?= htmlspecialchars($selectedCategoryId) ?>">
                    <?php endif; ?>
                    
                    <div class="text-center mb-4">
                        <svg class="mx-auto h-12 w-12 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                        </svg>
                    </div>
                    <p class="text-center text-base leading-relaxed text-gray-700 font-medium">
                        Are you sure you want to delete this block?
                    </p>
                    <p class="text-center text-sm text-gray-500 mt-1">
                        This action cannot be undone.
                    </p>
                </div>
                <!-- Modal footer -->
                <div class="flex items-center pt-2 pb-7 justify-center space-x-2 rounded-b">
                    <button type="submit" class="text-white bg-red-700 hover:bg-red-800 focus:ring-4 focus:outline-none focus:ring-red-300 font-medium rounded-lg text-sm px-5 py-2 text-center">Delete</button>
                    <button type="button" class="modelClose text-gray-500 bg-white hover:bg-gray-100 focus:ring-4 focus:outline-none focus:ring-gray-200 rounded-lg border border-gray-200 text-sm font-medium px-5 py-2 hover:text-gray-900 focus:z-10" data-modal-hide="deleteBlockModal">Close</button>
                </div>
            </form>
        </div>
  </div>
</div>

<style>
    .block-detail pre {
        font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
    }
    
    /* Tag Styles */
    .tag-input-container {
        position: relative;
    }

    .tag-suggestions {
        max-height: 200px;
        overflow-y: auto;
        z-index: 1000;
    }

    .tag-item {
        transition: all 0.2s ease;
    }

    .tag-item:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .tag-remove-btn {
        transition: color 0.2s ease;
    }

    .tag-remove-btn:hover {
        color: #dc2626;
    }
</style>
