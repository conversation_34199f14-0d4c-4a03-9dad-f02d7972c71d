<?php

use Core\Router;
use Middleware\Authenticate;
use Modules\Blocks\Controllers\BlockController;
use Modules\Blocks\Controllers\BlockCategoryController;
use Modules\Tag\Controllers\TagController;

// Block Categories routes
$router->group('block-categories', function($router) {
    $router->get('/', [BlockCategoryController::class, 'index'], [Authenticate::class]);
    $router->post('create', [BlockCategoryController::class, 'create'], [Authenticate::class]);
    $router->post('update', [BlockCategoryController::class, 'update'], [Authenticate::class]);
    $router->post('delete', [BlockCategoryController::class, 'delete'], [Authenticate::class]);
});

// Blocks routes
$router->group('blocks', function($router) {
    $router->get('/', [BlockController::class, 'index'], [Authenticate::class]);
    $router->get('list', [BlockController::class, 'list'], [Authenticate::class]);
    $router->get('form', [BlockController::class, 'form'], [Authenticate::class]);
    
    // POST routes for block operations
    $router->post('create', [BlockController::class, 'create'], [Authenticate::class]);
    $router->post('update', [BlockController::class, 'update'], [Authenticate::class]);
    $router->post('delete', [BlockController::class, 'delete'], [Authenticate::class]);
    $router->post('generate', [BlockController::class, 'generate'], [Authenticate::class]);
    $router->post('process-template', [BlockController::class, 'processTemplate'], [Authenticate::class]);
    $router->post('upload-image', [BlockController::class, 'uploadImage'], [Authenticate::class]);
});

// Tag routes needed for blocks form
$router->get('tags/search', [TagController::class, 'search'], [Authenticate::class]);
$router->post('tags/create', [TagController::class, 'create'], [Authenticate::class]);