<?php

namespace Modules\Blocks\Controllers;

use Core\Controller;
use Core\Request;
use Modules\Blocks\Services\BlockCategoryService;
use Modules\Blocks\Services\BlockCategoryTableService;

class BlockCategoryController extends Controller
{
    protected $categoryService;
    
    protected $defaultPagination = [
        'current_page' => 1,
        'per_page' => 10,
        'total' => 0,
        'from' => null,
        'to' => null,
        'next_page_url' => null,
        'prev_page_url' => null,
        'first_page_url' => null,
        'last_page_url' => null
    ];

    public function __construct()
    {
        $this->categoryService = new BlockCategoryService();
    }

    public function index(Request $request)
    {
        $this->setTitle('Block Categories');

        // Get data from service
        $result = $this->categoryService->getData($request);
        $categoriesList = $result['status'] == 'success' ? $result['data']['data'] : [];
        $pagination = $result['status'] === 'success' 
            ? $result['data'] 
            : $this->defaultPagination;

        // Get parent categories for dropdown
        $allCategoriesResult = $this->categoryService->getAllCategories();
        $parentCategories = $allCategoriesResult['status'] == 'success' ? $allCategoriesResult['data']['data'] : [];

        $categoryTable = new BlockCategoryTableService($categoriesList, $pagination, $request);
        $tableRender = $categoryTable->renderTable();

        if ($request->isAjax()) {
            if ($tableRender['success']) {
                return $this->jsonResponse([
                    'html' => $tableRender['table'],
                    'view' => $tableRender['view'],
                    'pagination' => $pagination,
                    'success' => true,
                    'message' => ''
                ]);
            }

            return $this->jsonResponse([
                'html' => '',
                'view' => 'table',
                'pagination' => $pagination,
                'success' => true,
                'message' => $tableRender['message']
            ]);
        }

        return $this->view('modules/Blocks/Views/index', [
            'categoryTable' => $tableRender['table'] ?? 'Error loading table',
            'parentCategories' => $parentCategories
        ]);
    }

    public function create(Request $request)
    {
        try {
            $result = $this->categoryService->create($request);
            $error = $result['status'] !== 'success' ? $result['message'] : '';
            
            return $this->jsonResponse([
                'success' => true,
                'message' => 'Category has been created successfully!',
            ]);
        } catch (\Exception $e) {
            return $this->jsonResponse([
                'success' => false,
                'error' => $e->getMessage()
            ]);
        }
    }

    public function update(Request $request)
    {
        try {
            $result = $this->categoryService->update($request);
            $error = $result['status'] !== 'success' ? $result['message'] : '';
            
            return $this->jsonResponse([
                'success' => true,
                'message' => 'Category has been updated successfully!',
            ]);
        } catch (\Exception $e) {
            return $this->jsonResponse([
                'success' => false,
                'error' => $e->getMessage()
            ]);
        }
    }

    public function delete(Request $request)
    {
        try {
            $result = $this->categoryService->delete($request);
            $error = $result['status'] !== 'success' ? $result['message'] : '';
            
            return $this->jsonResponse([
                'success' => true,
                'message' => 'Category has been deleted successfully!',
            ]);
        } catch (\Exception $e) {
            return $this->jsonResponse([
                'success' => false,
                'error' => $e->getMessage()
            ]);
        }
    }
}