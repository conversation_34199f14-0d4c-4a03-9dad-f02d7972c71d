<?php

namespace Modules\Blocks\Controllers;

use Core\Controller;
use Core\Request;
use Modules\Blocks\Services\BlockService;
use Modules\Blocks\Services\BlockTableService;
use Modules\Blocks\Services\BlockCategoryService;
use LightnCandy\LightnCandy;

class BlockController extends Controller
{
    protected $blockService;
    protected $categoryService;
    
    protected $defaultPagination = [
        'current_page' => 1,
        'per_page' => 10,
        'total' => 0,
        'from' => null,
        'to' => null,
        'next_page_url' => null,
        'prev_page_url' => null,
        'first_page_url' => null,
        'last_page_url' => null
    ];

    public function __construct()
    {
        $this->blockService = new BlockService();
        $this->categoryService = new BlockCategoryService();
    }

    public function index(Request $request)
    {
        // Redirect to categories
        return $this->redirect('block-categories');
    }

    public function list(Request $request)
    {
        $categoryId = $request->get('category_id');
        
        if (!$categoryId) {
            return $this->redirect('block-categories');
        }

        $this->setTitle('Blocks');

        // Get blocks for category
        $result = $this->blockService->getBlocksByCategory($categoryId, $request);
        
        // Debug: Log the result
        if (defined('DEBUG') && DEBUG) {
            error_log('BlockController::list - Result: ' . print_r($result, true));
        }
        
        $blocksList = $result['status'] == 'success' ? $result['data']['data'] : [];
        $pagination = $result['status'] === 'success' 
            ? $result['data'] 
            : $this->defaultPagination;

        // Get category details
        $categoryDetails = $this->categoryService->getCategoryDetails($categoryId);

        $blockTable = new BlockTableService($blocksList, $pagination, $request);
        $blockTable->setCategoryId($categoryId);
        $tableRender = $blockTable->renderTable();

        if ($request->isAjax()) {
            if ($tableRender['success']) {
                return $this->jsonResponse([
                    'html' => $tableRender['table'],
                    'view' => $tableRender['view'],
                    'pagination' => $pagination,
                    'success' => true,
                    'message' => ''
                ]);
            }

            return $this->jsonResponse([
                'html' => '',
                'view' => 'table',
                'pagination' => $pagination,
                'success' => false,
                'message' => $tableRender['message']
            ]);
        }

        return $this->view('modules/Blocks/Views/list', [
            'blockTable' => $tableRender['table'] ?? 'Error loading table',
            'categoryDetails' => $categoryDetails,
            'categoryId' => $categoryId
        ]);
    }

    public function form(Request $request)
    {
        $blockId = $request->get('block_id');
        $categoryId = $request->get('category_id') ?? $_SESSION['selected_category_id'] ?? null;
        
        $this->setTitle($blockId ? 'Edit Block' : 'Add New Block');

        $block = null;
        $isNewBlock = true;

        // Get categories for dropdown
        $categoriesResult = $this->categoryService->getAllCategories();
        $categories = $categoriesResult['status'] == 'success' ? $categoriesResult['data']['data'] : [];

        // Get tags - Fix namespace to match your module
        $tagsService = new \Modules\Tag\Services\TagService();
        $tagsRequest = new Request();
        $tagsRequest->setGetParams(['limit' => 100]);
        $tagsResult = $tagsService->getData($tagsRequest);
        $tags = $tagsResult['status'] == 'success' ? $tagsResult['data']['data'] : [];

        if ($blockId) {
            $isNewBlock = false;
            $blockResult = $this->blockService->getBlockWithTags($blockId);
            
            if ($blockResult['success']) {
                $block = $blockResult['data'];
                
                // Get block categories
                $blockCategories = $this->blockService->getBlockCategories($blockId);
                if ($blockCategories['success']) {
                    $block['categories'] = $blockCategories['data'];
                }
            }
        }

        return $this->view('modules/Blocks/Views/form', [
            'block' => $block,
            'isNewBlock' => $isNewBlock,
            'categories' => $categories,
            'tags' => $tags,
            'selectedCategoryId' => $categoryId,
            'selectedBlockId' => $blockId
        ]);
    }

    public function create(Request $request)
    {
        try {
            $result = $this->blockService->create($request);
            
            if ($result['success']) {
                $categoryId = $request->post('category_id');
                $redirectUrl = url("blocks/list?category_id=" . $categoryId);

                // For regular form submission, redirect directly
                header('Location: ' . $redirectUrl);
                exit();
            }
            
            // If failed, redirect back with error
            $errorUrl = url('blocks/form?category_id=' . $request->post('category_id') . '&error=' . urlencode($result['message'] ?? 'Failed to create block'));
            header('Location: ' . $errorUrl);
            exit();
        } catch (\Exception $e) {
            $errorUrl = url('blocks/form?category_id=' . $request->post('category_id') . '&error=' . urlencode($e->getMessage()));
            header('Location: ' . $errorUrl);
            exit();
        }
    }

    public function update(Request $request)
    {
        try {
            $result = $this->blockService->update($request);
            
            if ($result['success']) {
                $categoryId = $request->post('category_id');
                $redirectUrl = url("blocks/list?category_id=" . $categoryId);

                // For regular form submission, redirect directly
                header('Location: ' . $redirectUrl);
                exit();
            }

            // If failed, redirect back with error
            $errorUrl = url('blocks/form?block_id=' . $request->post('id') . '&category_id=' . $request->post('category_id') . '&error=' . urlencode($result['message'] ?? 'Failed to update block'));
            header('Location: ' . $errorUrl);
            exit();
        } catch (\Exception $e) {
            $errorUrl = url('blocks/form?block_id=' . $request->post('id') . '&category_id=' . $request->post('category_id') . '&error=' . urlencode($e->getMessage()));
            header('Location: ' . $errorUrl);
            exit();
        }
    }

    public function delete(Request $request)
    {
        try {
            $result = $this->blockService->delete($request);

            $categoryId = $request->post('category_id');
            $redirectUrl = url("blocks/list?category_id=" . $categoryId);

            return $this->jsonResponse([
                'success' => true,
                'message' => 'Block has been deleted successfully!',
                'redirect' => $redirectUrl
            ]);
        } catch (\Exception $e) {
            return $this->jsonResponse([
                'success' => false,
                'error' => $e->getMessage()
            ]);
        }
    }

    public function generate(Request $request)
    {
        try {
            $result = $this->blockService->generateWithAI($request);
            
            if ($result['success']) {
                return $this->jsonResponse($result);
            }
            
            return $this->jsonResponse([
                'success' => false,
                'error' => $result['error'] ?? 'Failed to generate block'
            ]);
        } catch (\Exception $e) {
            return $this->jsonResponse([
                'success' => false,
                'error' => $e->getMessage()
            ]);
        }
    }

    public function processTemplate(Request $request)
    {
        try {
            $template = $request->post('template', '');
            $jsonData = $request->post('json_data', '{}');

            // Process template with LightCandy using the existing method
            $result = $this->processHandlebarsTemplate($template, $jsonData);

            if ($result['success']) {
                return $this->jsonResponse([
                    'success' => true,
                    'html' => $result['html']
                ]);
            } else {
                return $this->jsonResponse([
                    'success' => false,
                    'message' => $result['message'],
                    'html' => '<div class="p-4 text-red-600">Template Error: ' . htmlspecialchars($result['message']) . '</div>'
                ]);
            }

        } catch (\Exception $e) {
            return $this->jsonResponse([
                'success' => false,
                'message' => 'Template processing failed: ' . $e->getMessage(),
                'html' => '<div class="p-4 text-red-600">Template Error: ' . htmlspecialchars($e->getMessage()) . '</div>'
            ]);
        }
    }

    public function uploadImage(Request $request)
    {
        try {
            $result = $this->blockService->uploadBlockImage($request);
            
            return $this->jsonResponse($result);
        } catch (\Exception $e) {
            return $this->jsonResponse([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
    }

    private function processHandlebarsTemplate($template, $jsonData)
    {
        $helpers = [
            'mod' => function($arg1, $arg2) {
                return intval($arg1) % intval($arg2);
            },
            'eq' => function($arg1, $arg2) {
                return $arg1 === $arg2;
            },
            'ne' => function($arg1, $arg2) {
                return $arg1 !== $arg2;
            },
            'neq' => function($arg1, $arg2) {
                return $arg1 !== $arg2;
            },
            'lt' => function($arg1, $arg2) {
                return $arg1 < $arg2;
            },
            'gt' => function($arg1, $arg2) {
                return $arg1 > $arg2;
            },
            'lte' => function($arg1, $arg2) {
                return $arg1 <= $arg2;
            },
            'gte' => function($arg1, $arg2) {
                return $arg1 >= $arg2;
            },
            'and' => function() {
                foreach (func_get_args() as $arg) {
                    if (!$arg) return false;
                }
                return true;
            },
            'or' => function() {
                foreach (func_get_args() as $arg) {
                    if ($arg) return true;
                }
                return false;
            },
            'not' => function($arg) {
                return !$arg;
            },
            'isEven' => function($arg1) {
                return intval($arg1) % 2 === 0;
            },
            'isOdd' => function($arg1) {
                return intval($arg1) % 2 !== 0;
            }
        ];

        try {
            $data = json_decode($jsonData, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                return ['success' => false, 'message' => 'Invalid JSON data: ' . json_last_error_msg()];
            }

            $phpStr = LightnCandy::compile($template, [
                'flags' => LightnCandy::FLAG_HANDLEBARS | 
                          LightnCandy::FLAG_ERROR_EXCEPTION | 
                          LightnCandy::FLAG_RUNTIMEPARTIAL |
                          LightnCandy::FLAG_NOESCAPE,
                'helpers' => $helpers
            ]);
            
            if (!$phpStr) {
                return ['success' => false, 'message' => 'Failed to compile template'];
            }

            $renderer = LightnCandy::prepare($phpStr);
            if (!$renderer) {
                return ['success' => false, 'message' => 'Failed to prepare template renderer'];
            }
            
            $result = $renderer($data);
            return ['success' => true, 'html' => $result];
        } catch (\Exception $e) {
            return ['success' => false, 'message' => 'Template processing error: ' . $e->getMessage()];
        }
    }
}