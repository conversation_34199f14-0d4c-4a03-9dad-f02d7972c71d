<?php

namespace Modules\Blocks\Services;

use Core\Request;
use Package\GridTable\Src\DataTable;
use Package\GridTable\Src\Helpers\TableHelper;

class BlockTableService
{
    private array $blocksList;
    private array $pagination;
    private Request $request;
    private ?string $categoryId = null;

    public function __construct($blocksList, $pagination, Request $request)
    {
        $this->blocksList = $blocksList;
        $this->pagination = $pagination;
        $this->request = $request;
    }

    public function setCategoryId($categoryId)
    {
        $this->categoryId = $categoryId;
        return $this;
    }

    public function renderTable()
    {
        try {
            $direction = $this->request->get('direction', 'desc');
            
            // Validate sort parameter 
            $sortColumns = ['id', 'name', 'slug', 'created_at', 'updated_at'];
            
            $table = new DataTable($this->blocksList, $this->getColumns(), $this->request, $this->pagination, $sortColumns, $direction);

            $table->setAjaxUrl('blocks/list?category_id=' . $this->categoryId);
            
            $table->setTableId('blocksTable')
                ->setBreadcrumbs($_SESSION['selected_category_name'] ?? 'Blocks', [
                    ['label' => 'Home', 'url' => url('/dashboard')],
                    ['label' => 'Block Categories', 'url' => url('/block-categories')],
                    ['label' => 'Blocks']
                ])
                ->setSearchConfig(true, 'blockSearch')
                ->setActionButtons($this->getActionButtons())
                ->setRowActions($this->getRowActionButtons())
                ->setGridConfig($this->getGridConfig());

            return [
                'table' => $table->render(),
                'view' => 'table',
                'success' => true,
                'message' => ''
            ];
        } catch (\Throwable $th) {
            return [
                'table' => '',
                'view' => 'table',
                'success' => false,
                'message' => $th->getMessage()
            ];
        }
    }

    private function getColumns()
    {
        return [
            'name' => [
                'label' => 'Block Name',
                'sortable' => true,
                'formatter' => function($value, $row) {
                    $id = $row['id'] ?? '';
                    $image = $row['image'] ?? '';
                    $imageHtml = empty($image) ?
                        '<div class="w-20 h-12 bg-gray-100 rounded flex items-center justify-center"><svg class="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path></svg></div>' :
                        '<div class="h-12 w-20 flex-shrink-0"><img src="' . htmlspecialchars($image) . '" alt="Block Image" class="h-full w-full object-cover rounded"></div>';
                    return '<a href="' . url('blocks/form?block_id=' . $id . '&category_id=' . $this->categoryId) . '"><div class="flex items-center space-x-3">' . $imageHtml . '<div><div class="font-semibold text-gray-900">' . htmlspecialchars($value) . '</div><div class="text-sm text-gray-500">Block ID: '.$id.'</div></div></div></a>';
                }
            ],
            'slug' => [
                'label' => 'Slug',
                'cellClass' => 'cell_slug',
                'sortable' => false
            ],
            'description' => [
                'label' => 'Description',
                'cellClass' => 'cell_description',
                'sortable' => false,
                'formatter' => function($value) {
                    if (empty($value)) {
                        return '-';
                    }
                    return strlen($value) > 50 ? substr(htmlspecialchars($value), 0, 50) . '...' : htmlspecialchars($value);
                }
            ],
            'created_at' => [
                'label' => 'Created At',
                'cellClass' => 'cell_created_at',
                'sortable' => true,
                'formatter' => function($value) {
                    return TableHelper::formatDate($value);
                }
            ],
            'updated_at' => [
                'label' => 'Updated At',
                'cellClass' => 'cell_updated_at',
                'sortable' => true,
                'formatter' => function($value) {
                    return TableHelper::formatDate($value);
                }
            ]
        ];
    }

    private function getRowActionButtons()
    {
        return [
            [
                'icon' => '<path d="M21.174 6.812a1 1 0 0 0-3.986-3.987L3.842 16.174a2 2 0 0 0-.5.83l-1.321 4.352a.5.5 0 0 0 .623.622l4.353-1.32a2 2 0 0 0 .83-.497z"/><path d="m15 5 4 4"/>',
                'tooltip' => 'Edit',
                'urlFormatter' => function($row) {
                    return url('blocks/form?block_id=' . ($row['id'] ?? '') . '&category_id=' . $this->categoryId);
                }
            ],
            [
                'icon' => '<path d="M3 6h18"/><path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"/><path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"/>',
                'tooltip' => 'Delete',
                'type' => 'button',
                'attributes' => [
                    'data-modal-target' => 'deleteBlockModal',
                    'data-modal-toggle' => 'deleteBlockModal',
                    'data-id' => function($row) { return $row['id'] ?? ''; }
                ]
            ]
        ];
    }

    private function getActionButtons()
    {
        return [
            [
                'label' => 'Back to Categories',
                'icon' => 'arrow-left',
                'class' => 'text-xs text-white bg-[#000000] px-2 py-1.5 rounded hover:bg-black flex items-center gap-1',
                'attributes' => [
                    'onclick' => "window.location.href='" . url('/block-categories') . "'"
                ]
            ],
            [
                'label' => 'Create New Block',
                'icon' => 'plus',
                'class' => 'text-xs text-white bg-emerald-500 px-2 py-1.5 rounded hover:bg-emerald-600 flex items-center gap-1',
                'attributes' => [
                    'onclick' => "window.location.href='" . url('/blocks/form?category_id=' . $this->categoryId) . "'"
                ]
            ]
        ];
    }

    private function getGridConfig()
    {
        return [
            'enabled' => false,
        ];
    }
}