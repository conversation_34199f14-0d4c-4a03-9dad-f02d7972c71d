<?php

namespace Modules\Blocks\Services;

use App\Services\ApiClient;
use Core\ServiceContainer;

class BlockCategoryMappingService
{
    protected $api;
    protected $endpoint = 'block_categories_mapping';

    public function __construct()
    {
        $this->api = ServiceContainer::resolve(ApiClient::class);
    }

    public function create($data)
    {
        return $this->api->post($this->endpoint, $data);
    }

    public function deleteByBlockId($blockId)
    {
        // Get all mappings for this block
        $mappings = $this->getByBlockId($blockId);
        
        if (!isset($mappings['data']['data']) || !is_array($mappings['data']['data'])) {
            return ['success' => true, 'message' => 'No mappings found to delete'];
        }
        
        // Delete each mapping individually
        $results = [];
        foreach ($mappings['data']['data'] as $mapping) {
            if (isset($mapping['id'])) {
                $result = $this->api->delete($this->endpoint . '/' . $mapping['id']);
                $results[] = $result;
            }
        }
        
        return ['success' => true, 'message' => 'Mappings deleted', 'results' => $results];
    }
    
    public function getByBlockId($blockId)
    {
        return $this->api->get($this->endpoint, ['filter[block_id]' => $blockId]);
    }
}