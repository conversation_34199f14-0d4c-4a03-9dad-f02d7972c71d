<?php

namespace Modules\Blocks\Services;

use Core\Request;
use Package\GridTable\Src\DataTable;
use Package\GridTable\Src\Helpers\TableHelper;

class BlockCategoryTableService
{
    private array $categoriesList;
    private array $pagination;
    private Request $request;

    public function __construct($categoriesList, $pagination, Request $request)
    {
        $this->categoriesList = $categoriesList;
        $this->pagination = $pagination;
        $this->request = $request;
    }

    public function renderTable()
    {
        try {
            $direction = $this->request->get('direction', 'desc');
            
            // Validate sort parameter 
            $sortColumns = ['id', 'name', 'created_at', 'updated_at'];
            
            $table = new DataTable($this->categoriesList, $this->getColumns(), $this->request, $this->pagination, $sortColumns, $direction);

            $table->setAjaxUrl('block-categories');
            
            $table->setTableId('blockCategoriesTable')
                ->setBreadcrumbs('Block Categories', [
                    ['label' => 'Home', 'url' => url('/dashboard')],
                    ['label' => 'Block Categories']
                ])
                ->setSearchConfig(true, 'categorySearch')
                ->setActionButtons($this->getActionButtons())
                ->setRowActions($this->getRowActionButtons())
                ->setGridConfig($this->getGridConfig());

            return [
                'table' => $table->render(),
                'view' => 'table',
                'success' => true,
                'message' => ''
            ];
        } catch (\Throwable $th) {
            return [
                'table' => '',
                'view' => 'table',
                'success' => false,
                'message' => $th->getMessage()
            ];
        }
    }

    private function getColumns()
    {
        return [
            'category_name' => [
                'label' => 'Category Name',
                'sortable' => true,
                'formatter' => function($value, $row) {
                    $id = $row['category_id'] ?? '';
                    $image = $row['category_image'] ?? '';
                    $imageHtml = empty($image) ? 
                        '<div class="w-20 h-12 bg-gray-100 rounded flex items-center justify-center"><svg class="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path></svg></div>' : 
                        '<div class="h-12 w-20 flex-shrink-0"><img src="' . url($image) . '" alt="Category Image" class="h-full w-full object-cover rounded"></div>';
                    return '<a href="' . url('blocks/list?category_id=' . $id) . '"><div class="flex items-center space-x-3">' . $imageHtml . '<div><div class="font-semibold text-gray-900">' . htmlspecialchars($value) . '</div><div class="text-sm text-gray-500">Category ID: '.$id.'</div></div></div></a>';
                }
            ],
            'block_count' => [
                'label' => 'Block Count',
                'cellClass' => 'px-6 py-3 whitespace-nowrap block_count',
                'sortable' => true,
                'formatter' => function ($value) {
                    return '<span class="inline-flex items-center justify-center px-2 py-1 rounded-full text-xs font-medium bg-[#0C5BE2] text-white">' . $value . '</span>';
                }
            ],
            'created_at' => [
                'label' => 'Created At',
                'cellClass' => 'px-6 py-3 whitespace-nowrap cell_created_at',
                'sortable' => true,
                'formatter' => function ($value) {
                    return TableHelper::formatDate($value);
                }
            ],
            'updated_at' => [
                'label' => 'Updated At',
                'cellClass' => 'px-6 py-3 whitespace-nowrap cell_updated_at',
                'sortable' => true,
                'formatter' => function ($value) {
                    return TableHelper::formatDate($value);
                }
            ]
        ];
    }

    private function getRowActionButtons()
    {
        return [
            [
                'icon' => '<path d="M21.174 6.812a1 1 0 0 0-3.986-3.987L3.842 16.174a2 2 0 0 0-.5.83l-1.321 4.352a.5.5 0 0 0 .623.622l4.353-1.32a2 2 0 0 0 .83-.497z"/><path d="m15 5 4 4"/>',
                'tooltip' => 'Edit',
                'type' => 'button',
                'attributes' => [
                    'data-modal-target' => 'editCategoryModal',
                    'data-modal-toggle' => 'editCategoryModal',
                    'data-id' => function ($row) {
                        return $row['category_id'] ?? '';
                    },
                    'data-name' => function ($row) {
                        return $row['category_name'] ?? '';
                    },
                    'data-image' => function ($row) {
                        return $row['image'] ?? '';
                    },
                    'data-parent' => function ($row) {
                        return $row['parent_category_id'] ?? '';
                    }
                ]
            ],
            [
                'icon' => '<path d="M3 6h18"/><path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"/><path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"/>',
                'tooltip' => 'Delete',
                'type' => 'button',
                'attributes' => [
                    'data-modal-target' => 'deleteCategoryModal',
                    'data-modal-toggle' => 'deleteCategoryModal',
                    'data-id' => function ($row) {
                        return $row['category_id'] ?? '';
                    },
                    'data-block-count' => function ($row) {
                        return $row['block_count'] ?? 0;
                    }
                ]
            ],
            [
                'icon' => '<path d="M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16Z"/><path d="m3.3 7 8.7 5 8.7-5"/><path d="M12 22V12"/>',
                'tooltip' => 'View Blocks',
                'urlFormatter' => function ($row) {
                    $_SESSION['selected_category_id'] = $row['category_id'] ?? '';
                    $_SESSION['selected_category_name'] = $row['category_name'] ?? '';
                    return url('blocks/list?category_id=' . ($row['category_id'] ?? ''));
                }
            ]
        ];
    }

    private function getActionButtons()
    {
        return [
            [
                'label' => 'Create New Category',
                'icon' => 'plus',
                'attributes' => [
                    'data-modal-target' => 'createCategoryModal',
                    'data-modal-toggle' => 'createCategoryModal'
                ]
            ],
            [
                'label' => 'Create New Block',
                'icon' => 'plus',
                'class' => 'text-xs text-white bg-emerald-500 px-2 py-1.5 rounded hover:bg-emerald-600 flex items-center gap-1',
                'attributes' => [
                    'onclick' => "window.location.href='" . url('/blocks/form') . "'"
                ]
            ]
        ];
    }

    private function getGridConfig()
    {
        return [
            'enabled' => false,
        ];
    }
}