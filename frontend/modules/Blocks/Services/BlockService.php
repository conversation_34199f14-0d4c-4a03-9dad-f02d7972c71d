<?php

namespace Modules\Blocks\Services;

use App\Services\ApiClient;
use Core\ServiceContainer;
use Core\Request;
use Core\FileUploadHelper;

class BlockService
{
    protected $api;
    protected $endpoint = 'blocks';
    protected $cmsEndpoint = 'block-list-by-categories';

    public function __construct()
    {
        $this->api = ServiceContainer::resolve(ApiClient::class);
    }

    public function getData(Request $request)
    {
        $sort = $request->get('sort') ?? '-id';
        $itemsPerPage = $request->get('limit') ?? 10;

        $params = [
            'page' => $request->get('page') ?? 1,
            'sort' => $sort,
            'limit' => $itemsPerPage
        ];

        $response = $this->api->get($this->endpoint, $params);

        return $this->formatResponse($response, $itemsPerPage);
    }

    public function getBlocksByCategory($categoryId, Request $request)
    {
        $sort = $request->get('sort') ?? '-id';
        $itemsPerPage = $request->get('limit') ?? 10;

        $params = [
            'category_id' => $categoryId,
            'page' => $request->get('page') ?? 1,
            'sort' => $sort,
            'per_page' => $itemsPerPage
        ];

        $response = $this->api->get($this->cmsEndpoint, $params, 'cms');

        // Check if we have the nested data structure
        if (isset($response['data']) && is_array($response['data'])) {
            // Handle case where data is directly in response['data']
            $data = $response['data'];
            $paginationData = $response;
        } else if (isset($response['data']['data'])) {
            // Handle case where data is in response['data']['data']
            $data = $response['data']['data'];
            $paginationData = $response['data'];
        } else {
            // No data found
            return [
                'status' => 'error',
                'message' => 'No data found'
            ];
        }

        return [
            'status' => 'success',
            'data' => [
                'data' => $data,
                'current_page' => $paginationData['current_page'] ?? 1,
                'per_page' => $paginationData['per_page'] ?? $itemsPerPage,
                'from' => $paginationData['from'] ?? null,
                'to' => $paginationData['to'] ?? null,
                'total' => $paginationData['total'] ?? null,
                'next_page_url' => $paginationData['next_page_url'] ?? null,
                'prev_page_url' => $paginationData['prev_page_url'] ?? null,
                'first_page_url' => $paginationData['first_page_url'] ?? null,
                'last_page_url' => $paginationData['last_page_url'] ?? null
            ]
        ];
    }

    public function getBlockWithTags($blockId)
    {
        try {
            $response = $this->api->get($this->endpoint . '/' . $blockId);
            
            if (!isset($response['data'])) {
                return ['success' => false, 'message' => 'Block not found'];
            }

            $block = $response['data'];

            // Get associated tags - Fix namespace
            $commonTagsService = new \Modules\CommonTags\Services\CommonTagService();
            $tagAssociations = $commonTagsService->getByModel('App\\Models\\Block', $blockId);
            
            // Ensure tagAssociations has proper structure
            if (!is_array($tagAssociations)) {
                $tagAssociations = ['success' => false, 'data' => []];
            }
            
            $block['tags'] = [];
            
            if (
                isset($tagAssociations['success']) &&
                $tagAssociations['success'] &&
                isset($tagAssociations['data']) &&
                is_array($tagAssociations['data']) &&
                !empty($tagAssociations['data'])
            ) {
                $tagsService = new \Modules\Tag\Services\TagService();
                
                foreach ($tagAssociations['data'] as $association) {
                    if (!is_array($association) || !isset($association['tag_id'])) {
                        continue;
                    }
                    
                    // Create a request object to get tag by ID
                    $tagRequest = new \Core\Request();
                    $tagRequest->setGetParams(['filter[id]' => $association['tag_id'], 'limit' => 1]);
                    $tagResult = $tagsService->getData($tagRequest);
                    
                    // Check if tagResult has the expected structure
                    if (is_array($tagResult) && isset($tagResult['status'])) {
                        if ($tagResult['status'] === 'success' && 
                            isset($tagResult['data']['data']) && 
                            is_array($tagResult['data']['data']) && 
                            !empty($tagResult['data']['data'])) {
                            $block['tags'][] = $tagResult['data']['data'][0];
                        }
                    } elseif (is_array($tagResult) && isset($tagResult['data']['data'])) {
                        // Handle case where status key might be missing
                        if (is_array($tagResult['data']['data']) && !empty($tagResult['data']['data'])) {
                            $block['tags'][] = $tagResult['data']['data'][0];
                        }
                    }
                }
            }

            return ['success' => true, 'data' => $block];
        } catch (\Exception $e) {
            error_log('Error in getBlockWithTags: ' . $e->getMessage());
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    public function getBlockCategories($blockId)
    {
        try {
            $mappingService = new BlockCategoryMappingService();
            $mappingsResult = $mappingService->getByBlockId($blockId);
            
            // Check if mappingsResult has proper structure
            if (!is_array($mappingsResult)) {
                return ['success' => false, 'data' => []];
            }
            
            // Check for success key before accessing it
            if (isset($mappingsResult['success']) && 
                $mappingsResult['success'] && 
                isset($mappingsResult['data']) && 
                isset($mappingsResult['data']['data']) &&
                is_array($mappingsResult['data']['data'])) {
                
                $categories = [];
                
                foreach ($mappingsResult['data']['data'] as $mapping) {
                    if (isset($mapping['category_id'])) {
                        $categories[] = ['id' => $mapping['category_id']];
                    }
                }
                
                return ['success' => true, 'data' => $categories];
            }
            
            // Also handle case where data might be directly in mappingsResult
            if (isset($mappingsResult['data']) && is_array($mappingsResult['data'])) {
                $categories = [];
                
                foreach ($mappingsResult['data'] as $mapping) {
                    if (isset($mapping['category_id'])) {
                        $categories[] = ['id' => $mapping['category_id']];
                    }
                }
                
                if (count($categories) > 0) {
                    return ['success' => true, 'data' => $categories];
                }
            }
            
            return ['success' => false, 'data' => []];
        } catch (\Exception $e) {
            error_log('Error in getBlockCategories: ' . $e->getMessage());
            return ['success' => false, 'data' => [], 'message' => $e->getMessage()];
        }
    }


    public function create(Request $request)
    {
        $data = [
            'name' => $request->post('name'),
            'slug' => $request->post('slug'),
            'description' => $request->post('description'),
            'classes' => $request->post('classes'),
            'image' => $request->post('image'),
            'tmpl_code' => $request->post('tmpl_code'),
            'json_code' => $request->post('json_code'),
            'css_code' => $request->post('css_code'),
            'js_code' => $request->post('js_code'),
            'dependencies' => $request->post('dependencies'),
            'created_at' => date('Y-m-d H:i:s')
        ];

        $tags = $request->post('tags') ?? [];
        $categories = $request->post('categories') ?? [];

        // Create block
        $response = $this->api->post($this->endpoint, $data);
        
        if (isset($response['data']['last_insert_id'])) {
            $blockId = $response['data']['last_insert_id'];
            
            // Handle tags
            if (!empty($tags)) {
                $this->handleTagAssociations($blockId, $tags, 'create');
            }
            
            // Handle categories
            if (!empty($categories)) {
                $this->handleCategoryAssociations($blockId, $categories);
            }
            
            return [
                'success' => true,
                'data' => $response['data'],
                'block_id' => $blockId
            ];
        }

        return [
            'success' => false,
            'message' => 'Failed to create block'
        ];
    }

    public function update(Request $request)
    {
        $id = $request->post('id');
        
        $data = [
            'name' => $request->post('name'),
            'slug' => $request->post('slug'),
            'description' => $request->post('description'),
            'classes' => $request->post('classes'),
            'image' => $request->post('image'),
            'tmpl_code' => $request->post('tmpl_code'),
            'json_code' => $request->post('json_code'),
            'css_code' => $request->post('css_code'),
            'js_code' => $request->post('js_code'),
            'dependencies' => $request->post('dependencies')
        ];

        if ($request->post('created_at')) {
            $data['created_at'] = date('Y-m-d H:i:s', strtotime($request->post('created_at')));
        }

        $tags = $request->post('tags') ?? [];
        $categories = $request->post('categories') ?? [];

        // Update block
        $response = $this->api->put($this->endpoint . '/' . $id, $data);
        
        // Handle tags
        $this->handleTagAssociations($id, $tags, 'update');
        
        // Handle categories
        $this->handleCategoryAssociations($id, $categories, 'update');
        
        return [
            'success' => true,
            'data' => $response['data'] ?? null
        ];
    }

    public function delete(Request $request)
    {
        $id = $request->post('id');
        $categoryId = $request->post('category_id');
        
        // Delete tag associations
        $this->deleteExistingTagAssociations($id);
        
        // Delete block
        $response = $this->api->delete($this->endpoint . '/' . $id);
        
        // Delete category mappings if provided
        if ($categoryId) {
            $mappingService = new BlockCategoryMappingService();
            $mappingService->deleteByBlockId($id);
        }
        
        return [
            'success' => true,
            'data' => $response['data'] ?? null
        ];
    }

    public function generateWithAI(Request $request)
    {
        $input = $request->getJson('input');
        $type = $request->getJson('type') ?? 'auto';
        $model = $request->getJson('model') ?? 'claude-3-5-sonnet-20241022';
        $customInstructions = $request->getJson('custom_instructions') ?? '';

        if (empty(trim($input))) {
            return ['success' => false, 'error' => 'Input content is required'];
        }

        // Get API key from config
        $apiKey = config('anthropic.api_key');
        
        if (!$apiKey || strpos($apiKey, 'sk-ant-api') !== 0) {
            return ['success' => false, 'error' => 'Invalid Anthropic API key configuration'];
        }

        // Detect input type if auto
        if ($type === 'auto') {
            $type = $this->detectInputType($input);
        }

        // Generate prompt
        $prompt = $this->generatePromptByType($input, $type, $customInstructions);

        // Call Anthropic API
        $result = $this->callAnthropicAPI($prompt, $model, $apiKey);
        
        if ($result['success']) {
            $parsedResult = $this->parseAIResponse($result['content']);
            
            return [
                'success' => true,
                'data' => $parsedResult,
                'input_type' => $type,
                'model' => $model,
                'message' => 'Block generated successfully using ' . $model
            ];
        }

        return $result;
    }

    public function uploadBlockImage(Request $request)
    {
        try {
            if (!$request->hasFile('image')) {
                throw new \Exception('No image file uploaded');
            }

            $uploader = new FileUploadHelper('blocks');
            $result = $uploader->handleUpload($request->getFile('image'));
            
            if (!$result['success']) {
                throw new \Exception($result['message']);
            }

            return [
                'success' => true,
                'path' => $result['url']
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    private function formatResponse($response, $itemsPerPage)
    {
        if (!isset($response['data'])) {
            return [
                'status' => 'error',
                'message' => 'Failed to fetch data'
            ];
        }

        $paginationData = $response['data'];

        return [
            'status' => 'success',
            'data' => [
                'data' => $paginationData['data'] ?? [],
                'current_page' => $paginationData['current_page'] ?? 1,
                'per_page' => $paginationData['per_page'] ?? $itemsPerPage,
                'from' => $paginationData['from'] ?? null,
                'to' => $paginationData['to'] ?? null,
                'total' => $paginationData['total'] ?? null,
                'next_page_url' => $paginationData['next_page_url'] ?? null,
                'prev_page_url' => $paginationData['prev_page_url'] ?? null,
                'first_page_url' => $paginationData['first_page_url'] ?? null,
                'last_page_url' => $paginationData['last_page_url'] ?? null
            ]
        ];
    }

    private function handleTagAssociations($blockId, $tags, $operation = 'create')
    {
        try {
            $commonTagsService = new \Modules\CommonTags\Services\CommonTagService();

            if ($operation === 'update') {
                $this->deleteExistingTagAssociations($blockId);
            }

            if (!empty($tags)) {
                $processedTags = array_unique(array_filter($tags));

                foreach ($processedTags as $tagId) {
                    if (!empty($tagId)) {
                        $existingAssociations = $commonTagsService->getByModel('App\\Models\\Block', $blockId);
                        $alreadyExists = false;

                        if ($existingAssociations['success']) {
                            foreach ($existingAssociations['data'] as $existing) {
                                if ($existing['tag_id'] == $tagId) {
                                    $alreadyExists = true;
                                    break;
                                }
                            }
                        }

                        if (!$alreadyExists) {
                            $commonTagsService->create([
                                'model_type' => 'App\\Models\\Block',
                                'model_id' => (int)$blockId,
                                'tag_id' => (int)$tagId
                            ]);
                        }
                    }
                }
            }
        } catch (\Exception $e) {
            error_log("Error handling tag associations: " . $e->getMessage());
        }
    }

    private function deleteExistingTagAssociations($blockId)
    {
        try {
            $commonTagsService = new \Modules\CommonTags\Services\CommonTagService();
            $commonTagsService->removeAllForModel('App\\Models\\Block', $blockId);
        } catch (\Exception $e) {
            error_log("Error deleting block tag associations: " . $e->getMessage());
        }
    }

    private function handleCategoryAssociations($blockId, $categories, $operation = 'create')
    {
        try {
            $mappingService = new BlockCategoryMappingService();

            if ($operation === 'update') {
                $mappingService->deleteByBlockId($blockId);
            }

            foreach ($categories as $categoryId) {
                $mappingService->create([
                    'block_id' => $blockId,
                    'category_id' => $categoryId
                ]);
            }
        } catch (\Exception $e) {
            error_log("Error handling category associations: " . $e->getMessage());
        }
    }

    private function detectInputType($input)
    {
        $input = trim($input);
        
        if (preg_match('/<[^>]+>/', $input)) {
            return 'html';
        }
        
        if (preg_match('/\.(jpg|jpeg|png|gif|svg|webp)$/i', $input) || 
            preg_match('/^data:image\//', $input)) {
            return 'image';
        }
        
        return 'message';
    }

    private function generatePromptByType($userInput, $type, $customInstructions = '')
    {
        $baseInstructions = "You are an expert web developer and UI/UX designer. Create a modern, responsive, and accessible web component.

IMPORTANT OUTPUT FORMAT:
Your response must be in this EXACT format:

TEMPLATE:
<template here>

JSON:
{json data here}

CSS:
<css styles here>

JAVASCRIPT:
<javascript code here>

Requirements:
- Use Tailwind CSS classes for styling
- Make it responsive (mobile-first)
- Include hover effects and transitions
- Use semantic HTML structure
- Provide realistic sample data
- Include proper accessibility attributes
- Use modern design patterns

";

        if (!empty($customInstructions)) {
            $baseInstructions .= "CUSTOM REQUIREMENTS:\n" . $customInstructions . "\n\n";
        }

        // Add type-specific instructions...
        return $baseInstructions . "User Request:\n" . $userInput;
    }

    private function callAnthropicAPI($prompt, $model, $apiKey)
    {
        $data = [
            "model" => $model,
            "max_tokens" => 4000,
            "messages" => [
                [
                    "role" => "user",
                    "content" => $prompt
                ]
            ]
        ];

        $ch = curl_init('https://api.anthropic.com/v1/messages');
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'x-api-key: ' . $apiKey,
            'anthropic-version: 2023-06-01'
        ]);
        curl_setopt($ch, CURLOPT_TIMEOUT, 60);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        if ($error) {
            return ['success' => false, 'error' => "Network error: " . $error];
        }

        if ($httpCode !== 200) {
            $result = json_decode($response, true);
            return ['success' => false, 'error' => $result['error']['message'] ?? 'API Error'];
        }

        $result = json_decode($response, true);
        
        if (!isset($result['content'][0]['text'])) {
            return ['success' => false, 'error' => 'No content received from API'];
        }

        return ['success' => true, 'content' => $result['content'][0]['text']];
    }

    private function parseAIResponse($content)
    {
        $template = '';
        $jsonData = [];
        $css = '';
        $javascript = '';

        // Parse TEMPLATE section
        if (preg_match('/TEMPLATE:\s*(.*?)(?=\n\s*JSON:|\n\s*CSS:|\n\s*JAVASCRIPT:|\n\s*$)/s', $content, $match)) {
            $template = trim($match[1]);
            $template = preg_replace('/^```[a-z]*\s*|\s*```$/m', '', $template);
        }

        // Parse JSON section
        if (preg_match('/JSON:\s*(.*?)(?=\n\s*CSS:|\n\s*JAVASCRIPT:|\n\s*$)/s', $content, $match)) {
            $jsonStr = trim($match[1]);
            $jsonStr = preg_replace('/^```[a-z]*\s*|\s*```$/m', '', $jsonStr);
            
            $jsonData = json_decode($jsonStr, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                $jsonData = ["content" => "Sample content"];
            }
        }

        // Parse CSS section
        if (preg_match('/CSS:\s*(.*?)(?=\n\s*JAVASCRIPT:|\n\s*$)/s', $content, $match)) {
            $css = trim($match[1]);
            $css = preg_replace('/^```[a-z]*\s*|\s*```$/m', '', $css);
        }

        // Parse JAVASCRIPT section
        if (preg_match('/JAVASCRIPT:\s*(.*?)$/s', $content, $match)) {
            $javascript = trim($match[1]);
            $javascript = preg_replace('/^```[a-z]*\s*|\s*```$/m', '', $javascript);
        }

        return [
            'template' => $template ?: '<div class="p-4 bg-gray-100 rounded">{{content}}</div>',
            'json' => $jsonData ?: ["content" => "Sample content"],
            'css' => $css,
            'js' => $javascript
        ];
    }
}