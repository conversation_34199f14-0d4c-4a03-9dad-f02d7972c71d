<?php

use Core\Router;
use Middleware\Authenticate;
use Modules\GlobalAssets\Controllers\GlobalAssetsController;

// Remove the return and directly define routes
$router->group('global-assets', function ($router) {
    $router->get('/', [GlobalAssetsController::class, 'index'], [Authenticate::class]);
    $router->post('create', [GlobalAssetsController::class, 'create'], [Authenticate::class]);
    $router->post('update', [GlobalAssetsController::class, 'update'], [Authenticate::class]);
    $router->post('delete', [GlobalAssetsController::class, 'delete'], [Authenticate::class]);
});
