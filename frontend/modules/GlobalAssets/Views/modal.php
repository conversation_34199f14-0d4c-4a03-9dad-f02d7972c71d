<!-- Create Asset Modal -->
<div id="createAssetModal" tabindex="-1" aria-hidden="true" class="fixed top-0 left-0 right-0 z-50 hidden w-full p-4 overflow-x-hidden overflow-y-auto md:inset-0 h-[calc(100%-1rem)] max-h-full">
    <div class="relative w-full max-w-2xl max-h-full">
        <!-- Modal content -->
        <div class="relative bg-white rounded-lg shadow">
            <!-- Modal header -->
            <div class="flex items-start justify-between p-4 border-b rounded-t">
                <div class="flex items-center">
                    <div class="flex items-center justify-center h-10 w-10 rounded-full bg-blue-50 text-blue-500 mr-3">
                        <svg class="h-5 w-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-globe-icon lucide-globe">
                            <circle cx="12" cy="12" r="10" />
                            <path d="M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20" />
                            <path d="M2 12h20" />
                        </svg>
                    </div>
                    <div>
                        <h3 class="text-lg font-medium text-gray-900">Create New Asset</h3>
                        <p class="text-sm text-gray-500">Create a new global asset.</p>
                    </div>
                </div>
            </div>
            <!-- Modal body -->
            <form id="createAssetForm" method="POST" action="<?= url('global-assets/create') ?>" data-ajax-form data-reset-on-success data-table-id="globalAssetsTable" data-ajax-url="global-assets">
                <div class="p-6 pb-0">
                    <div class="grid grid-cols-2 gap-4">
                        <div class="mb-4">
                            <label for="asset_type" class="block text-sm font-bold text-gray-700 mb-2">Asset Type</label>
                            <select name="asset_type" required class="block w-full px-4 py-2.5 text-sm text-gray-900 bg-white border border-gray-300 rounded-lg focus:border-blue-500 focus:ring-1 focus:ring-blue-500">
                                <option value="">Select Asset Type</option>
                                <option value="css">CSS</option>
                                <option value="js">JavaScript</option>
                            </select>
                        </div>
                        <div class="mb-4">
                            <label for="templates" class="block text-sm font-bold text-gray-700 mb-2">Templates (Optional)</label>
                            <select name="template_ids[]" multiple class="block w-full px-4 py-2.5 text-sm text-gray-900 bg-white border border-gray-300 rounded-lg focus:border-blue-500 focus:ring-1 focus:ring-blue-500" id="createTemplateIds">
                                <!-- Templates will be loaded via JavaScript -->
                            </select>
                        </div>
                    </div>
                    <div class="mb-4">
                        <label for="asset_value" class="block text-sm font-bold text-gray-700 mb-2">Asset URL</label>
                        <input type="url" name="asset_value" required class="block w-full px-4 py-2.5 text-sm text-gray-900 bg-white border border-gray-300 rounded-lg focus:border-blue-500 focus:ring-1 focus:ring-blue-500" placeholder="https://example.com/asset.css">
                    </div>
                </div>
                <!-- Modal footer -->
                <div class="flex items-center pb-7 px-6 justify-end space-x-2 rounded-b">
                    <button type="submit" class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2 text-center">Save</button>
                    <button type="button" class="modelClose text-gray-500 bg-white hover:bg-gray-100 focus:ring-4 focus:outline-none focus:ring-gray-200 rounded-lg border border-gray-200 text-sm font-medium px-5 py-2 hover:text-gray-900 focus:z-10" data-modal-hide="createAssetModal">Cancel</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Asset Modal -->
<div id="editAssetModal" tabindex="-1" aria-hidden="true" class="fixed top-0 left-0 right-0 z-50 hidden w-full p-4 overflow-x-hidden overflow-y-auto md:inset-0 h-[calc(100%-1rem)] max-h-full">
    <div class="relative w-full max-w-2xl max-h-full">
        <!-- Modal content -->
        <div class="relative bg-white rounded-lg shadow">
            <!-- Modal header -->
            <div class="flex items-start justify-between p-4 border-b rounded-t">
                <div class="flex items-center">
                    <div class="flex items-center justify-center h-10 w-10 rounded-full bg-blue-50 text-blue-500 mr-3">
                        <svg class="h-5 w-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-globe-icon lucide-globe">
                            <circle cx="12" cy="12" r="10" />
                            <path d="M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20" />
                            <path d="M2 12h20" />
                        </svg>
                    </div>
                    <div>
                        <h3 class="text-lg font-medium text-gray-900">Edit Asset</h3>
                        <p class="text-sm text-gray-500">Edit the details of the selected asset.</p>
                    </div>
                </div>
            </div>
            <!-- Modal body -->
            <form id="editAssetForm" method="POST" action="<?= url('global-assets/update') ?>" data-ajax-form data-reset-on-success data-table-id="globalAssetsTable" data-ajax-url="global-assets">
                <div class="p-6 pb-0">
                    <input type="hidden" name="id" id="editAssetId">
                    <div class="grid grid-cols-2 gap-4">
                        <div class="mb-4">
                            <label for="edit_asset_type" class="block text-sm font-bold text-gray-700 mb-2">Asset Type</label>
                            <select name="asset_type" id="editAssetType" required class="block w-full px-4 py-2.5 text-sm text-gray-900 bg-white border border-gray-300 rounded-lg focus:border-blue-500 focus:ring-1 focus:ring-blue-500">
                                <option value="">Select Asset Type</option>
                                <option value="css">CSS</option>
                                <option value="js">JavaScript</option>
                            </select>
                        </div>
                        <div class="mb-4">
                            <label for="edit_templates" class="block text-sm font-bold text-gray-700 mb-2">Templates (Optional)</label>
                            <select name="template_ids[]" multiple class="block w-full px-4 py-2.5 text-sm text-gray-900 bg-white border border-gray-300 rounded-lg focus:border-blue-500 focus:ring-1 focus:ring-blue-500" id="editTemplateIds">
                                <!-- Templates will be loaded via JavaScript -->
                            </select>
                        </div>
                    </div>
                    <div class="mb-4">
                        <label for="edit_asset_value" class="block text-sm font-bold text-gray-700 mb-2">Asset URL</label>
                        <input type="url" name="asset_value" id="editAssetValue" required class="block w-full px-4 py-2.5 text-sm text-gray-900 bg-white border border-gray-300 rounded-lg focus:border-blue-500 focus:ring-1 focus:ring-blue-500">
                    </div>
                </div>

                <!-- Modal footer -->
                <div class="flex items-center pb-7 px-6 justify-end space-x-2 rounded-b">
                    <button type="submit" class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2 text-center">Save</button>
                    <button type="button" class="modelClose text-gray-500 bg-white hover:bg-gray-100 focus:ring-4 focus:outline-none focus:ring-gray-200 rounded-lg border border-gray-200 text-sm font-medium px-5 py-2 hover:text-gray-900 focus:z-10" data-modal-hide="editAssetModal">Cancel</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Asset Confirmation Modal -->
<div id="deleteAssetModal" tabindex="-1" aria-hidden="true" class="fixed top-0 left-0 right-0 z-50 hidden w-full p-4 overflow-x-hidden overflow-y-auto md:inset-0 h-[calc(100%-1rem)] max-h-full">
    <div class="relative w-full max-w-md max-h-full">
        <!-- Modal content -->
        <div class="relative bg-white rounded-lg shadow">
            <!-- Modal body -->
            <form id="deleteAssetForm" method="POST" action="<?= url('global-assets/delete') ?>" data-ajax-form data-reset-on-success data-table-id="globalAssetsTable" data-ajax-url="global-assets">
                <div class="p-6">
                    <input type="hidden" name="id" id="deleteAssetId">
                    <div class="text-center mb-4">
                        <svg class="mx-auto h-12 w-12 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                        </svg>
                    </div>
                    <p class="text-center text-base leading-relaxed text-gray-700 font-medium">
                        Are you sure you want to delete this global asset?
                    </p>
                    <p class="text-center text-sm text-gray-500 mt-1">
                        This action cannot be undone.
                    </p>
                </div>
                <!-- Modal footer -->
                <div class="flex items-center pt-2 pb-7 justify-center space-x-2 rounded-b">
                    <button type="submit" class="text-white bg-red-700 hover:bg-red-800 focus:ring-4 focus:outline-none focus:ring-red-300 font-medium rounded-lg text-sm px-5 py-2 text-center">Delete</button>
                    <button type="button" class="modelClose text-gray-500 bg-white hover:bg-gray-100 focus:ring-4 focus:outline-none focus:ring-gray-200 rounded-lg border border-gray-200 text-sm font-medium px-5 py-2 hover:text-gray-900 focus:z-10" data-modal-hide="deleteAssetModal">Cancel</button>
                </div>
            </form>
        </div>
    </div>
</div>