<?php

namespace Modules\GlobalAssets\Controllers;

use Core\Controller;
use Core\Request;
use Dotenv\Exception\ExceptionInterface;
use Modules\GlobalAssets\Services\GlobalAssetsService;
use Modules\GlobalAssets\Services\GlobalAssetsTableService;

class GlobalAssetsController extends Controller
{
    protected $globalAssetsService;

    public function __construct()
    {
        $this->globalAssetsService = new GlobalAssetsService();
    }

    public function index()
    {
        $request = new Request();

        // Regular request - render full page
        $this->setTitle('Global Assets');

        // Get data from service
        try {
            $result = $this->globalAssetsService->getData($request);

            // Debug: Log the API response structure
            error_log('GlobalAssets API response: ' . json_encode($result));

            $error = $result['status'] !== 'success' ? $result['message'] : '';

            // Handle Laravel pagination response structure
            if ($result['status'] == 'success' && isset($result['data'])) {
                $paginationData = $result['data'];
                $assetsList = $paginationData['data'] ?? [];
                $pagination = $paginationData;

                // Debug: Log the assets list
                error_log('Assets list count: ' . count($assetsList));
                if (!empty($assetsList)) {
                    error_log('First asset: ' . json_encode($assetsList[0]));
                }
            } else {
                $assetsList = [];
                $pagination = [
                    'current_page' => 1,
                    'per_page' => 10,
                    'from' => null,
                    'to' => null,
                    'total' => 0,
                    'next_page_url' => null,
                    'prev_page_url' => null,
                    'first_page_url' => null,
                    'last_page_url' => null
                ];
            }

            $assetsTable = new GlobalAssetsTableService($assetsList, $pagination, $request);

            if ($request->isAjax()) {
                return $this->json([
                    'html' => $assetsTable->renderTable(),
                    'pagination' => $pagination,
                    'success' => true
                ]);
            }

            return $this->view('modules/GlobalAssets/Views/index', [
                'assetsTable' => $assetsTable->renderTable(),
                'error' => $error
            ]);
        } catch (ExceptionInterface $e) {
            $error = $e->getMessage();
            $assetsList = [];
            $pagination = [
                'current_page' => 1,
                'per_page' => 10,
                'from' => null,
                'to' => null,
                'total' => 0,
                'next_page_url' => null,
                'prev_page_url' => null,
                'first_page_url' => null,
                'last_page_url' => null
            ];
        }
    }

    public function create(Request $request)
    {
        try {
            // Debug: Log the request data
            error_log('GlobalAssets create request data: ' . json_encode($request->all()));

            $result = $this->globalAssetsService->create($request);

            if ($result['status'] === 'success') {
                return $this->jsonResponse([
                    'success' => true,
                    'message' => 'Global Asset has been created successfully!',
                ]);
            } else {
                return $this->json([
                    'success' => false,
                    'error' => $result['message'] ?? 'Failed to create global asset'
                ]);
            }
        } catch (ExceptionInterface $e) {
            return $this->json([
                'success' => false,
                'error' => $e->getMessage()
            ]);
        }
    }

    public function update(Request $request)
    {
        try {
            $result = $this->globalAssetsService->update($request);

            if ($result['status'] === 'success') {
                return $this->jsonResponse([
                    'success' => true,
                    'message' => 'Global Asset has been updated successfully!',
                ]);
            } else {
                return $this->json([
                    'success' => false,
                    'error' => $result['message'] ?? 'Failed to update global asset'
                ]);
            }
        } catch (ExceptionInterface $e) {
            return $this->json([
                'success' => false,
                'error' => $e->getMessage()
            ]);
        }
    }

    public function delete(Request $request)
    {
        try {
            $result = $this->globalAssetsService->delete($request);

            if ($result['status'] === 'success') {
                return $this->jsonResponse([
                    'success' => true,
                    'message' => 'Global Asset has been deleted successfully!',
                ]);
            } else {
                return $this->json([
                    'success' => false,
                    'error' => $result['message'] ?? 'Failed to delete global asset'
                ]);
            }
        } catch (ExceptionInterface $e) {
            return $this->json([
                'success' => false,
                'error' => $e->getMessage()
            ]);
        }
    }
}
