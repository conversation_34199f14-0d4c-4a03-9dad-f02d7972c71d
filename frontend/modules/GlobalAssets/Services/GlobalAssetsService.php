<?php

namespace Modules\GlobalAssets\Services;

use App\Services\ApiClient;
use Core\ServiceContainer;
use Core\Request;

class GlobalAssetsService
{
    protected $api;

    public function __construct()
    {
        $this->api = ServiceContainer::resolve(ApiClient::class);
    }

    public function getData(Request $request)
    {
        $sort = $request->get('sort') ?? '-id';
        $itemsPerPage = $request->get('limit') ?? 10;

        $params = [
            'page' => $request->get('page') ?? 1,
            'sort' => $sort,
            'per_page' => $itemsPerPage // Laravel API expects per_page, not limit
        ];

        // Add search parameter if provided
        if ($request->get('search')) {
            $params['search'] = $request->get('search');
        }

        // Add asset_type filter if provided
        if ($request->get('asset_type')) {
            $params['asset_type'] = $request->get('asset_type');
        }

        // Debug: Log the parameters being sent to API
        error_log('GlobalAssets API request params: ' . json_encode($params));

        return $this->api->get('global-assets', $params, true); // Use CMS API
    }

    public function create($request)
    {
        $assetType = trim($request->post('asset_type'));
        $assetValue = trim($request->post('asset_value'));
        $templateIds = $request->post('template_ids') ?? [];

        $data = [
            'asset_type' => $assetType,
            'asset_value' => $assetValue,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        // Add template_ids if provided
        if (!empty($templateIds)) {
            $data['template_ids'] = $templateIds;
        }

        // Debug: Log the data being sent
        error_log('GlobalAssets create data: ' . json_encode($data));

        $response = $this->api->post('global-assets', $data, true); // Use CMS API

        // Debug: Log the response
        error_log('GlobalAssets create response: ' . json_encode($response));

        return $response;
    }

    public function update($request)
    {
        $id = $request->post('id');
        $assetType = trim($request->post('asset_type'));
        $assetValue = trim($request->post('asset_value'));
        $templateIds = $request->post('template_ids') ?? [];

        $data = [
            'asset_type' => $assetType,
            'asset_value' => $assetValue,
            'updated_at' => date('Y-m-d H:i:s')
        ];

        // Add template_ids if provided
        if (!empty($templateIds)) {
            $data['template_ids'] = $templateIds;
        }

        return $this->api->put('global-assets/' . $id, $data, true); // Use CMS API
    }

    public function delete($request)
    {
        $id = $request->post('id');
        return $this->api->delete('global-assets/' . $id, [], true); // Use CMS API
    }
}
