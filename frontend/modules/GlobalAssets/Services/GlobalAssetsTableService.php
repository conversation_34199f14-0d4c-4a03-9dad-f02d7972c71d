<?php

namespace Modules\GlobalAssets\Services;

use Core\Request;
use Package\GridTable\Src\DataTable;
use Package\GridTable\Src\Helpers\TableHelper;

class GlobalAssetsTableService
{
    private array $assetsList;
    private array $pagination;
    private Request $request;

    public function __construct($assetsList, $pagination, Request $request)
    {
        $this->assetsList = $assetsList;
        $this->pagination = $pagination;
        $this->request = $request;
    }

    public function renderTable()
    {

        try {
            $direction = $this->request->get('direction', 'desc');

            // Validate sort parameter - include both ascending and descending options
            $sortColumns = ['-id', '-asset_type', '-asset_value', '-created_at', '-updated_at'];

            try {
                $table = new DataTable($this->assetsList, $this->getColumns(),  $this->request, $this->pagination, $sortColumns, $direction);
            } catch (\Throwable $th) {
                dd($th, $this->assetsList, $this->getColumns(), $this->pagination, $this->request, $sortColumns, $direction);
            }


            $table->setAjaxUrl('global-assets');

            $table->setTableId('globalAssetsTable')
                ->setBreadcrumbs('Global Assets', [
                    ['label' => 'Home', 'url' => url('/dashboard')],
                    ['label' => 'Global Assets']
                ])
                ->setSearchConfig(true, 'globalAssetsSearch')
                ->setActionButtons($this->getActionButtons())
                ->setRowActions($this->getRowActionButtons())
                ->setGridConfig($this->getGridConfig());

            return $table->render();
        } catch (\Throwable $th) {
            return "<div class='alert alert-danger'>Error rendering table: {$th->getMessage()}</div>";
        }
    }

    private function getColumns()
    {
        return [
            'id' => [
                'label' => 'ID',
                'sortable' => true,
                'width' => '80px'
            ],
            'asset_type' => [
                'label' => 'Type',
                'sortable' => true,
                'formatter' => function ($value) {
                    $badgeClass = $value === 'css' ? 'bg-blue-100 text-blue-800' : 'bg-green-100 text-green-800';
                    return '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ' . $badgeClass . '">' . strtoupper(htmlspecialchars($value)) . '</span>';
                }
            ],
            'asset_value' => [
                'label' => 'Asset URL',
                'sortable' => true,
                'formatter' => function ($value) {
                    $truncated = strlen($value) > 50 ? substr($value, 0, 50) . '...' : $value;
                    return '<a href="' . htmlspecialchars($value) . '" target="_blank" class="text-blue-600 hover:text-blue-900 underline" title="' . htmlspecialchars($value) . '">' . htmlspecialchars($truncated) . '</a>';
                }
            ],
            'templates' => [
                'label' => 'Templates',
                'sortable' => false,
                'formatter' => function ($value, $row) {
                    // $value is not used here as we work with the full row data
                    if (empty($row['templates'])) {
                        return '<span class="text-gray-500">No templates</span>';
                    }

                    // $templateNames = array_map(function ($template) {
                    //     return htmlspecialchars($template['name']);
                    // }, $row['templates']);

                    $badges = array_map(function ($template) {
                        $name = htmlspecialchars($template['name']);
                        return '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800 mr-1 mb-1">' . $name . '</span>';
                    }, $row['templates']);

                    if (count($badges) <= 2) {
                        return implode('', $badges);
                    } else {
                        // $displayed = array_slice($badges, 0, 2);
                        // $remaining = count($badges) - 2;
                        // return implode('', $displayed) . ' <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800 mr-1 mb-1">+' . $remaining . '</span>';

                        $displayed = array_slice($badges, 0, 2);
                        $remaining = count($badges) - 2;

                        // Get only the remaining template names for tooltip (those not displayed)
                        $remainingTemplates = array_slice($row['templates'], 2);
                        $remainingNames = array_map(function ($template) {
                            return htmlspecialchars($template['name']);
                        }, $remainingTemplates);
                        $tooltipContent = implode(', ', $remainingNames);

                        return implode('', $displayed) .
                            ' <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800 mr-1 mb-1 cursor-pointer" title="' . $tooltipContent . '">+' . $remaining . '</span>';
                    }
                }
            ],
            'created_at' => [
                'label' => 'Created At',
                'sortable' => true,
                'formatter' => function ($value) {
                    return TableHelper::formatDate($value);
                }
            ]
        ];
    }

    private function getRowActionButtons()
    {
        return [
            [
                'icon' => '<path d="M21.174 6.812a1 1 0 0 0-3.986-3.987L3.842 16.174a2 2 0 0 0-.5.83l-1.321 4.352a.5.5 0 0 0 .623.622l4.353-1.32a2 2 0 0 0 .83-.497z"/><path d="m15 5 4 4"/>',
                'tooltip' => 'Edit',
                'type' => 'button',
                'attributes' => [
                    'data-modal-target' => 'editAssetModal',
                    'data-modal-toggle' => 'editAssetModal',
                    'data-id' => function ($row) {
                        return $row['id'] ?? '';
                    },
                    'data-asset-type' => function ($row) {
                        return $row['asset_type'] ?? '';
                    },
                    'data-asset-value' => function ($row) {
                        return $row['asset_value'] ?? '';
                    },
                    'data-templates' => function ($row) {
                        return isset($row['templates']) ? json_encode(array_column($row['templates'], 'id')) : '[]';
                    }
                ]
            ],
            [
                'icon' => '<path d="M3 6h18"/><path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"/><path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"/>',
                'tooltip' => 'Delete',
                'type' => 'button',
                'attributes' => [
                    'data-modal-target' => 'deleteAssetModal',
                    'data-modal-toggle' => 'deleteAssetModal',
                    'data-id' => function ($row) {
                        return $row['id'] ?? '';
                    }
                ]
            ]
        ];
    }

    private function getActionButtons()
    {
        return [
            [
                'label' => 'Create New Asset',
                'icon' => 'plus',
                'attributes' => [
                    'data-modal-target' => 'createAssetModal',
                    'data-modal-toggle' => 'createAssetModal'
                ]
            ]
        ];
    }

    private function getGridConfig()
    {
        return [
            'enabled' => false,
        ];
    }
}
