// Global Assets Module JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Flowbite
    const initFlowbite = window.initFlowbite || (() => {});
    initFlowbite();
    
    // Initialize Select2 if available
    if (typeof $ !== 'undefined' && $.fn.select2) {
        console.log('Initializing Select2...');

        // Initialize create modal select2
        const createSelect = $('#createTemplateIds');
        if (createSelect.length) {
            createSelect.select2({
                placeholder: 'Select templates...',
                allowClear: true,
                width: '100%',
                dropdownParent: $('#createAssetModal')
            });
            console.log('Create Select2 initialized');
        }

        // Initialize edit modal select2
        const editSelect = $('#editTemplateIds');
        if (editSelect.length) {
            editSelect.select2({
                placeholder: 'Select templates...',
                allowClear: true,
                width: '100%',
                dropdownParent: $('#editAssetModal')
            });
            console.log('Edit Select2 initialized');
        }
    } else {
        console.warn('jQuery or Select2 not available');
    }

    // Load templates for dropdowns
    loadTemplates();

    // Handle modal events
    setupModalEvents();

    // Debug: Check if templates are already loaded in the select elements
    debugTemplateSelects();
    
    // Handle edit button clicks
    document.addEventListener('click', function(e) {
        const editButton = e.target.closest('[data-modal-target="editAssetModal"]');
        if (editButton) {
            const assetId = editButton.dataset.id;
            const assetType = editButton.dataset.assetType;
            const assetValue = editButton.dataset.assetValue;
            const templates = JSON.parse(editButton.dataset.templates || '[]');
            
            // Set form values
            const editForm = document.getElementById('editAssetForm');
            if (editForm) {
                document.getElementById('editAssetId').value = assetId;
                document.getElementById('editAssetType').value = assetType;
                document.getElementById('editAssetValue').value = assetValue;
                
                // Set selected templates using Select2
                const templateSelect = $('#editTemplateIds');
                if (templateSelect.length) {
                    templateSelect.val(templates).trigger('change');
                }
            }
        }

        // Handle delete button clicks
        const deleteButton = e.target.closest('[data-modal-target="deleteAssetModal"]');
        if (deleteButton) {
            const assetId = deleteButton.dataset.id;
            const deleteForm = document.getElementById('deleteAssetForm');
            if (deleteForm) {
                document.getElementById('deleteAssetId').value = assetId;
            }
        }
    });
});

// Function to load templates for the dropdowns
async function loadTemplates() {
    try {
        // Get the base URL from the global variable or construct it
        const baseUrl = window.AppConfig?.baseUrl || window.baseUrl || '/frontcms';

        // Try to fetch templates from the API endpoint
        const response = await fetch(`${baseUrl}templates`, {
            headers: {
                'Accept': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        });

        if (response.ok) {
            const result = await response.json();
            console.log('Templates API response:', result);

            // Handle different response formats
            let templates = [];
            if (result.success && result.pagination && result.pagination.data) {
                templates = result.pagination.data;
            } else if (result.success && result.data) {
                templates = Array.isArray(result.data) ? result.data : result.data.data || [];
            } else if (result.data && Array.isArray(result.data)) {
                templates = result.data;
            }

            if (templates.length > 0) {
                console.log('Loading templates:', templates);
                populateTemplateSelects(templates);
                return;
            }
        }

        // Fallback: use mock data for development
        console.warn('Using mock template data for development');
        const mockTemplates = [
            { id: 1, name: 'Test Template' },
            { id: 2, name: 'IndiaNIC Template' },
            { id: 3, name: 'Sports Club Template' },
            { id: 4, name: 'Ship Builders Template' },
            { id: 5, name: 'First Template' }
        ];
        populateTemplateSelects(mockTemplates);

    } catch (error) {
        console.error('Error loading templates:', error);

        // Fallback: use mock data
        console.warn('Using mock template data due to error');
        const mockTemplates = [
            { id: 1, name: 'Test Template' },
            { id: 2, name: 'IndiaNIC Template' },
            { id: 3, name: 'Sports Club Template' }
        ];
        populateTemplateSelects(mockTemplates);
    }
}

// Global variable to store templates
let availableTemplates = [];

// Function to populate template select elements
function populateTemplateSelects(templates) {
    availableTemplates = templates;
    console.log('Populating templates:', templates);

    // Populate create modal select
    const createSelect = document.getElementById('createTemplateIds');
    console.log('Create select:', createSelect);
    if (createSelect) {
        // Clear existing options
        createSelect.innerHTML = '';

        // Add templates as options
        templates.forEach(template => {
            const option = document.createElement('option');
            option.value = template.id;
            option.textContent = template.name;
            createSelect.appendChild(option);
        });

        // Refresh Select2 if it's initialized
        if (typeof $ !== 'undefined' && $(createSelect).hasClass('select2-hidden-accessible')) {
            $(createSelect).trigger('change');
        }
    }

    // Populate edit modal select
    const editSelect = document.getElementById('editTemplateIds');
    if (editSelect) {
        // Clear existing options
        editSelect.innerHTML = '';

        // Add templates as options
        templates.forEach(template => {
            const option = document.createElement('option');
            option.value = template.id;
            option.textContent = template.name;
            editSelect.appendChild(option);
        });

        // Refresh Select2 if it's initialized
        if (typeof $ !== 'undefined' && $(editSelect).hasClass('select2-hidden-accessible')) {
            $(editSelect).trigger('change');
        }
    }

    console.log('Templates populated successfully');
}

// Setup modal events
function setupModalEvents() {
    // Handle create modal show event
    const createModal = document.getElementById('createAssetModal');
    if (createModal) {
        createModal.addEventListener('shown.bs.modal', function () {
            console.log('Create modal shown');
            // Refresh Select2 when modal is shown
            if (typeof $ !== 'undefined') {
                $('#createTemplateIds').select2('open').select2('close');
            }
        });
    }

    // Handle edit modal show event
    const editModal = document.getElementById('editAssetModal');
    if (editModal) {
        editModal.addEventListener('shown.bs.modal', function () {
            console.log('Edit modal shown');
            // Refresh Select2 when modal is shown
            if (typeof $ !== 'undefined') {
                $('#editTemplateIds').select2('open').select2('close');
            }
        });
    }

    // Handle create modal reset
    const createForm = document.getElementById('createAssetForm');
    if (createForm) {
        createForm.addEventListener('reset', function() {
            setTimeout(() => {
                if (typeof $ !== 'undefined') {
                    $('#createTemplateIds').val(null).trigger('change');
                }
            }, 100);
        });
    }
}

// Debug function to check template selects
function debugTemplateSelects() {
    const createSelect = document.getElementById('createTemplateIds');
    const editSelect = document.getElementById('editTemplateIds');

    console.log('=== Template Select Debug ===');
    console.log('Create select element:', createSelect);
    console.log('Create select options count:', createSelect ? createSelect.options.length : 'N/A');
    if (createSelect && createSelect.options.length > 0) {
        console.log('Create select options:', Array.from(createSelect.options).map(opt => ({value: opt.value, text: opt.text})));
    }

    console.log('Edit select element:', editSelect);
    console.log('Edit select options count:', editSelect ? editSelect.options.length : 'N/A');
    if (editSelect && editSelect.options.length > 0) {
        console.log('Edit select options:', Array.from(editSelect.options).map(opt => ({value: opt.value, text: opt.text})));
    }

    // Check if Select2 is initialized
    if (typeof $ !== 'undefined') {
        console.log('Create Select2 initialized:', createSelect ? $(createSelect).hasClass('select2-hidden-accessible') : false);
        console.log('Edit Select2 initialized:', editSelect ? $(editSelect).hasClass('select2-hidden-accessible') : false);
    }
    console.log('=== End Debug ===');
}

// Helper function to set selected templates in edit modal
function setEditModalTemplates(templateIds) {
    const editSelect = document.getElementById('editTemplateIds');
    if (editSelect && templateIds) {
        // Clear current selection
        $(editSelect).val(null).trigger('change');

        // Set new selection
        const idsArray = Array.isArray(templateIds) ? templateIds : templateIds.split(',').filter(id => id);
        $(editSelect).val(idsArray).trigger('change');
    }
}

// Helper function to clear template selections
function clearTemplateSelections() {
    const createSelect = document.getElementById('createTemplateIds');
    const editSelect = document.getElementById('editTemplateIds');

    if (createSelect && typeof $ !== 'undefined') {
        $(createSelect).val(null).trigger('change');
    }

    if (editSelect && typeof $ !== 'undefined') {
        $(editSelect).val(null).trigger('change');
    }
}

// Helper function to escape HTML
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}
