/**
 * PagesBlocks Module JavaScript
 * Handles page block operations like create, update, delete, and position management
 */

document.addEventListener('DOMContentLoaded', function() {
    window.PagesBlocks = {
        
        /**
         * Create a new page block
         */
        create: function(pageId, blockId, position = 0, additionalData = {}) {
            const data = {
                page_id: pageId,
                block_id: blockId,
                position: position,
                css_code: additionalData.css_code || '',
                js_code: additionalData.js_code || '',
                json_code: additionalData.json_code || '{}',
                classes: additionalData.classes || ''
            };

            return fetch(`${baseUrl}pages-blocks/create`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': csrfToken
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .catch(error => {
                console.error('Error creating page block:', error);
                throw error;
            });
        },

        /**
         * Update a page block
         */
        update: function(id, data) {
            const updateData = {
                id: id,
                ...data
            };

            return fetch(`${baseUrl}pages-blocks/update`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': csrfToken
                },
                body: JSON.stringify(updateData)
            })
            .then(response => response.json())
            .catch(error => {
                console.error('Error updating page block:', error);
                throw error;
            });
        },

        /**
         * Delete a page block
         */
        delete: function(id) {
            return fetch(`${baseUrl}pages-blocks/delete`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': csrfToken
                },
                body: JSON.stringify({ id: id })
            })
            .then(response => response.json())
            .catch(error => {
                console.error('Error deleting page block:', error);
                throw error;
            });
        },

        /**
         * Get page blocks for a specific page
         */
        getPageBlocks: function(pageId) {
            return fetch(`${baseUrl}pages-blocks/get?page_id=${pageId}`, {
                headers: {
                    'X-CSRF-TOKEN': csrfToken
                }
            })
            .then(response => response.json())
            .catch(error => {
                console.error('Error fetching page blocks:', error);
                throw error;
            });
        },

        /**
         * Save all page blocks for a page (bulk operation)
         */
        savePageBlocks: function(pageId, blocks) {
            return fetch(`${baseUrl}pages-blocks/save-page-blocks`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': csrfToken
                },
                body: JSON.stringify({
                    page_id: pageId,
                    blocks: blocks
                })
            })
            .then(response => response.json())
            .catch(error => {
                console.error('Error saving page blocks:', error);
                throw error;
            });
        },

        /**
         * Update positions for multiple blocks
         */
        updatePositions: function(blocks) {
            return fetch(`${baseUrl}pages-blocks/update-positions`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': csrfToken
                },
                body: JSON.stringify({ blocks: blocks })
            })
            .then(response => response.json())
            .catch(error => {
                console.error('Error updating block positions:', error);
                throw error;
            });
        },

        /**
         * Update block classes
         */
        updateClasses: function(id, classes) {
            return fetch(`${baseUrl}pages-blocks/update-classes`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': csrfToken
                },
                body: JSON.stringify({
                    id: id,
                    classes: classes
                })
            })
            .then(response => response.json())
            .catch(error => {
                console.error('Error updating block classes:', error);
                throw error;
            });
        },

        /**
         * Clone page blocks from source page to target page
         */
        clonePageBlocks: function(sourcePageId, targetPageId) {
            return fetch(`${baseUrl}pages-blocks/clone`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': csrfToken
                },
                body: JSON.stringify({
                    source_page_id: sourcePageId,
                    target_page_id: targetPageId
                })
            })
            .then(response => response.json())
            .catch(error => {
                console.error('Error cloning page blocks:', error);
                throw error;
            });
        }
    };
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = window.PagesBlocks;
} 