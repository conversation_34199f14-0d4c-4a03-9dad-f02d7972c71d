/**
 * PagesBlocks Module CSS
 * Styles for page block operations and drag-and-drop functionality
 */

/* Page Block Container */
.page-block-container {
    position: relative;
    margin-bottom: 10px;
    padding: 10px;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    background-color: #fff;
}

.page-block-container.dragging {
    opacity: 0.5;
    transform: rotate(2deg);
}

.page-block-container.drag-over {
    border-color: #007cba;
    background-color: #f0f8ff;
}

/* Page Block Header */
.page-block-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    padding: 5px 10px;
    background-color: #f5f5f5;
    border-radius: 3px;
}

.page-block-title {
    font-weight: bold;
    font-size: 14px;
    color: #333;
}

.page-block-actions {
    display: flex;
    gap: 5px;
}

.page-block-actions .btn {
    padding: 4px 8px;
    font-size: 12px;
    border-radius: 3px;
    cursor: pointer;
}

.page-block-actions .btn-edit {
    background-color: #007cba;
    color: white;
    border: none;
}

.page-block-actions .btn-delete {
    background-color: #dc3545;
    color: white;
    border: none;
}

.page-block-actions .btn-move {
    background-color: #6c757d;
    color: white;
    border: none;
    cursor: move;
}

/* Page Block Content */
.page-block-content {
    min-height: 40px;
    padding: 10px;
    border: 1px dashed #ccc;
    border-radius: 3px;
    background-color: #fafafa;
}

.page-block-content.has-content {
    border: 1px solid #ddd;
    background-color: #fff;
}

/* Page Block Position Indicator */
.page-block-position {
    position: absolute;
    top: -10px;
    right: -10px;
    background-color: #007cba;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    font-weight: bold;
}

/* Drag Drop Area */
.drag-drop-area {
    min-height: 100px;
    border: 2px dashed #ccc;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #666;
    font-size: 14px;
    margin: 10px 0;
    transition: all 0.3s ease;
}

.drag-drop-area.active {
    border-color: #007cba;
    background-color: #f0f8ff;
    color: #007cba;
}

.drag-drop-area.drag-over {
    border-color: #28a745;
    background-color: #f8fff8;
    color: #28a745;
}

/* Block List */
.blocks-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 15px;
    margin-top: 20px;
}

.block-item {
    padding: 15px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: #fff;
    cursor: pointer;
    transition: all 0.3s ease;
}

.block-item:hover {
    border-color: #007cba;
    box-shadow: 0 2px 4px rgba(0, 124, 186, 0.1);
}

.block-item.selected {
    border-color: #007cba;
    background-color: #f0f8ff;
}

.block-item .block-name {
    font-weight: bold;
    margin-bottom: 5px;
}

.block-item .block-category {
    font-size: 12px;
    color: #666;
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #ccc;
    border-top: 2px solid #007cba;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Error States */
.error-message {
    background-color: #f8d7da;
    color: #721c24;
    padding: 10px;
    border-radius: 4px;
    margin: 10px 0;
    border: 1px solid #f5c6cb;
}

.success-message {
    background-color: #d4edda;
    color: #155724;
    padding: 10px;
    border-radius: 4px;
    margin: 10px 0;
    border: 1px solid #c3e6cb;
}

/* Responsive Design */
@media (max-width: 768px) {
    .page-block-header {
        flex-direction: column;
        gap: 10px;
    }
    
    .page-block-actions {
        width: 100%;
        justify-content: center;
    }
    
    .blocks-list {
        grid-template-columns: 1fr;
    }
}

/* Sortable Styles */
.sortable-ghost {
    opacity: 0.4;
}

.sortable-chosen {
    cursor: move;
}

.sortable-drag {
    opacity: 0.8;
    transform: rotate(2deg);
}

/* Form Styles */
.page-block-form {
    background-color: #f8f9fa;
    padding: 20px;
    border-radius: 4px;
    border: 1px solid #dee2e6;
}

.page-block-form .form-group {
    margin-bottom: 15px;
}

.page-block-form label {
    display: block;
    font-weight: bold;
    margin-bottom: 5px;
    color: #333;
}

.page-block-form input,
.page-block-form textarea,
.page-block-form select {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ccc;
    border-radius: 4px;
    font-size: 14px;
}

.page-block-form textarea {
    min-height: 100px;
    resize: vertical;
}

.page-block-form .btn-group {
    display: flex;
    gap: 10px;
    margin-top: 20px;
}

.page-block-form .btn {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
}

.page-block-form .btn-primary {
    background-color: #007cba;
    color: white;
}

.page-block-form .btn-secondary {
    background-color: #6c757d;
    color: white;
} 