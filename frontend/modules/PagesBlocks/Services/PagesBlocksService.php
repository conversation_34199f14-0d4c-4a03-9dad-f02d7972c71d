<?php

namespace Modules\PagesBlocks\Services;

use App\Services\ApiClient;
use Core\ServiceContainer;
use Core\Request;

class PagesBlocksService
{
    protected $api;
    protected $endpoint = 'page_blocks';

    public function __construct()
    {
        $this->api = ServiceContainer::resolve(ApiClient::class);
    }

    /**
     * Get all page blocks with pagination
     */
    public function getData(Request $request)
    {
        $sort = $request->get('sort') ?? '-id';
        $itemsPerPage = $request->get('limit') ?? 10;

        $params = [
            'page' => $request->get('page') ?? 1,
            'sort' => $sort,
            'limit' => $itemsPerPage
        ];

        // Add filters if provided
        $pageId = $request->get('page_id');
        if ($pageId) {
            $params['page_id'] = $pageId;
        }

        $blockId = $request->get('block_id');
        if ($blockId) {
            $params['block_id'] = $blockId;
        }

        $response = $this->api->get($this->endpoint, $params);

        return $this->formatResponse($response, $itemsPerPage);
    }

    /**
     * Get all page blocks for a specific page
     */
    public function getByPageId($pageId, Request $request = null)
    {
        if (!$request) {
            $request = new Request();
        }

        $params = [
            'page_id' => $pageId,
            'sort' => $request->get('sort') ?? 'position',
            'limit' => $request->get('limit') ?? 1000
        ];

        $response = $this->api->get($this->endpoint, $params);

        return $this->formatResponse($response, 1000);
    }

    /**
     * Get all page blocks for a specific block
     */
    public function getByBlockId($blockId, Request $request = null)
    {
        if (!$request) {
            $request = new Request();
        }

        $params = [
            'block_id' => $blockId,
            'sort' => $request->get('sort') ?? '-id',
            'limit' => $request->get('limit') ?? 1000
        ];

        $response = $this->api->get($this->endpoint, $params);

        return $this->formatResponse($response, 1000);
    }

    /**
     * Create a new page block
     */
    public function create(Request $request)
    {
        try {
            $data = [
                'page_id' => $request->post('page_id'),
                'block_id' => $request->post('block_id'),
                'position' => $request->post('position') ?? 0,
                'css_code' => $request->post('css_code') ?? '',
                'js_code' => $request->post('js_code') ?? '',
                'json_code' => $request->post('json_code') ?? '{}',
                'classes' => $request->post('classes') ?? ''
            ];

            // Validate required fields
            if (empty($data['page_id'])) {
                return ['success' => false, 'message' => 'Page ID is required'];
            }

            if (empty($data['block_id'])) {
                return ['success' => false, 'message' => 'Block ID is required'];
            }

            $response = $this->api->post($this->endpoint, $data);

            if (isset($response['data'])) {
                return ['success' => true, 'data' => $response['data']];
            }

            return ['success' => false, 'message' => 'Failed to create page block'];
        } catch (\Exception $e) {
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    /**
     * Update a page block
     */
    public function update(Request $request)
    {
        try {
            $id = $request->post('id');
            if (empty($id)) {
                return ['success' => false, 'message' => 'Page block ID is required'];
            }

            $data = [
                'position' => $request->post('position'),
                'css_code' => $request->post('css_code'),
                'js_code' => $request->post('js_code'),
                'json_code' => $request->post('json_code'),
                'classes' => $request->post('classes')
            ];

            // Remove null values
            $data = array_filter($data, function($value) {
                return $value !== null;
            });

            $response = $this->api->put($this->endpoint . '/' . $id, $data);

            if (isset($response['data'])) {
                return ['success' => true, 'data' => $response['data']];
            }

            return ['success' => false, 'message' => 'Failed to update page block'];
        } catch (\Exception $e) {
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    /**
     * Delete a page block
     */
    public function delete(Request $request)
    {
        try {
            $id = $request->post('id');
            if (empty($id)) {
                return ['success' => false, 'message' => 'Page block ID is required'];
            }

            $response = $this->api->delete($this->endpoint . '/' . $id);

            return ['success' => true, 'message' => 'Page block deleted successfully'];
        } catch (\Exception $e) {
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    /**
     * Save all page blocks for a page (bulk operation)
     */
    public function savePageBlocks($pageId, $blocks)
    {
        try {
            // First, get existing blocks for this page
            $existingResult = $this->getByPageId($pageId);
            
            // Delete existing blocks
            if ($existingResult['status'] === 'success' && isset($existingResult['data']['data'])) {
                foreach ($existingResult['data']['data'] as $existingBlock) {
                    $deleteRequest = new Request();
                    $deleteRequest->setPostData(['id' => $existingBlock['id']]);
                    $this->delete($deleteRequest);
                }
            }

            // Create new blocks
            $results = [];
            foreach ($blocks as $index => $block) {
                $createRequest = new Request();
                $createRequest->setPostData([
                    'page_id' => $pageId,
                    'block_id' => $block['block_id'],
                    'position' => $block['position'] ?? $index,
                    'css_code' => $block['css_code'] ?? '',
                    'js_code' => $block['js_code'] ?? '',
                    'json_code' => $block['json_code'] ?? '{}',
                    'classes' => $block['classes'] ?? ''
                ]);

                $result = $this->create($createRequest);
                $results[] = $result;
            }

            return ['success' => true, 'data' => $results];
        } catch (\Exception $e) {
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    /**
     * Update positions for multiple blocks
     */
    public function updatePositions($blocks)
    {
        try {
            $results = [];
            
            foreach ($blocks as $block) {
                if (isset($block['id']) && isset($block['position'])) {
                    $updateRequest = new Request();
                    $updateRequest->setPostData([
                        'id' => $block['id'],
                        'position' => $block['position']
                    ]);

                    $result = $this->update($updateRequest);
                    $results[] = $result;
                }
            }

            return ['success' => true, 'data' => $results];
        } catch (\Exception $e) {
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    /**
     * Clone page blocks from source page to target page
     */
    public function clonePageBlocks($sourcePageId, $targetPageId)
    {
        try {
            // Get blocks from source page
            $sourceBlocks = $this->getByPageId($sourcePageId);
            
            if ($sourceBlocks['status'] !== 'success') {
                return ['success' => false, 'message' => 'Failed to get source page blocks'];
            }

            $blocksToClone = [];
            foreach ($sourceBlocks['data']['data'] as $block) {
                $blocksToClone[] = [
                    'block_id' => $block['block_id'],
                    'position' => $block['position'],
                    'css_code' => $block['css_code'],
                    'js_code' => $block['js_code'],
                    'json_code' => $block['json_code'],
                    'classes' => $block['classes']
                ];
            }

            // Save to target page
            return $this->savePageBlocks($targetPageId, $blocksToClone);
        } catch (\Exception $e) {
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    /**
     * Format API response
     */
    private function formatResponse($response, $itemsPerPage)
    {
        if (!$response || !isset($response['data'])) {
            return [
                'status' => 'error',
                'message' => 'No data received from API'
            ];
        }

        // Handle direct data array or nested data structure
        if (isset($response['data']['data'])) {
            $data = $response['data']['data'];
            $paginationData = $response['data'];
        } else {
            $data = $response['data'];
            $paginationData = $response;
        }

        return [
            'status' => 'success',
            'data' => [
                'data' => $data,
                'current_page' => $paginationData['current_page'] ?? 1,
                'per_page' => $paginationData['per_page'] ?? $itemsPerPage,
                'from' => $paginationData['from'] ?? null,
                'to' => $paginationData['to'] ?? null,
                'total' => $paginationData['total'] ?? count($data),
                'next_page_url' => $paginationData['next_page_url'] ?? null,
                'prev_page_url' => $paginationData['prev_page_url'] ?? null,
                'first_page_url' => $paginationData['first_page_url'] ?? null,
                'last_page_url' => $paginationData['last_page_url'] ?? null
            ]
        ];
    }
} 