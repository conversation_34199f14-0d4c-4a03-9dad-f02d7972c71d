<?php

use Core\Router;
use Middleware\Authenticate;
use Mo<PERSON>les\PagesBlocks\Controllers\PagesBlocksController;



// Pages Blocks routes
$router->group('pages-blocks', function($router) {
    // GET routes
    $router->get('/', [PagesBlocksController::class, 'getPageBlocks'], [Authenticate::class]);
    $router->get('get', [PagesBlocksController::class, 'getPageBlocks'], [Authenticate::class]);
    
    // POST routes for page block operations
    $router->post('create', [PagesBlocksController::class, 'create'], [Authenticate::class]);
    $router->post('update', [PagesBlocksController::class, 'update'], [Authenticate::class]);
    $router->post('delete', [PagesBlocksController::class, 'delete'], [Authenticate::class]);
    
    // Bulk operations
    $router->post('save', [PagesBlocksController::class, 'savePageBlocks'], [Authenticate::class]);
    $router->post('save-page-blocks', [PagesBlocksController::class, 'savePageBlocks'], [Authenticate::class]);
    $router->post('update-positions', [PagesBlocksController::class, 'updatePositions'], [Authenticate::class]);
    $router->post('update-classes', [PagesBlocksController::class, 'updateClasses'], [Authenticate::class]);
    $router->post('clone', [PagesBlocksController::class, 'clonePageBlocks'], [Authenticate::class]);
});

// Alternative route patterns for backward compatibility
$router->post('save-block-to-page', [PagesBlocksController::class, 'create'], [Authenticate::class]);
$router->post('update-block', [PagesBlocksController::class, 'update'], [Authenticate::class]);
$router->post('delete-block', [PagesBlocksController::class, 'delete'], [Authenticate::class]);
$router->post('update-block-position', [PagesBlocksController::class, 'updatePositions'], [Authenticate::class]);
$router->post('update-block-classes', [PagesBlocksController::class, 'updateClasses'], [Authenticate::class]); 