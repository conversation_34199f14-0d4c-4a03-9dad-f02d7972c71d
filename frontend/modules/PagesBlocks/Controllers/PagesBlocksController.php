<?php

namespace Modules\PagesBlocks\Controllers;

use Core\Controller;
use Core\Request;
use Modules\PagesBlocks\Services\PagesBlocksService;

class PagesBlocksController extends Controller
{
    protected $pagesBlocksService;

    protected $defaultPagination = [
        'current_page' => 1,
        'per_page' => 10,
        'total' => 0,
        'from' => null,
        'to' => null,
        'next_page_url' => null,
        'prev_page_url' => null,
        'first_page_url' => null,
        'last_page_url' => null
    ];

    public function __construct()
    {
        $this->pagesBlocksService = new PagesBlocksService();
    }

    /**
     * Get page blocks for a specific page (AJAX endpoint)
     */
    public function getPageBlocks(Request $request)
    {
        try {
            $pageId = $request->get('page_id');
            
            if (!$pageId) {
                return $this->jsonResponse([
                    'success' => false,
                    'message' => 'Page ID is required'
                ]);
            }

            $result = $this->pagesBlocksService->getByPageId($pageId, $request);
            
            if ($result['status'] === 'success') {
                return $this->jsonResponse([
                    'success' => true,
                    'data' => $result['data']['data'],
                    'pagination' => $result['data']
                ]);
            }

            return $this->jsonResponse([
                'success' => false,
                'message' => $result['message'] ?? 'Failed to fetch page blocks'
            ]);
        } catch (\Exception $e) {
            return $this->jsonResponse([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
    }

    /**
     * Create a new page block
     */
    public function create(Request $request)
    {
        try {
            $result = $this->pagesBlocksService->create($request);
            
            if ($result['success']) {
                if ($request->isAjax()) {
                    return $this->jsonResponse([
                        'success' => true,
                        'data' => $result['data'],
                        'message' => 'Page block created successfully'
                    ]);
                }

                // Redirect for regular form submission
                $pageId = $request->post('page_id');
                $redirectUrl = url("pages/view?id=" . $pageId);
                header('Location: ' . $redirectUrl);
                exit();
            }

            if ($request->isAjax()) {
                return $this->jsonResponse([
                    'success' => false,
                    'message' => $result['message']
                ]);
            }

            // Redirect with error
            $pageId = $request->post('page_id');
            $errorUrl = url("pages/view?id=" . $pageId . "&error=" . urlencode($result['message']));
            header('Location: ' . $errorUrl);
            exit();
        } catch (\Exception $e) {
            if ($request->isAjax()) {
                return $this->jsonResponse([
                    'success' => false,
                    'message' => $e->getMessage()
                ]);
            }

            $pageId = $request->post('page_id');
            $errorUrl = url("pages/view?id=" . $pageId . "&error=" . urlencode($e->getMessage()));
            header('Location: ' . $errorUrl);
            exit();
        }
    }

    /**
     * Update a page block
     */
    public function update(Request $request)
    {
        try {
            $result = $this->pagesBlocksService->update($request);
            
            if ($result['success']) {
                if ($request->isAjax()) {
                    return $this->jsonResponse([
                        'success' => true,
                        'data' => $result['data'],
                        'message' => 'Page block updated successfully'
                    ]);
                }

                // Redirect for regular form submission
                $pageId = $request->post('page_id');
                $redirectUrl = url("pages/view?id=" . $pageId);
                header('Location: ' . $redirectUrl);
                exit();
            }

            if ($request->isAjax()) {
                return $this->jsonResponse([
                    'success' => false,
                    'message' => $result['message']
                ]);
            }

            // Redirect with error
            $pageId = $request->post('page_id');
            $errorUrl = url("pages/view?id=" . $pageId . "&error=" . urlencode($result['message']));
            header('Location: ' . $errorUrl);
            exit();
        } catch (\Exception $e) {
            if ($request->isAjax()) {
                return $this->jsonResponse([
                    'success' => false,
                    'message' => $e->getMessage()
                ]);
            }

            $pageId = $request->post('page_id');
            $errorUrl = url("pages/view?id=" . $pageId . "&error=" . urlencode($e->getMessage()));
            header('Location: ' . $errorUrl);
            exit();
        }
    }

    /**
     * Delete a page block
     */
    public function delete(Request $request)
    {
        try {
            $result = $this->pagesBlocksService->delete($request);
            
            if ($result['success']) {
                if ($request->isAjax()) {
                    return $this->jsonResponse([
                        'success' => true,
                        'message' => 'Page block deleted successfully'
                    ]);
                }

                // Redirect for regular form submission
                $pageId = $request->post('page_id');
                $redirectUrl = url("pages/view?id=" . $pageId);
                header('Location: ' . $redirectUrl);
                exit();
            }

            if ($request->isAjax()) {
                return $this->jsonResponse([
                    'success' => false,
                    'message' => $result['message']
                ]);
            }

            // Redirect with error
            $pageId = $request->post('page_id');
            $errorUrl = url("pages/view?id=" . $pageId . "&error=" . urlencode($result['message']));
            header('Location: ' . $errorUrl);
            exit();
        } catch (\Exception $e) {
            if ($request->isAjax()) {
                return $this->jsonResponse([
                    'success' => false,
                    'message' => $e->getMessage()
                ]);
            }

            $pageId = $request->post('page_id');
            $errorUrl = url("pages/view?id=" . $pageId . "&error=" . urlencode($e->getMessage()));
            header('Location: ' . $errorUrl);
            exit();
        }
    }

    /**
     * Save all page blocks for a page (bulk operation)
     */
    public function savePageBlocks(Request $request)
    {
        try {
            $pageId = $request->post('page_id');
            $blocks = $request->post('blocks');

            if (!$pageId) {
                return $this->jsonResponse([
                    'success' => false,
                    'message' => 'Page ID is required'
                ]);
            }

            if (!is_array($blocks)) {
                $blocks = json_decode($blocks, true) ?: [];
            }

            $result = $this->pagesBlocksService->savePageBlocks($pageId, $blocks);
            
            if ($result['success']) {
                return $this->jsonResponse([
                    'success' => true,
                    'data' => $result['data'],
                    'message' => 'Page blocks saved successfully'
                ]);
            }

            return $this->jsonResponse([
                'success' => false,
                'message' => $result['message']
            ]);
        } catch (\Exception $e) {
            return $this->jsonResponse([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
    }

    /**
     * Update positions for multiple blocks
     */
    public function updatePositions(Request $request)
    {
        try {
            $blocks = $request->post('blocks');

            if (!is_array($blocks)) {
                $blocks = json_decode($blocks, true) ?: [];
            }

            $result = $this->pagesBlocksService->updatePositions($blocks);
            
            if ($result['success']) {
                return $this->jsonResponse([
                    'success' => true,
                    'data' => $result['data'],
                    'message' => 'Block positions updated successfully'
                ]);
            }

            return $this->jsonResponse([
                'success' => false,
                'message' => $result['message']
            ]);
        } catch (\Exception $e) {
            return $this->jsonResponse([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
    }

    /**
     * Update block classes
     */
    public function updateClasses(Request $request)
    {
        try {
            $id = $request->post('id');
            $classes = $request->post('classes');

            if (!$id) {
                return $this->jsonResponse([
                    'success' => false,
                    'message' => 'Page block ID is required'
                ]);
            }

            // Create a new request with only the classes data
            $updateRequest = new Request();
            $updateRequest->setPostData([
                'id' => $id,
                'classes' => $classes
            ]);

            $result = $this->pagesBlocksService->update($updateRequest);
            
            if ($result['success']) {
                return $this->jsonResponse([
                    'success' => true,
                    'data' => $result['data'],
                    'message' => 'Block classes updated successfully'
                ]);
            }

            return $this->jsonResponse([
                'success' => false,
                'message' => $result['message']
            ]);
        } catch (\Exception $e) {
            return $this->jsonResponse([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
    }

    /**
     * Clone page blocks from source page to target page
     */
    public function clonePageBlocks(Request $request)
    {
        try {
            $sourcePageId = $request->post('source_page_id');
            $targetPageId = $request->post('target_page_id');

            if (!$sourcePageId || !$targetPageId) {
                return $this->jsonResponse([
                    'success' => false,
                    'message' => 'Source and target page IDs are required'
                ]);
            }

            $result = $this->pagesBlocksService->clonePageBlocks($sourcePageId, $targetPageId);
            
            if ($result['success']) {
                return $this->jsonResponse([
                    'success' => true,
                    'data' => $result['data'],
                    'message' => 'Page blocks cloned successfully'
                ]);
            }

            return $this->jsonResponse([
                'success' => false,
                'message' => $result['message']
            ]);
        } catch (\Exception $e) {
            return $this->jsonResponse([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
    }
} 