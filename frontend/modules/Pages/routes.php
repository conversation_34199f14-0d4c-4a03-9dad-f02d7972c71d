<?php

use Core\Router;
use Middleware\Authenticate;
use Modules\Pages\Controllers\PageController;
use Modules\Pages\Controllers\PreviewController;

$router->group('pages', function($router) {
    $router->get('/', [PageController::class, 'index'], [Authenticate::class]);
    $router->get('/view', [PageController::class, 'builder'], [Authenticate::class]);
    $router->post('/create', [PageController::class, 'create'], [Authenticate::class]);
    $router->post('/update', [PageController::class, 'update'], [Authenticate::class]);
    $router->post('/delete', [PageController::class, 'delete'], [Authenticate::class]);
    $router->post('/publish', [PageController::class, 'publish'], [Authenticate::class]);
    $router->get('/by-template', [PageController::class, 'getByTemplate'], [Authenticate::class]);
    
    // Preview routes
    $router->get('/preview', [PreviewController::class, 'index']); // No auth for preview
    $router->post('/preview/ajax', [PreviewController::class, 'ajax'], [Authenticate::class]);
});