<?php

namespace Modules\Pages\Services;

use Core\Request;
use Package\GridTable\Src\DataTable;
use Package\GridTable\Src\Helpers\TableHelper;

class PageTableService
{
    private array $pagesList;
    private array $pagination;
    private Request $request;
    private array $templatesList;

    public function __construct($pagesList, $pagination, Request $request, $templatesList = [])
    {
        $this->pagesList = $pagesList;
        $this->pagination = $pagination;
        $this->request = $request;
        $this->templatesList = $templatesList;
    }

    public function renderTable()
    {
        try {
            $direction = $this->request->get('direction', 'desc');
            
            // Validate sort parameter 
            $sortColumns = ['-id', '-name', '-slug', '-template_id', '-status', '-created_at', '-updated_at'];
            
            $table = new DataTable($this->pagesList, $this->getColumns(), $this->request, $this->pagination, $sortColumns, $direction);

            $table->setAjaxUrl('pages');
            
            $table->setTableId('pagesTable')
                ->setBreadcrumbs('Pages', [
                    ['label' => 'Home', 'url' => url('/dashboard')],
                    ['label' => 'Pages']
                ])
                ->setSearchConfig(true, 'searchPages')
                ->setActionButtons($this->getActionButtons())
                ->setRowActions($this->getRowActionButtons())
                ->setGridConfig($this->getGridConfig());

            return $table->render();
        } catch (\Throwable $th) {
            return "<div class='alert alert-danger'>Error rendering table: {$th->getMessage()}</div>";
        }
    }

    private function getColumns()
    {
        return [
            'name' => [
                'label' => 'Name',
                'sortable' => true
            ],
            'slug' => [
                'label' => 'Slug',
                'sortable' => true
            ],
            'template_id' => [
                'label' => 'Template',
                'sortable' => true,
                'formatter' => function($value) {
                    if (empty($value)) return '-';
                    
                    foreach ($this->templatesList as $template) {
                        if ($template['id'] == $value) {
                            return htmlspecialchars($template['name']);
                        }
                    }
                    return htmlspecialchars($value);
                }
            ],
            'status' => [
                'label' => 'Status',
                'sortable' => true,
                'formatter' => function($value) {
                    $status = ($value === 'active') ? 'Active' : 'Inactive';
                    
                    return TableHelper::formatStatusBadge($status, [
                        'active' => [
                            'bg' => 'bg-green-100',
                            'text' => 'text-green-800'
                        ],
                        'inactive' => [
                            'bg' => 'bg-red-100',
                            'text' => 'text-red-800'
                        ]
                    ]);
                }
            ],
            'description' => [
                'label' => 'Description',
                'sortable' => false,
                'formatter' => function($value) {
                    return !empty($value) ? htmlspecialchars(substr($value, 0, 50)) . (strlen($value) > 50 ? '...' : '') : '-';
                }
            ],
            'created_at' => [
                'label' => 'Created At',
                'sortable' => true,
                'formatter' => function($value) {
                    return TableHelper::formatDate($value);
                }
            ],
            'updated_at' => [
                'label' => 'Updated At',
                'sortable' => true,
                'formatter' => function($value) {
                    return TableHelper::formatDate($value);
                }
            ]
        ];
    }

    private function getRowActionButtons()
    {
        return [
            [
                'icon' => '<path d="M21.174 6.812a1 1 0 0 0-3.986-3.987L3.842 16.174a2 2 0 0 0-.5.83l-1.321 4.352a.5.5 0 0 0 .623.622l4.353-1.32a2 2 0 0 0 .83-.497z"/><path d="m15 5 4 4"/>',
                'tooltip' => 'Edit',
                'type' => 'button',
                'attributes' => [
                    'data-modal-target' => 'editPageModal',
                    'data-modal-toggle' => 'editPageModal',
                    'data-id' => function($row) { return $row['id'] ?? ''; },
                    'data-name' => function($row) { return $row['name'] ?? ''; },
                    'data-slug' => function($row) { return $row['slug'] ?? ''; },
                    'data-status' => function($row) { return $row['status'] ?? 'inactive'; },
                    'data-template-id' => function($row) { return $row['template_id'] ?? ''; },
                    'data-description' => function($row) { return $row['description'] ?? ''; }
                ]
            ],
            [
                'icon' => '<path d="M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0"/><circle cx="12" cy="12" r="3"/>',
                'tooltip' => 'View',
                'type' => 'link',
                'urlFormatter' => function($row) { return url('/pages/view?id=' . ($row['id'] ?? '')); }
            ],
            [
                'icon' => '<path d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/><path d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>',
                'tooltip' => 'Preview',
                'type' => 'link',
                'target' => '_blank',
                'urlFormatter' => function($row) { return url('/preview?type=page&id=' . ($row['id'] ?? '') . '&preview=true'); }
            ],
            [
                'icon' => '<path d="M3 6h18"/><path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"/><path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"/>',
                'tooltip' => 'Delete',
                'type' => 'button',
                'attributes' => [
                    'data-modal-target' => 'deletePageModal',
                    'data-modal-toggle' => 'deletePageModal',
                    'data-id' => function($row) { return $row['id'] ?? ''; }
                ]
            ]
        ];
    }

    private function getActionButtons()
    {
        return [
            [
                'label' => 'Create New Page',
                'icon' => 'plus',
                'attributes' => [
                    'data-modal-target' => 'createPageModal',
                    'data-modal-toggle' => 'createPageModal'
                ]
            ]
        ];
    }

    private function getGridConfig()
    {
        return [
            'enabled' => false,
        ];
    }
}