<?php

namespace Modules\Pages\Services;

use App\Services\ApiClient;
use Core\ServiceContainer;
use Core\Request;
use Exception;

class PageService
{
    protected $api;
    protected $endpoint = 'pages';

    public function __construct()
    {
        $this->api = ServiceContainer::resolve(ApiClient::class);
    }

    public function getData(Request $request)
    {
        $sort = $request->get('sort') ?? '-id';
        $itemsPerPage = $request->get('limit') ?? 10;

        $params = [
            'page' => $request->get('page') ?? 1,
            'sort' => $sort,
            'limit' => $itemsPerPage
        ];

        try {
            $response = $this->api->get($this->endpoint, $params);
            
            // Process status conversion
            if (isset($response['data']['data']) && is_array($response['data']['data'])) {
                foreach ($response['data']['data'] as $key => $page) {
                    $response['data']['data'][$key]['status'] = $this->convertStatusFromApi($page['status'] ?? '0');
                }
            }
            
            return $response;
        } catch (Exception $e) {
            return [
                'status' => 'error',
                'message' => $e->getMessage(),
                'data' => [
                    'data' => [],
                    'current_page' => 1,
                    'per_page' => 10,
                    'from' => null,
                    'to' => null,
                    'total' => 0,
                    'next_page_url' => null,
                    'prev_page_url' => null,
                    'first_page_url' => null,
                    'last_page_url' => null
                ]
            ];
        }
    }

    public function getById($id)
    {
        if (empty($id)) {
            return [
                'success' => false,
                'message' => 'ID is required'
            ];
        }

        try {
            $response = $this->api->get($this->endpoint . '/' . $id);
            
            if (isset($response['data'])) {
                $response['data']['status'] = $this->convertStatusFromApi($response['data']['status'] ?? '0');
            }
            
            return [
                'success' => true,
                'data' => $response['data'] ?? null
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    public function getByTemplateId($templateId)
    {
        return $this->getData(new Request(['template_id' => $templateId]));
    }

    public function create(Request $request)
    {
        $data = $this->prepareData([
            'name' => trim($request->post('name')),
            'slug' => $this->generateSlug($request->post('slug'), $request->post('name')),
            'status' => $request->post('status') ?? 'inactive',
            'description' => trim($request->post('description') ?? ''),
            'template_id' => $request->post('template_id')
        ]);

        try {
            $response = $this->api->post($this->endpoint, $data);
            return [
                'success' => $response['status'] === 'success',
                'data' => $response['data'] ?? null,
                'message' => $response['message'] ?? ''
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    public function update(Request $request)
    {
        $id = $request->post('id');
        
        if (empty($id)) {
            return [
                'success' => false,
                'message' => 'ID is required'
            ];
        }

        $data = $this->prepareData([
            'name' => trim($request->post('name')),
            'slug' => trim($request->post('slug')),
            'status' => $request->post('status') ?? 'inactive',
            'description' => trim($request->post('description') ?? ''),
            'template_id' => $request->post('template_id')
        ]);

        try {
            $response = $this->api->put($this->endpoint . '/' . $id, $data);
            return [
                'success' => $response['status'] === 'success',
                'data' => $response['data'] ?? null,
                'message' => $response['message'] ?? ''
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    public function delete(Request $request)
    {
        $id = $request->post('id');
        
        if (empty($id)) {
            return [
                'success' => false,
                'message' => 'ID is required'
            ];
        }

        try {
            $response = $this->api->delete($this->endpoint . '/' . $id);
            return [
                'success' => $response['status'] === 'success',
                'data' => $response['data'] ?? null,
                'message' => $response['message'] ?? ''
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    public function publish($pageId)
    {
        return $this->update(new Request([
            'id' => $pageId,
            'status' => 'active'
        ], 'POST'));
    }

    private function prepareData($data)
    {
        // Convert status for API
        if (isset($data['status'])) {
            $data['status'] = $this->convertStatusForApi($data['status']);
        }

        // Set timestamps
        $currentTime = date('Y-m-d H:i:s');
        if (!isset($data['created_at'])) {
            $data['created_at'] = $currentTime;
        }
        $data['updated_at'] = $currentTime;

        // Remove empty template_id
        if (empty($data['template_id'])) {
            unset($data['template_id']);
        }

        return $data;
    }

    private function generateSlug($slug, $name)
    {
        if (!empty(trim($slug))) {
            return trim($slug);
        }
        
        return strtolower(trim(preg_replace('/[^A-Za-z0-9-]+/', '-', $name)));
    }

    private function convertStatusForApi($status)
    {
        return $status === 'active' ? "1" : "0";
    }

    private function convertStatusFromApi($status)
    {
        return $status === "1" ? 'active' : 'inactive';
    }
}