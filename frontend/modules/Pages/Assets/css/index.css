 /* Page Builder Styles */

/* Block items */
.block-item {
    transition: all 0.2s ease;
}

.block-item:hover {
    transform: translateY(-2px);
}

.block-item.dragging {
    opacity: 0.5;
    cursor: grabbing;
}

.block-item img {
    width: 100%;
    height: 150px;
    object-fit: cover;
}

/* Canvas area */
#pageCanvas {
    background: #f9fafb;
}

/* Sidebar transitions */
#leftSidebar {
    transition: transform 0.3s ease-in-out;
}

#mainCanvasArea {
    transition: width 0.3s ease-in-out, margin-left 0.3s ease-in-out;
}

/* Tab styles */
.custom-tabs {
    transition: all 0.2s ease;
}

.custom-tabs:hover {
    transform: translateY(-1px);
}

/* Viewport buttons */
#mobileViewBtn svg,
#tabletViewBtn svg,
#desktopViewBtn svg {
    transition: all 0.2s ease;
}

#mobileViewBtn:hover svg,
#tabletViewBtn:hover svg,
#desktopViewBtn:hover svg {
    transform: scale(1.1);
}

/* Search input */
#blockSearch:focus {
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Success/Error messages */
@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.fixed[class*="bg-green"],
.fixed[class*="bg-red"] {
    animation: slideIn 0.3s ease-out;
}

/* Scrollbar styling for blocks list */
#blocksList::-webkit-scrollbar {
    width: 6px;
}

#blocksList::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

#blocksList::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
}

#blocksList::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

/* Responsive iframe container */
#pageCanvas {
    min-height: 600px;
}

/* Settings tab styles */
#settings input,
#settings select,
#settings textarea {
    transition: all 0.2s ease;
}

#settings input:focus,
#settings select:focus,
#settings textarea:focus {
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}