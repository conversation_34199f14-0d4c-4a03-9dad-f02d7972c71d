document.addEventListener('DOMContentLoaded', function() {
    // Handle edit button clicks
    document.addEventListener('click', function(e) {
        if (e.target.closest('[data-modal-toggle="editPageModal"]')) {
            const button = e.target.closest('[data-modal-toggle="editPageModal"]');
            const pageId = button.dataset.id;
            const pageName = button.dataset.name;
            const pageSlug = button.dataset.slug;
            const pageStatus = button.dataset.status;
            const pageTemplateId = button.dataset.templateId;
            const pageDescription = button.dataset.description;
            
            // Set form values
            document.getElementById('editPageId').value = pageId || '';
            document.getElementById('editPageName').value = pageName || '';
            document.getElementById('editPageSlug').value = pageSlug || '';
            document.getElementById('editPageTemplateId').value = pageTemplateId || '';
            document.getElementById('editPageDescription').value = pageDescription || '';

            // Set status dropdown
            const statusDropdown = document.getElementById('editPageStatus');
            if (statusDropdown) {
                const status = pageStatus === 'active' ? 'active' : 'inactive';
                statusDropdown.value = status;
            }
        }
    });

    // Handle delete button clicks
    document.addEventListener('click', function(e) {
        if (e.target.closest('[data-modal-toggle="deletePageModal"]')) {
            const button = e.target.closest('[data-modal-toggle="deletePageModal"]');
            const pageId = button.dataset.id;
            
            document.getElementById('deletePageId').value = pageId || '';
        }
    });

    // Auto-generate slug from name
    const nameInputs = document.querySelectorAll('input[name="name"]');
    nameInputs.forEach(nameInput => {
        nameInput.addEventListener('input', function() {
            const slugInput = this.closest('form').querySelector('input[name="slug"]');
            if (slugInput && !slugInput.value) {
                const slug = this.value
                    .toLowerCase()
                    .replace(/[^a-z0-9]+/g, '-')
                    .replace(/^-+|-+$/g, '');
                slugInput.value = slug;
            }
        });
    });
});