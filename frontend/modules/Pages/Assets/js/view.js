/**
 * Page Builder JavaScript
 * Handles preview functionality, page navigation, and builder interactions
 */

document.addEventListener('DOMContentLoaded', function() {
    // Page Builder Elements
    const pageSelector = document.getElementById('pageSelector');
    const pageCanvas = document.getElementById('pageCanvas');
    const previewBtn = document.getElementById('previewBtn');
    const publishBtn = document.getElementById('publishBtn');
    const leftSidebar = document.getElementById('leftSidebar');
    const toggleSidebarBtn = document.getElementById('toggleSidebarBtn');
    const mainCanvasArea = document.getElementById('mainCanvasArea');
    
    // Device View Buttons
    const mobileViewBtn = document.getElementById('mobileViewBtn');
    const tabletViewBtn = document.getElementById('tabletViewBtn');
    const desktopViewBtn = document.getElementById('desktopViewBtn');
    
    // Tab Elements
    const blocksTabs = document.querySelectorAll('.custom-tabs');
    const tabPanels = document.querySelectorAll('.custom-tabpanel');
    
    // Block Search and Filter
    const blockSearch = document.getElementById('blockSearch');
    const blocksList = document.getElementById('blocksList');
    const blockItems = document.querySelectorAll('.block-item');
    
    // State Management
    let currentPageId = null;
    let previewMode = false;
    let sidebarVisible = true;
    let currentDeviceView = 'desktop';
    
    // Initialize
    init();
    
    function init() {
        // Set initial page ID
        currentPageId = pageSelector.value;
        
        // Setup event listeners
        setupEventListeners();
        
        // Setup tabs
        setupTabs();
        
        // Setup drag and drop
        setupDragAndDrop();
        
        // Initialize preview
        initializePreview();
        
        console.log('Page Builder initialized');
    }
    
    function setupEventListeners() {
        // Page selector change
        if (pageSelector) {
            pageSelector.addEventListener('change', function() {
                const selectedPageId = this.value;
                if (selectedPageId) {
                    navigateToPage(selectedPageId);
                }
            });
        }
        
        // Preview button
        if (previewBtn) {
            previewBtn.addEventListener('click', function() {
                togglePreviewMode();
            });
        }
        
        // Publish button
        if (publishBtn) {
            publishBtn.addEventListener('click', function() {
                publishPage();
            });
        }
        
        // Sidebar toggle
        if (toggleSidebarBtn) {
            toggleSidebarBtn.addEventListener('click', function() {
                toggleSidebar();
            });
        }
        
        // Device view buttons
        if (mobileViewBtn) {
            mobileViewBtn.addEventListener('click', function() {
                setDeviceView('mobile');
            });
        }
        
        if (tabletViewBtn) {
            tabletViewBtn.addEventListener('click', function() {
                setDeviceView('tablet');
            });
        }
        
        if (desktopViewBtn) {
            desktopViewBtn.addEventListener('click', function() {
                setDeviceView('desktop');
            });
        }
        
        // Block search
        if (blockSearch) {
            blockSearch.addEventListener('input', function() {
                filterBlocks(this.value);
            });
        }
        
        // Settings save button
        const saveSettings = document.getElementById('saveSettings');
        if (saveSettings) {
            saveSettings.addEventListener('click', function() {
                savePageSettings();
            });
        }
    }
    
    function setupTabs() {
        blocksTabs.forEach(tab => {
            tab.addEventListener('click', function() {
                const targetId = this.getAttribute('data-tabs-target');
                switchTab(targetId);
            });
        });
        
        // Show first tab by default
        if (blocksTabs.length > 0) {
            const firstTab = blocksTabs[0];
            const firstTargetId = firstTab.getAttribute('data-tabs-target');
            switchTab(firstTargetId);
        }
    }
    
    function switchTab(targetId) {
        // Hide all tab panels
        tabPanels.forEach(panel => {
            panel.classList.add('hidden');
        });
        
        // Remove active class from all tabs
        blocksTabs.forEach(tab => {
            tab.classList.remove('bg-blue-50', 'text-blue-600');
            tab.classList.add('text-gray-500', 'hover:bg-gray-100');
        });
        
        // Show target panel
        const targetPanel = document.querySelector(targetId);
        if (targetPanel) {
            targetPanel.classList.remove('hidden');
        }
        
        // Activate clicked tab
        const activeTab = document.querySelector(`[data-tabs-target="${targetId}"]`);
        if (activeTab) {
            activeTab.classList.add('bg-blue-50', 'text-blue-600');
            activeTab.classList.remove('text-gray-500', 'hover:bg-gray-100');
        }
    }
    
    function setupDragAndDrop() {
        // Setup drag events for block items
        blockItems.forEach(item => {
            item.addEventListener('dragstart', handleDragStart);
            item.addEventListener('dragend', handleDragEnd);
        });
        
        // Setup drop events for canvas
        if (pageCanvas) {
            pageCanvas.addEventListener('dragover', handleDragOver);
            pageCanvas.addEventListener('drop', handleDrop);
        }
    }
    
    function handleDragStart(e) {
        const blockId = this.getAttribute('data-block-id');
        const blockType = this.getAttribute('data-block-type');
        
        e.dataTransfer.setData('text/plain', JSON.stringify({
            blockId: blockId,
            blockType: blockType
        }));
        
        this.classList.add('opacity-50');
    }
    
    function handleDragEnd(e) {
        this.classList.remove('opacity-50');
    }
    
    function handleDragOver(e) {
        e.preventDefault();
    }
    
    function handleDrop(e) {
        e.preventDefault();
        
        try {
            const data = JSON.parse(e.dataTransfer.getData('text/plain'));
            addBlockToPage(data.blockId, data.blockType);
        } catch (error) {
            console.error('Error handling drop:', error);
        }
    }
    
    function navigateToPage(pageId) {
        if (!pageId) return;
        
        currentPageId = pageId;
        
        // Update URL
        const newUrl = `${window.location.pathname}?id=${pageId}`;
        history.pushState({pageId: pageId}, '', newUrl);
        
        // Update iframe source
        if (pageCanvas) {
            const previewUrl = `/pages/preview?id=${pageId}`;
            pageCanvas.src = previewUrl;
        }
        
        // Update UI
        updatePageInfo(pageId);
        
        console.log('Navigated to page:', pageId);
    }
    
    function updatePageInfo(pageId) {
        // Update page selector
        if (pageSelector) {
            pageSelector.value = pageId;
        }
        
        // Load page settings
        loadPageSettings(pageId);
    }
    
    function loadPageSettings(pageId) {
        // Make API call to get page settings
        fetch(`/pages/api/get?id=${pageId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success && data.data) {
                    populatePageSettings(data.data);
                }
            })
            .catch(error => {
                console.error('Error loading page settings:', error);
            });
    }
    
    function populatePageSettings(pageData) {
        // Update page status
        const pageStatus = document.getElementById('pageStatus');
        if (pageStatus && pageData.status) {
            pageStatus.value = pageData.status;
        }
        
        // Update page template
        const pageTemplate = document.getElementById('pageTemplate');
        if (pageTemplate && pageData.template_id) {
            pageTemplate.value = pageData.template_id;
        }
        
        // Update SEO settings
        const metaTitle = document.getElementById('metaTitle');
        if (metaTitle && pageData.meta_title) {
            metaTitle.value = pageData.meta_title;
        }
        
        const metaDescription = document.getElementById('metaDescription');
        if (metaDescription && pageData.meta_description) {
            metaDescription.value = pageData.meta_description;
        }
    }
    
    function togglePreviewMode() {
        previewMode = !previewMode;
        
        if (previewMode) {
            // Enable preview mode
            previewBtn.innerHTML = `
                <svg class="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                </svg>
                Edit Mode
            `;
            previewBtn.classList.add('bg-blue-800');
            
            // Update iframe with preview mode
            if (pageCanvas && currentPageId) {
                const previewUrl = `/pages/preview?id=${currentPageId}&preview=true`;
                pageCanvas.src = previewUrl;
            }
        } else {
            // Disable preview mode
            previewBtn.innerHTML = `
                <svg class="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                </svg>
                Preview
            `;
            previewBtn.classList.remove('bg-blue-800');
            
            // Update iframe without preview mode
            if (pageCanvas && currentPageId) {
                const previewUrl = `/pages/preview?id=${currentPageId}`;
                pageCanvas.src = previewUrl;
            }
        }
        
        console.log('Preview mode:', previewMode);
    }
    
    function publishPage() {
        if (!currentPageId) {
            showNotification('Please select a page to publish', 'error');
            return;
        }
        
        // Show loading state
        publishBtn.disabled = true;
        publishBtn.innerHTML = `
            <svg class="animate-spin w-3.5 h-3.5 mr-1" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Publishing...
        `;
        
        // Make API call to publish page
        fetch('/pages/publish', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                page_id: currentPageId
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('Page published successfully!', 'success');
                
                // Open preview in new tab
                window.open(`/pages/preview?id=${currentPageId}&preview=true`, '_blank');
            } else {
                showNotification(data.message || 'Failed to publish page', 'error');
            }
        })
        .catch(error => {
            console.error('Error publishing page:', error);
            showNotification('Error publishing page', 'error');
        })
        .finally(() => {
            // Reset button state
            publishBtn.disabled = false;
            publishBtn.innerHTML = `
                <svg class="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                Publish
            `;
        });
    }
    
    function toggleSidebar() {
        sidebarVisible = !sidebarVisible;
        
        if (sidebarVisible) {
            leftSidebar.classList.remove('-translate-x-full');
            mainCanvasArea.classList.remove('w-full');
            mainCanvasArea.classList.add('w-[calc(100%-320px)]');
        } else {
            leftSidebar.classList.add('-translate-x-full');
            mainCanvasArea.classList.remove('w-[calc(100%-320px)]');
            mainCanvasArea.classList.add('w-full');
        }
        
        console.log('Sidebar visibility:', sidebarVisible);
    }
    
    function setDeviceView(device) {
        currentDeviceView = device;
        
        // Remove active class from all device buttons
        [mobileViewBtn, tabletViewBtn, desktopViewBtn].forEach(btn => {
            if (btn) {
                btn.classList.remove('bg-blue-100');
                btn.classList.add('hover:bg-gray-100');
            }
        });
        
        // Update iframe width based on device
        if (pageCanvas) {
            switch (device) {
                case 'mobile':
                    pageCanvas.style.width = '375px';
                    pageCanvas.style.margin = '0 auto';
                    mobileViewBtn.classList.add('bg-blue-100');
                    mobileViewBtn.classList.remove('hover:bg-gray-100');
                    break;
                case 'tablet':
                    pageCanvas.style.width = '768px';
                    pageCanvas.style.margin = '0 auto';
                    tabletViewBtn.classList.add('bg-blue-100');
                    tabletViewBtn.classList.remove('hover:bg-gray-100');
                    break;
                case 'desktop':
                default:
                    pageCanvas.style.width = '100%';
                    pageCanvas.style.margin = '0';
                    desktopViewBtn.classList.add('bg-blue-100');
                    desktopViewBtn.classList.remove('hover:bg-gray-100');
                    break;
            }
        }
        
        console.log('Device view:', device);
    }
    
    function filterBlocks(searchTerm) {
        const blocks = blocksList.querySelectorAll('.block-item');
        
        blocks.forEach(block => {
            const blockName = block.querySelector('h3').textContent.toLowerCase();
            const blockDesc = block.querySelector('p').textContent.toLowerCase();
            
            if (blockName.includes(searchTerm.toLowerCase()) || 
                blockDesc.includes(searchTerm.toLowerCase())) {
                block.style.display = 'block';
            } else {
                block.style.display = 'none';
            }
        });
    }
    
    function addBlockToPage(blockId, blockType) {
        if (!currentPageId) {
            showNotification('Please select a page first', 'error');
            return;
        }
        
        // Make API call to add block to page
        fetch('/pages/blocks/add', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                page_id: currentPageId,
                block_id: blockId,
                block_type: blockType
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('Block added successfully!', 'success');
                
                // Refresh preview
                refreshPreview();
            } else {
                showNotification(data.message || 'Failed to add block', 'error');
            }
        })
        .catch(error => {
            console.error('Error adding block:', error);
            showNotification('Error adding block', 'error');
        });
    }
    
    function savePageSettings() {
        if (!currentPageId) {
            showNotification('Please select a page first', 'error');
            return;
        }
        
        // Collect form data
        const formData = {
            page_id: currentPageId,
            status: document.getElementById('pageStatus').value,
            template_id: document.getElementById('pageTemplate').value,
            meta_title: document.getElementById('metaTitle').value,
            meta_description: document.getElementById('metaDescription').value
        };
        
        // Make API call to save settings
        fetch('/pages/settings/save', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(formData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('Settings saved successfully!', 'success');
            } else {
                showNotification(data.message || 'Failed to save settings', 'error');
            }
        })
        .catch(error => {
            console.error('Error saving settings:', error);
            showNotification('Error saving settings', 'error');
        });
    }
    
    function refreshPreview() {
        if (pageCanvas && currentPageId) {
            const currentSrc = pageCanvas.src;
            const url = new URL(currentSrc);
            url.searchParams.set('_t', Date.now()); // Add timestamp to force reload
            pageCanvas.src = url.toString();
        }
    }
    
    function initializePreview() {
        // Setup iframe load event
        if (pageCanvas) {
            pageCanvas.addEventListener('load', function() {
                console.log('Preview loaded successfully');
                
                // Add any post-load processing here
                try {
                    // Access iframe content if same-origin
                    const iframeDoc = this.contentDocument || this.contentWindow.document;
                    if (iframeDoc) {
                        // Add any iframe-specific event listeners here
                        console.log('Iframe content accessible');
                    }
                } catch (error) {
                    // Cross-origin iframe - limited access
                    console.log('Iframe content not accessible (cross-origin)');
                }
            });
        }
    }
    
    function showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg text-white ${getNotificationColor(type)}`;
        notification.innerHTML = `
            <div class="flex items-center">
                <span class="mr-2">${getNotificationIcon(type)}</span>
                <span>${message}</span>
                <button class="ml-4 text-white hover:text-gray-200" onclick="this.parentElement.parentElement.remove()">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
        `;
        
        document.body.appendChild(notification);
        
        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 5000);
    }
    
    function getNotificationColor(type) {
        switch (type) {
            case 'success': return 'bg-green-500';
            case 'error': return 'bg-red-500';
            case 'warning': return 'bg-yellow-500';
            default: return 'bg-blue-500';
        }
    }
    
    function getNotificationIcon(type) {
        switch (type) {
            case 'success': return '✓';
            case 'error': return '✗';
            case 'warning': return '⚠';
            default: return 'ℹ';
        }
    }
    
    // Handle browser back/forward buttons
    window.addEventListener('popstate', function(event) {
        if (event.state && event.state.pageId) {
            navigateToPage(event.state.pageId);
        }
    });
    
    // Auto-refresh preview every 30 seconds
    setInterval(refreshPreview, 30000);
    
    // Expose functions to global scope for debugging
    window.pageBuilder = {
        navigateToPage,
        togglePreviewMode,
        publishPage,
        refreshPreview,
        showNotification
    };
}); 