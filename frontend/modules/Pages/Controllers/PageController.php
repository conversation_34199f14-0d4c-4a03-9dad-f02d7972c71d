<?php

namespace Modules\Pages\Controllers;

use Core\Controller;
use Core\Request;
use Exception;
use Modules\Pages\Services\PageService;
use Modules\Pages\Services\PageTableService;
use Modules\Templates\Services\TemplatesService;

class PageController extends Controller
{
    protected $pageService;
    protected $templateService;

    public function __construct()
    {
        $this->pageService = new PageService();
        $this->templateService = new TemplatesService();
    }

    public function index()
    {
        $request = new Request();
        
        // Set page title
        $this->setTitle('Pages');

        try {
            // Get data from service
            $result = $this->pageService->getData($request);
            $error = $result['status'] !== 'success' ? $result['message'] : '';
            $pagesList = $result['status'] == 'success' ? $result['data']['data'] : [];
            $pagination = $result['status'] == 'success' ? $result['data'] : [
                'current_page' => 1,
                'per_page' => 10,
                'from' => null,
                'to' => null,
                'total' => 0,
                'next_page_url' => null,
                'prev_page_url' => null,
                'first_page_url' => null,
                'last_page_url' => null
            ];

            // Get templates for dropdown
            $templatesResult = $this->templateService->getData(new Request());
            $templatesList = $templatesResult['status'] == 'success' ? $templatesResult['data']['data'] : [];

            $pageTable = new PageTableService($pagesList, $pagination, $request, $templatesList);

            if ($request->isAjax()) {
                return $this->json([
                    'html' => $pageTable->renderTable(),
                    'pagination' => $pagination,
                    'success' => true
                ]);
            }

            return $this->view('modules/Pages/Views/index', [
                'pageTable' => $pageTable->renderTable(),
                'templatesList' => $templatesList,
                'error' => $error
            ]);
        } catch (Exception $e) {
            $error = $e->getMessage();
            
            if ($request->isAjax()) {
                return $this->json([
                    'success' => false,
                    'error' => $error
                ]);
            }
            
            return $this->view('modules/Pages/Views/index', [
                'pageTable' => '',
                'templatesList' => [],
                'error' => $error
            ]);
        }
    }

    public function builder()
    {
        $request = new Request();
        $pageId = $request->get('id');
        
        $this->setTitle('Page Builder');
        
        try {
            // Get all pages for dropdown
            $pagesResult = $this->pageService->getData($request);
            $allPages = $pagesResult['status'] == 'success' ? $pagesResult['data']['data'] : [];
            
            // Get current page data if ID provided
            $pageData = null;
            if ($pageId) {
                $pageResult = $this->pageService->getById($pageId);
                $pageData = $pageResult['success'] ? $pageResult['data'] : null;
            }
            
            // Get blocks for page builder
            // You'll need to implement BlockService
            $blocks = []; // Placeholder
            
            return $this->view('modules/Pages/Views/view', [
                'pageId' => $pageId,
                'pageData' => $pageData,
                'allPages' => $allPages,
                'blocks' => $blocks,
                'error' => null
            ]);
        } catch (Exception $e) {
            return $this->view('modules/Pages/Views/view', [
                'pageId' => null,
                'pageData' => null,
                'allPages' => [],
                'blocks' => [],
                'error' => $e->getMessage()
            ]);
        }
    }

    public function create(Request $request)
    {
        try {
            $result = $this->pageService->create($request);
            
            return $this->jsonResponse([
                'success' => $result['success'],
                'message' => $result['success'] ? 'Page created successfully!' : $result['message'],
                'data' => $result['data'] ?? null
            ]);
        } catch (Exception $e) {
            return $this->json([
                'success' => false,
                'error' => $e->getMessage()
            ]);
        }
    }

    public function update(Request $request)
    {
        try {
            $result = $this->pageService->update($request);
            
            return $this->jsonResponse([
                'success' => $result['success'],
                'message' => $result['success'] ? 'Page updated successfully!' : $result['message']
            ]);
        } catch (Exception $e) {
            return $this->json([
                'success' => false,
                'error' => $e->getMessage()
            ]);
        }
    }

    public function delete(Request $request)
    {
        try {
            $result = $this->pageService->delete($request);
            
            return $this->jsonResponse([
                'success' => $result['success'],
                'message' => $result['success'] ? 'Page deleted successfully!' : $result['message']
            ]);
        } catch (Exception $e) {
            return $this->json([
                'success' => false,
                'error' => $e->getMessage()
            ]);
        }
    }

    public function publish(Request $request)
    {
        try {
            $pageId = $request->post('page_id');
            
            if (!$pageId) {
                return $this->json([
                    'success' => false,
                    'message' => 'Page ID is required'
                ]);
            }
            
            $result = $this->pageService->publish($pageId);
            
            return $this->jsonResponse([
                'success' => $result['success'],
                'message' => $result['success'] ? 'Page published successfully!' : $result['message']
            ]);
        } catch (Exception $e) {
            return $this->json([
                'success' => false,
                'error' => $e->getMessage()
            ]);
        }
    }

    public function getByTemplate(Request $request)
    {
        try {
            $templateId = $request->get('template_id');
            
            if (!$templateId) {
                return $this->json([
                    'success' => false,
                    'message' => 'Template ID is required'
                ]);
            }
            
            $result = $this->pageService->getByTemplateId($templateId);
            
            return $this->json([
                'success' => true,
                'data' => $result['data'] ?? []
            ]);
        } catch (Exception $e) {
            return $this->json([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
    }
}