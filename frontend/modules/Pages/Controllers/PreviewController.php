<?php

namespace Modules\Pages\Controllers;

use Core\Controller;
use Core\Request;
use Core\Services\PreviewService;
use Exception;

class PreviewController extends Controller
{
    private $previewService;
    
    public function __construct()
    {
        $this->previewService = new PreviewService();
    }
    
    /**
     * Show page preview
     */
    public function index(Request $request)
    {
        try {
            $pageId = $request->get('id');
            $previewMode = $request->get('preview') === 'true';
            
            if (!$pageId) {
                return $this->renderError('Page ID is required');
            }
            
            $options = [
                'preview_mode' => $previewMode,
                'hide_controls' => $previewMode
            ];
            
            $html = $this->previewService->renderPreview('page', $pageId, $options);
            
            if (isset($html['error'])) {
                return $this->renderError($html['error']);
            }
            
            // Return the generated HTML directly
            echo $html;
            exit;
            
        } catch (Exception $e) {
            return $this->renderError($e->getMessage());
        }
    }
    
    /**
     * Handle AJAX requests for page preview
     */
    public function ajax(Request $request)
    {
        try {
            $result = $this->previewService->handleAjaxRequest($request);
            return $this->json($result);
        } catch (Exception $e) {
            return $this->json([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * Render error page
     */
    private function renderError($message)
    {
        $html = '<!DOCTYPE html>';
        $html .= '<html lang="en">';
        $html .= '<head>';
        $html .= '<meta charset="UTF-8">';
        $html .= '<meta name="viewport" content="width=device-width, initial-scale=1.0">';
        $html .= '<title>Preview Error</title>';
        $html .= '<script src="https://cdn.tailwindcss.com"></script>';
        $html .= '</head>';
        $html .= '<body class="bg-gray-100">';
        $html .= '<div class="flex items-center justify-center min-h-screen">';
        $html .= '<div class="text-center">';
        $html .= '<div class="inline-flex items-center justify-center w-16 h-16 rounded-full bg-red-100 mb-4">';
        $html .= '<svg class="w-8 h-8 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">';
        $html .= '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.864-.833-2.634 0L4.183 16.5c-.77.833.192 2.5 1.732 2.5z"></path>';
        $html .= '</svg>';
        $html .= '</div>';
        $html .= '<h1 class="text-2xl font-bold text-gray-900 mb-2">Preview Error</h1>';
        $html .= '<p class="text-gray-600">' . htmlspecialchars($message) . '</p>';
        $html .= '<button onclick="history.back()" class="mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">';
        $html .= 'Go Back';
        $html .= '</button>';
        $html .= '</div>';
        $html .= '</div>';
        $html .= '</body>';
        $html .= '</html>';
        
        echo $html;
        exit;
    }
} 