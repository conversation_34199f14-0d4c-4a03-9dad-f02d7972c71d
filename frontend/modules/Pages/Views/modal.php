<!-- Create Page Modal -->
<div id="createPageModal" tabindex="-1" aria-hidden="true" class="fixed top-0 left-0 right-0 z-50 hidden w-full p-4 overflow-x-hidden overflow-y-auto md:inset-0 h-[calc(100%-1rem)] max-h-full">
    <div class="relative w-full max-w-2xl max-h-full">
        <!-- Modal content -->
        <div class="relative bg-white rounded-lg shadow">
            <!-- Modal header -->
            <div class="flex items-start justify-between p-4 border-b rounded-t">
                <div class="flex items-center">
                    <div class="flex items-center justify-center h-10 w-10 rounded-full bg-blue-50 text-blue-500 mr-3">
                        <svg class="h-5 w-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z"/>
                            <path d="M14 2v4a2 2 0 0 0 2 2h4"/>
                        </svg>
                    </div>
                    <div>
                        <h3 class="text-lg font-medium text-gray-900">Create New Page</h3>
                        <p class="text-sm text-gray-500">Create a new page with the details below.</p>
                    </div>
                </div>
            </div>
            <!-- Modal body -->
            <form id="createPageForm" method="POST" action="<?= url('pages/create') ?>" data-ajax-form data-reset-on-success data-table-id="pagesTable" data-ajax-url="pages">
                <div class="p-6 pb-0">
                    <input type="hidden" name="save_page" value="1">
                    <div class="grid grid-cols-2 gap-4">
                        <div class="mb-4">
                            <label for="create_name" class="block text-sm font-bold text-gray-700 mb-2">Name</label>
                            <input type="text" name="name" id="create_name" required class="block w-full px-4 py-2.5 text-sm text-gray-900 bg-white border border-gray-300 rounded-lg focus:border-blue-500 focus:ring-1 focus:ring-blue-500">
                        </div>
                        <div class="mb-4">
                            <label for="create_slug" class="block text-sm font-bold text-gray-700 mb-2">Slug</label>
                            <input type="text" name="slug" id="create_slug" required class="block w-full px-4 py-2.5 text-sm text-gray-900 bg-white border border-gray-300 rounded-lg focus:border-blue-500 focus:ring-1 focus:ring-blue-500">
                        </div>
                    </div>
                    <div class="grid grid-cols-2 gap-4">
                        <div class="mb-4">
                            <label for="create_status" class="block text-sm font-bold text-gray-700 mb-2">Status</label>
                            <select name="status" id="create_status" required class="block w-full px-4 py-2.5 text-sm text-gray-900 bg-white border border-gray-300 rounded-lg focus:border-blue-500 focus:ring-1 focus:ring-blue-500">
                                <option value="active">Published</option>
                                <option value="inactive" selected>Draft</option>
                            </select>
                        </div>
                        <div class="mb-4">
                            <label for="create_template_id" class="block text-sm font-bold text-gray-700 mb-2">Template (Optional)</label>
                            <select name="template_id" id="create_template_id" class="block w-full px-4 py-2.5 text-sm text-gray-900 bg-white border border-gray-300 rounded-lg focus:border-blue-500 focus:ring-1 focus:ring-blue-500">
                                <option value="">Select Template</option>
                                <?php foreach ($templatesList as $template): ?>
                                    <option value="<?= $template['id'] ?>"><?= htmlspecialchars($template['name']) ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                    <div class="mb-4 w-full">
                        <label for="create_description" class="block text-sm font-bold text-gray-700 mb-2">Description</label>
                        <textarea name="description" id="create_description" rows="3" class="block w-full px-4 py-2.5 text-sm text-gray-900 bg-white border border-gray-300 rounded-lg focus:border-blue-500 focus:ring-1 focus:ring-blue-500" placeholder="Optional page description..."></textarea>
                    </div>
                </div>
                
                <!-- Modal footer -->
                <div class="flex items-center pb-7 px-6 justify-end space-x-2 rounded-b">
                    <button type="submit" class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2 text-center">Save</button>
                    <button type="button" class="modelClose text-gray-500 bg-white hover:bg-gray-100 focus:ring-4 focus:outline-none focus:ring-gray-200 rounded-lg border border-gray-200 text-sm font-medium px-5 py-2 hover:text-gray-900 focus:z-10" data-modal-hide="createPageModal">Cancel</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Page Modal -->
<div id="editPageModal" tabindex="-1" aria-hidden="true" class="fixed top-0 left-0 right-0 z-50 hidden w-full p-4 overflow-x-hidden overflow-y-auto md:inset-0 h-[calc(100%-1rem)] max-h-full">
    <div class="relative w-full max-w-2xl max-h-full">
        <!-- Modal content -->
        <div class="relative bg-white rounded-lg shadow">
            <!-- Modal header -->
            <div class="flex items-start justify-between p-4 border-b rounded-t">
                <div class="flex items-center">
                    <div class="flex items-center justify-center h-10 w-10 rounded-full bg-blue-50 text-blue-500 mr-3">
                        <svg class="h-5 w-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z"/>
                            <path d="M14 2v4a2 2 0 0 0 2 2h4"/>
                        </svg>
                    </div>
                    <div>
                        <h3 class="text-lg font-medium text-gray-900">Edit Page</h3>
                        <p class="text-sm text-gray-500">Edit the details of the selected page.</p>
                    </div>
                </div>
            </div>
            <!-- Modal body -->
            <form id="editPageForm" method="POST" action="<?= url('pages/update') ?>" data-ajax-form data-reset-on-success data-table-id="pagesTable" data-ajax-url="pages">
                <div class="p-6 pb-0">
                    <input type="hidden" name="save_page" value="1">
                    <input type="hidden" name="id" id="editPageId">
                    <div class="grid grid-cols-2 gap-4">
                        <div class="mb-4">
                            <label for="editPageName" class="block text-sm font-bold text-gray-700 mb-2">Name</label>
                            <input type="text" name="name" id="editPageName" required class="block w-full px-4 py-2.5 text-sm text-gray-900 bg-white border border-gray-300 rounded-lg focus:border-blue-500 focus:ring-1 focus:ring-blue-500">
                        </div>
                        <div class="mb-4">
                            <label for="editPageSlug" class="block text-sm font-bold text-gray-700 mb-2">Slug</label>
                            <input type="text" name="slug" id="editPageSlug" required class="block w-full px-4 py-2.5 text-sm text-gray-900 bg-white border border-gray-300 rounded-lg focus:border-blue-500 focus:ring-1 focus:ring-blue-500">
                        </div>
                    </div>
                    <div class="grid grid-cols-2 gap-4">
                        <div class="mb-4">
                            <label for="editPageStatus" class="block text-sm font-bold text-gray-700 mb-2">Status</label>
                            <select name="status" id="editPageStatus" required class="block w-full px-4 py-2.5 text-sm text-gray-900 bg-white border border-gray-300 rounded-lg focus:border-blue-500 focus:ring-1 focus:ring-blue-500">
                                <option value="active">Published</option>
                                <option value="inactive">Draft</option>
                            </select>
                        </div>
                        <div class="mb-4">
                            <label for="editPageTemplateId" class="block text-sm font-bold text-gray-700 mb-2">Template (Optional)</label>
                            <select name="template_id" id="editPageTemplateId" class="block w-full px-4 py-2.5 text-sm text-gray-900 bg-white border border-gray-300 rounded-lg focus:border-blue-500 focus:ring-1 focus:ring-blue-500">
                                <option value="">Select Template</option>
                                <?php foreach ($templatesList as $template): ?>
                                    <option value="<?= $template['id'] ?>"><?= htmlspecialchars($template['name']) ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                    <div class="mb-4">
                        <label for="editPageDescription" class="block text-sm font-bold text-gray-700 mb-2">Description</label>
                        <textarea name="description" id="editPageDescription" rows="3" class="block w-full px-4 py-2.5 text-sm text-gray-900 bg-white border border-gray-300 rounded-lg focus:border-blue-500 focus:ring-1 focus:ring-blue-500" placeholder="Optional page description..."></textarea>
                    </div>
                </div>
                <!-- Modal footer -->
                <div class="flex items-center pb-7 px-6 justify-end space-x-2 rounded-b">
                    <button type="submit" class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2 text-center">Save</button>
                    <button type="button" class="modelClose text-gray-500 bg-white hover:bg-gray-100 focus:ring-4 focus:outline-none focus:ring-gray-200 rounded-lg border border-gray-200 text-sm font-medium px-5 py-2 hover:text-gray-900 focus:z-10" data-modal-hide="editPageModal">Cancel</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Page Confirmation Modal -->
<div id="deletePageModal" tabindex="-1" aria-hidden="true" class="fixed top-0 left-0 right-0 z-50 hidden w-full p-4 overflow-x-hidden overflow-y-auto md:inset-0 h-[calc(100%-1rem)] max-h-full">
    <div class="relative w-full max-w-md max-h-full">
        <!-- Modal content -->
        <div class="relative bg-white rounded-lg shadow">
            <!-- Modal body -->
            <form id="deletePageForm" method="POST" action="<?= url('pages/delete') ?>" data-ajax-form data-reset-on-success data-table-id="pagesTable" data-ajax-url="pages">
                <div class="p-6">
                    <input type="hidden" name="delete" value="1">
                    <input type="hidden" name="id" id="deletePageId">
                    <div class="text-center mb-4">
                        <svg class="mx-auto h-12 w-12 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                        </svg>
                    </div>
                    <p class="text-center text-base leading-relaxed text-gray-700 font-medium">
                        Are you sure you want to delete this page?
                    </p>
                    <p class="text-center text-sm text-gray-500 mt-1">
                        This action cannot be undone.
                    </p>
                </div>
                <!-- Modal footer -->
                <div class="flex items-center pt-2 pb-7 justify-center space-x-2 rounded-b">
                    <button type="submit" class="text-white bg-red-700 hover:bg-red-800 focus:ring-4 focus:outline-none focus:ring-red-300 font-medium rounded-lg text-sm px-5 py-2 text-center">Delete</button>
                    <button type="button" class="modelClose text-gray-500 bg-white hover:bg-gray-100 focus:ring-4 focus:outline-none focus:ring-gray-200 rounded-lg border border-gray-200 text-sm font-medium px-5 py-2 hover:text-gray-900 focus:z-10" data-modal-hide="deletePageModal">Cancel</button>
                </div>
            </form>
        </div>
    </div>
</div>