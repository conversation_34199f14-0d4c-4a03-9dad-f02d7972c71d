<?php
    add_style(asset('modules/Pages/Assets/css/view.css'));
    add_script(asset('modules/Pages/Assets/js/view.js'));
    
    // Get blocks HTML - you'll need to implement BlockService
    $globalBlockList = '';
    if (!empty($blocks)) {
        ob_start();
        foreach ($blocks as $block) {
            if (!isset($block['id']) || !isset($block['name'])) {
                continue;
            }
            
            $blockType = $block['slug'] ?? 'default-block';
            $blockName = $block['name'] ?? 'Unnamed Block';
            $blockImage = $block['image'] ? asset($block['image']) : 'https://placehold.co/600x300/e5e7eb/9ca3af?text=' . urlencode($blockName);
            $blockDesc = $block['description'] ?? '';
            ?>
            <div class="block-item cursor-move bg-white border border-gray-200 rounded-lg mb-2 hover:border-blue-500 hover:shadow-sm overflow-hidden" 
            draggable="true" 
            data-block-type="<?= htmlspecialchars($blockType) ?>" 
            data-block-id="<?= htmlspecialchars($block['id']) ?>">
                <div class="flex items-start relative">
                    <img src="<?= htmlspecialchars($blockImage) ?>" alt="Block Image">
                    <p class="hidden"><?= htmlspecialchars($blockDesc) ?></p>
                </div>
                <h3 class="text-sm font-medium text-gray-900 w-full px-3 py-2 bg-gradient-to-t from-[#f7f7f7] to-white border-t border-gray-100"><?= htmlspecialchars($blockName) ?></h3>
            </div>
            <?php
        }
        $globalBlockList = ob_get_clean();
    }
?>

<div class="container-fluid">
    <?php if ($error): ?>
    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
        <p><?= htmlspecialchars($error) ?></p>
    </div>
    <?php endif; ?>
    
    <!-- Main Content Area -->
    <div class="flex h-[calc(100dvh-106px)]">
        <!-- Canvas Area -->
        <div id="mainCanvasArea" class="w-[calc(100%-320px)] transition-all duration-300 ease-in-out ml-auto relative">
            <!-- Page Builder Header -->
            <div class="border-b border-gray-200 p-3">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-2">
                        <div class="flex items-center space-x-2">
                            <button id="toggleSidebarBtn" type="button">
                                <svg class="w-4 h-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <rect width="18" height="18" x="3" y="3" rx="2"/><path d="M9 3v18"/>
                                </svg>
                            </button>
                        </div>
                        <div class="flex items-center space-x-2">
                            <label for="pageSelector" class="text-xs font-medium text-gray-700">Page:</label>
                            <select id="pageSelector" class="text-xs bg-white border border-gray-300 text-gray-900 rounded focus:ring-blue-500 focus:border-blue-500 p-1.5">
                                <option value="">Select a page</option>
                                <?php foreach ($allPages as $page): ?>
                                    <option value="<?= htmlspecialchars($page['id']) ?>" <?= ($pageId == $page['id']) ? 'selected' : '' ?>>
                                        <?= htmlspecialchars($page['name']) ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                    <div class="flex items-center space-x-2">
                        <div class="flex items-center space-x-2 bg-white border border-gray-300 rounded-lg p-0.5">
                            <button id="mobileViewBtn" class="p-0.5 rounded hover:bg-gray-100" title="Mobile View">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z" />
                                </svg>
                            </button>
                            <button id="tabletViewBtn" class="p-0.5 rounded hover:bg-gray-100" title="Tablet View">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                                </svg>
                            </button>
                            <button id="desktopViewBtn" class="p-0.5 rounded bg-blue-100" title="Desktop View">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                                </svg>
                            </button>
                        </div>
                    </div>
                    
                    <div class="flex items-center space-x-2">
                        <button id="previewBtn" class="text-xs text-white bg-[#0C5BE2] px-2 py-1.5 rounded hover:bg-blue-700 flex items-center gap-1">
                            <svg class="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                            </svg>
                            Preview
                        </button>
                        
                        <button id="publishBtn" class="text-xs text-white bg-emerald-500 px-2 py-1.5 rounded hover:bg-emerald-600 flex items-center gap-1">
                            <svg class="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            Publish
                        </button>
                    </div>
                </div>
            </div>
            <?php if ($pageId): ?>
                <iframe id="pageCanvas" src="<?= url('/pages/preview?id=' . $pageId) ?>" class="w-full h-full border-0 transition-all duration-300"></iframe>
            <?php else: ?>
                <div class="flex items-center justify-center h-full">
                    <div class="text-center">
                        <div class="inline-flex items-center justify-center w-16 h-16 rounded-full bg-gray-200 mb-4">
                            <svg class="w-8 h-8 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                        </div>
                        <p class="text-lg font-medium text-gray-900">Select a page to start building</p>
                    </div>
                </div>
            <?php endif; ?>
        </div>
        
        <!-- Left Sidebar -->
        <div id="leftSidebar" class="transition-all duration-300 ease-in-out translate-x-0 h-full w-80 fixed left-0 bg-white border-r border-gray-200 flex flex-col z-10">
            <!-- Tabs -->
            <div class="border-b border-gray-200 p-3">
                <ul class="flex flex-wrap -mb-px text-xs font-medium text-center" id="blocksTabs" role="tablist">
                    <li class="mr-2" role="presentation">
                        <button class="inline-flex items-center gap-1.5 px-3 py-[7px] text-xs font-medium rounded-full bg-blue-50 text-blue-600 custom-tabs" id="global-blocks-tab" data-tabs-target="#global-blocks" type="button" role="tab" aria-controls="global-blocks" aria-selected="true">Global Blocks</button>
                    </li>
                    <li class="mr-2" role="presentation">
                        <button class="inline-flex items-center gap-1.5 px-3 py-[7px] text-xs font-medium rounded-full hover:bg-gray-100 text-gray-500 custom-tabs" id="settings-tab" data-tabs-target="#settings" type="button" role="tab" aria-controls="settings" aria-selected="false">Settings</button>
                    </li>
                </ul>
            </div>
            
            <div id="blocksTabContent" class="flex-1 flex flex-col">
                <!-- Global Blocks Tab -->
                <div class="hidden flex-1 flex flex-col custom-tabpanel" id="global-blocks" role="tabpanel" aria-labelledby="global-blocks-tab">
                    <div class="p-3">
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                                <svg class="w-4 h-4 text-gray-500" aria-hidden="true" fill="none" viewBox="0 0 20 20">
                                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z"/>
                                </svg>
                            </div>
                            <input type="text" id="blockSearch" class="block w-full p-2 pl-10 text-sm text-gray-900 border border-gray-300 rounded-lg bg-gray-50 focus:ring-blue-500 focus:border-blue-500" placeholder="Search blocks...">
                        </div>
                    </div>
                    
                    <div class="flex-1 pl-3">
                        <!-- Block Types -->
                        <div id="blocksList" class="mb-4 h-[calc(100dvh-158px)] overflow-auto pr-3">
                            <?= $globalBlockList ?>
                        </div>
                    </div>
                </div>
                
                <!-- Settings Tab -->
                <div class="hidden flex-1 flex flex-col custom-tabpanel" id="settings" role="tabpanel" aria-labelledby="settings-tab">
                    <div class="flex-1 pl-3">
                        <div class="mb-4 h-[calc(100vh-121px)] overflow-auto pr-3 pt-3">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Page Settings</h3>
                            
                            <div class="space-y-4">
                                <!-- Page Status -->
                                <div>
                                    <label for="pageStatus" class="block text-sm font-medium text-gray-700 mb-1">Page Status</label>
                                    <select id="pageStatus" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5">
                                        <option value="draft" <?= ($pageData && $pageData['status'] == 'draft') ? 'selected' : '' ?>>Draft</option>
                                        <option value="published" <?= ($pageData && $pageData['status'] == 'published') ? 'selected' : '' ?>>Published</option>
                                    </select>
                                </div>
                                
                                <!-- Page Template -->
                                <div>
                                    <label for="pageTemplate" class="block text-sm font-medium text-gray-700 mb-1">Page Template</label>
                                    <select id="pageTemplate" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5">
                                        <option value="">Default Template</option>
                                        <option value="landing" <?= ($pageData && isset($pageData['template_id']) && $pageData['template_id'] == 'landing') ? 'selected' : '' ?>>Landing Page</option>
                                        <option value="blog" <?= ($pageData && isset($pageData['template_id']) && $pageData['template_id'] == 'blog') ? 'selected' : '' ?>>Blog Page</option>
                                    </select>
                                </div>
                                
                                <!-- SEO Settings -->
                                <div class="border-t border-gray-200 pt-4 mt-4">
                                    <h4 class="text-md font-medium text-gray-900 mb-3">SEO Settings</h4>
                                    
                                    <div class="space-y-3">
                                        <div>
                                            <label for="metaTitle" class="block text-sm font-medium text-gray-700 mb-1">Meta Title</label>
                                            <input type="text" id="metaTitle" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5" placeholder="Meta title" value="<?= ($pageData && isset($pageData['meta_title'])) ? htmlspecialchars($pageData['meta_title']) : '' ?>">
                                        </div>
                                        
                                        <div>
                                            <label for="metaDescription" class="block text-sm font-medium text-gray-700 mb-1">Meta Description</label>
                                            <textarea id="metaDescription" rows="3" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5" placeholder="Meta description"><?= ($pageData && isset($pageData['meta_description'])) ? htmlspecialchars($pageData['meta_description']) : '' ?></textarea>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Save Button -->
                                <div class="pt-4">
                                    <button type="button" id="saveSettings" class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center">Save Settings</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>