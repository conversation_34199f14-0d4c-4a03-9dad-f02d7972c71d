<?php

    add_style(asset('modules/Dashboard/Assets/css/dashboard.css'));
    // Add view-specific scripts
    add_script(asset('modules/Dashboard/Assets/js/dashboard.js'), ['defer' => true]);
?>

<!-- Dashboard Content -->
<div class="p-6">
    <!-- Welcome Section -->
    <div class="mb-8">
        <h1 class="text-2xl font-semibold text-gray-900">Welcome to CMS Dashboard</h1>
        <p class="mt-2 text-gray-600">Here's an overview of your content management system.</p>
    </div>

    <!-- Stats Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
        <!-- Templates Stats -->
        <div class="p-4 bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600">Total Templates</p>
                    <h3 class="text-2xl font-bold text-[<?= $stats['templates']['color'] ?>]">
                        <?= $stats['templates']['count'] ?>
                    </h3>
                </div>
                <div class="p-2 bg-[<?= $stats['templates']['color'] ?>]/10 rounded-lg">
                    <i data-lucide="<?= $stats['templates']['icon'] ?>" class="w-6 h-6 text-[<?= $stats['templates']['color'] ?>]"></i>
                </div>
            </div>
        </div>

        <!-- Blocks Stats -->
        <div class="p-4 bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600">Total Blocks</p>
                    <h3 class="text-2xl font-bold text-[<?= $stats['blocks']['color'] ?>]">
                        <?= $stats['blocks']['count'] ?>
                    </h3>
                </div>
                <div class="p-2 bg-[<?= $stats['blocks']['color'] ?>]/10 rounded-lg">
                    <i data-lucide="<?= $stats['blocks']['icon'] ?>" class="w-6 h-6 text-[<?= $stats['blocks']['color'] ?>]"></i>
                </div>
            </div>
        </div>

        <!-- Pages Stats -->
        <div class="p-4 bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600">Total Pages</p>
                    <h3 class="text-2xl font-bold text-[<?= $stats['pages']['color'] ?>]">
                        <?= $stats['pages']['count'] ?>
                    </h3>
                </div>
                <div class="p-2 bg-[<?= $stats['pages']['color'] ?>]/10 rounded-lg">
                    <i data-lucide="<?= $stats['pages']['icon'] ?>" class="w-6 h-6 text-[<?= $stats['pages']['color'] ?>]"></i>
                </div>
            </div>
        </div>

        <!-- Users Stats -->
        <div class="p-4 bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600">Active Users</p>
                    <h3 class="text-2xl font-bold text-[<?= $stats['users']['color'] ?>]">
                        <?= $stats['users']['count'] ?>
                    </h3>
                </div>
                <div class="p-2 bg-[<?= $stats['users']['color'] ?>]/10 rounded-lg">
                    <i data-lucide="<?= $stats['users']['icon'] ?>" class="w-6 h-6 text-[<?= $stats['users']['color'] ?>]"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activity Section -->
    <div class="mb-8">
        <h2 class="text-xl font-semibold text-gray-900 mb-4">Recent Activity</h2>
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="p-4">
                <div class="space-y-4">
                    <?php foreach ($recentActivities as $activity): ?>
                    <div class="flex items-center">
                        <div class="p-2 bg-[<?= $activity['color'] ?>]/10 rounded-lg mr-4">
                            <i data-lucide="<?= $activity['icon'] ?>" class="w-5 h-5 text-[<?= $activity['color'] ?>]"></i>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-900"><?= $activity['title'] ?></p>
                            <p class="text-xs text-gray-500"><?= $activity['time'] ?></p>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>
</div>