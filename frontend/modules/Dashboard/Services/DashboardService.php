<?php

namespace Modules\Dashboard\Services;

use Core\Service;

class DashboardService extends Service
{


    public function getDashboardStats()
    {
        // In a real application, these would come from database queries
        return [
            'templates' => [
                'count' => 25,
                'color' => '#0C5BE2',
                'icon' => 'layout-template'
            ],
            'blocks' => [
                'count' => 42,
                'color' => '#3CBF61',
                'icon' => 'box'
            ],
            'pages' => [
                'count' => 18,
                'color' => '#F69964',
                'icon' => 'file-text'
            ],
            'users' => [
                'count' => 8,
                'color' => '#3C58BF',
                'icon' => 'users'
            ]
        ];
    }

    public function getRecentActivities()
    {
        // In a real application, these would come from database queries
        return [
            [
                'title' => 'New template created',
                'time' => '2 hours ago',
                'icon' => 'layout-template',
                'color' => '#0C5BE2'
            ],
            [
                'title' => 'Block updated',
                'time' => '5 hours ago',
                'icon' => 'box',
                'color' => '#3CBF61'
            ],
            [
                'title' => 'New page published',
                'time' => '1 day ago',
                'icon' => 'file-text',
                'color' => '#F69964'
            ]
        ];
    }
}
