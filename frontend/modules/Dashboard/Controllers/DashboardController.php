<?php

namespace Modules\Dashboard\Controllers;

use App\Services\Auth;
use Core\Controller;
use Core\Request;
use Modules\Dashboard\Services\DashboardService;

class DashboardController extends Controller
{
    protected $dashboardService;

    public function __construct()
    {
        $this->dashboardService = new DashboardService();
    }

    public function index(Request $request)
{
    if (Auth::check() === false) {
        return $this->redirect('/login');
    }

    // Regular request - render full page
    $this->setTitle('Dashboard');

    // Get data from service
    $stats = $this->dashboardService->getDashboardStats();
    $recentActivities = $this->dashboardService->getRecentActivities();

    return $this->view('modules/Dashboard/Views/index', [
        'breadcrumbs' => [
            ['label' => 'Dashboard', 'url' => url('dashboard')]
        ],
        'stats' => $stats,
        'recentActivities' => $recentActivities,
    ]);
}
}