<?php
add_style(asset('modules/media/Assets/css/index.css'));
add_script(asset('modules/media/Assets/js/index.js'));
?>

<header class="bg-white py-3 border-b border-gray-200 mx-4">
    <div class="flex items-center justify-between">
        <!-- Left Side - Search Bar -->
        <div class="flex-1 max-w-xl">
            <div class="flex items-center">
                <div class="flex flex-col">
                    <!-- Breadcrumb -->
                    <div class="flex items-center space-x-2 text-[12px] text-gray-500">
                        <a href="<?= url('/dashboard') ?>" class="hover:text-[#0C5BE2]">Home</a>
                        <span>/</span>
                        <span>Media</span>
                    </div>
                    <!-- page Title -->
                    <h1 class="text-xl font-semibold text-gray-900">
                        Media Manager
                    </h1>
                </div>
            </div>
        </div>
        <!-- Right Side - Actions -->
        <div class="flex items-center space-x-2">
            <button id="createFolderBtn" type="button" class="text-xs text-white bg-[#0C5BE2] px-2 py-1.5 rounded hover:bg-blue-700 flex items-center gap-1">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" data-lucide="plus" class="lucide lucide-plus w-4 h-4">
                    <path d="M5 12h14"></path>
                    <path d="M12 5v14"></path>
                </svg>
                <span>Create Folder</span>
            </button>
            <button id="uploadBtn" type="button" class="text-xs text-white bg-green-600 px-2 py-1.5 rounded hover:bg-green-700 flex items-center gap-1">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                </svg>
                <span>Upload Files</span>
            </button>
        </div>
    </div>
</header>

<div class="container-fluid px-4 py-4">
    <!-- Stats Bar -->
    <div id="statsBar" class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <div class="bg-white p-4 rounded-lg shadow-sm border">
            <div class="flex items-center">
                <div class="p-2 bg-blue-100 rounded-lg">
                    <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Total Folders</p>
                    <p id="totalFolders" class="text-2xl font-semibold text-gray-900">0</p>
                </div>
            </div>
        </div>
        <div class="bg-white p-4 rounded-lg shadow-sm border">
            <div class="flex items-center">
                <div class="p-2 bg-green-100 rounded-lg">
                    <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Total Files</p>
                    <p id="totalFiles" class="text-2xl font-semibold text-gray-900">0</p>
                </div>
            </div>
        </div>
        <div class="bg-white p-4 rounded-lg shadow-sm border">
            <div class="flex items-center">
                <div class="p-2 bg-purple-100 rounded-lg">
                    <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z" />
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Current Location</p>
                    <p id="currentLocation" class="text-lg font-semibold text-gray-900">Home</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Breadcrumb -->
    <div class="bg-white rounded-lg shadow-sm border p-4 mb-6">
        <nav class="flex" aria-label="Breadcrumb">
            <ol id="breadcrumb" class="inline-flex items-center space-x-1 md:space-x-3">
                <li class="inline-flex items-center">
                    <span class="text-gray-700 hover:text-blue-600 cursor-pointer font-medium" data-path="">
                        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z" />
                        </svg>
                        Home
                    </span>
                </li>
            </ol>
        </nav>
    </div>

    <!-- Upload Area -->
    <div id="uploadArea" class="hidden bg-white rounded-lg border-2 border-dashed border-gray-300 p-8 mb-6 text-center hover:border-blue-400 transition-colors duration-200">
        <svg class="mx-auto h-12 w-12 text-gray-400 mb-4" stroke="currentColor" fill="none" viewBox="0 0 48 48">
            <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
        </svg>
        <p class="text-xl font-medium text-gray-900 mb-2">Drop files here to upload</p>
        <p class="text-gray-500 mb-4">or click to select files (Max 10MB per file)</p>
        <input type="file" id="fileInput" class="hidden" multiple accept="image/*">
        <button id="selectFileBtn" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200">
            Select Files
        </button>
    </div>

    <!-- Loading Spinner -->
    <div id="loadingSpinner" class="hidden text-center py-12">
        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <p class="mt-4 text-gray-600">Loading media files...</p>
    </div>

    <!-- Media Grid -->
    <div id="mediaGrid" class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 xl:grid-cols-8 gap-4 mb-8">
        <!-- Content will be loaded here -->
    </div>

    <!-- No Content Message -->
    <div id="noContent" class="hidden text-center py-16">
        <svg class="mx-auto h-16 w-16 text-gray-300 mb-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
        </svg>
        <h3 class="text-xl font-medium text-gray-900 mb-2">No media files found</h3>
        <p class="text-gray-500 mb-6">Start by uploading some images or creating folders to organize your media</p>
        <button onclick="document.getElementById('uploadBtn').click()" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200">
            Upload Your First File
        </button>
    </div>
</div>

<?php require_once __DIR__ . '/modals.php'; ?>

<script>
    // Pass upload configuration from PHP to JavaScript
    window.uploadConfig = <?= json_encode($uploadConfig ?? []) ?>;
</script>