<?php

namespace Modules\Media\Services;

use Package\FileUpload\FileUploadManager;
use Package\FileUpload\Storage\StorageDriverFactory;

class MediaService
{

    private $uploadBaseDir;
    private $allowedTypes = [];
    private $allowedExtensions = [];
    private $maxFileSize;
    protected $fileUploadManager;
    protected $storageDriver;

    // Method chaining configuration properties
    protected bool $imageProcessingEnabled = false;
    protected bool $thumbnailGenerationEnabled = false;
    protected bool $imageCompressionEnabled = false;
    protected bool $metadataStrippingEnabled = false;
    protected bool $validationEnabled = true;
    protected bool $securityScanEnabled = false;
    protected int $imageQuality = 85;
    protected array $thumbnailSizes = [];
    protected array $runtimeValidationRules = [];

    public function __construct()
    {
        // Load configuration from environment variables first
        $this->loadConfiguration();

        // Set upload directory from environment variable or default
        $uploadRoot = $_ENV['FILEUPLOAD_LOCAL_ROOT'] ?? 'uploads/media';

        // Convert relative path to absolute path with better compatibility
        if (!str_starts_with($uploadRoot, '/')) {
            // Try different base paths for better compatibility
            $possibleBases = [
                __DIR__ . '/../../../',         // Module relative path
                getcwd() . '/',                 // Current working directory
                $_SERVER['DOCUMENT_ROOT'] ?? __DIR__ . '/../../../', // Document root
            ];

            foreach ($possibleBases as $base) {
                $testPath = $base . $uploadRoot;
                $testDir = dirname($testPath);
                if (is_dir($testDir) && is_writable($testDir)) {
                    $this->uploadBaseDir = $testPath;
                    break;
                }
            }

            // Fallback to module relative if none worked
            if (!isset($this->uploadBaseDir)) {
                $this->uploadBaseDir = __DIR__ . '/../../../' . $uploadRoot;
            }
        } else {
            $this->uploadBaseDir = $uploadRoot;
        }

        // Ensure the directory exists
        if (!file_exists($this->uploadBaseDir)) {
            mkdir($this->uploadBaseDir, 0777, true);
            chmod($this->uploadBaseDir, 0777);
        }

        // Initialize FileUpload package
        try {
            $this->storageDriver = StorageDriverFactory::createFromConfig();
            $this->fileUploadManager = new FileUploadManager();
        } catch (\Exception $e) {
            // Fallback to manual file handling if FileUpload package fails
            error_log('FileUpload package initialization failed: ' . $e->getMessage());
            $this->fileUploadManager = null;
            $this->storageDriver = null;
        }
    }

    /**
     * Create MediaService with method chaining configuration
     */
    public static function createWithChaining(): self
    {
        return new self();
    }

    /**
     * Create MediaService for avatar uploads
     */
    public static function createForAvatars(): self
    {
        return (new self())
            ->setImageProcessing(true, 95)
            ->setThumbnailGeneration(true, [
                ['name' => 'thumb', 'width' => 100, 'height' => 100, 'mode' => 'crop'],
                ['name' => 'profile', 'width' => 200, 'height' => 200, 'mode' => 'crop']
            ])
            ->setImageCompression(true, 95)
            ->setMetadataStripping(true)
            ->setValidation(true, ['max_file_size' => 1048576]) // 1MB
            ->setSecurityScan(true)
            ->setAllowedExtensions(['jpg', 'jpeg', 'png'])
            ->setMaxFileSize(1048576);
    }

    /**
     * Create MediaService for gallery uploads
     */
    public static function createForGallery(): self
    {
        return (new self())
            ->setImageProcessing(true, 90)
            ->setThumbnailGeneration(true, [
                ['name' => 'thumb', 'width' => 200, 'height' => 200, 'mode' => 'crop'],
                ['name' => 'medium', 'width' => 600, 'height' => 400, 'mode' => 'fit'],
                ['name' => 'large', 'width' => 1200, 'height' => 800, 'mode' => 'fit']
            ])
            ->setImageCompression(true, 90)
            ->setMetadataStripping(true)
            ->setValidation(true, ['max_file_size' => 20971520]) // 20MB
            ->setSecurityScan(false)
            ->setAllowedExtensions(['jpg', 'jpeg', 'png', 'gif', 'webp'])
            ->setMaxFileSize(20971520);
    }

    /**
     * Create MediaService for document uploads
     */
    public static function createForDocuments(): self
    {
        return (new self())
            ->setImageProcessing(false)
            ->setThumbnailGeneration(false)
            ->setImageCompression(false)
            ->setMetadataStripping(false)
            ->setValidation(true, ['max_file_size' => 10485760]) // 10MB
            ->setSecurityScan(true)
            ->setAllowedExtensions(['pdf', 'doc', 'docx', 'txt'])
            ->setMaxFileSize(10485760);
    }

    /**
     * Enable/disable image processing with method chaining
     */
    public function setImageProcessing(bool $enabled, int $quality = 85): self
    {
        $this->imageProcessingEnabled = $enabled;
        if ($enabled) {
            $this->imageQuality = $quality;
        }
        return $this;
    }

    /**
     * Enable/disable thumbnail generation with method chaining
     */
    public function setThumbnailGeneration(bool $enabled, array $sizes = []): self
    {
        $this->thumbnailGenerationEnabled = $enabled;
        if ($enabled && !empty($sizes)) {
            $this->thumbnailSizes = $sizes;
        }
        return $this;
    }

    /**
     * Enable/disable image compression with method chaining
     */
    public function setImageCompression(bool $enabled, int $quality = 85): self
    {
        $this->imageCompressionEnabled = $enabled;
        if ($enabled) {
            $this->imageQuality = $quality;
        }
        return $this;
    }

    /**
     * Enable/disable metadata stripping with method chaining
     */
    public function setMetadataStripping(bool $enabled): self
    {
        $this->metadataStrippingEnabled = $enabled;
        return $this;
    }

    /**
     * Enable/disable file validation with method chaining
     */
    public function setValidation(bool $enabled, array $rules = []): self
    {
        $this->validationEnabled = $enabled;
        if ($enabled && !empty($rules)) {
            $this->runtimeValidationRules = array_merge($this->runtimeValidationRules, $rules);
        }
        return $this;
    }

    /**
     * Enable/disable security scanning with method chaining
     */
    public function setSecurityScan(bool $enabled): self
    {
        $this->securityScanEnabled = $enabled;
        return $this;
    }

    /**
     * Set allowed file extensions with method chaining
     */
    public function setAllowedExtensions(array $extensions): self
    {
        $this->allowedExtensions = $extensions;
        return $this;
    }

    /**
     * Set maximum file size with method chaining
     */
    public function setMaxFileSize(int $sizeInBytes): self
    {
        $this->maxFileSize = $sizeInBytes;
        return $this;
    }

    /**
     * Set allowed MIME types with method chaining
     */
    public function setAllowedTypes(array $types): self
    {
        $this->allowedTypes = $types;
        return $this;
    }

    /**
     * Load configuration from environment variables
     */
    private function loadConfiguration()
    {
        // Load allowed file types from environment variable
        $allowedTypesEnv = $_ENV['FILEUPLOAD_ALLOWED_TYPES'] ?? '';
        $this->allowedTypes = !empty($allowedTypesEnv) ?
            array_map('trim', explode(',', $allowedTypesEnv)) :
            ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];

        // Load allowed extensions from environment variable
        $allowedExtensionsEnv = $_ENV['FILEUPLOAD_ALLOWED_EXTENSIONS'] ?? '';
        $this->allowedExtensions = !empty($allowedExtensionsEnv) ?
            array_map('trim', explode(',', $allowedExtensionsEnv)) :
            ['jpg', 'jpeg', 'png', 'gif', 'webp'];

        // Load max file size from environment variable
        $this->maxFileSize = (int)($_ENV['FILEUPLOAD_MAX_SIZE'] ?? 10485760); // Default 10MB
    }

    /**
     * Get media files and folders list
     */
    public function getMediaList($folder = '')
    {
        $basePath = $this->uploadBaseDir;
        if (!empty($folder)) {
            $basePath .= '/' . $folder;
        }

        if (!file_exists($basePath)) {
            return [
                'success' => true,
                'data' => [
                    'folders' => [],
                    'files' => [],
                    'current_path' => $folder,
                    'total_folders' => 0,
                    'total_files' => 0
                ]
            ];
        }

        $folders = [];
        $files = [];

        $items = scandir($basePath);
        foreach ($items as $item) {
            if ($item === '.' || $item === '..') continue;

            $fullPath = $basePath . '/' . $item;
            $relativePath = $folder ? $folder . '/' . $item : $item;

            if (is_dir($fullPath)) {
                $folders[] = [
                    'name' => $item,
                    'path' => $relativePath,
                    'created' => date('Y-m-d H:i:s', filemtime($fullPath)),
                    'file_count' => $this->countFiles($fullPath)
                ];
            } else {
                $fileInfo = $this->getFileInfo($fullPath, $relativePath);
                if ($fileInfo) {
                    $files[] = $fileInfo;
                }
            }
        }

        // Sort folders and files alphabetically
        usort($folders, function ($a, $b) {
            return strcasecmp($a['name'], $b['name']);
        });

        usort($files, function ($a, $b) {
            return strcasecmp($a['name'], $b['name']);
        });

        return [
            'success' => true,
            'data' => [
                'folders' => $folders,
                'files' => $files,
                'current_path' => $folder,
                'total_folders' => count($folders),
                'total_files' => count($files)
            ]
        ];
    }

    /**
     * Create a new folder using FileUpload package
     */
    public function createFolder($folderName, $parentFolder = '')
    {
        try {
            // Sanitize folder name
            $folderName = $this->sanitizeFolderName($folderName);

            if (empty($folderName)) {
                return [
                    'success' => false,
                    'message' => 'Invalid folder name'
                ];
            }

            // Use FileUpload package if available, otherwise fallback to manual creation
            if ($this->fileUploadManager) {
                try {
                    $folderPath = !empty($parentFolder) ? $parentFolder . '/' . $folderName : $folderName;
                    $result = $this->fileUploadManager->createFolder($folderPath);

                    if ($result->isSuccess()) {
                        return [
                            'success' => true,
                            'message' => $result->getMessage() ?: 'Folder created successfully',
                            'data' => [
                                'name' => $folderName,
                                'path' => $result->getPath() ?: $folderPath
                            ]
                        ];
                    } else {
                        return [
                            'success' => false,
                            'message' => $result->getMessage() ?: 'Failed to create folder'
                        ];
                    }
                } catch (\Exception $e) {
                    // Fallback to manual creation if FileUpload fails
                    error_log('FileUpload createFolder failed, falling back to manual: ' . $e->getMessage());
                }
            }

            // Manual folder creation fallback
            $physicalPath = $this->uploadBaseDir;
            if (!empty($parentFolder)) {
                $physicalPath .= '/' . $parentFolder;
            }
            $physicalPath .= '/' . $folderName;

            // Check if folder already exists
            if (file_exists($physicalPath)) {
                return [
                    'success' => false,
                    'message' => 'Folder already exists'
                ];
            }

            // Create folder directly
            if (!mkdir($physicalPath, 0777, true)) {
                return [
                    'success' => false,
                    'message' => 'Failed to create folder'
                ];
            }

            // Set permissions
            chmod($physicalPath, 0777);

            return [
                'success' => true,
                'message' => 'Folder created successfully',
                'data' => [
                    'name' => $folderName,
                    'path' => $parentFolder ? $parentFolder . '/' . $folderName : $folderName
                ]
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Failed to create folder: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Upload file using FileUpload package with method chaining configuration
     */
    public function uploadFile($file, $folder = '')
    {
        try {
            $this->validateUpload($file);

            // Use FileUpload package for full feature support (thumbnails, processing, etc.)
            if ($this->fileUploadManager) {
                try {
                    // Apply method chaining configuration to FileUpload package
                    $uploader = \Package\FileUpload\FileUploadManager::createWithChaining()
                        ->setImageProcessing($this->imageProcessingEnabled, $this->imageQuality)
                        ->setThumbnailGeneration($this->thumbnailGenerationEnabled, $this->thumbnailSizes)
                        ->setImageCompression($this->imageCompressionEnabled, $this->imageQuality)
                        ->setMetadataStripping($this->metadataStrippingEnabled)
                        ->setValidation($this->validationEnabled, $this->runtimeValidationRules)
                        ->setSecurityScan($this->securityScanEnabled)
                        ->setUploadDirectory($folder ?: 'uploads/media')
                        ->setAllowedExtensions($this->allowedExtensions)
                        ->setMaxFileSize($this->maxFileSize);

                    $result = $uploader->upload($file);

                    if ($result->isSuccess()) {
                        // Extract filename from path
                        $filename = basename($result->getPath() ?: $file['name']);

                        // Generate URL using environment variable
                        $baseUrl = $_ENV['FILEUPLOAD_LOCAL_URL'] ?? '/uploads/media';
                        $relativePath = $folder ? $folder . '/' . $filename : $filename;
                        $url = rtrim($baseUrl, '/') . '/' . ltrim($relativePath, '/');

                        return [
                            'success' => true,
                            'message' => $result->getMessage() ?: 'File uploaded successfully',
                            'data' => [
                                'name' => $filename,
                                'path' => $result->getPath(),
                                'url' => $url,
                                'size' => $file['size'],
                                'type' => $file['type'],
                                'metadata' => $result->getMetadata()
                            ]
                        ];
                    } else {
                        return [
                            'success' => false,
                            'message' => $result->getMessage() ?: 'Failed to upload file',
                            'errors' => $result->getErrors()
                        ];
                    }
                } catch (\Exception $e) {
                    // Fallback to manual upload if FileUpload fails
                    error_log('FileUpload upload failed, falling back to manual: ' . $e->getMessage());
                }
            }

            // Manual upload fallback
            $uploadDir = $this->uploadBaseDir;
            if (!empty($folder)) {
                $uploadDir .= '/' . $folder;
                if (!file_exists($uploadDir)) {
                    mkdir($uploadDir, 0777, true);
                    chmod($uploadDir, 0777);
                }
            }

            $uniqueName = $this->generateUniqueFilename($file['name']);
            $targetPath = $uploadDir . '/' . $uniqueName;

            if (!move_uploaded_file($file['tmp_name'], $targetPath)) {
                return [
                    'success' => false,
                    'message' => 'Failed to upload file'
                ];
            }

            // Set permissions
            chmod($targetPath, 0777);

            // Generate URL using environment variable
            $baseUrl = $_ENV['FILEUPLOAD_LOCAL_URL'] ?? '/uploads/media';
            $relativePath = $folder ? $folder . '/' . $uniqueName : $uniqueName;
            $url = rtrim($baseUrl, '/') . '/' . ltrim($relativePath, '/');

            return [
                'success' => true,
                'message' => 'File uploaded successfully',
                'data' => [
                    'name' => $uniqueName,
                    'original_name' => $file['name'],
                    'url' => $url,
                    'folder' => $folder,
                    'size' => $file['size'],
                    'type' => $file['type'],
                    'path' => $folder ? $folder . '/' . $uniqueName : $uniqueName
                ]
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Failed to upload file: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Rename folder or file
     */
    public function renameItem($oldPath, $newName, $type = 'file')
    {
        try {
            // Sanitize the new name
            if ($type === 'folder') {
                $newName = $this->sanitizeFolderName($newName);
            } else {
                $newName = $this->sanitizeFileName($newName);
            }

            if (empty($newName)) {
                return [
                    'success' => false,
                    'message' => 'Invalid name provided'
                ];
            }

            $oldFullPath = $this->uploadBaseDir . '/' . $oldPath;

            // Get the parent directory
            $parentDir = dirname($oldFullPath);
            $newFullPath = $parentDir . '/' . $newName;

            // Check if old path exists
            if (!file_exists($oldFullPath)) {
                return [
                    'success' => false,
                    'message' => ucfirst($type) . ' not found'
                ];
            }

            // Check if new name already exists
            if (file_exists($newFullPath)) {
                return [
                    'success' => false,
                    'message' => ucfirst($type) . ' with this name already exists'
                ];
            }

            // Rename the item
            if (!rename($oldFullPath, $newFullPath)) {
                return [
                    'success' => false,
                    'message' => 'Failed to rename ' . $type
                ];
            }

            // Calculate new relative path
            $newRelativePath = str_replace($this->uploadBaseDir . '/', '', $newFullPath);

            return [
                'success' => true,
                'message' => ucfirst($type) . ' renamed successfully',
                'data' => [
                    'old_path' => $oldPath,
                    'new_path' => $newRelativePath,
                    'new_name' => $newName
                ]
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Error renaming ' . $type . ': ' . $e->getMessage()
            ];
        }
    }

    /**
     * Check if folder is empty
     */
    public function isFolderEmpty($path)
    {
        $fullPath = $this->uploadBaseDir . '/' . $path;

        if (!is_dir($fullPath)) {
            return true; // Not a directory, consider it "empty"
        }

        $items = scandir($fullPath);
        foreach ($items as $item) {
            if ($item !== '.' && $item !== '..') {
                return false; // Found a file or subdirectory
            }
        }

        return true;
    }

    /**
     * Get folder contents count
     */
    public function getFolderContents($path)
    {
        $fullPath = $this->uploadBaseDir . '/' . $path;

        if (!is_dir($fullPath)) {
            return ['files' => 0, 'folders' => 0];
        }

        $fileCount = 0;
        $folderCount = 0;

        $items = scandir($fullPath);
        foreach ($items as $item) {
            if ($item === '.' || $item === '..') continue;

            $itemPath = $fullPath . '/' . $item;
            if (is_dir($itemPath)) {
                $folderCount++;
            } else {
                $fileCount++;
            }
        }

        return ['files' => $fileCount, 'folders' => $folderCount];
    }

    /**
     * Delete folder or file
     */
    public function deleteItem($path, $type = 'file', $force = false)
    {
        try {
            // For folders, check if they contain items (unless force delete)
            if ($type === 'folder' && !$force) {
                $contents = $this->getFolderContents($path);
                if ($contents['files'] > 0 || $contents['folders'] > 0) {
                    return [
                        'success' => false,
                        'message' => 'Folder is not empty',
                        'data' => [
                            'requires_confirmation' => true,
                            'contents' => $contents
                        ]
                    ];
                }
            }

            // Use FileUpload package if available, otherwise fallback to manual deletion
            if ($this->fileUploadManager) {
                try {
                    if ($type === 'folder') {
                        $result = $this->fileUploadManager->deleteFolder($path);

                        if ($result->isSuccess()) {
                            return [
                                'success' => true,
                                'message' => $result->getMessage() ?: 'Folder deleted successfully'
                            ];
                        } else {
                            return [
                                'success' => false,
                                'message' => $result->getMessage() ?: 'Failed to delete folder'
                            ];
                        }
                    } else {
                        $result = $this->fileUploadManager->deleteFile($path);

                        if ($result->isSuccess()) {
                            return [
                                'success' => true,
                                'message' => $result->getMessage() ?: 'File deleted successfully'
                            ];
                        } else {
                            return [
                                'success' => false,
                                'message' => $result->getMessage() ?: 'Failed to delete file'
                            ];
                        }
                    }
                } catch (\Exception $e) {
                    // Fallback to manual deletion if FileUpload fails
                    error_log('FileUpload delete failed, falling back to manual: ' . $e->getMessage());
                }
            }

            // Manual deletion fallback
            $fullPath = $this->uploadBaseDir . '/' . $path;

            if (!file_exists($fullPath)) {
                return [
                    'success' => false,
                    'message' => 'Item not found'
                ];
            }

            if ($type === 'folder') {
                if (!$this->deleteDirectory($fullPath)) {
                    return [
                        'success' => false,
                        'message' => 'Failed to delete folder'
                    ];
                }
            } else {
                if (!unlink($fullPath)) {
                    return [
                        'success' => false,
                        'message' => 'Failed to delete file'
                    ];
                }
            }

            return [
                'success' => true,
                'message' => ucfirst($type) . ' deleted successfully'
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Failed to delete ' . $type . ': ' . $e->getMessage()
            ];
        }
    }

    /**
     * Get file URL for copying
     */
    public function getFileUrl($path)
    {
        // Get URL from environment variable or default
        $baseUrl = $_ENV['FILEUPLOAD_LOCAL_URL'] ?? '/uploads/media';
        $url = rtrim($baseUrl, '/') . '/' . ltrim($path, '/');

        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
        $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
        $fullUrl = $protocol . '://' . $host . $url;

        return [
            'success' => true,
            'data' => [
                'url' => $url,
                'full_url' => $fullUrl
            ]
        ];
    }

    // Helper methods
    private function sanitizeFolderName($name)
    {
        $name = preg_replace('/[^a-zA-Z0-9\-_\s]/', '', $name);
        $name = preg_replace('/\s+/', '-', $name);
        return trim($name, '-');
    }

    private function sanitizeFileName($name)
    {
        // Get file extension
        $extension = pathinfo($name, PATHINFO_EXTENSION);
        $filename = pathinfo($name, PATHINFO_FILENAME);

        // Remove dangerous characters from filename
        $filename = preg_replace('/[^a-zA-Z0-9\-_\s]/', '', $filename);
        $filename = preg_replace('/\s+/', '-', $filename);
        $filename = trim($filename, '-');

        // Return sanitized name with extension
        return $filename . ($extension ? '.' . $extension : '');
    }

    private function generateUniqueFilename($originalName)
    {
        $extension = pathinfo($originalName, PATHINFO_EXTENSION);
        $filename = pathinfo($originalName, PATHINFO_FILENAME);
        $filename = preg_replace('/[^a-zA-Z0-9\-_]/', '', $filename);
        $filename = substr($filename, 0, 50); // Limit filename length
        return $filename . '_' . uniqid() . '.' . strtolower($extension);
    }

    private function validateUpload($file)
    {
        if ($file['error'] !== UPLOAD_ERR_OK) {
            throw new \Exception('Upload error: ' . $this->getUploadErrorMessage($file['error']));
        }

        if ($file['size'] > $this->maxFileSize) {
            throw new \Exception('File size exceeds limit of ' . ($this->maxFileSize / 1024 / 1024) . 'MB');
        }

        // Validate MIME type
        if (!in_array($file['type'], $this->allowedTypes)) {
            $allowedTypesStr = implode(', ', $this->allowedTypes);
            throw new \Exception('Invalid file type. Allowed types: ' . $allowedTypesStr);
        }

        // Validate file extension
        $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        if (!in_array($extension, $this->allowedExtensions)) {
            $allowedExtensionsStr = implode(', ', $this->allowedExtensions);
            throw new \Exception('Invalid file extension. Allowed extensions: ' . $allowedExtensionsStr);
        }

        // Additional validation for image files to prevent EXIF errors
        if (strpos($file['type'], 'image/') === 0) {
            $this->validateImageFile($file);
        }
    }

    /**
     * Validate image file to prevent EXIF errors
     */
    private function validateImageFile($file)
    {
        $tmpPath = $file['tmp_name'];

        // Skip getimagesize() validation for SVG files as they are XML-based
        if ($file['type'] === 'image/svg+xml') {
            $this->validateSvgFile($tmpPath);
            return; // Exit early for SVG files
        }

        // Check if file is a valid image (for non-SVG images)
        $imageInfo = @getimagesize($tmpPath);
        if ($imageInfo === false) {
            throw new \Exception('Invalid image file or corrupted image data');
        }

        // For JPEG files, check if EXIF data can be read safely
        if ($file['type'] === 'image/jpeg' || $file['type'] === 'image/jpg') {
            // Only try to read EXIF if the function exists and file supports it
            if (function_exists('exif_read_data')) {
                // Suppress errors and check if EXIF can be read
                $exifData = @exif_read_data($tmpPath);
                // We don't throw an error if EXIF can't be read, just log it
                if ($exifData === false) {
                    error_log("EXIF data could not be read from file: " . $file['name']);
                }
            }
        }
    }

    /**
     * Validate SVG file for security
     */
    private function validateSvgFile($filePath)
    {
        $content = file_get_contents($filePath);

        // Check for potentially dangerous content in SVG
        $dangerousPatterns = [
            '/<script/i',
            '/javascript:/i',
            '/on\w+\s*=/i', // onclick, onload, etc.
            '/<iframe/i',
            '/<object/i',
            '/<embed/i'
        ];

        foreach ($dangerousPatterns as $pattern) {
            if (preg_match($pattern, $content)) {
                throw new \Exception('SVG file contains potentially dangerous content and cannot be uploaded');
            }
        }
    }

    private function getFileInfo($fullPath, $relativePath)
    {
        $pathInfo = pathinfo($fullPath);
        $extension = strtolower($pathInfo['extension'] ?? '');

        // Get allowed extensions from environment configuration
        $allowedExtensions = array_map('trim', explode(',', $_ENV['FILEUPLOAD_ALLOWED_EXTENSIONS'] ?? ''));
        $allowedExtensions = array_map('strtolower', $allowedExtensions);

        // Check if the file extension is allowed
        if (!in_array($extension, $allowedExtensions)) {
            return null;
        }

        // Generate URL using environment variable
        $baseUrl = $_ENV['FILEUPLOAD_LOCAL_URL'] ?? '/uploads/media';
        $url = rtrim($baseUrl, '/') . '/' . ltrim($relativePath, '/');

        // Check if it's an image for dimensions
        $imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'svg', 'webp'];
        $isImage = in_array($extension, $imageExtensions);

        return [
            'name' => basename($fullPath),
            'path' => $relativePath,
            'url' => $url,
            'size' => filesize($fullPath),
            'type' => mime_content_type($fullPath),
            'extension' => $extension,
            'created' => date('Y-m-d H:i:s', filemtime($fullPath)),
            'dimensions' => $isImage ? $this->getImageDimensions($fullPath) : null,
            'is_image' => $isImage
        ];
    }

    private function getImageDimensions($path)
    {
        try {
            // Check if this is an SVG file
            $extension = strtolower(pathinfo($path, PATHINFO_EXTENSION));
            if ($extension === 'svg') {
                // For SVG files, we can't use getimagesize(), so return null dimensions
                return ['width' => null, 'height' => null];
            }

            $imageSize = getimagesize($path);
            return [
                'width' => $imageSize[0] ?? 0,
                'height' => $imageSize[1] ?? 0
            ];
        } catch (\Exception $e) {
            return ['width' => 0, 'height' => 0];
        }
    }

    private function countFiles($dir)
    {
        if (!is_dir($dir)) return 0;

        $count = 0;
        $items = scandir($dir);

        // Get allowed extensions from environment configuration
        $allowedExtensions = array_map('trim', explode(',', $_ENV['FILEUPLOAD_ALLOWED_EXTENSIONS'] ?? ''));
        $allowedExtensions = array_map('strtolower', $allowedExtensions);

        foreach ($items as $item) {
            if ($item === '.' || $item === '..') continue;

            $fullPath = $dir . '/' . $item;
            if (is_file($fullPath)) {
                // Only count files with allowed extensions
                $pathInfo = pathinfo($fullPath);
                $extension = strtolower($pathInfo['extension'] ?? '');

                if (in_array($extension, $allowedExtensions)) {
                    $count++;
                }
            } elseif (is_dir($fullPath)) {
                $count += $this->countFiles($fullPath);
            }
        }

        return $count;
    }

    private function deleteDirectory($dir)
    {
        if (!is_dir($dir)) {
            return unlink($dir);
        }

        $files = array_diff(scandir($dir), ['.', '..']);
        foreach ($files as $file) {
            $this->deleteDirectory($dir . '/' . $file);
        }

        return rmdir($dir);
    }

    private function getUploadErrorMessage($code)
    {
        switch ($code) {
            case UPLOAD_ERR_INI_SIZE:
                return 'File exceeds upload_max_filesize';
            case UPLOAD_ERR_FORM_SIZE:
                return 'File exceeds MAX_FILE_SIZE';
            case UPLOAD_ERR_PARTIAL:
                return 'File was only partially uploaded';
            case UPLOAD_ERR_NO_FILE:
                return 'No file was uploaded';
            case UPLOAD_ERR_NO_TMP_DIR:
                return 'Missing temporary folder';
            case UPLOAD_ERR_CANT_WRITE:
                return 'Failed to write file to disk';
            case UPLOAD_ERR_EXTENSION:
                return 'PHP extension stopped upload';
            default:
                return 'Unknown upload error';
        }
    }
}
