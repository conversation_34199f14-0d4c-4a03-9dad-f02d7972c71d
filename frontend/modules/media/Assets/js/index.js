// Media Manager JavaScript
document.addEventListener('DOMContentLoaded', function() {
    class MediaManager {
        constructor() {
            this.currentPath = '';
            this.apiBase = window.location.origin + '/media';
            this.currentPreviewPath = '';
            this.currentPreviewUrl = '';
            this.currentPreviewFullUrl = '';
            this.init();
        }

        init() {
            this.bindEvents();
            this.loadMedia();
        }

        bindEvents() {
            // Create folder events
            document.getElementById('createFolderBtn').addEventListener('click', () => {
                document.getElementById('folderModal').classList.remove('hidden');
                document.getElementById('folderNameInput').focus();
            });

            document.getElementById('closeFolderModal').addEventListener('click', () => {
                this.closeFolderModal();
            });

            document.getElementById('cancelFolderBtn').addEventListener('click', () => {
                this.closeFolderModal();
            });

            document.getElementById('confirmFolderBtn').addEventListener('click', () => {
                this.createFolder();
            });

            // Upload events
            document.getElementById('uploadBtn').addEventListener('click', () => {
                const uploadArea = document.getElementById('uploadArea');
                uploadArea.classList.toggle('hidden');
            });

            document.getElementById('selectFileBtn').addEventListener('click', () => {
                document.getElementById('fileInput').click();
            });

            document.getElementById('fileInput').addEventListener('change', (e) => {
                this.handleFiles(e.target.files);
            });

            // Drag and drop events
            const uploadArea = document.getElementById('uploadArea');
            
            uploadArea.addEventListener('dragover', (e) => {
                e.preventDefault();
                uploadArea.classList.add('border-blue-400', 'bg-blue-50');
            });

            uploadArea.addEventListener('dragleave', (e) => {
                e.preventDefault();
                uploadArea.classList.remove('border-blue-400', 'bg-blue-50');
            });

            uploadArea.addEventListener('drop', (e) => {
                e.preventDefault();
                uploadArea.classList.remove('border-blue-400', 'bg-blue-50');
                this.handleFiles(e.dataTransfer.files);
            });

            // Preview modal events
            document.getElementById('closePreviewBtn').addEventListener('click', () => {
                document.getElementById('previewModal').classList.add('hidden');
            });

            document.getElementById('copyUrlBtn').addEventListener('click', () => {
                this.copyToClipboard(this.currentPreviewUrl, 'URL copied to clipboard!');
            });

            document.getElementById('copyFullUrlBtn').addEventListener('click', () => {
                this.copyToClipboard(this.currentPreviewFullUrl, 'Full URL copied to clipboard!');
            });

            document.getElementById('showTransformDetailsBtn').addEventListener('click', () => {
                this.toggleTransformDetails();
            });

            document.getElementById('deleteFileBtn').addEventListener('click', () => {
                this.deleteCurrentFile();
            });

            // Keyboard shortcuts
            document.addEventListener('keydown', (e) => {
                if (e.key === 'Escape') {
                    document.getElementById('folderModal').classList.add('hidden');
                    document.getElementById('previewModal').classList.add('hidden');
                }
            });

            // Enter key for folder creation
            document.getElementById('folderNameInput').addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.createFolder();
                }
            });
        }

        async loadMedia(path = '', retryCount = 0) {
            this.currentPath = path;
            this.showLoading(true);
            
            try {
                const response = await fetch(`${this.apiBase}?action=list&folder=${encodeURIComponent(path)}`, {
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                });

                if (!response.ok) {
                    throw new Error(`Server returned ${response.status}: ${response.statusText}`);
                }
                
                const responseText = await response.text();
                
                if (!responseText.trim() && retryCount < 3) {
                    console.log(`Empty response received, retrying (${retryCount + 1}/3)...`);
                    setTimeout(() => {
                        this.loadMedia(path, retryCount + 1);
                    }, 500);
                    return;
                }
                
                let result;
                try {
                    result = JSON.parse(responseText);
                } catch (e) {
                    console.error('Invalid JSON response:', responseText);
                    throw new Error('Server returned invalid JSON');
                }
                
                if (result.success) {
                    this.renderMedia(result.data);
                    this.updateBreadcrumb(path);
                    this.updateStats(result.data);
                } else {
                    this.showError(result.message);
                }
            } catch (error) {
                this.showError('Failed to load media: ' + error.message);
                console.error('Media loading error:', error);
            } finally {
                this.showLoading(false);
            }
        }

        renderMedia(data) {
            const grid = document.getElementById('mediaGrid');
            const noContent = document.getElementById('noContent');
            
            if (data.folders.length === 0 && data.files.length === 0) {
                grid.innerHTML = '';
                noContent.classList.remove('hidden');
                return;
            }
            
            noContent.classList.add('hidden');
            
            let html = '';
            
            // Render folders
            data.folders.forEach(folder => {
                html += `
                    <div class="bg-white rounded-lg shadow-sm border hover:shadow-md transition-shadow duration-200 cursor-pointer group media-grid-item" onclick="mediaManager.loadMedia('${folder.path}')">
                        <div class="p-4 text-center">
                            <div class="relative">
                                <svg class="w-12 h-12 mx-auto mb-3 text-yellow-500 group-hover:text-yellow-600 transition-colors duration-200 media-folder-icon" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M10 4H4c-1.11 0-2 .89-2 2v12c0 1.11.89 2 2 2h16c1.11 0 2-.89 2-2V8c0-1.11-.89-2-2-2h-8l-2-2z"/>
                                </svg>
                                ${folder.file_count > 0 ? `<span class="absolute -top-1 -right-1 bg-blue-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">${folder.file_count}</span>` : ''}
                            </div>
                            <p class="text-sm font-medium text-gray-900 truncate mb-1">${folder.name}</p>
                            <p class="text-xs text-gray-500">${this.formatDate(folder.created)}</p>
                            <div class="mt-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 space-x-2">
                                <button onclick="event.stopPropagation(); mediaManager.renameItem('${folder.path}', '${folder.name}', 'folder')" class="text-blue-500 hover:text-blue-700 text-xs">
                                    Rename
                                </button>
                                <button onclick="event.stopPropagation(); mediaManager.deleteItem('${folder.path}', 'folder')" class="text-red-500 hover:text-red-700 text-xs">
                                    Delete
                                </button>
                            </div>
                        </div>
                    </div>
                `;
            });
            
            // Render files
            data.files.forEach(file => {
                // Handle dimensions safely
                const dimensions = file.dimensions && file.dimensions.width && file.dimensions.height
                    ? `${file.dimensions.width}x${file.dimensions.height}`
                    : 'N/A';

                // Determine if file is an image
                const isImage = file.is_image || ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'].includes(file.extension.toLowerCase());

                // Generate preview content based on file type
                let previewContent;
                if (isImage) {
                    previewContent = `<img src="${file.url}" alt="${file.name}" class="w-full h-24 object-cover rounded-md media-image-preview">`;
                } else {
                    // Show file type icon for non-images
                    const iconClass = this.getFileTypeIcon(file.extension);
                    previewContent = `
                        <div class="w-full h-24 flex items-center justify-center bg-gray-100 rounded-md">
                            <div class="text-center">
                                <i class="${iconClass} text-3xl text-gray-600 mb-1"></i>
                                <div class="text-xs font-bold text-gray-700">${file.extension.toUpperCase()}</div>
                            </div>
                        </div>
                    `;
                }

                html += `
                    <div class="bg-white rounded-lg shadow-sm border hover:shadow-md transition-shadow duration-200 cursor-pointer group media-grid-item" onclick="mediaManager.previewFile('${file.path}', '${file.name}', '${file.url}', ${file.size}, '${dimensions}', '${file.type}', '${file.created}')">
                        <div class="p-2">
                            <div class="aspect-w-1 aspect-h-1 mb-2 relative">
                                ${previewContent}
                                <div class="file-type-indicator">${file.extension.toUpperCase()}</div>
                            </div>
                            <p class="text-xs font-medium text-gray-900 truncate px-1">${file.name}</p>
                            <p class="text-xs text-gray-500 px-1">${this.formatFileSize(file.size)}</p>
                            ${isImage ? `<p class="text-xs text-gray-400 px-1">${dimensions}</p>` : `<p class="text-xs text-gray-400 px-1">${file.type}</p>`}
                        </div>
                    </div>
                `;
            });
            
            grid.innerHTML = html;
        }

        updateBreadcrumb(path) {
            const breadcrumb = document.getElementById('breadcrumb');
            const parts = path ? path.split('/') : [];
            
            let html = `
                <li class="inline-flex items-center">
                    <span class="text-gray-700 hover:text-blue-600 cursor-pointer font-medium breadcrumb-item" data-path="" onclick="mediaManager.loadMedia('')">
                        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"/>
                        </svg>
                        Home
                    </span>
                </li>
            `;
            
            let currentPath = '';
            parts.forEach((part, index) => {
                currentPath = currentPath ? `${currentPath}/${part}` : part;
                html += `
                    <li>
                        <div class="flex items-center">
                            <svg class="flex-shrink-0 h-5 w-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
                            </svg>
                            <span class="ml-1 text-gray-700 hover:text-blue-600 cursor-pointer font-medium breadcrumb-item" onclick="mediaManager.loadMedia('${currentPath}')">${part}</span>
                        </div>
                    </li>
                `;
            });
            
            breadcrumb.innerHTML = html;
        }

        updateStats(data) {
            document.getElementById('totalFolders').textContent = data.total_folders;
            document.getElementById('totalFiles').textContent = data.total_files;
            document.getElementById('currentLocation').textContent = data.current_path || 'Home';
        }

        async createFolder() {
            const folderName = document.getElementById('folderNameInput').value.trim();
            if (!folderName) {
                this.showError('Please enter a folder name');
                return;
            }
            
            try {
                const response = await fetch(`${this.apiBase}/create-folder`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: JSON.stringify({
                        name: folderName,
                        parent: this.currentPath
                    })
                });
                
                const responseText = await response.text();
                let result;
                try {
                    result = JSON.parse(responseText);
                } catch (e) {
                    console.error('Invalid JSON response:', responseText);
                    throw new Error('Server returned invalid JSON');
                }
                
                if (result.success) {
                    this.closeFolderModal();
                    this.loadMedia(this.currentPath);
                    this.showSuccess('Folder created successfully');
                } else {
                    this.showError(result.message);
                }
            } catch (error) {
                this.showError('Failed to create folder: ' + error.message);
            }
        }

        closeFolderModal() {
            document.getElementById('folderModal').classList.add('hidden');
            document.getElementById('folderNameInput').value = '';
        }

        async handleFiles(files) {
            const fileArray = Array.from(files);
            let uploadCount = 0;

            // Get upload configuration from window object (passed from PHP)
            const config = window.uploadConfig || {};
            const allowedTypes = config.allowedTypes || [];
            const allowedExtensions = config.allowedExtensions || [];
            const forbiddenExtensions = config.forbiddenExtensions || [];
            const maxFileSize = config.maxFileSize || 10485760;

            for (const file of fileArray) {
                // Validate file type
                if (allowedTypes.length > 0 && !allowedTypes.includes(file.type)) {
                    const friendlyTypes = this.getFriendlyFileTypes(allowedTypes);
                    this.showError(`"${file.name}" is not a supported file type. Supported formats: ${friendlyTypes}`);
                    continue;
                }

                // Validate file extension
                const extension = file.name.split('.').pop().toLowerCase();
                if (allowedExtensions.length > 0 && !allowedExtensions.includes(extension)) {
                    const extensionList = allowedExtensions.map(ext => ext.toUpperCase()).join(', ');
                    this.showError(`"${file.name}" has an unsupported file extension. Supported extensions: ${extensionList}`);
                    continue;
                }

                // Check forbidden extensions
                if (forbiddenExtensions.length > 0 && forbiddenExtensions.includes(extension)) {
                    this.showError(`"${file.name}" cannot be uploaded for security reasons. ${extension.toUpperCase()} files are not allowed.`);
                    continue;
                }

                // Validate file size
                if (file.size > maxFileSize) {
                    const maxSizeMB = (maxFileSize / 1024 / 1024).toFixed(1);
                    const fileSizeMB = (file.size / 1024 / 1024).toFixed(1);
                    this.showError(`"${file.name}" is too large (${fileSizeMB}MB). Maximum file size is ${maxSizeMB}MB.`);
                    continue;
                }

                const success = await this.uploadFile(file);
                if (success) uploadCount++;
            }

            if (uploadCount > 0) {
                this.loadMedia(this.currentPath);
                this.showSuccess(`Successfully uploaded ${uploadCount} file(s)`);
            }
        }

        async uploadFile(file) {
            const formData = new FormData();
            formData.append('file', file);
            formData.append('folder', this.currentPath);
            
            try {
                const response = await fetch(`${this.apiBase}/upload`, {
                    method: 'POST',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: formData
                });
                
                const responseText = await response.text();
                let result;
                try {
                    result = JSON.parse(responseText);
                } catch (e) {
                    console.error('Invalid JSON response:', responseText);
                    throw new Error('Server returned invalid JSON');
                }
                
                return result.success;
            } catch (error) {
                this.showError(`Failed to upload ${file.name}: ${error.message}`);
                return false;
            }
        }

        previewFile(path, name, url, size, dimensions, type, created) {
            this.currentPreviewPath = path;
            this.currentPreviewUrl = url;
            this.currentPreviewType = type;
            this.currentPreviewName = name;

            // Get full URL
            const protocol = window.location.protocol;
            const host = window.location.host;
            this.currentPreviewFullUrl = `${protocol}//${host}${url}`;

            document.getElementById('previewTitle').textContent = name;

            // Show/hide transform details button based on file type
            const transformBtn = document.getElementById('showTransformDetailsBtn');
            if (type.startsWith('image/')) {
                transformBtn.classList.remove('hidden');
                this.transformDetailsVisible = false; // Reset state
                this.updateTransformDetailsButtonText();
            } else {
                transformBtn.classList.add('hidden');
            }

            // Generate preview content based on file type
            const previewContent = this.generatePreviewContent(url, name, type);
            document.getElementById('previewContent').innerHTML = previewContent;
            // Generate dynamic info content based on file type
            let infoContent = `
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <p class="text-sm text-gray-600 mb-1"><strong>File Name:</strong></p>
                        <p class="text-sm text-gray-900 mb-3">${name}</p>

                        <p class="text-sm text-gray-600 mb-1"><strong>File Size:</strong></p>
                        <p class="text-sm text-gray-900 mb-3">${this.formatFileSize(size)}</p>

                        <p class="text-sm text-gray-600 mb-1"><strong>Dimensions:</strong></p>
                        <p class="text-sm text-gray-900 mb-3">${dimensions}</p>
                    </div>
                    <div>
                        <p class="text-sm text-gray-600 mb-1"><strong>File Type:</strong></p>
                        <p class="text-sm text-gray-900 mb-3">${type}</p>

                        <p class="text-sm text-gray-600 mb-1"><strong>Created:</strong></p>
                        <p class="text-sm text-gray-900 mb-3">${this.formatDate(created)}</p>

                        <p class="text-sm text-gray-600 mb-1"><strong>URL:</strong></p>
                        <p class="text-xs text-gray-900 bg-gray-100 p-2 rounded break-all">${url}</p>
                    </div>
                </div>
            `;

            // Add collapsible dynamic transformation examples for images
            if (type.startsWith('image/')) {
                const baseUrl = window.location.protocol + '//' + window.location.host;
                const transformExamples = this.generateTransformExamples(url);

                infoContent += `
                    <div id="transformDetailsSection" class="mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200 hidden">
                        <h4 class="text-sm font-semibold text-blue-900 mb-3 flex items-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                            </svg>
                            Dynamic Image Transformation URLs
                        </h4>
                        <p class="text-xs text-blue-700 mb-3">Use these URLs to get automatically resized versions of this image:</p>
                        <div class="space-y-2">
                            ${transformExamples.map(example => `
                                <div class="bg-white p-2 rounded border">
                                    <div class="flex justify-between items-center">
                                        <span class="text-xs font-medium text-blue-800">${example.label}:</span>
                                        <button onclick="navigator.clipboard.writeText('${baseUrl}${example.url}'); window.mediaManager.showSuccess('URL copied!')"
                                                class="text-xs text-blue-600 hover:text-blue-800 underline">Copy</button>
                                    </div>
                                    <code class="text-xs text-gray-700 break-all">${baseUrl}${example.url}</code>
                                </div>
                            `).join('')}
                        </div>
                        <div class="mt-3 p-2 bg-blue-100 rounded text-xs text-blue-800">
                            <strong>How to use:</strong> Replace dimensions (e.g., 300x300) with your desired size.
                            Supported modes: <code>fit</code> (default), <code>crop</code>, <code>fill</code>
                        </div>
                    </div>
                `;
            }

            document.getElementById('previewInfo').innerHTML = infoContent;

            document.getElementById('previewModal').classList.remove('hidden');
        }

        async copyToClipboard(text, message) {
            try {
                await navigator.clipboard.writeText(text);
                this.showSuccess(message);
            } catch (error) {
                // Fallback for older browsers
                const textArea = document.createElement('textarea');
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                this.showSuccess(message);
            }
        }

        updateTransformDetailsButtonText() {
            const transformText = document.getElementById('transformDetailsText');
            if (this.transformDetailsVisible) {
                transformText.textContent = 'Hide Transform Details';
            } else {
                transformText.textContent = 'Show Transform Details';
            }
        }

        toggleTransformDetails() {
            const detailsSection = document.getElementById('transformDetailsSection');

            if (this.transformDetailsVisible) {
                // Hide details
                detailsSection.classList.add('hidden');
                this.transformDetailsVisible = false;
            } else {
                // Show details
                detailsSection.classList.remove('hidden');
                this.transformDetailsVisible = true;
            }

            this.updateTransformDetailsButtonText();
        }

        generateTransformExamples(originalUrl) {
            // Extract the path from the original URL
            const pathParts = originalUrl.split('/');
            const filename = pathParts.pop(); // Remove filename
            const basePath = pathParts.join('/'); // Get base path

            return [
                {
                    label: 'Small (150x150)',
                    url: `${basePath}/150x150/${filename}`
                },
                {
                    label: 'Medium (300x300)',
                    url: `${basePath}/300x300/${filename}`
                },
                {
                    label: 'Large (600x400)',
                    url: `${basePath}/600x400/${filename}`
                },
                {
                    label: 'Crop Mode (200x200)',
                    url: `${basePath}/200x200/crop=${filename}`
                },
                {
                    label: 'Fill Mode (400x300)',
                    url: `${basePath}/400x300/fill=${filename}`
                }
            ];
        }

        async deleteCurrentFile() {
            if (!confirm('Are you sure you want to delete this file? This action cannot be undone.')) {
                return;
            }

            await this.deleteItem(this.currentPreviewPath, 'file');
            document.getElementById('previewModal').classList.add('hidden');
        }

        async renameItem(path, currentName, type) {
            const newName = prompt(`Enter new name for this ${type}:`, currentName);
            if (!newName || newName === currentName) {
                return;
            }

            try {
                const response = await fetch(`${this.apiBase}/rename`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: JSON.stringify({
                        path: path,
                        new_name: newName,
                        type: type
                    })
                });

                if (!response.ok) {
                    throw new Error(`Server returned ${response.status}: ${response.statusText}`);
                }

                const result = await response.json();

                if (result.success) {
                    this.showSuccess(result.message);
                    this.loadMedia(this.currentPath);
                } else {
                    this.showError(result.message);
                }
            } catch (error) {
                this.showError(`Failed to rename ${type}: ${error.message}`);
            }
        }

        async deleteItem(path, type) {
            if (!confirm(`Are you sure you want to delete this ${type}? This action cannot be undone.`)) {
                return;
            }

            try {
                const response = await fetch(`${this.apiBase}/delete`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: JSON.stringify({
                        path: path,
                        type: type
                    })
                });

                const responseText = await response.text();
                let result;
                try {
                    result = JSON.parse(responseText);
                } catch (e) {
                    console.error('Invalid JSON response:', responseText);
                    throw new Error('Server returned invalid JSON');
                }

                if (result.success) {
                    this.showSuccess(result.message);
                    this.loadMedia(this.currentPath);
                } else {
                    this.showError(result.message);
                }
            } catch (error) {
                this.showError(`Failed to delete ${type}: ${error.message}`);
            }
        }

        formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'short',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });
        }

        showLoading(show) {
            const spinner = document.getElementById('loadingSpinner');
            const grid = document.getElementById('mediaGrid');
            const noContent = document.getElementById('noContent');

            if (show) {
                spinner.classList.remove('hidden');
                grid.classList.add('hidden');
                noContent.classList.add('hidden');
            } else {
                spinner.classList.add('hidden');
                grid.classList.remove('hidden');
            }
        }

        showSuccess(message) {
            this.showToast(message, 'success');
        }

        showError(message) {
            this.showToast(message, 'error');
        }

        showToast(message, type) {
            const toastId = type === 'success' ? 'toast' : 'errorToast';
            const messageId = type === 'success' ? 'toastMessage' : 'errorToastMessage';

            const toast = document.getElementById(toastId);
            const messageEl = document.getElementById(messageId);

            messageEl.textContent = message;
            toast.classList.remove('hidden');

            // Auto hide after 3 seconds
            setTimeout(() => {
                toast.classList.add('hidden');
            }, 3000);
        }

        getFileTypeIcon(extension) {
            const ext = extension.toLowerCase();
            const iconMap = {
                // Documents
                'pdf': 'fas fa-file-pdf',
                'doc': 'fas fa-file-word',
                'docx': 'fas fa-file-word',
                'txt': 'fas fa-file-alt',
                'rtf': 'fas fa-file-alt',

                // Spreadsheets
                'xls': 'fas fa-file-excel',
                'xlsx': 'fas fa-file-excel',
                'csv': 'fas fa-file-csv',

                // Presentations
                'ppt': 'fas fa-file-powerpoint',
                'pptx': 'fas fa-file-powerpoint',

                // Archives
                'zip': 'fas fa-file-archive',
                'rar': 'fas fa-file-archive',
                '7z': 'fas fa-file-archive',
                'tar': 'fas fa-file-archive',
                'gz': 'fas fa-file-archive',

                // Videos
                'mp4': 'fas fa-file-video',
                'avi': 'fas fa-file-video',
                'mov': 'fas fa-file-video',
                'wmv': 'fas fa-file-video',
                'flv': 'fas fa-file-video',
                'webm': 'fas fa-file-video',

                // Audio
                'mp3': 'fas fa-file-audio',
                'wav': 'fas fa-file-audio',
                'flac': 'fas fa-file-audio',
                'aac': 'fas fa-file-audio',
                'ogg': 'fas fa-file-audio',

                // Code
                'js': 'fas fa-file-code',
                'html': 'fas fa-file-code',
                'css': 'fas fa-file-code',
                'php': 'fas fa-file-code',
                'py': 'fas fa-file-code',
                'java': 'fas fa-file-code',
                'cpp': 'fas fa-file-code',
                'c': 'fas fa-file-code',
                'json': 'fas fa-file-code',
                'xml': 'fas fa-file-code'
            };

            return iconMap[ext] || 'fas fa-file';
        }

        getFriendlyFileTypes(mimeTypes) {
            const friendlyNames = {
                // Images
                'image/jpeg': 'JPEG Images',
                'image/jpg': 'JPG Images',
                'image/png': 'PNG Images',
                'image/gif': 'GIF Images',
                'image/webp': 'WebP Images',
                'image/svg+xml': 'SVG Images',

                // Documents
                'application/pdf': 'PDF Documents',
                'text/plain': 'Text Files',
                'application/msword': 'Word Documents',
                'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'Word Documents (DOCX)',
                'application/vnd.ms-excel': 'Excel Spreadsheets',
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 'Excel Spreadsheets (XLSX)',
                'application/vnd.ms-powerpoint': 'PowerPoint Presentations',
                'application/vnd.openxmlformats-officedocument.presentationml.presentation': 'PowerPoint Presentations (PPTX)',

                // Videos
                'video/mp4': 'MP4 Videos',
                'video/quicktime': 'QuickTime Videos',
                'video/x-msvideo': 'AVI Videos',
                'video/webm': 'WebM Videos',

                // Audio
                'audio/mpeg': 'MP3 Audio',
                'audio/wav': 'WAV Audio',
                'audio/ogg': 'OGG Audio',

                // Archives
                'application/zip': 'ZIP Archives',
                'application/x-rar-compressed': 'RAR Archives'
            };

            const uniqueTypes = [...new Set(mimeTypes.map(type => friendlyNames[type] || type))];
            return uniqueTypes.join(', ');
        }

        generatePreviewContent(url, name, type) {
            const extension = name.split('.').pop().toLowerCase();

            // Image files
            if (type.startsWith('image/')) {
                return `<img src="${url}" alt="${name}" class="max-w-full max-h-96 object-contain mx-auto rounded-lg shadow-lg">`;
            }

            // PDF files
            if (type === 'application/pdf' || extension === 'pdf') {
                return `
                    <div class="bg-gray-100 rounded-lg p-8">
                        <div class="text-center mb-4">
                            <i class="fas fa-file-pdf text-6xl text-red-600 mb-4"></i>
                            <h4 class="text-lg font-semibold text-gray-900 mb-2">PDF Document</h4>
                            <p class="text-gray-600 mb-4">${name}</p>
                        </div>
                        <div class="flex justify-center gap-3">
                            <a href="${url}" target="_blank" class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg flex items-center gap-2">
                                <i class="fas fa-external-link-alt"></i>
                                Open PDF
                            </a>
                            <a href="${url}" download="${name}" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg flex items-center gap-2">
                                <i class="fas fa-download"></i>
                                Download
                            </a>
                        </div>
                    </div>
                `;
            }

            // Video files
            if (type.startsWith('video/')) {
                return `
                    <div class="bg-black rounded-lg overflow-hidden">
                        <video controls class="max-w-full max-h-96 mx-auto" preload="metadata">
                            <source src="${url}" type="${type}">
                            <div class="text-center p-8 text-white">
                                <i class="fas fa-video text-4xl mb-4"></i>
                                <p>Your browser does not support the video tag.</p>
                                <a href="${url}" class="text-blue-400 hover:text-blue-300">Download video</a>
                            </div>
                        </video>
                    </div>
                `;
            }

            // Audio files
            if (type.startsWith('audio/')) {
                return `
                    <div class="bg-gray-100 rounded-lg p-8">
                        <div class="text-center mb-4">
                            <i class="fas fa-music text-6xl text-purple-600 mb-4"></i>
                            <h4 class="text-lg font-semibold text-gray-900 mb-4">Audio File</h4>
                        </div>
                        <audio controls class="w-full max-w-md mx-auto">
                            <source src="${url}" type="${type}">
                            Your browser does not support the audio element.
                        </audio>
                    </div>
                `;
            }

            // Text files
            if (type.startsWith('text/') || extension === 'txt') {
                return `
                    <div class="bg-gray-100 rounded-lg p-8">
                        <div class="text-center mb-4">
                            <i class="fas fa-file-alt text-6xl text-blue-600 mb-4"></i>
                            <h4 class="text-lg font-semibold text-gray-900 mb-2">Text Document</h4>
                            <p class="text-gray-600 mb-4">${name}</p>
                        </div>
                        <div class="flex justify-center gap-3">
                            <a href="${url}" target="_blank" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2">
                                <i class="fas fa-external-link-alt"></i>
                                Open File
                            </a>
                            <a href="${url}" download="${name}" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg flex items-center gap-2">
                                <i class="fas fa-download"></i>
                                Download
                            </a>
                        </div>
                    </div>
                `;
            }

            // Default for other file types
            const icon = this.getFileTypeIcon(extension);
            return `
                <div class="bg-gray-100 rounded-lg p-8">
                    <div class="text-center mb-4">
                        <i class="${icon} text-6xl text-gray-600 mb-4"></i>
                        <h4 class="text-lg font-semibold text-gray-900 mb-2">${extension.toUpperCase()} File</h4>
                        <p class="text-gray-600 mb-4">${name}</p>
                    </div>
                    <div class="flex justify-center gap-3">
                        <a href="${url}" target="_blank" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg flex items-center gap-2">
                            <i class="fas fa-external-link-alt"></i>
                            Open File
                        </a>
                        <a href="${url}" download="${name}" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center gap-2">
                            <i class="fas fa-download"></i>
                            Download
                        </a>
                    </div>
                </div>
            `;
        }
    }

    // Initialize the media manager when DOM is loaded
    window.mediaManager = new MediaManager();
});
