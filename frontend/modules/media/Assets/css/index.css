/* Media Module Styles */

.media-grid-item {
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.media-grid-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.media-folder-icon {
    transition: color 0.2s ease-in-out;
}

.media-folder-icon:hover {
    color: #f59e0b;
}

.media-image-preview {
    object-fit: cover;
    transition: opacity 0.2s ease-in-out;
}

.media-image-preview:hover {
    opacity: 0.9;
}

.upload-area-active {
    border-color: #3b82f6 !important;
    background-color: #eff6ff !important;
}

.breadcrumb-item {
    transition: color 0.2s ease-in-out;
}

.breadcrumb-item:hover {
    color: #3b82f6;
}

/* Loading animation */
@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

.animate-spin {
    animation: spin 1s linear infinite;
}

/* Modal animations */
.modal-enter {
    opacity: 0;
    transform: scale(0.95);
}

.modal-enter-active {
    opacity: 1;
    transform: scale(1);
    transition: opacity 0.2s ease-out, transform 0.2s ease-out;
}

.modal-exit {
    opacity: 1;
    transform: scale(1);
}

.modal-exit-active {
    opacity: 0;
    transform: scale(0.95);
    transition: opacity 0.2s ease-in, transform 0.2s ease-in;
}

/* Toast animations */
.toast-enter {
    opacity: 0;
    transform: translateX(100%);
}

.toast-enter-active {
    opacity: 1;
    transform: translateX(0);
    transition: opacity 0.3s ease-out, transform 0.3s ease-out;
}

.toast-exit {
    opacity: 1;
    transform: translateX(0);
}

.toast-exit-active {
    opacity: 0;
    transform: translateX(100%);
    transition: opacity 0.3s ease-in, transform 0.3s ease-in;
}

/* File upload progress */
.upload-progress {
    height: 4px;
    background-color: #e5e7eb;
    border-radius: 2px;
    overflow: hidden;
}

.upload-progress-bar {
    height: 100%;
    background-color: #3b82f6;
    transition: width 0.3s ease-in-out;
}

/* Responsive grid adjustments */
@media (max-width: 640px) {
    .media-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.75rem;
    }
}

@media (min-width: 641px) and (max-width: 768px) {
    .media-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (min-width: 769px) and (max-width: 1024px) {
    .media-grid {
        grid-template-columns: repeat(4, 1fr);
    }
}

@media (min-width: 1025px) and (max-width: 1280px) {
    .media-grid {
        grid-template-columns: repeat(6, 1fr);
    }
}

@media (min-width: 1281px) {
    .media-grid {
        grid-template-columns: repeat(8, 1fr);
    }
}

/* Custom scrollbar for modal content */
.modal-content::-webkit-scrollbar {
    width: 6px;
}

.modal-content::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 3px;
}

.modal-content::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
}

.modal-content::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

/* File type indicators */
.file-type-indicator {
    position: absolute;
    top: 4px;
    right: 4px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 10px;
    font-weight: 500;
    text-transform: uppercase;
}

/* Drag and drop styles */
.drag-over {
    border-color: #3b82f6 !important;
    background-color: #eff6ff !important;
}

.drag-over::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(59, 130, 246, 0.1);
    border: 2px dashed #3b82f6;
    border-radius: 8px;
    pointer-events: none;
}
