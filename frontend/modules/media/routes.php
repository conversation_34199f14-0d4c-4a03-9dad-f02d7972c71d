<?php

use Core\Router;
use Middleware\Authenticate;
use Modules\Media\Controllers\MediaController;

// Manually require the media classes (temporary fix for autoloading issue)
require_once __DIR__ . '/Services/MediaService.php';
require_once __DIR__ . '/Controllers/MediaController.php';

// Media module routes
$router->group('media', function ($router) {
    $router->get('/', [MediaController::class, 'index'], [Authenticate::class]);
    $router->post('create-folder', [MediaController::class, 'createFolder'], [Authenticate::class]);
    $router->post('upload', [MediaController::class, 'upload'], [Authenticate::class]);
    $router->post('rename', [MediaController::class, 'rename'], [Authenticate::class]);
    $router->post('delete', [MediaController::class, 'delete'], [Authenticate::class]);
    $router->get('file-url', [MediaController::class, 'getFileUrl'], [Authenticate::class]);
});
