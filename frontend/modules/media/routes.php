<?php

use Core\Router;
use Middleware\Authenticate;
use Modules\Media\Controllers\MediaController;

// Manually require the media classes (temporary fix for autoloading issue)
require_once __DIR__ . '/Services/MediaService.php';
require_once __DIR__ . '/Controllers/MediaController.php';

// Media module routes (temporarily without authentication for testing)
$router->get('media', ['Modules\Media\Controllers\MediaController', 'index']);

// Test if class exists
$router->get('media-test', function () {
    // Try to manually require the files
    require_once __DIR__ . '/Services/MediaService.php';
    require_once __DIR__ . '/Controllers/MediaController.php';

    if (class_exists('Modules\Media\Controllers\MediaController')) {
        echo "MediaController class exists after manual require!";
    } else {
        echo "MediaController class STILL NOT found!";
    }
});

$router->post('media/create-folder', ['Modules\Media\Controllers\MediaController', 'createFolder']);
$router->post('media/upload', ['Modules\Media\Controllers\MediaController', 'upload']);
$router->post('media/delete', ['Modules\Media\Controllers\MediaController', 'delete']);
$router->get('media/file-url', ['Modules\Media\Controllers\MediaController', 'getFileUrl']);
