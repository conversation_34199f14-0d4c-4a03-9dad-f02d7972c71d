<?php

namespace Modules\Media\Controllers;

use Core\Controller;
use Core\Request;
use Modules\Media\Services\MediaService;

class MediaController extends Controller
{
    protected $mediaService;

    public function __construct()
    {
        parent::__construct();
        try {
            // Use default MediaService configuration
            $this->mediaService = new MediaService();
        } catch (\Exception $e) {
            error_log('MediaService initialization failed: ' . $e->getMessage());
            $this->mediaService = null;
        }
    }

    /**
     * Get MediaService with specific configuration based on upload type
     */
    protected function getConfiguredMediaService($uploadType = 'default'): MediaService
    {
        switch ($uploadType) {
            case 'avatar':
                return MediaService::createForAvatars();

            case 'gallery':
                return MediaService::createForGallery();

            case 'document':
                return MediaService::createForDocuments();

            case 'custom':
                // Allow custom configuration via method chaining
                return MediaService::createWithChaining()
                    ->setImageProcessing(true, 85)
                    ->setThumbnailGeneration(true, [
                        ['name' => 'thumb', 'width' => 150, 'height' => 150, 'mode' => 'crop']
                    ])
                    ->setImageCompression(true, 80)
                    ->setMetadataStripping(true)
                    ->setValidation(true)
                    ->setSecurityScan(false);

            default:
                return $this->mediaService ?: new MediaService();
        }
    }

    public function index(Request $request)
    {
        // Set page title
        $this->setTitle('Media Manager');

        // Check if MediaService is available
        if ($this->mediaService === null) {
            if ($request->isAjax()) {
                $this->jsonResponse([
                    'success' => false,
                    'message' => 'Media service not available'
                ], 500);
                return;
            } else {
                $this->error('Media service not available', 500);
                return;
            }
        }

        // Check if this is an AJAX request for data
        if ($request->isAjax()) {
            return $this->handleAjaxRequest($request);
        }

        // Regular request - render full page
        $folder = $request->get('folder', '');
        $result = $this->mediaService->getMediaList($folder);

        // Get file upload configuration from environment
        $uploadConfig = [
            'maxFileSize' => (int)($_ENV['FILEUPLOAD_MAX_SIZE'] ?? 10485760),
            'allowedTypes' => array_map('trim', explode(',', $_ENV['FILEUPLOAD_ALLOWED_TYPES'] ?? '')),
            'allowedExtensions' => array_map('trim', explode(',', $_ENV['FILEUPLOAD_ALLOWED_EXTENSIONS'] ?? '')),
            'forbiddenExtensions' => array_map('trim', explode(',', $_ENV['FILEUPLOAD_FORBIDDEN_EXTENSIONS'] ?? ''))
        ];

        return $this->view('modules/media/Views/index', [
            'mediaData' => $result,
            'currentFolder' => $folder,
            'uploadConfig' => $uploadConfig
        ]);
    }

    public function createFolder(Request $request)
    {
        try {
            if ($this->mediaService === null) {
                $this->jsonResponse([
                    'success' => false,
                    'message' => 'Media service not available'
                ], 500);
                return;
            }

            // Handle both JSON and form data
            $data = $request->json();
            if (empty($data)) {
                $data = $request->all();
            }

            $folderName = trim($data['name'] ?? '');
            $parentFolder = $data['parent'] ?? '';

            if (empty($folderName)) {
                $this->jsonResponse([
                    'success' => false,
                    'message' => 'Folder name is required'
                ], 400);
                return;
            }

            $result = $this->mediaService->createFolder($folderName, $parentFolder);

            $this->jsonResponse($result);
            return;
        } catch (\Exception $e) {
            $this->jsonResponse([
                'success' => false,
                'message' => 'Failed to create folder: ' . $e->getMessage()
            ], 500);
            return;
        }
    }

    public function upload(Request $request)
    {
        try {
            if ($this->mediaService === null) {
                $this->jsonResponse([
                    'success' => false,
                    'message' => 'Media service not available'
                ], 500);
                return;
            }

            if (!isset($_FILES['file'])) {
                $this->jsonResponse([
                    'success' => false,
                    'message' => 'No file uploaded'
                ], 400);
                return;
            }

            $folder = $request->post('folder', '');
            $uploadType = $request->post('upload_type', 'default'); // avatar, gallery, document, custom, default

            // Get configured MediaService based on upload type
            $configuredService = $this->getConfiguredMediaService($uploadType);

            $result = $configuredService->uploadFile($_FILES['file'], $folder);

            $this->jsonResponse($result);
            return;
        } catch (\Exception $e) {
            $this->jsonResponse([
                'success' => false,
                'message' => 'Failed to upload file: ' . $e->getMessage()
            ], 500);
            return;
        }
    }

    public function delete(Request $request)
    {
        try {
            if ($this->mediaService === null) {
                $this->jsonResponse([
                    'success' => false,
                    'message' => 'Media service not available'
                ], 500);
                return;
            }

            // Handle both JSON and form data
            $data = $request->json();
            if (empty($data)) {
                $data = $request->all();
            }

            $path = $data['path'] ?? '';
            $type = $data['type'] ?? 'file';
            $force = $data['force'] ?? false;

            if (empty($path)) {
                $this->jsonResponse([
                    'success' => false,
                    'message' => 'Path is required'
                ], 400);
                return;
            }

            $result = $this->mediaService->deleteItem($path, $type, $force);

            $this->jsonResponse($result);
            return;
        } catch (\Exception $e) {
            $this->jsonResponse([
                'success' => false,
                'message' => 'Failed to delete item: ' . $e->getMessage()
            ], 500);
            return;
        }
    }

    public function rename(Request $request)
    {
        try {
            if ($this->mediaService === null) {
                $this->jsonResponse([
                    'success' => false,
                    'message' => 'Media service not available'
                ], 500);
                return;
            }

            // Handle both JSON and form data
            $data = $request->json();
            if (empty($data)) {
                $data = $request->all();
            }

            $path = $data['path'] ?? '';
            $newName = $data['new_name'] ?? '';
            $type = $data['type'] ?? 'file';

            if (empty($path)) {
                $this->jsonResponse([
                    'success' => false,
                    'message' => 'Path is required'
                ], 400);
                return;
            }

            if (empty($newName)) {
                $this->jsonResponse([
                    'success' => false,
                    'message' => 'New name is required'
                ], 400);
                return;
            }

            $result = $this->mediaService->renameItem($path, $newName, $type);

            $this->jsonResponse($result);
            return;
        } catch (\Exception $e) {
            $this->jsonResponse([
                'success' => false,
                'message' => 'Failed to rename item: ' . $e->getMessage()
            ], 500);
            return;
        }
    }

    public function getFileUrl(Request $request)
    {
        try {
            if ($this->mediaService === null) {
                $this->jsonResponse([
                    'success' => false,
                    'message' => 'Media service not available'
                ], 500);
                return;
            }

            $path = $request->get('path');

            if (empty($path)) {
                $this->jsonResponse([
                    'success' => false,
                    'message' => 'Path is required'
                ], 400);
                return;
            }

            $result = $this->mediaService->getFileUrl($path);

            $this->jsonResponse($result);
            return;
        } catch (\Exception $e) {
            $this->jsonResponse([
                'success' => false,
                'message' => 'Failed to get file URL: ' . $e->getMessage()
            ], 500);
            return;
        }
    }

    private function handleAjaxRequest(Request $request)
    {
        if ($this->mediaService === null) {
            $this->jsonResponse([
                'success' => false,
                'message' => 'Media service not available'
            ], 500);
            return;
        }

        $action = $request->get('action', 'list');

        switch ($action) {
            case 'list':
                $folder = $request->get('folder', '');
                $result = $this->mediaService->getMediaList($folder);
                $this->jsonResponse($result);
                return;

            case 'url':
                $this->getFileUrl($request);
                return;

            default:
                $this->jsonResponse([
                    'success' => false,
                    'message' => 'Invalid action'
                ], 400);
                return;
        }
    }
}
