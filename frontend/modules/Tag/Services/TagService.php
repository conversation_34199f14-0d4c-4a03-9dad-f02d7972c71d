<?php

namespace Modules\Tag\Services;

use App\Services\ApiClient;
use Core\ServiceContainer;
use Core\Request;

class TagService
{
    protected $api;

    public function __construct()
    {
        $this->api = ServiceContainer::resolve(ApiClient::class);
    }

    public function getData(Request $request)
    {

        $sort = $request->get('sort') ?? '-id';

        // Check if sort has a negative prefix (for descending)
        // if (substr($sort, 0, 1) === '-') {
        //     $realSort = substr($sort, 1); // Remove the minus sign
        //     $direction = 'desc';
        // } else {
        //     $realSort = $sort;
        // }

        // dd($sort);

        $itemsPerPage = $request->get('limit') ?? 10;

        $params = [
            'page' => $request->get('page') ?? 1,
            'sort' => $sort,
            'limit' => $itemsPerPage
        ];
        $response = $this->api->get('tags',$params);

        return $response;
    }

    public function create($request) {
        $tagName = trim($request->post('tag'));
        $response = $this->api->post('tags', ['tag' => $tagName, 'created_at' => date('Y-m-d H:i:s'), 'updated_at' => date('Y-m-d H:i:s')]);
        return $response;
    }

    public function update($request) {
        $id = $request->post('id');
        $tagName = trim($request->post('tag'));
        $response = $this->api->put('tags/'.$id, ['tag' => $tagName, 'updated_at' => date('Y-m-d H:i:s')]);
        return $response;
    }

    public function delete($request) {
        $id = $request->post('id');
        $response = $this->api->delete('tags/'.$id);
        return $response;
    }
}
