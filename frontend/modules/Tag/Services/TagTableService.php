<?php

namespace Modules\Tag\Services;

use Core\Request;
use Package\GridTable\Src\DataTable;
use Package\GridTable\Src\Helpers\TableHelper;

class TagTableService
{
    private array $templatesList;
    private array $pagination;
    private Request $request;

    public function __construct($templatesList, $pagination, Request $request) {
        $this->templatesList = $templatesList;
        $this->pagination = $pagination;
        $this->request = $request;
    }

    public function renderTable() {

        try {
            $direction = $this->request->get('direction', 'desc');
            
            // Validate sort parameter 
            $sortColumns = ['id', 'tag', 'created_at', 'updated_at'];
            
            $table = new DataTable($this->templatesList, $this->getColumns(),  $this->request, $this->pagination, $sortColumns, $direction);

            $table->setAjaxUrl('tags');
            
            $table->setTableId('tagsTable')
                ->setBreadcrumbs('Tags', [
                    ['label' => 'Home', 'url' => url('/dashboard')],
                    ['label' => 'Tags']
                ])
                ->setSearchConfig(true, 'tagSearch')
                ->setActionButtons($this->getActionButtons())
                ->setRowActions($this->getRowActionButtons())
                ->setGridConfig($this->getGridConfig());

                return [
                    'table' => $table->render(),
                    'view' => 'table',
                    'success' => true,
                    'message' => ''
                ];
        } catch (\Throwable $th) {
            return [
                'table' => '',
                'view' => 'table',
                'success' => false,
                'message' => $th->getMessage()
            ];
        }
        
    }

    private function getColumns()
    {
        return [
            'id' => [
                'label' => 'ID',
                'sortable' => true
            ],
            'tag' => [
                'label' => 'Tag Name',
                'sortable' => true
            ],
            'created_at' => [
                'label' => 'Created At',
                'sortable' => true,
                'formatter' => function($value) {
                    return TableHelper::formatDate($value);
                }
            ],
            'updated_at' => [
                'label' => 'Updated At',
                'sortable' => true,
                'formatter' => function($value) {
                    return TableHelper::formatDate($value);
                }
            ]
        ];
    }

    private function getRowActionButtons()
    {
        return [
            [
                'icon' => '<path d="M21.174 6.812a1 1 0 0 0-3.986-3.987L3.842 16.174a2 2 0 0 0-.5.83l-1.321 4.352a.5.5 0 0 0 .623.622l4.353-1.32a2 2 0 0 0 .83-.497z"/><path d="m15 5 4 4"/>',
                'tooltip' => 'Edit',
                'type' => 'button',
                'attributes' => [
                    'data-modal-target' => 'editTagModal',
                    'data-modal-toggle' => 'editTagModal',
                    'data-id' => function($row) { return $row['id'] ?? ''; },
                    'data-tag' => function($row) { return $row['tag'] ?? ''; }
                ]
            ],
            [
                'icon' => '<path d="M3 6h18"/><path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"/><path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"/>',
                'tooltip' => 'Delete',
                'type' => 'button',
                'attributes' => [
                    'data-modal-target' => 'deleteTagModal',
                    'data-modal-toggle' => 'deleteTagModal',
                    'data-id' => function($row) { return $row['id'] ?? ''; }
                ]
            ]
        ];
    }

    private function getActionButtons ()
    {
        return [
            [
                'label' => 'Create New Tag',
                'icon' => 'plus',
                'attributes' => [
                    'data-modal-target' => 'createTagModal',
                    'data-modal-toggle' => 'createTagModal'
                ]
            ]
        ];
    }

    private function getGridConfig()
    {
        return [
            'enabled' => false,
        ];
    }
}