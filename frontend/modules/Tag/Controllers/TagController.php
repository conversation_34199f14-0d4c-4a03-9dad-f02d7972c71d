<?php

namespace Modules\Tag\Controllers;

use Core\Controller;
use Core\Request;
use Dotenv\Exception\ExceptionInterface;
use Modules\Tag\Services\TagService;
use Modules\Tag\Services\TagTableService;

class TagController extends Controller
{
    protected $tagService;

    public function __construct()
    {
        $this->tagService = new TagService();
    }

    public function index(Request $request)
    {
        // Regular request - render full page
        $this->setTitle('Tags');

        // Get data from service
        $result  = $this->tagService->getData($request);
        $tagsList =  $result['status'] == 'success' ? $result['data']['data'] : [];
        $pagination = $result['status'] === 'success' 
        ? $result['data'] 
        : $this->defaultPagination;

        $tagTable = new TagTableService($tagsList, $pagination, $request);

        $tableRender = $tagTable->renderTable();
        if ($request->isAjax()) {
            if ($tableRender['success']) {
                return $this->jsonResponse([
                    'html' => $tableRender['table'],
                    'view' => $tableRender['view'],
                    'pagination' => $pagination,
                    'success' => true,
                    'message' => ''
                ]);
            }

            return $this->jsonResponse([
                'html' => '',
                'view' => 'table',
                'pagination' => $pagination,
                'success' => true,
                'message' => $tableRender['message']
            ]);
        }

        return $this->view('modules/Tag/Views/index', [
            'tagTable' => $tableRender['table'] ?? 'Error loading table'
        ]);
            
    }

    public function create(Request $request) {
        try {
            $result  = $this->tagService->create($request);
            $error = $result['status'] !== 'success' ? $result['message'] : '';
            return$this->jsonResponse([
                'success' => true,
                'message' => 'Tags has been created successfully!',
            ]);
        } catch (ExceptionInterface $e) {
            $error = $e->getMessage();
            return $this->json([
                'success' => false,
                'error' => $error
            ]);
        }
    }

    public function update(Request $request) {
        try {
            $result  = $this->tagService->update($request);
            $error = $result['status'] !== 'success' ? $result['message'] : '';
            return$this->jsonResponse([
                'success' => true,
                'message' => 'Tags has been updated successfully!',
            ]);
        } catch (ExceptionInterface $e) {
            $error = $e->getMessage();
            return $this->json([
                'success' => false,
                'error' => $error
            ]);
        }
    }
    public function delete(Request $request) {
        try {
            $result  = $this->tagService->delete($request);
            $error = $result['status'] !== 'success' ? $result['message'] : '';
            return$this->jsonResponse([
                'success' => true,
                'message' => 'Tags has been deleted successfully!',
            ]);
        } catch (ExceptionInterface $e) {
            $error = $e->getMessage();
            return $this->json([
                'success' => false,
                'error' => $error
            ]);
        }
    }
}