// In modules/Tag/Assets/js/index.js
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Flowbite
    window.addEventListener('load', function () {
        const initFlowbite = window.initFlowbite || (() => {});
        
        // Initialize Flowbite globally (this binds data attributes)
        initFlowbite();
    
        // List of modal IDs you expect to conditionally initialize
        const modalIds = ['deleteTagModal', 'editTagModal', 'createTagModal']; // Replace with your actual modal IDs
    
        modalIds.forEach(modalId => {
            const modalElement = document.getElementById(modalId);
            const triggerButton = document.querySelector(`[data-modal-target="${modalId}"]`);
    
            // Only initialize the modal if both modal element and its trigger button exist
            if (modalElement && triggerButton) {
                try {
                    const modal = FlowbiteInstances.getInstance('Modal', modalId);
                    console.log(`Initialized modal: ${modalId}`);
                } catch (error) {
                    console.warn(`Could not initialize modal: ${modalId}`, error);
                }
            } else {
                console.log(`Skipped modal "${modalId}" — missing element or trigger.`);
            }
        });
    });
    
    // Handle edit button clicks
    document.addEventListener('click', function(e) {
        const editButton = e.target.closest('[data-modal-target="editTagModal"]');
        if (editButton) {
            const tagId = editButton.dataset.id;
            const tagName = editButton.dataset.tag;
            
            // Set form values
            const editForm = document.getElementById('editTagForm');
            if (editForm) {
                document.getElementById('editTagId').value = tagId;
                document.getElementById('editTagName').value = tagName;
            }
        }

        // Handle delete button clicks
        const deleteButton = e.target.closest('[data-modal-target="deleteTagModal"]');
        if (deleteButton) {
            const tagId = deleteButton.dataset.id;
            const deleteForm = document.getElementById('deleteTagForm');
            if (deleteForm) {
                document.getElementById('deleteTagId').value = tagId;
            }
        }
    });
});