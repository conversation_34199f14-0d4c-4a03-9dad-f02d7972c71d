<?php

use Core\Router;
use Middleware\Authenticate;
use Modules\Tag\Controllers\TagController;

// Remove the return and directly define routes
$router->group('tags', function($router) {
    $router->get('/', [TagController::class, 'index'], [Authenticate::class]);
    $router->post('create', [TagController::class, 'create'], [Authenticate::class]);
    $router->post('update', [TagController::class, 'update'], [Authenticate::class]);
    $router->post('delete', [TagController::class, 'delete'], [Authenticate::class]);
});