<?php
namespace Core;

class Request
{
    /**
     * Get the request URI path
     */
    public function uri(): string
    {
        return parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
    }

    /**
     * Get a query (GET) parameter
     */
    public function get(string $key, $default = null): mixed
    {
        return $_GET[$key] ?? $default;
    }

    /**
     * Get a POST parameter
     */
    public function post(string $key, $default = null): mixed
    {
        return $_POST[$key] ?? $default;
    }

    /**
     * Get all input data (GET + POST)
     */
    public function all(): array
    {
        return array_merge($_GET, $_POST);
    }

    /**
     * Get input from either GET or POST
     */
    public function input(string $key, $default = null): mixed
    {
        return $this->all()[$key] ?? $default;
    }

    /**
     * Get all uploaded files
     */
    public function files(): array
    {
        return $_FILES;
    }

    /**
     * Get a specific uploaded file
     */
    public function file(string $key): ?array
    {
        return $_FILES[$key] ?? null;
    }

    /**
     * Check if a file was uploaded
     */
    public function hasFile(string $key): bool
    {
        return isset($_FILES[$key]) && $_FILES[$key]['error'] !== UPLOAD_ERR_NO_FILE;
    }

    /**
     * Check if file upload was successful
     */
    public function isValidFile(string $key): bool
    {
        return $this->hasFile($key) && $_FILES[$key]['error'] === UPLOAD_ERR_OK;
    }

    /**
     * Move an uploaded file to destination
     */
    public function moveFile(string $key, string $destination): bool
    {
        if (!$this->isValidFile($key)) {
            return false;
        }

        return move_uploaded_file($_FILES[$key]['tmp_name'], $destination);
    }

    /**
     * Check if the request is AJAX
     */
    public function isAjax(): bool
    {
        return !empty($_SERVER['HTTP_X_REQUESTED_WITH']) && 
            strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
    }

    /**
     * Get a header value
     */
    public function getHeaderLine(string $name): string
    {
        $name = 'HTTP_' . strtoupper(str_replace('-', '_', $name));
        return $_SERVER[$name] ?? '';
    }

    /**
     * Get the request method
     */
    public function method(): string
    {
        return $_SERVER['REQUEST_METHOD'];
    }

    /**
     * Check if the request is a specific method
     */
    public function isMethod(string $method): bool
    {
        return strtoupper($this->method()) === strtoupper($method);
    }

    /**
     * Get JSON input (for API requests)
     */
    public function json(): array
    {
        $input = file_get_contents('php://input');
        return json_decode($input, true) ?? [];
    }

    /**
     * Get only the specified keys from the request input
     * 
     * @param array $keys Keys to include in the result
     * @param bool $includeEmpty Whether to include keys that don't exist (with null values)
     * @return array Filtered array with only the specified keys
     */
    public function only(array $keys, bool $includeEmpty = true): array
    {
        $input = $this->all();
        
        if ($includeEmpty) {
            // Return all specified keys, with null for missing ones (Laravel-like behavior)
            $result = [];
            foreach ($keys as $key) {
                $result[$key] = $input[$key] ?? null;
            }
            return $result;
        }
        
        // Return only keys that actually exist in the input
        return array_filter($input, function($key) use ($keys) {
            return in_array($key, $keys);
        }, ARRAY_FILTER_USE_KEY);
    }

    /**
     * Determine if the request is asking for JSON.
     */
    public function wantsJson(): bool
    {
        $accept = $this->getHeaderLine('Accept');
        return str_contains($accept, '/json') || str_contains($accept, '+json');
    }

    /**
     * Get all GET parameters
     */
    public function query(): array
    {
        return $_GET;
    }

    public function cookie($key)
    {
        return $_COOKIE[$key] ?? null;
    }
}