<?php

namespace Core;

use App\Services\Auth;
use Core\Request;

abstract class Controller
{
    /**
     * The authenticated user
     */
    protected ?array $user = null;

    /**
     * The page title
     */
    protected string $pageTitle = '';

    /**
     * The request instance
     */
    protected Request $request;

    protected $defaultPagination = [
        'current_page' => 1,
        'per_page' => 10,
        'from' => null,
        'to' => null,
        'total' => 0,
        'next_page_url' => null,
        'prev_page_url' => null,
        'first_page_url' => null,
        'last_page_url' => null
    ];

    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        // $this->request = new Request();
        
        if (Auth::check()) {
            $this->user = Auth::user();
            $this->shareToView('auth', $this->user);
        }
    }

    /**
     * Return a JSON response
     */
    protected function jsonResponse($data, int $status = 200, array $headers = []): void
    {
        if (!empty($headers)) {
            Response::withHeaders($headers);
        }
        Response::json($data, $status);
    }

    /**
     * Redirect to a URL
     */
    protected function redirect(string $url, int $status = 302, array $headers = []): void
    {
        if (!empty($headers)) {
            Response::withHeaders($headers);
        }
        Response::redirect($url);
    }

    /**
     * Redirect back to the previous URL
     */
    protected function back(): void
    {
        // $referer = $this->request->getHeaderLine('Referer') ?? '/';
        // $this->redirect($referer);
    }

    /**
     * Share data with all views
     */
    protected function shareToView(string $key, $value): void
    {
        View::share($key, $value);
    }

    /**
     * Get the authenticated user
     */
    protected function auth(): ?array
    {
        return $this->user;
    }

    /**
     * Check if the user is authenticated
     */
    protected function isAuthenticated(): bool
    {
        return $this->user !== null;
    }

    /**
     * Get the authenticated user's ID
     */
    protected function userId(): ?int
    {
        return $this->user['id'] ?? null;
    }

    /**
     * Set the page title
     */
    protected function setTitle(string $title): void
    {
        $this->pageTitle = $title;
        $this->shareToView('pageTitle', $title);
    }

    /**
     * Render a view
     */
    protected function view(string $path, array $data = [])
    {
        $data['pageTitle'] = $this->pageTitle;

        View::render($path, $data);
    }

    /**
     * Return a not found response
     */
    protected function notFound(string $message = 'Not Found'): void
    {
        $this->jsonResponse(['error' => $message], 404);
    }

    /**
     * Return an unauthorized response
     */
    protected function unauthorized(string $message = 'Unauthorized'): void
    {
        $this->jsonResponse(['error' => $message], 401);
    }

    /**
     * Return a forbidden response
     */
    protected function forbidden(string $message = 'Forbidden'): void
    {
        $this->jsonResponse(['error' => $message], 403);
    }

    /**
     * Return an error response
     */
    protected function error(string $message, int $statusCode = 500): void
    {
        $this->jsonResponse(['error' => $message], $statusCode);
    }

    /**
     * Send a JSON response (alias for jsonResponse)
     */
    protected function json(array $data, int $status = 200, array $headers = [])
    {
        return $this->jsonResponse($data, $status, $headers);
    }
}