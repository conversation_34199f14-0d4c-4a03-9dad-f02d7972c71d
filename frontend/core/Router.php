<?php
namespace Core;

class Router
{ 
    private array $routes = [];
    private Request $request;
    private array $groupStack = [];

    public function __construct(Request $request)
    {
        $this->request = $request;
    }

    public function get(string $uri, callable|array $action, array $middleware = []): void
    {
        $this->addRoute('GET', $uri, $action, $middleware);
    }

    public function post(string $uri, callable|array $action, array $middleware = []): void
    {
        $this->addRoute('POST', $uri, $action, $middleware);
    }

    public function put(string $uri, callable|array $action, array $middleware = []): void
    {
        $this->addRoute('PUT', $uri, $action, $middleware);
    }

    public function delete(string $uri, callable|array $action, array $middleware = []): void
    {
        $this->addRoute('DELETE', $uri, $action, $middleware);
    }

    public function patch(string $uri, callable|array $action, array $middleware = []): void
    {
        $this->addRoute('PATCH', $uri, $action, $middleware);
    }

    public function options(string $uri, callable|array $action, array $middleware = []): void
    {
        $this->addRoute('OPTIONS', $uri, $action, $middleware);
    }

    public function dispatch(): void
    {
        $method = $this->request->method();
        $uri = trim($this->request->uri(), '/');
        
        // Handle root path
        $uri = $uri === '' ? '/' : $uri;

        $route = $this->routes[$method][$uri] ?? null;

        if (!$route) {
            $this->handleNotFound();
            return;
        }

        // Handle the action
        $action = $route['action'];
        $response = null;

        try {
            // Apply middleware
            if (!empty($route['middleware'])) {
                foreach ($route['middleware'] as $middlewareClass) {
                    $middleware = new $middlewareClass();
                    $response = $middleware->handle($this->request, function($request) use ($action) {
                        if (is_array($action) && class_exists($action[0])) {
                            $controller = new $action[0]();
                            $method = $action[1] ?? 'index';
                            
                            if (!method_exists($controller, $method)) {
                                throw new \RuntimeException("Method {$method} does not exist on controller " . get_class($controller));
                            }
                            
                            return $controller->$method($request);
                        } elseif (is_callable($action)) {
                            return $action($request);
                        } else {
                            throw new \RuntimeException('Invalid route handler');
                        }
                    });

                    // If middleware returns a response, stop further execution
                    if ($response !== null) {
                        break;
                    }
                }
            } else {
                // No middleware, just execute the action
                if (is_array($action) && class_exists($action[0])) {
                    $controller = new $action[0]();
                    $method = $action[1] ?? 'index';
                    $response = $controller->$method($this->request);
                } elseif (is_callable($action)) {
                    $response = $action($this->request);
                } else {
                    throw new \RuntimeException('Invalid route handler');
                }
            }

            // Send the response
            if ($response !== null) {
                $this->sendResponse($response);
            }
        } catch (\Throwable $e) {
            $this->handleError($e);
        }
    }

    private function sendResponse($response, int $statusCode = 200): void
    {
        http_response_code($statusCode);
        header('Content-Type: application/json');
        
        if (is_array($response) || is_object($response)) {
            echo json_encode($response);
        } else {
            echo json_encode(['data' => $response]);
        }
    }

    private function handleError(\Throwable $e): void
    {
        $statusCode = 500;
        $message = 'Internal Server Error';
        
        if ($e instanceof \RuntimeException) {
            switch ($e->getMessage()) {
                case 'Not Found':
                    $statusCode = 404;
                    $message = 'Not Found';
                    break;
                case 'Unauthorized':
                    $statusCode = 401;
                    $message = 'Unauthorized';
                    break;
                case 'Forbidden':
                    $statusCode = 403;
                    $message = 'Forbidden';
                    break;
            }
        }

        $this->sendResponse([
            'error' => $message,
            'message' => $e->getMessage(),
            'trace' => config('app.debug') ? $e->getTraceAsString() : null
        ], $statusCode);
    }

    private function handleNotFound(): void
    {
        // Check if this is an API request (expects JSON response)
        if ($this->request->wantsJson() || $this->request->isAjax()) {
            $this->sendResponse(['error' => 'Not Found'], 404);
            return;
        }
        
        // Otherwise, try to show a 404 view if it exists
        if (file_exists(__DIR__ . '/../views/errors/404.php')) {
            http_response_code(404);
            extract(['message' => 'Page not found']);
            include __DIR__ . '/../views/errors/404.php';
            return;
        }
        
        // Fallback to simple 404 response
        http_response_code(404);
        echo '404 Not Found';
    }

    /**
     * Route group with common prefix and/or middleware
     */
    public function group(string $prefix, \Closure $callback, array $middleware = []): void
    {
        // Push new group context onto the stack
        $this->groupStack[] = [
            'prefix' => $this->formatPrefix($prefix),
            'middleware' => $middleware
        ];

        // Execute the callback to register routes
        $callback($this);

        // Remove the group from the stack
        array_pop($this->groupStack);
    }

    private function formatPrefix(string $prefix): string
    {
        // Remove leading/trailing slashes
        $prefix = trim($prefix, '/');

        // Ensure empty prefix becomes empty string (not a single slash)
        return $prefix === '' ? '' : $prefix;
    }

    private function addRoute(string $method, string $uri, $action, array $middleware = []): void
    {
        $uri = $this->formatUri($uri);
        
        // Apply all active group prefixes
        $prefix = $this->getGroupPrefix();
        if ($prefix !== '') {
            $uri = $uri === '/' ? $prefix : $prefix.'/'.ltrim($uri, '/');
        }

        // Merge all group middleware with route middleware
        $middleware = array_merge($this->getGroupMiddleware(), $middleware);

        $this->routes[$method][$uri] = [
            'action' => $action,
            'middleware' => $middleware
        ];
    }

    private function formatUri(string $uri): string
    {
        // Special case: preserve root path
        if ($uri === '/') {
            return '/';
        }
        
        // Trim slashes and normalize
        return trim($uri, '/');
    }

private function getGroupPrefix(): string
{
    $prefix = '';
    
    foreach ($this->groupStack as $group) {
            if (empty($group['prefix'])) {
                continue;
            }

            if ($prefix === '') {
                $prefix = $group['prefix'];
            } else {
                $prefix .= '/'.$group['prefix'];
            }
        }

        return $prefix;
    }

    private function getGroupMiddleware(): array
    {
        $middleware = [];
        
        foreach ($this->groupStack as $group) {
            $middleware = array_merge($middleware, $group['middleware']);
        }

        return $middleware;
    }

}