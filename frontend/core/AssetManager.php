<?php
// core/AssetManager.php

namespace Core;

class AssetManager
{
    private static $instance = null;
    private $styles = [];
    private $scripts = [];
    private $inlineScripts = [];
    private $inlineStyles = [];

    private function __construct() {}

    public static function getInstance(): self
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    public function addStyle(string $path, array $attributes = []): void
    {
        $this->styles[md5($path)] = [
            'path' => $path,
            'attributes' => $attributes
        ];
    }

    public function addScript(string $path, array $attributes = []): void
    {
        $this->scripts[md5($path)] = [
            'path' => $path,
            'attributes' => $attributes
        ];
    }

    public function addInlineScript(string $script): void
    {
        $this->inlineScripts[] = $script;
    }

    public function addInlineStyle(string $style): void
    {
        $this->inlineStyles[] = $style;
    }

    public function renderStyles(): string
    {
        $output = '';
        foreach ($this->styles as $style) {
            $output .= '<link rel="stylesheet" href="' . $style['path'] . '"';
            foreach ($style['attributes'] as $key => $value) {
                $output .= ' ' . $key . '="' . htmlspecialchars($value) . '"';
            }
            $output .= ">\n";
        }
        foreach ($this->inlineStyles as $style) {
            $output .= "<style>\n{$style}\n</style>\n";
        }
        return $output;
    }

    public function renderScripts(): string
    {
        $output = '';
        foreach ($this->scripts as $script) {
            $output .= '<script src="' . $script['path'] . '"';
            foreach ($script['attributes'] as $key => $value) {
                $output .= ' ' . $key . '="' . htmlspecialchars($value) . '"';
            }
            $output .= "></script>\n";
        }
        foreach ($this->inlineScripts as $script) {
            $output .= "<script type='text/javascript'>\n{$script}\n</script>\n";
        }
        return $output;
    }
}