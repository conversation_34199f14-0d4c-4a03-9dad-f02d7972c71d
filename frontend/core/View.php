<?php
namespace Core;

class View
{
    /**
     * Shared data across all views
     *
     * @var array
     */
    protected static array $sharedData = [];

    /**
     * Share data across all views
     */
    public static function share(string $key, $value): void
    {
        static::$sharedData[$key] = $value;
    }

    /**
     * Get all shared data
     */
    public static function getSharedData(): array
    {
        return static::$sharedData;
    }

    public static function render(string $view, array $data = []): Response
    {
        try {
            // Get the root directory of the project
            $rootDir = dirname(__DIR__, 2). '/'. config('view_path'); // Goes up two levels from /core to /frontcms
            
            // Merge shared data with view-specific data
            $data = array_merge(static::$sharedData, $data);
            
            // Extract variables to be available in the view
            extract($data);
            
            // Start output buffering to prevent any accidental output
            ob_start();

            // Remove any leading slashes from the view path
            $view = ltrim($view, '/');
            
            // Check if it's a module view
            if (strpos($view, 'modules/') === 0) {
                $viewPath = $rootDir . '/' . $view . '.php';
            } else {
                $viewPath = $rootDir . '/views/' . str_replace('.', '/', $view) . '.php';
            }

            // Check if view exists
            if (!file_exists($viewPath)) {
                throw new \Exception("View [{$view}] not found at path: {$viewPath}");
            }

            include $viewPath;

            $contentView = ob_get_clean();

            // Include the layout first if it exists
            $layoutPath = $rootDir . '/views/layouts/main.php';
            
            if (!file_exists($layoutPath)) {
                throw new \Exception("Layout [main] not found at path: {$layoutPath}");
            }

            include $layoutPath;
            
            exit;
            
        } catch (\Throwable $e) {
            // Clean any previous output
            while (ob_get_level() > 0) {
                ob_end_clean();
            }
            
            // Log the error
            error_log(sprintf(
                'View Error: %s in %s:%s',
                $e->getMessage(),
                $e->getFile(),
                $e->getLine()
            ));
            
            // Set the HTTP response code
            http_response_code(500);
            
            // Render error page
            self::renderErrorPage($e);
            
            // Make sure we exit after rendering the error
            exit(1);
        }
    }

    public static function include(string $partial, array $data = []): void
    {
        // Merge shared data with partial-specific data
        $data = array_merge(static::$sharedData, $data);

        extract($data);
        ob_start();

        $path = self::resolvePath($partial);
        
        if (file_exists($path)) {
            include $path;
        }
    }   

    private static function resolvePath(string $view): string
    {
        return dirname(__DIR__) . '/views/' . str_replace('.', '/', $view) . '.php';
    }

    private static function renderLayout(string $name): void
    {
        $path = __DIR__ . "/../views/$name.php";

        if (file_exists($path)) {
            require $path;
        }
    }

    public static function renderErrorPage($e): void
    {
        // In debug mode, show detailed error
        if (config('app.debug')) {
            // Include the error view with exception details
            $errorFile = __DIR__ . '/../views/errors/500.php';
            if (file_exists($errorFile)) {
                // Pass the exception to the view
                $error = $e; // Make exception available in the view scope
                include $errorFile;
            } else {
                // Fallback error display
                echo '<h1>500 Internal Server Error</h1>';
                echo '<p>An error occurred while processing your request.</p>';
                if (config('app.debug')) {
                    echo '<pre>' . htmlspecialchars((string) $e) . '</pre>';
                }
            }
        } else {
            // In production, show a generic error page
            $errorFile = __DIR__ . '/../views/errors/500.php';
            if (file_exists($errorFile)) {
                include $errorFile;
            } else {
                echo 'An error occurred. Please try again later.';
            }
        }
    }

    // Add this method to your View class
    public static function capture($view, array $data = []) {
        ob_start();
        static::render($view, $data);
        return ob_get_clean();
    }
}