<?php

namespace Core\Services;

use Exception;
use Core\Request;
use Core\ServiceContainer;
use App\Services\ApiClient;
use LightnCandy\LightnCandy;

class PreviewService
{
    private $apiClient;
    
    // Handlebars helper functions
    private static $helpers = [];
    
    public function __construct()
    {
        // Use ServiceContainer to resolve ApiClient with proper configuration
        $this->apiClient = ServiceContainer::resolve(ApiClient::class);
        
        $this->initializeHelpers();
    }
    
    /**
     * Initialize Handlebars helper functions
     */
    private function initializeHelpers()
    {
        self::$helpers = [
            'mod' => function($arg1, $arg2) {
                return intval($arg1) % intval($arg2);
            },
            'eq' => function($arg1, $arg2) {
                return $arg1 === $arg2;
            },
            'ne' => function($arg1, $arg2) {
                return $arg1 !== $arg2;
            },
            'neq' => function($arg1, $arg2) {
                return $arg1 !== $arg2;
            },
            'lt' => function($arg1, $arg2) {
                return $arg1 < $arg2;
            },
            'gt' => function($arg1, $arg2) {
                return $arg1 > $arg2;
            },
            'lte' => function($arg1, $arg2) {
                return $arg1 <= $arg2;
            },
            'gte' => function($arg1, $arg2) {
                return $arg1 >= $arg2;
            },
            'and' => function() {
                foreach (func_get_args() as $arg) {
                    if (!$arg) return false;
                }
                return true;
            },
            'or' => function() {
                foreach (func_get_args() as $arg) {
                    if ($arg) return true;
                }
                return false;
            },
            'not' => function($arg) {
                return !$arg;
            },
            'isEven' => function($arg1) {
                return intval($arg1) % 2 === 0;
            },
            'isOdd' => function($arg1) {
                return intval($arg1) % 2 !== 0;
            }
        ];
    }
    
    /**
     * Main preview rendering function
     */
    public function renderPreview($type, $id, $options = [])
    {
        try {
            $previewMode = $options['preview_mode'] ?? false;
            $hideControls = $options['hide_controls'] ?? $previewMode;
            $specificPage = $options['page_slug'] ?? null;
            
            if ($type === 'template') {
                return $this->renderTemplatePreview($id, $options);
            } else {
                return $this->renderPagePreview($id, $options);
            }
        } catch (Exception $e) {
            return ['error' => $e->getMessage()];
        }
    }
    
    /**
     * Template preview with all pages
     */
    private function renderTemplatePreview($templateId, $options = [])
    {
        // Get template data using V1 API
        $templateResponse = $this->apiClient->get("templates/{$templateId}", [], false);
        
        if (!$templateResponse || !isset($templateResponse['data'])) {
            return ['error' => 'Template not found'];
        }
        
        $templateData = $templateResponse['data'];
        
        // Get all pages for this template using V1 API
        $pagesResponse = $this->apiClient->get("pages", ['template_id' => $templateId], false);
        
        $templatePages = [];
        if ($pagesResponse && isset($pagesResponse['data']['data'])) {
            $templatePages = $pagesResponse['data']['data'];
        }
        
        // If specific page requested
        if (!empty($options['page_slug'])) {
            $templatePages = array_filter($templatePages, function($page) use ($options) {
                return $page['slug'] === $options['page_slug'];
            });
        }
        
        // Get all blocks for all pages
        $allBlocks = [];
        foreach ($templatePages as $page) {
            $pageBlocks = $this->getPageBlocks($page['id']);
            
            foreach ($pageBlocks as $block) {
                $block['page_name'] = $page['name'];
                $block['page_slug'] = $page['slug'];
                $block['page_id'] = $page['id'];
                $allBlocks[] = $block;
            }
        }
        
        return $this->generatePreviewHTML($allBlocks, $templateData, null, $options);
    }
    
    /**
     * Single page preview
     */
    private function renderPagePreview($pageId, $options = [])
    {
        // Get page data using V1 API
        $pageResponse = $this->apiClient->get("pages/{$pageId}", [], false);
        
        if (!$pageResponse || !isset($pageResponse['data'])) {
            return ['error' => 'Page not found'];
        }
        
        $pageData = $pageResponse['data'];
        
        // Get template data if page has template using V1 API
        $templateData = null;
        if (!empty($pageData['template_id'])) {
            $templateResponse = $this->apiClient->get("templates/{$pageData['template_id']}", [], false);
            if ($templateResponse && isset($templateResponse['data'])) {
                $templateData = $templateResponse['data'];
            }
        }
        
        // Get page blocks
        $pageBlocks = $this->getPageBlocks($pageId);
        
        // Add page info to blocks
        foreach ($pageBlocks as &$block) {
            $block['page_name'] = $pageData['name'];
            $block['page_slug'] = $pageData['slug'];
            $block['page_id'] = $pageId;
        }
        
        return $this->generatePreviewHTML($pageBlocks, $templateData, $pageData, $options);
    }
    
    /**
     * Get blocks for a specific page
     */
    private function getPageBlocks($pageId)
    {
        $pageBlocks = [];
        
        try {
            // Add memory limit check
            $memoryLimit = ini_get('memory_limit');
            $currentMemory = memory_get_usage(true);
            
            if ($currentMemory > (256 * 1024 * 1024)) { // 256MB
                error_log("Memory usage high: " . round($currentMemory / 1024 / 1024, 2) . " MB");
                return [];
            }
            
            // Use the new PagesBlocks service instead of direct API call
            $pagesBlocksService = new \Modules\PagesBlocks\Services\PagesBlocksService();
            $pageBlocksResponse = $pagesBlocksService->getByPageId($pageId);
            
            $pageBlocksData = [];
            if ($pageBlocksResponse && isset($pageBlocksResponse['data']['data'])) {
                $pageBlocksData = $pageBlocksResponse['data']['data'];
            }
            
            // Limit blocks to prevent memory issues
            $pageBlocksData = array_slice($pageBlocksData, 0, 50); // Max 50 blocks
            
            foreach ($pageBlocksData as $pageBlock) {
                if (isset($pageBlock['block_id'])) {
                    // Get block data using V1 API
                    $blockResponse = $this->apiClient->get("blocks/{$pageBlock['block_id']}", [], false);
                    
                    if ($blockResponse && isset($blockResponse['data'])) {
                        $blockData = $blockResponse['data'];
                        
                        $tmplCode = $blockData['tmpl_code'] ?? '';
                        $jsonCode = $pageBlock['json_code'] ?? ($blockData['json_code'] ?? '{}');
                        $cssCode = $pageBlock['css_code'] ?? ($blockData['css_code'] ?? '');
                        $jsCode = $pageBlock['js_code'] ?? ($blockData['js_code'] ?? '');
                        $blockClasses = $pageBlock['classes'] ?? ($blockData['classes'] ?? '');
                        
                        // Limit template size to prevent memory issues
                        if (strlen($tmplCode) > 100000) { // 100KB limit
                            $tmplCode = substr($tmplCode, 0, 100000);
                        }
                        
                        // Process template safely with memory check
                        $processedContent = '';
                        if (!empty($tmplCode)) {
                            $processedContent = $this->processTemplate($tmplCode, $jsonCode);
                        }
                        
                        // Add block classes
                        if (!empty($blockClasses) && !empty($processedContent)) {
                            $processedContent = $this->addClassesToHtml($processedContent, $blockClasses);
                        }
                        
                        // Handle dependencies
                        $dependencies = $blockData['dependencies'] ?? '';
                        $dependenciesArray = [];
                        if (!empty($dependencies)) {
                            $dependenciesArray = json_decode($dependencies, true) ?: [];
                        }
                        
                        $pageBlocks[] = [
                            'id' => $pageBlock['block_id'],
                            'page_block_id' => $pageBlock['id'] ?? '',
                            'position' => $pageBlock['position'] ?? 0,
                            'processed_content' => $processedContent,
                            'css_code' => $cssCode,
                            'js_code' => $jsCode,
                            'classes' => $blockClasses,
                            'dependencies' => $dependenciesArray,
                            'json_code' => $jsonCode,
                            'tmpl_code' => $tmplCode
                        ];
                        
                        // Clear variables to free memory
                        unset($blockData, $tmplCode, $jsonCode, $cssCode, $jsCode, $processedContent);
                    }
                }
            }
            
            // Sort blocks by position
            usort($pageBlocks, function($a, $b) {
                return floatval($a['position']) <=> floatval($b['position']);
            });
            
        } catch (Exception $e) {
            error_log("Error getting page blocks: " . $e->getMessage());
        }
        
        return $pageBlocks;
    }
    
    /**
     * Process Handlebars template with JSON data using LightnCandy
     */
    public function processTemplate($template, $jsonData)
    {
        try {
            if (empty($template)) {
                return '';
            }
            
            $data = json_decode($jsonData, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                error_log('JSON decode error: ' . json_last_error_msg());
                return $template; // Return original template on error
            }

            $phpStr = LightnCandy::compile($template, [
                'flags' => LightnCandy::FLAG_HANDLEBARS | 
                          LightnCandy::FLAG_ERROR_EXCEPTION | 
                          LightnCandy::FLAG_RUNTIMEPARTIAL |
                          LightnCandy::FLAG_NOESCAPE,
                'helpers' => self::$helpers
            ]);
            
            if (!$phpStr) {
                error_log('Failed to compile template');
                return $template;
            }

            $renderer = LightnCandy::prepare($phpStr);
            if (!$renderer) {
                error_log('Failed to prepare template renderer');
                return $template;
            }
            
            return $renderer($data);
        } catch (Exception $e) {
            error_log('Template processing error: ' . $e->getMessage());
            return $template; // Return original template on error
        }
    }
    
    /**
     * Process Handlebars template for AJAX requests
     */
    public function processTemplateToString($template, $jsonData)
    {
        try {
            $data = json_decode($jsonData, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                return ['success' => false, 'message' => 'Invalid JSON data: ' . json_last_error_msg()];
            }

            $phpStr = LightnCandy::compile($template, [
                'flags' => LightnCandy::FLAG_HANDLEBARS | 
                        LightnCandy::FLAG_ERROR_EXCEPTION | 
                        LightnCandy::FLAG_RUNTIMEPARTIAL |
                        LightnCandy::FLAG_NOESCAPE,
                'helpers' => self::$helpers
            ]);
            
            if (!$phpStr) {
                return ['success' => false, 'message' => 'Failed to compile template'];
            }

            $renderer = LightnCandy::prepare($phpStr);
            if (!$renderer) {
                return ['success' => false, 'message' => 'Failed to prepare template renderer'];
            }
            
            $result = $renderer($data);
            
            return ['success' => true, 'html' => $result];
        } catch (Exception $e) {
            error_log('Template processing error: ' . $e->getMessage());
            return ['success' => false, 'message' => 'Template processing error: ' . $e->getMessage()];
        }
    }
    
    /**
     * Add CSS classes to HTML content
     */
    private function addClassesToHtml($html, $classes)
    {
        if (empty($classes) || empty($html)) {
            return $html;
        }
        
        // Simple implementation - find first HTML tag and add classes
        $pattern = '/(<[^>]+class=["\']?)([^"\']*)/';
        if (preg_match($pattern, $html)) {
            $html = preg_replace($pattern, '$1$2 ' . trim($classes), $html);
        } else {
            // If no class attribute exists, add one to the first tag
            $html = preg_replace('/(<[^>]+?)(\s*>)/', '$1 class="' . trim($classes) . '"$2', $html, 1);
        }
        
        return $html;
    }
    
    /**
     * Generate the complete preview HTML with drag-and-drop support
     */
    private function generatePreviewHTML($blocks, $templateData, $pageData, $options = [])
    {
        $previewMode = $options['preview_mode'] ?? false;
        $hideControls = $options['hide_controls'] ?? $previewMode;
        
        // Generate HTML structure
        $html = '<!DOCTYPE html>';
        $html .= '<html lang="en">';
        $html .= '<head>';
        $html .= '<meta charset="UTF-8">';
        $html .= '<meta name="viewport" content="width=device-width, initial-scale=1.0">';
        $html .= '<title>' . ($pageData['name'] ?? 'Preview') . '</title>';
        $html .= '<script src="https://cdn.tailwindcss.com"></script>';
        
        if (!$hideControls) {
            $html .= '<link href="https://cdnjs.cloudflare.com/ajax/libs/flowbite/2.2.1/flowbite.min.css" rel="stylesheet" />';
        }
        
        // Add template global assets if available
        if ($templateData && isset($templateData['global_assets'])) {
            foreach ($templateData['global_assets'] as $asset) {
                if (isset($asset['type']) && isset($asset['url'])) {
                    if ($asset['type'] === 'css') {
                        $html .= '<link rel="stylesheet" href="' . htmlspecialchars($asset['url']) . '">';
                    } elseif ($asset['type'] === 'js') {
                        $html .= '<script src="' . htmlspecialchars($asset['url']) . '"></script>';
                    }
                }
            }
        }
        
        // Add styles for drag-and-drop and edit controls
        $html .= '<style>';
        $html .= '
            /* Block wrapper controls */
            .block-wrapper-controls {
                position: relative;
                margin: 8px 0;
                transition: all 0.2s ease;
            }
            .block-wrapper-controls:hover {
                outline: 2px solid #3b82f6;
                outline-offset: 2px;
                border-radius: 6px;
            }
            .block-wrapper-controls:hover .block-controls {
                display: flex;
            }
            
            /* Block edit controls */
            .block-controls {
                display: none;
                position: absolute;
                top: 8px;
                right: 8px;
                background: rgba(255, 255, 255, 0.95);
                backdrop-filter: blur(10px);
                border: 1px solid rgba(229, 231, 235, 0.8);
                border-radius: 6px;
                padding: 4px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
                z-index: 10000;
                transition: all 0.2s ease;
            }
            .block-controls:hover {
                background: rgba(255, 255, 255, 1);
                box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
            }
            .block-controls button {
                margin: 0 1px;
                padding: 8px;
                background: none;
                border: none;
                cursor: pointer;
                transition: all 0.2s ease;
                border-radius: 4px;
                color: #6b7280;
            }
            .block-controls button:hover {
                background: rgba(59, 130, 246, 0.1);
                color: #3b82f6;
                transform: scale(1.05);
            }
            .block-controls button.block-delete:hover {
                background: rgba(239, 68, 68, 0.1);
                color: #ef4444;
            }
            .block-controls button.block-edit:hover {
                background: rgba(16, 185, 129, 0.1);
                color: #10b981;
            }
            
            /* Drop zones */
            .drop-zone {
                height: 60px;
                margin: 8px 4px 4px;
                border: 2px dashed #d1d5db;
                border-radius: 8px;
                background-color: #f9fafb;
                display: none;
                align-items: center;
                justify-content: center;
                transition: all 0.3s ease;
                opacity: 0;
            }
            .drop-zone.active {
                display: flex;
                opacity: 1;
                border-color: #3b82f6;
                background-color: #eff6ff;
            }
            .drop-zone.drag-over {
                border-color: #1d4ed8;
                background-color: #dbeafe;
                transform: scale(1.02);
            }
            .drop-zone-text {
                color: #6b7280;
                font-size: 14px;
                display: flex;
                align-items: center;
                gap: 8px;
            }
            .main-drop-area {
                min-height: 200px;
                margin: 8px 4px 4px;
                border: 2px dashed #d1d5db;
                border-radius: 12px;
                background-color: #f9fafb;
                display: flex;
                align-items: center;
                justify-content: center;
                transition: all 0.3s ease;
            }
            .main-drop-area.drag-over {
                border-color: #3b82f6;
                background-color: #eff6ff;
                transform: scale(1.01);
            }
        ';
        
        // Add block-specific CSS
        foreach ($blocks as $block) {
            if (!empty($block['css_code'])) {
                $html .= '/* Block ID: ' . $block['id'] . ' */' . "\n";
                $html .= $block['css_code'] . "\n\n";
            }
        }
        
        $html .= '</style>';
        $html .= '</head>';
        $html .= '<body>';
        
        // Add drop zones and block content
        if (!$hideControls && empty($blocks)) {
            // Main drop area when no blocks exist
            $html .= '<div id="mainDropArea" class="main-drop-area">';
            $html .= '<div class="drop-zone-text">';
            $html .= '<svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">';
            $html .= '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>';
            $html .= '</svg>';
            $html .= '<span>Drop blocks here to get started</span>';
            $html .= '</div>';
            $html .= '</div>';
        }
        
        // Add blocks content with controls
        foreach ($blocks as $index => $block) {
            if (!$hideControls) {
                // Add drop zone before each block
                $html .= '<div class="drop-zone" data-position="' . ($index + 1) . '">';
                $html .= '<div class="drop-zone-text">';
                $html .= '<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">';
                $html .= '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>';
                $html .= '</svg>';
                $html .= '<span>Drop block here</span>';
                $html .= '</div>';
                $html .= '</div>';
            }
            
            // Block wrapper with controls
            if (!$hideControls) {
                $html .= '<div class="block-wrapper-controls" data-block-id="' . $block['id'] . '" data-page-block-id="' . ($block['page_block_id'] ?? '') . '">';
                
                // Block controls
                $html .= '<div class="block-controls">';
                $html .= '<button class="block-edit" onclick="editBlock(\'' . $block['id'] . '\', \'' . htmlspecialchars($block['json_code']) . '\')" title="Edit Block">';
                $html .= '<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">';
                $html .= '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>';
                $html .= '</svg>';
                $html .= '</button>';
                $html .= '<button class="block-delete" onclick="deleteBlock(\'' . $block['id'] . '\')" title="Delete Block">';
                $html .= '<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">';
                $html .= '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>';
                $html .= '</svg>';
                $html .= '</button>';
                $html .= '</div>';
            }
            
            $html .= $block['processed_content'];
            
            if (!$hideControls) {
                $html .= '</div>';
            }
        }
        
        // Add final drop zone
        if (!$hideControls) {
            $html .= '<div class="drop-zone" data-position="' . (count($blocks) + 1) . '">';
            $html .= '<div class="drop-zone-text">';
            $html .= '<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">';
            $html .= '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>';
            $html .= '</svg>';
            $html .= '<span>Drop block here</span>';
            $html .= '</div>';
            $html .= '</div>';
        }
        
        // Add edit drawer
        if (!$hideControls) {
            $html .= $this->generateEditDrawer();
        }
        
        // Add block-specific JavaScript
        foreach ($blocks as $block) {
            if (!empty($block['js_code'])) {
                $html .= '<script>' . $block['js_code'] . '</script>';
            }
        }
        
        // Add JavaScript for drag-and-drop and edit functionality
        if (!$hideControls) {
            $html .= $this->generatePreviewJavaScript();
        }
        
        if (!$hideControls) {
            $html .= '<script src="https://cdnjs.cloudflare.com/ajax/libs/flowbite/2.2.1/flowbite.min.js"></script>';
        }
        
        $html .= '</body>';
        $html .= '</html>';
        
        return $html;
    }
    
    /**
     * Generate edit drawer HTML
     */
    private function generateEditDrawer()
    {
        return '
        <div id="edit-drawer" class="fixed top-0 right-0 z-40 h-screen p-4 overflow-y-auto transition-transform translate-x-full bg-white w-80 dark:bg-gray-800" tabindex="-1" aria-labelledby="drawer-right-label">
            <h5 id="drawer-right-label" class="inline-flex items-center mb-4 text-base font-semibold text-gray-500 dark:text-gray-400">
                <svg class="w-4 h-4 me-2.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5ZM9.5 4a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3ZM12 15H8a1 1 0 0 1 0-2h1v-3H8a1 1 0 0 1 0-2h2a1 1 0 0 1 1 1v4h1a1 1 0 0 1 0 2Z"/>
                </svg>
                Edit Block
            </h5>
            <button type="button" data-drawer-hide="edit-drawer" aria-controls="edit-drawer" class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 absolute top-2.5 end-2.5 inline-flex items-center justify-center dark:hover:bg-gray-600 dark:hover:text-white">
                <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"/>
                </svg>
                <span class="sr-only">Close menu</span>
            </button>
            <div class="mb-6">
                <form id="blockEditForm" class="space-y-4">
                    <div id="formFields" class="space-y-4">
                        <p class="text-gray-500 text-center py-4">Select a block to edit its content</p>
                    </div>
                </form>
            </div>
            <div class="flex-shrink-0 p-4 border-t pt-4 space-y-4 border-t border-gray-200 dark:border-gray-700">
                <div class="flex space-x-2">
                    <button id="saveChanges" type="button" class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800 flex-1">
                        Save Changes
                    </button>
                    <button id="cancelChanges" type="button" data-drawer-hide="edit-drawer" class="py-2.5 px-5 text-sm font-medium text-gray-900 focus:outline-none bg-white rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-100 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700">
                        Cancel
                    </button>
                </div>
            </div>
        </div>';
    }
    
    /**
     * Generate JavaScript for drag-and-drop and edit functionality
     */
    private function generatePreviewJavaScript()
    {
        return '
        <script>
            // Initialize global tracking variables
            window.currentEditingBlock = null;
            window.processingBlocks = {};
            window.dropZoneInitialized = false;
            window.dragActive = false;
            
            // Initialize blocks functionality on load
            document.addEventListener("DOMContentLoaded", function() {
                console.log("DOM Content Loaded - Initializing blocks...");
                initializeBlocks();
                setupBlockControls();
                updateMainDropAreaVisibility();
            });
            
            // Function to update main drop area visibility
            function updateMainDropAreaVisibility() {
                const mainDropArea = document.getElementById("mainDropArea");
                if (!mainDropArea) return;
                
                const blockElements = document.querySelectorAll(".block-wrapper-controls");
                const hasBlocks = blockElements.length > 0;
                
                if (hasBlocks) {
                    mainDropArea.style.display = "none";
                } else {
                    mainDropArea.style.display = "block";
                }
            }
            
            // Initialize drag and drop zones
            function initializeBlocks() {
                if (!window.dropZoneInitialized) {
                    initializeDropZones();
                    window.dropZoneInitialized = true;
                }
            }
            
            function initializeDropZones() {
                console.log("Setting up drop zones");
                
                // Listen for messages from parent window for drag and drop
                window.addEventListener("message", function(event) {
                    if (event.data.type === "dragStart") {
                        showDropZones();
                    }
                    if (event.data.type === "dragEnd") {
                        hideDropZones();
                    }
                    
                    if (event.data.type === "addBlock") {
                        const normalizedData = normalizeBlockData(event.data.blockData);
                        if (normalizedData) {
                            addNewBlock(normalizedData);
                        }
                    }
                });
            }
            
            function setupBlockControls() {
                console.log("Setting up block controls");
            }
            
            function showDropZones() {
                const dropZones = document.querySelectorAll(".drop-zone");
                dropZones.forEach(zone => {
                    zone.classList.add("active");
                });
                window.dragActive = true;
            }
            
            function hideDropZones() {
                const dropZones = document.querySelectorAll(".drop-zone");
                dropZones.forEach(zone => {
                    zone.classList.remove("active", "drag-over");
                });
                window.dragActive = false;
            }
            
            function normalizeBlockData(blockData) {
                return {
                    id: blockData.id || blockData.block_id,
                    name: blockData.name || blockData.block_name,
                    tmpl_code: blockData.tmpl_code || blockData.template_code,
                    json_code: blockData.json_code || "{}",
                    css_code: blockData.css_code || "",
                    js_code: blockData.js_code || ""
                };
            }
            
            function addNewBlock(blockData) {
                console.log("Adding new block:", blockData);
                // Implementation for adding new block
            }
            
            // Edit block function
            function editBlock(blockId, jsonCode) {
                window.currentEditingBlock = blockId;
                
                try {
                    const data = JSON.parse(jsonCode);
                    generateFormFields(data);
                    
                    // Show the drawer
                    const drawer = document.getElementById("edit-drawer");
                    if (drawer) {
                        drawer.classList.remove("translate-x-full");
                    }
                } catch (e) {
                    console.error("Error parsing block data:", e);
                }
            }
            
            // Delete block function
            function deleteBlock(blockId) {
                if (confirm("Are you sure you want to delete this block?")) {
                    console.log("Deleting block:", blockId);
                    // Implementation for deleting block
                }
            }
            
            // Generate form fields based on JSON data
            function generateFormFields(data) {
                const formFields = document.getElementById("formFields");
                if (!formFields) return;
                
                formFields.innerHTML = "";
                
                Object.keys(data).forEach(key => {
                    const value = data[key];
                    const fieldDiv = document.createElement("div");
                    fieldDiv.className = "mb-4";
                    
                    const label = document.createElement("label");
                    label.className = "block mb-2 text-sm font-medium text-gray-900 dark:text-white";
                    label.textContent = key.charAt(0).toUpperCase() + key.slice(1).replace(/_/g, " ");
                    
                    let input;
                    if (typeof value === "string" && value.length > 50) {
                        input = document.createElement("textarea");
                        input.rows = 3;
                    } else {
                        input = document.createElement("input");
                        input.type = typeof value === "number" ? "number" : "text";
                    }
                    
                    input.className = "bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500";
                    input.name = key;
                    input.value = value;
                    
                    fieldDiv.appendChild(label);
                    fieldDiv.appendChild(input);
                    formFields.appendChild(fieldDiv);
                });
            }
            
            // Save changes function
            document.addEventListener("DOMContentLoaded", function() {
                const saveBtn = document.getElementById("saveChanges");
                if (saveBtn) {
                    saveBtn.addEventListener("click", function() {
                        if (!window.currentEditingBlock) return;
                        
                        const form = document.getElementById("blockEditForm");
                        const formData = new FormData(form);
                        const data = {};
                        
                        for (let [key, value] of formData.entries()) {
                            data[key] = value;
                        }
                        
                        console.log("Saving block data:", data);
                        // Implementation for saving block data
                    });
                }
            });
        </script>';
    }
    
    /**
     * Handle AJAX requests for block updates
     */
    public function handleAjaxRequest(Request $request)
    {
        $action = $request->post('action');
        
        switch ($action) {
            case 'update_block':
                return $this->updateBlockData($request);
                
            case 'update_block_position':
                return $this->updateBlockPosition($request);
                
            case 'process_template':
                return $this->processTemplateAjax($request);
                
            default:
                return ['success' => false, 'message' => 'Invalid action'];
        }
    }
    
    /**
     * Update block data via API
     */
    private function updateBlockData(Request $request)
    {
        try {
            $blockId = $request->post('block_id');
            $jsonCode = $request->post('json_code', '{}');
            $classes = $request->post('classes', '');
            
            $requestData = [
                'block_id' => $blockId,
                'json_code' => $jsonCode,
                'classes' => $classes
            ];
            
            // Make API call to update block using V1 API
            $response = $this->apiClient->put("pages-blocks/{$blockId}", $requestData, false);
            
            return [
                'success' => isset($response['status']) && $response['status'] === 'success',
                'message' => $response['message'] ?? 'Block updated successfully'
            ];
        } catch (Exception $e) {
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }
    
    /**
     * Update block position
     */
    private function updateBlockPosition(Request $request)
    {
        try {
            $blockId = $request->post('block_id');
            $position = $request->post('position');
            
            $requestData = [
                'block_id' => $blockId,
                'position' => floatval($position)
            ];
            
            // Make API call to update block position using V1 API
            $response = $this->apiClient->put("pages-blocks/{$blockId}", $requestData, false);
            
            return [
                'success' => isset($response['status']) && $response['status'] === 'success',
                'message' => $response['message'] ?? 'Block position updated successfully'
            ];
        } catch (Exception $e) {
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }
    
    /**
     * Process template via AJAX
     */
    private function processTemplateAjax(Request $request)
    {
        try {
            $template = $request->post('template', '');
            $jsonData = $request->post('json_data', '{}');
            
            return $this->processTemplateToString($template, $jsonData);
        } catch (Exception $e) {
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }
} 