<?php
namespace Core;

use Monolog\Logger;
use Monolog\Handler\StreamHandler;

class ErrorHandler
{
    protected static Logger $logger;

    public static function register(): void
    {
        static::$logger = new Logger('app');
        static::$logger->pushHandler(new StreamHandler(__DIR__ . '/../storage/logs/app.log'));

        set_error_handler([self::class, 'handle']);
        set_exception_handler([self::class, 'handleException']);
    }

    public static function handle($severity, $message, $file, $line): void
    {
        dd($message, $severity, $file, $line);
        $logMessage = "PHP Error [$severity]: $message in $file on line $line";
        static::$logger->error($logMessage);

        http_response_code(500);
        if (config('app.debug')) {
            // Include the error view with exception details
            $errorFile = __DIR__ . '/../views/errors/500.php';
            if (file_exists($errorFile)) {
                // Pass the exception to the view
                $error = $logMessage; // Make exception available in the view scope
                include $errorFile;
            } else {
                // Fallback error display
                echo '<h1>500 Internal Server Error</h1>';
                echo '<p>An error occurred while processing your request.</p>';
                if (config('app.debug')) {
                    echo '<pre>' . htmlspecialchars((string) $logMessage) . '</pre>';
                }
            }
        } else {
            // In production, show a generic error page
            $errorFile = __DIR__ . '/../views/errors/500.php';
            if (file_exists($errorFile)) {
                include $errorFile;
            } else {
                echo 'An error occurred. Please try again later.';
            }
        }
        exit;
    }

    public static function handleException($exception): void
    {
        $logMessage = "Uncaught Exception: " . $exception->getMessage();
        static::$logger->error($logMessage);

        http_response_code(500);
            if (config('app.debug')) {
            // Include the error view with exception details
            $errorFile = __DIR__ . '/../views/errors/500.php';
            if (file_exists($errorFile)) {
                // Pass the exception to the view
                $error = $logMessage; // Make exception available in the view scope
                include $errorFile;
            } else {
                // Fallback error display
                echo '<h1>500 Internal Server Error</h1>';
                echo '<p>An error occurred while processing your request.</p>';
                if (config('app.debug')) {
                    echo '<pre>' . htmlspecialchars((string) $logMessage) . '</pre>';
                }
            }
        } else {
            // In production, show a generic error page
            $errorFile = __DIR__ . '/../views/errors/500.php';
            if (file_exists($errorFile)) {
                include $errorFile;
            } else {
                echo 'An error occurred. Please try again later.';
            }
        }
        exit;
    }
}
