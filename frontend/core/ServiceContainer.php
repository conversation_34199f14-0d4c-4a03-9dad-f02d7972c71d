<?php
namespace Core;

class ServiceContainer
{
    protected static array $bindings = [];

    public static function bind(string $abstract, callable $resolver)
    {
        static::$bindings[$abstract] = $resolver;
    }

    public static function resolve(string $abstract)
    {
        if (!isset(static::$bindings[$abstract])) {
            throw new \Exception("Service not bound: $abstract");
        }

        return static::$bindings[$abstract]();
    }
}
