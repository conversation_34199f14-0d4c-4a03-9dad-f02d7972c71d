<?php
// config/app.php

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

return [
    // Application Settings
    'name' => $_ENV['APP_NAME'] ?? 'FrontCMS',
    'env' => $_ENV['APP_ENV'] ?? 'development',
    'debug' => filter_var($_ENV['APP_DEBUG'] ?? false, FILTER_VALIDATE_BOOLEAN),
    'verify_url' => filter_var($_ENV['APP_VERIFY_URL'] ?? false, FILTER_VALIDATE_BOOLEAN),
    'app_url' => $_ENV['APP_URL'] ?? '/frontcms',
    'timeout' => $_ENV['APP_TIMEOUT'] ?? 30,
    'view_path' => $_ENV['APP_VIEW_PATH'] ?? '',

    // API Configuration
    'api' => [
        'v1_url' => $_ENV['API_BASE_URL_V1'] ?? 'http://127.0.0.1:8000/api/v1',
        'cms_url' => $_ENV['API_BASE_URL_CMS'] ?? 'http://127.0.0.1:8000/api/cms',
        'key' => $_ENV['API_KEY'] ?? null,
        'token' => $_ENV['X_API_TOKEN'] ?? null,
    ],

    // File Uploads
    'upload' => [
        'dir' => $_ENV['UPLOAD_DIR'] ?? __DIR__ . '/../uploads',
        'url' => $_ENV['UPLOAD_URL'] ?? '/frontcms/uploads',
    ],

    // Session Configuration
    'session' => [
        'driver' => $_ENV['SESSION_DRIVER'] ?? 'file',
        'lifetime' => (int)($_ENV['SESSION_LIFETIME'] ?? 120),
    ],

    // Application Modules
    'modules' => isset($_ENV['MODULES']) ? array_map('trim', explode(',', $_ENV['MODULES'])) : [
        'block_categories',
        'template_categories',
        'tags',
        'templates',
        'pages',
        'blocks',
        'pages_blocks',
    ],
];
