<?php

/**
 * Parse thumbnail sizes from environment variables
 */
function parseThumbnailSizes()
{
    // First try to parse JSON format from FILEUPLOAD_THUMBNAIL_SIZES
    $jsonSizes = $_ENV['FILEUPLOAD_THUMBNAIL_SIZES'] ?? '';
    if (!empty($jsonSizes)) {
        $decoded = json_decode($jsonSizes, true);
        if (json_last_error() === JSON_ERROR_NONE && is_array($decoded)) {
            $sizes = [];
            foreach ($decoded as $size) {
                if (isset($size['name'], $size['width'], $size['height'])) {
                    $sizes[$size['name']] = [
                        'width' => (int)$size['width'],
                        'height' => (int)$size['height'],
                        'mode' => $size['mode'] ?? 'crop',
                    ];
                }
            }
            return $sizes;
        }
    }

    // Fallback to individual environment variables
    return [
        'small' => [
            'width' => (int)($_ENV['FILEUPLOAD_THUMB_SMALL_WIDTH'] ?? 150),
            'height' => (int)($_ENV['FILEUPLOAD_THUMB_SMALL_HEIGHT'] ?? 150),
            'mode' => $_ENV['FILEUPLOAD_THUMB_SMALL_MODE'] ?? 'crop',
        ],
        'medium' => [
            'width' => (int)($_ENV['FILEUPLOAD_THUMB_MEDIUM_WIDTH'] ?? 300),
            'height' => (int)($_ENV['FILEUPLOAD_THUMB_MEDIUM_HEIGHT'] ?? 300),
            'mode' => $_ENV['FILEUPLOAD_THUMB_MEDIUM_MODE'] ?? 'crop',
        ],
        'large' => [
            'width' => (int)($_ENV['FILEUPLOAD_THUMB_LARGE_WIDTH'] ?? 600),
            'height' => (int)($_ENV['FILEUPLOAD_THUMB_LARGE_HEIGHT'] ?? 600),
            'mode' => $_ENV['FILEUPLOAD_THUMB_LARGE_MODE'] ?? 'fit',
        ],
    ];
}

return [
    /*
    |--------------------------------------------------------------------------
    | Default Storage Driver
    |--------------------------------------------------------------------------
    |
    | This option controls the default storage driver that will be used
    | for file uploads. You may set this to any of the drivers defined
    | in the "drivers" array below.
    |
    | Supported: "local", "s3", "gcs", "azure"
    |
    */
    'default' => $_ENV['FILEUPLOAD_DRIVER'] ?? 'local',

    /*
    |--------------------------------------------------------------------------
    | Storage Drivers
    |--------------------------------------------------------------------------
    |
    | Here you may configure as many storage drivers as you wish, and you
    | may even configure multiple drivers of the same type. Defaults have
    | been setup for each driver as an example of the required options.
    |
    */
    'drivers' => [
        'local' => [
            'driver' => 'local',
            'root' => (function () {
                $root = $_ENV['FILEUPLOAD_LOCAL_ROOT'] ?? 'uploads';
                // Convert relative path to absolute path
                if (!str_starts_with($root, '/')) {
                    $root = __DIR__ . '/../' . $root;
                }
                return $root;
            })(),
            'url' => $_ENV['FILEUPLOAD_LOCAL_URL'] ?? '/uploads',
            'permissions' => [
                'file' => [
                    'public' => 0644,
                    'private' => 0600,
                ],
                'dir' => [
                    'public' => 0755,
                    'private' => 0700,
                ],
            ],
        ],

        's3' => [
            'driver' => 's3',
            'key' => $_ENV['AWS_ACCESS_KEY_ID'] ?? '',
            'secret' => $_ENV['AWS_SECRET_ACCESS_KEY'] ?? '',
            'region' => $_ENV['AWS_DEFAULT_REGION'] ?? 'us-east-1',
            'bucket' => $_ENV['AWS_BUCKET'] ?? '',
            'url' => $_ENV['AWS_URL'] ?? null,
            'endpoint' => $_ENV['AWS_ENDPOINT'] ?? null,
            'use_path_style_endpoint' => filter_var($_ENV['AWS_USE_PATH_STYLE_ENDPOINT'] ?? false, FILTER_VALIDATE_BOOLEAN),
            'visibility' => 'public',
            'options' => [
                'CacheControl' => 'max-age=31536000',
                'Metadata' => [
                    'uploaded_by' => 'fileupload_package',
                ],
            ],
        ],

        'gcs' => [
            'driver' => 'gcs',
            'project_id' => $_ENV['GOOGLE_CLOUD_PROJECT_ID'] ?? '',
            'key_file' => $_ENV['GOOGLE_CLOUD_KEY_FILE'] ?? null,
            'bucket' => $_ENV['GOOGLE_CLOUD_STORAGE_BUCKET'] ?? '',
            'path_prefix' => $_ENV['GOOGLE_CLOUD_STORAGE_PATH_PREFIX'] ?? '',
            'api_uri' => $_ENV['GOOGLE_CLOUD_STORAGE_API_URI'] ?? null,
            'visibility' => 'public',
        ],

        'azure' => [
            'driver' => 'azure',
            'account_name' => $_ENV['AZURE_STORAGE_ACCOUNT'] ?? '',
            'account_key' => $_ENV['AZURE_STORAGE_KEY'] ?? '',
            'container' => $_ENV['AZURE_STORAGE_CONTAINER'] ?? '',
            'url' => $_ENV['AZURE_STORAGE_URL'] ?? null,
            'visibility' => 'public',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | File Validation Rules
    |--------------------------------------------------------------------------
    |
    | Configure default validation rules for uploaded files.
    |
    */
    'validation' => [
        'max_file_size' => $_ENV['FILEUPLOAD_MAX_SIZE'] ?? ********, // 10MB in bytes
        'allowed_mime_types' => array_filter(explode(',', $_ENV['FILEUPLOAD_ALLOWED_TYPES'] ?? 'image/jpeg,image/png,image/gif,image/webp,application/pdf')),
        'allowed_extensions' => array_filter(explode(',', $_ENV['FILEUPLOAD_ALLOWED_EXTENSIONS'] ?? 'jpg,jpeg,png,gif,webp,pdf')),
        'forbidden_extensions' => array_filter(explode(',', $_ENV['FILEUPLOAD_FORBIDDEN_EXTENSIONS'] ?? 'php,exe,bat,sh,js,html')),
        'scan_for_viruses' => filter_var($_ENV['FILEUPLOAD_VIRUS_SCAN'] ?? false, FILTER_VALIDATE_BOOLEAN),
        'check_image_dimensions' => filter_var($_ENV['FILEUPLOAD_CHECK_DIMENSIONS'] ?? true, FILTER_VALIDATE_BOOLEAN),
        'max_width' => (int)($_ENV['FILEUPLOAD_MAX_WIDTH'] ?? 4096),
        'max_height' => (int)($_ENV['FILEUPLOAD_MAX_HEIGHT'] ?? 4096),
        'min_width' => (int)($_ENV['FILEUPLOAD_MIN_WIDTH'] ?? 1),
        'min_height' => (int)($_ENV['FILEUPLOAD_MIN_HEIGHT'] ?? 1),
    ],

    /*
    |--------------------------------------------------------------------------
    | Image Processing Configuration
    |--------------------------------------------------------------------------
    |
    | Configure image processing options including thumbnail generation,
    | resizing, and format conversion.
    |
    */
    'processing' => [
        'enabled' => filter_var($_ENV['FILEUPLOAD_PROCESSING_ENABLED'] ?? true, FILTER_VALIDATE_BOOLEAN),
        'driver' => $_ENV['FILEUPLOAD_PROCESSING_DRIVER'] ?? 'gd', // gd (PHP built-in)
        'compress_images' => filter_var($_ENV['FILEUPLOAD_COMPRESS_IMAGES'] ?? true, FILTER_VALIDATE_BOOLEAN),
        'quality' => (int)($_ENV['FILEUPLOAD_IMAGE_QUALITY'] ?? 85),
        'auto_orient' => filter_var($_ENV['FILEUPLOAD_AUTO_ORIENT'] ?? true, FILTER_VALIDATE_BOOLEAN),
        'strip_metadata' => filter_var($_ENV['FILEUPLOAD_STRIP_METADATA'] ?? true, FILTER_VALIDATE_BOOLEAN),

        'thumbnails' => [
            'enabled' => filter_var($_ENV['FILEUPLOAD_THUMBNAILS_ENABLED'] ?? true, FILTER_VALIDATE_BOOLEAN),
            'directory' => $_ENV['FILEUPLOAD_THUMBNAILS_DIRECTORY'] ?? 'thumbnails',
            'quality' => (int)($_ENV['FILEUPLOAD_THUMBNAIL_QUALITY'] ?? 80),
            'sizes' => parseThumbnailSizes(),
        ],

        'watermark' => [
            'enabled' => filter_var($_ENV['FILEUPLOAD_WATERMARK_ENABLED'] ?? false, FILTER_VALIDATE_BOOLEAN),
            'image' => $_ENV['FILEUPLOAD_WATERMARK_IMAGE'] ?? null,
            'position' => $_ENV['FILEUPLOAD_WATERMARK_POSITION'] ?? 'bottom-right', // top-left, top-right, bottom-left, bottom-right, center
            'opacity' => (int)($_ENV['FILEUPLOAD_WATERMARK_OPACITY'] ?? 50),
            'margin' => (int)($_ENV['FILEUPLOAD_WATERMARK_MARGIN'] ?? 10),
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Background Processing
    |--------------------------------------------------------------------------
    |
    | Configure background processing for large files and intensive operations.
    |
    */
    'queue' => [
        'enabled' => filter_var($_ENV['FILEUPLOAD_QUEUE_ENABLED'] ?? false, FILTER_VALIDATE_BOOLEAN),
        'connection' => $_ENV['FILEUPLOAD_QUEUE_CONNECTION'] ?? 'default',
        'queue' => $_ENV['FILEUPLOAD_QUEUE_NAME'] ?? 'file-uploads',
        'delay' => (int)($_ENV['FILEUPLOAD_QUEUE_DELAY'] ?? 0),
        'timeout' => (int)($_ENV['FILEUPLOAD_QUEUE_TIMEOUT'] ?? 300),
        'retry_after' => (int)($_ENV['FILEUPLOAD_QUEUE_RETRY_AFTER'] ?? 600),
        'max_tries' => (int)($_ENV['FILEUPLOAD_QUEUE_MAX_TRIES'] ?? 3),
    ],

    /*
    |--------------------------------------------------------------------------
    | Security Configuration
    |--------------------------------------------------------------------------
    |
    | Configure security-related options for file uploads.
    |
    */
    'security' => [
        'scan_uploads' => filter_var($_ENV['FILEUPLOAD_SECURITY_SCAN'] ?? true, FILTER_VALIDATE_BOOLEAN),
        'quarantine_suspicious' => filter_var($_ENV['FILEUPLOAD_QUARANTINE'] ?? true, FILTER_VALIDATE_BOOLEAN),
        'check_file_headers' => filter_var($_ENV['FILEUPLOAD_CHECK_HEADERS'] ?? true, FILTER_VALIDATE_BOOLEAN),
        'randomize_names' => filter_var($_ENV['FILEUPLOAD_RANDOMIZE_NAMES'] ?? true, FILTER_VALIDATE_BOOLEAN),
        'preserve_original_name' => filter_var($_ENV['FILEUPLOAD_PRESERVE_NAME'] ?? false, FILTER_VALIDATE_BOOLEAN),
    ],

    /*
    |--------------------------------------------------------------------------
    | Logging Configuration
    |--------------------------------------------------------------------------
    |
    | Configure logging for file upload operations.
    |
    */
    'logging' => [
        'enabled' => filter_var($_ENV['FILEUPLOAD_LOGGING_ENABLED'] ?? true, FILTER_VALIDATE_BOOLEAN),
        'level' => $_ENV['FILEUPLOAD_LOG_LEVEL'] ?? 'info', // debug, info, warning, error
        'log_successful_uploads' => filter_var($_ENV['FILEUPLOAD_LOG_SUCCESS'] ?? true, FILTER_VALIDATE_BOOLEAN),
        'log_failed_uploads' => filter_var($_ENV['FILEUPLOAD_LOG_FAILURES'] ?? true, FILTER_VALIDATE_BOOLEAN),
        'log_validation_errors' => filter_var($_ENV['FILEUPLOAD_LOG_VALIDATION'] ?? true, FILTER_VALIDATE_BOOLEAN),
    ],

    /*
    |--------------------------------------------------------------------------
    | Cleanup Configuration
    |--------------------------------------------------------------------------
    |
    | Configure automatic cleanup of temporary and orphaned files.
    |
    */
    'cleanup' => [
        'enabled' => filter_var($_ENV['FILEUPLOAD_CLEANUP_ENABLED'] ?? true, FILTER_VALIDATE_BOOLEAN),
        'temp_file_lifetime' => (int)($_ENV['FILEUPLOAD_TEMP_LIFETIME'] ?? 3600), // 1 hour in seconds
        'orphaned_file_lifetime' => (int)($_ENV['FILEUPLOAD_ORPHANED_LIFETIME'] ?? 86400), // 24 hours in seconds
        'schedule' => $_ENV['FILEUPLOAD_CLEANUP_SCHEDULE'] ?? '0 2 * * *', // Daily at 2 AM
    ],
];
