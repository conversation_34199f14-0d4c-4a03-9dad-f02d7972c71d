<?php

use Core\AssetManager;
use Core\Router;



if (!function_exists('config')) {
    /**
     * Get a configuration value using dot notation
     *
     * @param string $key
     * @param mixed $default
     * @return mixed
     */
    function config(string $key, $default = null)
    {
        static $config = [];

        // Load configuration only once
        if (empty($config)) {
            $configFile = __DIR__ . '/../config/app.php';

            if (!file_exists($configFile)) {
                error_log('Config file not found: ' . $configFile);
                return $default;
            }

            $config = require $configFile;

            if (!is_array($config)) {
                error_log('Invalid configuration format in: ' . $configFile);
                $config = [];
            }

            // Debug: Log loaded config keys
            if (($config['debug'] ?? false) === true) {
                error_log('Loaded config keys: ' . implode(', ', array_keys($config)));
            }
        }

        // Handle empty key - return entire config
        if (empty($key)) {
            return $config;
        }

        // Debug: Log the requested key
        $debug = $config['debug'] ?? false;
        if ($debug === true) {
            error_log('Config key requested: ' . $key);
        }

        // First try direct key access (for root level keys like 'app_url')
        if (array_key_exists($key, $config)) {
            return $config[$key];
        }

        // Then try dot notation for nested keys
        if (strpos($key, '.') !== false) {
            $keys = explode('.', $key);
            $value = $config;

            foreach ($keys as $segment) {
                if (is_array($value) && array_key_exists($segment, $value)) {
                    $value = $value[$segment];
                } else {
                    if ($debug === true) {
                        error_log('Config key not found: ' . $key);
                    }
                    return $default;
                }
            }
            return $value;
        }

        // Key not found
        if ($debug === true) {
            error_log('Config key not found: ' . $key);
        }
        return $default;
    }
}

if (!function_exists('env')) {
    /**
     * Gets the value of an environment variable
     *
     * @param string $key
     * @param mixed $default
     * @return mixed
     */
    function env(string $key, $default = null)
    {
        return $_ENV[$key] ?? $default;
    }
}

if (!function_exists('pageTitle')) {

    /**
     * Summary of pageTitle
     * @param mixed $title
     * @return string
     */
    function pageTitle(?string $title = null)
    {
        $appName = config('name');

        if (empty($title)) {
            return 'Dashboard | ' . $appName;
        }

        return $title . ' | ' . $appName;
    }
}

if (!function_exists('asset')) {
    /**
     * Generate a URL for an asset in the public directory
     *
     * @param string $path
     * @return string
     */

    function asset(string $path): string
    {
        try {
            // Remove any leading slash from path
            $path = ltrim($path, '/');

            // For absolute URLs, return as is
            if (strpos($path, 'http://') === 0 || strpos($path, 'https://') === 0) {
                return $path;
            }

            // Get base URL from config with fallback to server variables
            $baseUrl = '';

            // Try to get from config if function exists
            if (function_exists('config')) {
                $baseUrl = config('app_url', '');
            }

            // If still empty, determine from server
            if (empty($baseUrl)) {
                $scheme = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on') ? 'https' : 'http';
                $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
                $baseUrl = "{$scheme}://{$host}";
            }

            // Normalize base URL
            $baseUrl = rtrim($baseUrl, '/');

            
            // For module assets, use direct path
            if (strpos($path, 'modules/') === 0) {
                return "{$baseUrl}/{$path}";
            }

            // For public assets, add the public/assets prefix if not already present
            if (strpos($path, 'public/') !== 0 && strpos($path, 'assets/') !== 0) {
                // dd($path);
                $path = "public/assets/{$path}";
            }

            return "{$baseUrl}/{$path}";
        } catch (\Exception $e) {
            // Log the error but don't break the page
            error_log('Asset helper error: ' . $e->getMessage());
            return "/{$path}"; // Fallback to relative path
        }
    }
}

if (!function_exists('add_style')) {
    function add_style(string $path, array $attributes = []): void
    {
        AssetManager::getInstance()->addStyle($path, $attributes);
    }
}

if (!function_exists('add_script')) {
    function add_script(string $path, array $attributes = []): void
    {
        AssetManager::getInstance()->addScript($path, $attributes);
    }
}

if (!function_exists('render_styles')) {
    function render_styles(): string
    {
        return AssetManager::getInstance()->renderStyles();
    }
}

if (!function_exists('render_scripts')) {
    function render_scripts(): string
    {
        return AssetManager::getInstance()->renderScripts();
    }
}

if (!function_exists('public_path')) {
    /**
     * Get the path to the public directory
     *
     * @param string $path
     * @return string
     */
    function public_path(string $path = ''): string
    {
        $basePath = dirname(__DIR__) . '/public';
        return $basePath . ($path ? '/' . ltrim($path, '/') : '');
    }
}

if (!function_exists('url')) {
    /**
     * Generate a URL for the given path
     */
    function url(string $path = ''): string
    {
        $baseUrl = rtrim(config('app_url', ''), '/');
        $path = ltrim($path, '/');
        return $baseUrl . ($path ? '/' . $path : '');
    }
}

if (!function_exists('is_active')) {
    /**
     * Check if the current route is active
     */
    function is_active(string $path): bool
    {
        $current = trim(parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH), '/');
        return strpos($current, trim($path, '/')) === 0;
    }
}

if (!function_exists('add_inline_script')) {
    function add_inline_script(string $script): void
    {
        AssetManager::getInstance()->addInlineScript($script);
    }
}

if (!function_exists('add_inline_style')) {
    function add_inline_style(string $style): void
    {
        AssetManager::getInstance()->addInlineStyle($style);
    }
}

if (!function_exists('dd')) {
    function dd(...$args)
    {
        if (in_array(\PHP_SAPI, ['cli', 'phpdbg'], true)) {
            // For CLI, use simple output
            foreach ($args as $x) {
                var_dump($x);
            }
        } else {
            // For web, use styled output
            echo '<div style="background: #f5f5f5; padding: 20px; margin: 10px; border: 1px solid #e0e0e0; border-radius: 4px; font-family: monospace; font-size: 14px; line-height: 1.5; color: #333; overflow-x: auto;">';
            echo '<h3 style="margin: 0 0 10px 0; padding: 0; font-size: 16px; color: #e53935;">Debug Dump</h3>';
            echo '<div style="background: white; padding: 10px; border: 1px solid #ddd; border-radius: 3px;">';

            foreach ($args as $arg) {
                echo '<div style="margin-bottom: 10px;">';
                echo '<pre style="margin: 0; white-space: pre-wrap; word-wrap: break-word;">';
                if (is_bool($arg)) {
                    echo $arg ? 'true' : 'false';
                } elseif (is_null($arg)) {
                    echo 'null';
                } elseif (is_string($arg)) {
                    echo htmlspecialchars($arg, ENT_QUOTES, 'UTF-8');
                } elseif (is_scalar($arg)) {
                    var_dump($arg);
                } else {
                    print_r($arg);
                }
                echo '</pre>';
                echo '</div>';
            }

            // Add backtrace
            $backtrace = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 1)[0] ?? [];
            if (!empty($backtrace)) {
                echo '<div style="margin-top: 15px; padding-top: 10px; border-top: 1px solid #eee; color: #666; font-size: 12px;">';
                echo 'Called from: ' . ($backtrace['file'] ?? 'unknown') . ' on line ' . ($backtrace['line'] ?? '0');
                echo '</div>';
            }

            echo '</div>'; // End of white box
            echo '</div>'; // End of main container
        }

        exit(1);
    }
}


if (!function_exists('csrf_token')) {
    /**
     * Get the CSRF token value
     *
     * @return string|null The CSRF token if it exists, null otherwise
     */
    function csrf_token(): ?string
    {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }

        // Generate a new token if one doesn't exist
        if (empty($_SESSION['token'])) {
            $_SESSION['token'] = bin2hex(random_bytes(32));
        }

        return $_SESSION['token'] ?? null;
    }
}


// Add content to the footer of the page
if (!function_exists('add_footer_content')) {
    function add_footer_content($content)
    {
        static $footer_content = '';
        if ($content) {
            $footer_content .= $content;
        }
        return $footer_content;
    }
}
