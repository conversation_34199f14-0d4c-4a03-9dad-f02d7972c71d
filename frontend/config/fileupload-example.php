<?php
/**
 * FileUpload Package Configuration Template
 * 
 * This file demonstrates the recommended configuration approach:
 * 1. Use environment variables for deployment-specific settings
 * 2. Provide sensible defaults for all settings
 * 3. Support both relative and absolute paths
 * 4. Allow runtime overrides when needed
 * 
 * Copy this file to config/fileupload.php and customize as needed.
 */

// Helper function to resolve dynamic paths
function resolveUploadPath($path) {
    // If already absolute, return as-is
    if (str_starts_with($path, '/')) {
        return $path;
    }

    // Try different base paths for relative paths
    $possibleBases = [
        __DIR__ . '/../',                                    // Config directory relative
        getcwd() . '/',                                      // Current working directory
        $_SERVER['DOCUMENT_ROOT'] ?? __DIR__ . '/../',       // Document root
    ];

    foreach ($possibleBases as $base) {
        $fullPath = $base . $path;
        $dir = dirname($fullPath);
        if (is_dir($dir) && is_writable($dir)) {
            return $fullPath;
        }
    }

    // Fallback to config directory relative
    return __DIR__ . '/../' . $path;
}

// Helper function to parse thumbnail sizes from environment
function parseThumbnailSizes() {
    $envSizes = $_ENV['FILEUPLOAD_THUMBNAIL_SIZES'] ?? '';

    if (!empty($envSizes)) {
        $decoded = json_decode($envSizes, true);
        if (json_last_error() === JSON_ERROR_NONE && is_array($decoded)) {
            return $decoded;
        }
    }

    // Default thumbnail sizes
    return [
        ['name' => 'small', 'width' => 150, 'height' => 150, 'mode' => 'crop'],
        ['name' => 'medium', 'width' => 300, 'height' => 300, 'mode' => 'fit'],
        ['name' => 'large', 'width' => 600, 'height' => 600, 'mode' => 'fit'],
    ];
}

return [
    /*
    |--------------------------------------------------------------------------
    | Default Storage Driver
    |--------------------------------------------------------------------------
    |
    | This option controls the default storage driver that will be used
    | to store uploaded files. You may set this to any of the drivers
    | defined in the "drivers" array below.
    |
    | Supported: "local", "s3", "gcs", "azure"
    |
    */
    'default' => $_ENV['FILEUPLOAD_DRIVER'] ?? 'local',

    /*
    |--------------------------------------------------------------------------
    | Storage Drivers
    |--------------------------------------------------------------------------
    |
    | Here you may configure as many storage drivers as you wish, and you
    | may even configure multiple drivers of the same type. Defaults have
    | been setup for each driver as an example of the required options.
    |
    */
    'drivers' => [
        'local' => [
            'driver' => 'local',
            'root' => resolveUploadPath($_ENV['FILEUPLOAD_LOCAL_ROOT'] ?? 'public/uploads/media'),
            'url' => $_ENV['FILEUPLOAD_LOCAL_URL'] ?? '/uploads/media',
            'permissions' => [
                'file' => [
                    'public' => 0644,
                    'private' => 0600,
                ],
                'dir' => [
                    'public' => 0755,
                    'private' => 0700,
                ],
            ],
        ],

        's3' => [
            'driver' => 's3',
            'key' => $_ENV['AWS_ACCESS_KEY_ID'] ?? '',
            'secret' => $_ENV['AWS_SECRET_ACCESS_KEY'] ?? '',
            'region' => $_ENV['AWS_DEFAULT_REGION'] ?? 'us-east-1',
            'bucket' => $_ENV['AWS_BUCKET'] ?? '',
            'url' => $_ENV['AWS_URL'] ?? null,
            'endpoint' => $_ENV['AWS_ENDPOINT'] ?? null,
            'use_path_style_endpoint' => filter_var($_ENV['AWS_USE_PATH_STYLE_ENDPOINT'] ?? false, FILTER_VALIDATE_BOOLEAN),
            'visibility' => 'public',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | File Validation Rules
    |--------------------------------------------------------------------------
    |
    | Configure default validation rules for uploaded files.
    | These can be overridden at runtime when calling upload methods.
    |
    */
    'validation' => [
        'max_file_size' => (int)($_ENV['FILEUPLOAD_MAX_SIZE'] ?? 10485760), // 10MB default
        'allowed_mime_types' => array_filter(explode(',', $_ENV['FILEUPLOAD_ALLOWED_TYPES'] ?? 'image/jpeg,image/png,image/gif,image/webp,application/pdf')),
        'allowed_extensions' => array_filter(explode(',', $_ENV['FILEUPLOAD_ALLOWED_EXTENSIONS'] ?? 'jpg,jpeg,png,gif,webp,pdf')),
        'forbidden_extensions' => array_filter(explode(',', $_ENV['FILEUPLOAD_FORBIDDEN_EXTENSIONS'] ?? 'php,exe,bat,sh,js,html')),
    ],

    /*
    |--------------------------------------------------------------------------
    | File Processing Configuration
    |--------------------------------------------------------------------------
    |
    | Configure how uploaded files should be processed.
    | Image processing, compression, and thumbnail generation settings.
    |
    */
    'processing' => [
        'enabled' => filter_var($_ENV['FILEUPLOAD_PROCESSING_ENABLED'] ?? true, FILTER_VALIDATE_BOOLEAN),
        'driver' => $_ENV['FILEUPLOAD_PROCESSING_DRIVER'] ?? 'gd',
        'compress_images' => filter_var($_ENV['FILEUPLOAD_COMPRESS_IMAGES'] ?? true, FILTER_VALIDATE_BOOLEAN),
        'quality' => (int)($_ENV['FILEUPLOAD_IMAGE_QUALITY'] ?? 85),
        'strip_metadata' => filter_var($_ENV['FILEUPLOAD_STRIP_METADATA'] ?? true, FILTER_VALIDATE_BOOLEAN),

        'thumbnails' => [
            'enabled' => filter_var($_ENV['FILEUPLOAD_THUMBNAILS_ENABLED'] ?? false, FILTER_VALIDATE_BOOLEAN),
            'directory' => $_ENV['FILEUPLOAD_THUMBNAILS_DIRECTORY'] ?? 'thumbnails',
            'quality' => (int)($_ENV['FILEUPLOAD_THUMBNAIL_QUALITY'] ?? 80),
            'sizes' => parseThumbnailSizes(),
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Security Configuration
    |--------------------------------------------------------------------------
    |
    | Configure security-related settings for file uploads.
    |
    */
    'security' => [
        'scan_uploads' => filter_var($_ENV['FILEUPLOAD_SCAN_UPLOADS'] ?? true, FILTER_VALIDATE_BOOLEAN),
        'check_file_headers' => filter_var($_ENV['FILEUPLOAD_CHECK_HEADERS'] ?? true, FILTER_VALIDATE_BOOLEAN),
        'randomize_names' => filter_var($_ENV['FILEUPLOAD_RANDOMIZE_NAMES'] ?? false, FILTER_VALIDATE_BOOLEAN),
        'quarantine_suspicious' => filter_var($_ENV['FILEUPLOAD_QUARANTINE'] ?? false, FILTER_VALIDATE_BOOLEAN),
    ],

    /*
    |--------------------------------------------------------------------------
    | Logging Configuration
    |--------------------------------------------------------------------------
    |
    | Configure logging for file upload operations.
    |
    */
    'logging' => [
        'enabled' => filter_var($_ENV['FILEUPLOAD_LOGGING_ENABLED'] ?? true, FILTER_VALIDATE_BOOLEAN),
        'level' => $_ENV['FILEUPLOAD_LOG_LEVEL'] ?? 'info',
        'file' => $_ENV['FILEUPLOAD_LOG_FILE'] ?? null,
    ],

    /*
    |--------------------------------------------------------------------------
    | Performance Configuration
    |--------------------------------------------------------------------------
    |
    | Configure performance-related settings.
    |
    */
    'performance' => [
        'chunk_size' => (int)($_ENV['FILEUPLOAD_CHUNK_SIZE'] ?? 8192),
        'memory_limit' => $_ENV['FILEUPLOAD_MEMORY_LIMIT'] ?? '256M',
        'timeout' => (int)($_ENV['FILEUPLOAD_TIMEOUT'] ?? 300),
    ],
];
?>
