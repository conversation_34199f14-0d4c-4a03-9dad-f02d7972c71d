<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FileUpload Configuration Demo</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 1200px; margin: 0 auto; padding: 20px; }
        .demo-section { background: #f5f5f5; padding: 20px; margin: 20px 0; border-radius: 8px; }
        .config-display { background: #fff; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .upload-form { background: #e8f4fd; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .result { background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .error { background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0; }
        button { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; }
        button:hover { background: #0056b3; }
        select, input[type="file"] { padding: 8px; margin: 5px; }
        .code { background: #f8f9fa; padding: 10px; border-radius: 3px; font-family: monospace; }
    </style>
</head>
<body>
    <h1>🚀 FileUpload Package - Configuration Demo</h1>
    <p>This demo shows how the layered configuration system works with different upload scenarios.</p>

    <?php
    // Load environment variables
    if (file_exists('.env')) {
        $lines = file('.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
        foreach ($lines as $line) {
            if (strpos($line, '=') !== false && !str_starts_with($line, '#')) {
                list($key, $value) = explode('=', $line, 2);
                $_ENV[trim($key)] = trim($value);
            }
        }
    }

    // Configuration scenarios
    $scenarios = [
        'default' => [
            'name' => 'Default Configuration',
            'description' => 'Uses environment variables and config file',
            'config' => []
        ],
        'avatar' => [
            'name' => 'Avatar Upload',
            'description' => 'Small images only, high quality',
            'config' => [
                'validation' => [
                    'max_file_size' => 1048576, // 1MB
                    'allowed_extensions' => ['jpg', 'jpeg', 'png'],
                    'allowed_mime_types' => ['image/jpeg', 'image/png']
                ],
                'processing' => [
                    'enabled' => true,
                    'quality' => 90
                ]
            ]
        ],
        'document' => [
            'name' => 'Document Upload',
            'description' => 'Documents only, no image processing',
            'config' => [
                'validation' => [
                    'max_file_size' => 10485760, // 10MB
                    'allowed_extensions' => ['pdf', 'doc', 'docx', 'txt'],
                    'allowed_mime_types' => [
                        'application/pdf',
                        'application/msword',
                        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                        'text/plain'
                    ]
                ],
                'processing' => ['enabled' => false]
            ]
        ],
        'gallery' => [
            'name' => 'Gallery Upload',
            'description' => 'Large images with thumbnail generation',
            'config' => [
                'validation' => [
                    'max_file_size' => 20971520, // 20MB
                    'allowed_extensions' => ['jpg', 'jpeg', 'png', 'gif', 'webp'],
                    'allowed_mime_types' => ['image/jpeg', 'image/png', 'image/gif', 'image/webp']
                ],
                'processing' => [
                    'enabled' => true,
                    'quality' => 85,
                    'thumbnails' => ['enabled' => true]
                ]
            ]
        ]
    ];

    // Handle form submission
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['demo_file'])) {
        $selectedScenario = $_POST['scenario'] ?? 'default';
        $scenario = $scenarios[$selectedScenario];
        
        echo "<div class='demo-section'>";
        echo "<h3>📤 Upload Result for: {$scenario['name']}</h3>";
        
        try {
            // Load the configuration
            $config = include 'config/fileupload.php';
            
            // Apply runtime configuration if specified
            if (!empty($scenario['config'])) {
                $config = array_merge_recursive($config, $scenario['config']);
            }
            
            // Simulate upload validation
            $file = $_FILES['demo_file'];
            $fileSize = $file['size'];
            $fileName = $file['name'];
            $fileExt = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));
            $fileMime = $file['type'];
            
            // Check file size
            $maxSize = $config['validation']['max_file_size'] ?? 10485760;
            if ($fileSize > $maxSize) {
                throw new Exception("File size (" . formatBytes($fileSize) . ") exceeds maximum allowed size (" . formatBytes($maxSize) . ")");
            }
            
            // Check file extension
            $allowedExts = $config['validation']['allowed_extensions'] ?? [];
            if (!empty($allowedExts) && !in_array($fileExt, $allowedExts)) {
                throw new Exception("File extension '$fileExt' is not allowed. Allowed: " . implode(', ', $allowedExts));
            }
            
            // Check MIME type
            $allowedMimes = $config['validation']['allowed_mime_types'] ?? [];
            if (!empty($allowedMimes) && !in_array($fileMime, $allowedMimes)) {
                throw new Exception("File type '$fileMime' is not allowed.");
            }
            
            // Simulate successful upload
            $uploadPath = $config['drivers']['local']['root'] ?? 'uploads';
            $uploadUrl = $config['drivers']['local']['url'] ?? '/uploads';
            
            echo "<div class='result'>";
            echo "<h4>✅ Upload Successful!</h4>";
            echo "<p><strong>File:</strong> $fileName</p>";
            echo "<p><strong>Size:</strong> " . formatBytes($fileSize) . "</p>";
            echo "<p><strong>Type:</strong> $fileMime</p>";
            echo "<p><strong>Extension:</strong> $fileExt</p>";
            echo "<p><strong>Upload Path:</strong> $uploadPath</p>";
            echo "<p><strong>URL:</strong> $uploadUrl/$fileName</p>";
            
            if ($config['processing']['enabled'] ?? false) {
                echo "<p><strong>Processing:</strong> Enabled (Quality: " . ($config['processing']['quality'] ?? 85) . "%)</p>";
                if ($config['processing']['thumbnails']['enabled'] ?? false) {
                    echo "<p><strong>Thumbnails:</strong> Would be generated</p>";
                }
            } else {
                echo "<p><strong>Processing:</strong> Disabled</p>";
            }
            echo "</div>";
            
        } catch (Exception $e) {
            echo "<div class='error'>";
            echo "<h4>❌ Upload Failed!</h4>";
            echo "<p><strong>Error:</strong> " . $e->getMessage() . "</p>";
            echo "<p><strong>File:</strong> $fileName (" . formatBytes($fileSize) . ")</p>";
            echo "</div>";
        }
        
        echo "</div>";
    }

    function formatBytes($bytes, $precision = 2) {
        if ($bytes == 0) return '0 B';
        $units = array('B', 'KB', 'MB', 'GB', 'TB');
        for ($i = 0; $bytes > 1024; $i++) {
            $bytes /= 1024;
        }
        return round($bytes, $precision) . ' ' . $units[$i];
    }
    ?>

    <div class="demo-section">
        <h3>🎯 Configuration Scenarios</h3>
        <p>Each scenario demonstrates different configuration approaches:</p>
        
        <?php foreach ($scenarios as $key => $scenario): ?>
            <div class="config-display">
                <h4><?= $scenario['name'] ?></h4>
                <p><?= $scenario['description'] ?></p>
                
                <?php if (!empty($scenario['config'])): ?>
                    <div class="code">
                        <strong>Runtime Configuration:</strong><br>
                        <?php
                        if (isset($scenario['config']['validation']['max_file_size'])) {
                            echo "Max Size: " . formatBytes($scenario['config']['validation']['max_file_size']) . "<br>";
                        }
                        if (isset($scenario['config']['validation']['allowed_extensions'])) {
                            echo "Extensions: " . implode(', ', $scenario['config']['validation']['allowed_extensions']) . "<br>";
                        }
                        if (isset($scenario['config']['processing']['enabled'])) {
                            echo "Processing: " . ($scenario['config']['processing']['enabled'] ? 'Enabled' : 'Disabled') . "<br>";
                        }
                        if (isset($scenario['config']['processing']['quality'])) {
                            echo "Quality: " . $scenario['config']['processing']['quality'] . "%<br>";
                        }
                        ?>
                    </div>
                <?php else: ?>
                    <div class="code">
                        <strong>Uses:</strong> Environment variables + Config file<br>
                        Max Size: <?= formatBytes((int)($_ENV['FILEUPLOAD_MAX_SIZE'] ?? 10485760)) ?><br>
                        Extensions: <?= $_ENV['FILEUPLOAD_ALLOWED_EXTENSIONS'] ?? 'jpg,png,gif,pdf' ?><br>
                        Processing: <?= ($_ENV['FILEUPLOAD_PROCESSING_ENABLED'] ?? 'true') === 'true' ? 'Enabled' : 'Disabled' ?>
                    </div>
                <?php endif; ?>
            </div>
        <?php endforeach; ?>
    </div>

    <div class="demo-section">
        <h3>📤 Test File Upload</h3>
        <form method="POST" enctype="multipart/form-data" class="upload-form">
            <div>
                <label for="scenario">Choose Configuration Scenario:</label>
                <select name="scenario" id="scenario">
                    <?php foreach ($scenarios as $key => $scenario): ?>
                        <option value="<?= $key ?>"><?= $scenario['name'] ?></option>
                    <?php endforeach; ?>
                </select>
            </div>
            
            <div>
                <label for="demo_file">Select File to Upload:</label>
                <input type="file" name="demo_file" id="demo_file" required>
            </div>
            
            <div>
                <button type="submit">Test Upload</button>
            </div>
        </form>
        
        <p><strong>Note:</strong> This demo only validates the file against the configuration rules. 
        No actual file upload occurs - it's just testing the configuration system.</p>
    </div>

    <div class="demo-section">
        <h3>📋 Current Environment Configuration</h3>
        <div class="config-display">
            <div class="code">
                <strong>From .env file:</strong><br>
                FILEUPLOAD_LOCAL_ROOT: <?= $_ENV['FILEUPLOAD_LOCAL_ROOT'] ?? 'Not set' ?><br>
                FILEUPLOAD_MAX_SIZE: <?= formatBytes((int)($_ENV['FILEUPLOAD_MAX_SIZE'] ?? 0)) ?><br>
                FILEUPLOAD_ALLOWED_EXTENSIONS: <?= $_ENV['FILEUPLOAD_ALLOWED_EXTENSIONS'] ?? 'Not set' ?><br>
                FILEUPLOAD_PROCESSING_ENABLED: <?= $_ENV['FILEUPLOAD_PROCESSING_ENABLED'] ?? 'Not set' ?><br>
                FILEUPLOAD_IMAGE_QUALITY: <?= $_ENV['FILEUPLOAD_IMAGE_QUALITY'] ?? 'Not set' ?>%
            </div>
        </div>
    </div>

    <div class="demo-section">
        <h3>💡 Usage Examples</h3>
        <div class="code">
// 1. Default configuration (Environment + Config file)<br>
$uploader = FileUploadManager::create();<br>
$result = $uploader->upload($_FILES['file']);<br><br>

// 2. Runtime configuration override<br>
$uploader = FileUploadManager::createWithConfig([<br>
&nbsp;&nbsp;&nbsp;&nbsp;'validation' => ['max_file_size' => 1048576], // 1MB<br>
&nbsp;&nbsp;&nbsp;&nbsp;'processing' => ['quality' => 90]<br>
]);<br><br>

// 3. Specific validation rules<br>
$uploader = FileUploadManager::createWithValidation([<br>
&nbsp;&nbsp;&nbsp;&nbsp;'max_file_size' => 10485760, // 10MB<br>
&nbsp;&nbsp;&nbsp;&nbsp;'allowed_extensions' => ['pdf', 'doc', 'docx']<br>
]);<br><br>

// 4. Custom directory<br>
$uploader = FileUploadManager::createForDirectory(<br>
&nbsp;&nbsp;&nbsp;&nbsp;'public/gallery', '/gallery'<br>
);
        </div>
    </div>
</body>
</html>
