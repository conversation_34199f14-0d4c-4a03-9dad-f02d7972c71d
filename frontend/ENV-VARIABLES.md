# FileUpload Package - Environment Variables Reference

This document provides a complete reference of all environment variables available for the FileUpload package.

## 📋 Quick Reference

Copy `.env.example` to `.env` and configure according to your needs:

```bash
cp .env.example .env
```

## 🗂️ Variable Categories

### 1. Storage Driver Configuration
| Variable | Default | Description |
|----------|---------|-------------|
| `FILEUPLOAD_DRIVER` | `local` | Storage driver: local, s3, gcs, azure |

### 2. Local Storage Configuration
| Variable | Default | Description |
|----------|---------|-------------|
| `FILEUPLOAD_LOCAL_ROOT` | `uploads` | Root directory for local storage |
| `FILEUPLOAD_LOCAL_URL` | `/uploads` | Base URL for file access |
| `FILEUPLOAD_LOCAL_PERMISSIONS` | `0755` | Directory permissions (octal) |

### 3. File Validation Settings
| Variable | Default | Description |
|----------|---------|-------------|
| `FILEUPLOAD_MAX_SIZE` | `10485760` | Maximum file size in bytes (10MB) |
| `FILEUPLOAD_MIN_SIZE` | `0` | Minimum file size in bytes |
| `FILEUPLOAD_ALLOWED_TYPES` | `image/jpeg,image/png...` | Allowed MIME types |
| `FILEUPLOAD_ALLOWED_EXTENSIONS` | `jpg,jpeg,png...` | Allowed file extensions |
| `FILEUPLOAD_FORBIDDEN_EXTENSIONS` | `php,exe,bat...` | Forbidden extensions |
| `FILEUPLOAD_MAX_FILENAME_LENGTH` | `255` | Maximum filename length |

### 4. Image Processing Settings
| Variable | Default | Description |
|----------|---------|-------------|
| `FILEUPLOAD_PROCESSING_ENABLED` | `true` | Enable image processing |
| `FILEUPLOAD_IMAGE_QUALITY` | `85` | JPEG quality (1-100) |
| `FILEUPLOAD_MAX_IMAGE_WIDTH` | `4000` | Maximum image width |
| `FILEUPLOAD_MAX_IMAGE_HEIGHT` | `4000` | Maximum image height |
| `FILEUPLOAD_AUTO_ORIENT` | `true` | Auto-correct orientation |

### 5. Thumbnail Generation
| Variable | Default | Description |
|----------|---------|-------------|
| `FILEUPLOAD_THUMBNAILS_ENABLED` | `false` | Enable thumbnail generation |
| `FILEUPLOAD_THUMBNAILS_DIRECTORY` | `thumbnails` | Thumbnail directory name |
| `FILEUPLOAD_THUMBNAIL_SIZES` | `[JSON]` | Thumbnail size definitions |
| `FILEUPLOAD_THUMBNAIL_QUALITY` | `80` | Thumbnail quality (1-100) |

### 6. Security Settings
| Variable | Default | Description |
|----------|---------|-------------|
| `FILEUPLOAD_VALIDATE_HEADERS` | `true` | Validate file headers |
| `FILEUPLOAD_SANITIZE_FILENAMES` | `true` | Sanitize filenames |
| `FILEUPLOAD_VIRUS_SCAN` | `false` | Enable virus scanning |
| `FILEUPLOAD_CLAMAV_PATH` | `/usr/bin/clamscan` | ClamAV scanner path |
| `FILEUPLOAD_RATE_LIMITING` | `false` | Enable rate limiting |
| `FILEUPLOAD_RATE_LIMIT_PER_HOUR` | `100` | Max uploads per IP/hour |

### 7. Amazon S3 Configuration
| Variable | Default | Description |
|----------|---------|-------------|
| `FILEUPLOAD_S3_BUCKET` | - | S3 bucket name |
| `FILEUPLOAD_S3_REGION` | `us-east-1` | S3 region |
| `FILEUPLOAD_S3_KEY` | - | S3 access key ID |
| `FILEUPLOAD_S3_SECRET` | - | S3 secret access key |
| `FILEUPLOAD_S3_ENDPOINT` | - | Custom S3 endpoint |
| `FILEUPLOAD_S3_PREFIX` | `uploads` | S3 path prefix |
| `FILEUPLOAD_S3_VISIBILITY` | `public` | File visibility |
| `FILEUPLOAD_S3_URL` | - | Custom domain URL |

### 8. Google Cloud Storage Configuration
| Variable | Default | Description |
|----------|---------|-------------|
| `FILEUPLOAD_GCS_BUCKET` | - | GCS bucket name |
| `FILEUPLOAD_GCS_PROJECT_ID` | - | GCS project ID |
| `FILEUPLOAD_GCS_KEY_FILE` | - | Service account key file |
| `FILEUPLOAD_GCS_PREFIX` | `uploads` | GCS path prefix |
| `FILEUPLOAD_GCS_VISIBILITY` | `public` | File visibility |

### 9. Azure Blob Storage Configuration
| Variable | Default | Description |
|----------|---------|-------------|
| `FILEUPLOAD_AZURE_ACCOUNT` | - | Azure account name |
| `FILEUPLOAD_AZURE_KEY` | - | Azure account key |
| `FILEUPLOAD_AZURE_CONTAINER` | `uploads` | Azure container name |
| `FILEUPLOAD_AZURE_PREFIX` | - | Azure path prefix |
| `FILEUPLOAD_AZURE_VISIBILITY` | `public` | File visibility |

### 10. Dynamic Image Transformation
| Variable | Default | Description |
|----------|---------|-------------|
| `FILEUPLOAD_TRANSFORM_ENABLED` | `true` | Enable transformations |
| `FILEUPLOAD_TRANSFORM_DEFAULT_MODE` | `fit` | Default transform mode |
| `FILEUPLOAD_TRANSFORM_MAX_WIDTH` | `2000` | Max transform width |
| `FILEUPLOAD_TRANSFORM_MAX_HEIGHT` | `2000` | Max transform height |
| `FILEUPLOAD_TRANSFORM_CACHE_DURATION` | `********` | Cache duration (seconds) |
| `FILEUPLOAD_TRANSFORM_SIGN_URLS` | `false` | Enable URL signing |
| `FILEUPLOAD_TRANSFORM_SECRET_KEY` | - | URL signing secret |

### 11. Logging and Debugging
| Variable | Default | Description |
|----------|---------|-------------|
| `FILEUPLOAD_DEBUG` | `false` | Enable debug mode |
| `FILEUPLOAD_LOG_LEVEL` | `error` | Log level |
| `FILEUPLOAD_LOG_FILE` | `storage/logs/fileupload.log` | Log file path |
| `FILEUPLOAD_LOG_STATS` | `false` | Log upload statistics |

### 12. Performance Settings
| Variable | Default | Description |
|----------|---------|-------------|
| `FILEUPLOAD_CACHE_ENABLED` | `true` | Enable caching |
| `FILEUPLOAD_CACHE_DRIVER` | `file` | Cache driver |
| `FILEUPLOAD_CACHE_DURATION` | `3600` | Cache duration (seconds) |
| `FILEUPLOAD_GZIP_ENABLED` | `true` | Enable gzip compression |
| `FILEUPLOAD_MEMORY_LIMIT` | `256` | Memory limit (MB) |

### 13. CDN Configuration
| Variable | Default | Description |
|----------|---------|-------------|
| `FILEUPLOAD_CDN_URL` | - | CDN base URL |
| `FILEUPLOAD_CDN_ENABLED` | `false` | Enable CDN |

### 14. Webhook Configuration
| Variable | Default | Description |
|----------|---------|-------------|
| `FILEUPLOAD_WEBHOOKS_ENABLED` | `false` | Enable webhooks |
| `FILEUPLOAD_WEBHOOK_SUCCESS_URL` | - | Success webhook URL |
| `FILEUPLOAD_WEBHOOK_FAILURE_URL` | - | Failure webhook URL |
| `FILEUPLOAD_WEBHOOK_SECRET` | - | Webhook secret |

### 15. Folder Operations
| Variable | Default | Description |
|----------|---------|-------------|
| `FILEUPLOAD_FOLDERS_ENABLED` | `true` | Enable folder operations |
| `FILEUPLOAD_MAX_FOLDER_DEPTH` | `10` | Maximum folder depth |
| `FILEUPLOAD_FOLDER_PERMISSIONS` | `0755` | Folder permissions |
| `FILEUPLOAD_ALLOW_RECURSIVE_DELETE` | `true` | Allow recursive deletion |

### 16. Backup Configuration
| Variable | Default | Description |
|----------|---------|-------------|
| `FILEUPLOAD_BACKUP_ENABLED` | `false` | Enable backups |
| `FILEUPLOAD_BACKUP_DRIVER` | `local` | Backup storage driver |
| `FILEUPLOAD_BACKUP_PATH` | `backups` | Backup directory |
| `FILEUPLOAD_BACKUP_RETENTION_DAYS` | `30` | Backup retention period |

### 17. Custom Configuration
| Variable | Default | Description |
|----------|---------|-------------|
| `FILEUPLOAD_DIRECTORY_PATTERN` | `uploads/{year}/{month}` | Directory pattern |
| `FILEUPLOAD_GENERATE_UNIQUE_NAMES` | `true` | Generate unique names |
| `FILEUPLOAD_FILENAME_PATTERN` | `{timestamp}_{random}_{original}` | Filename pattern |

### 18. Integration Settings
| Variable | Default | Description |
|----------|---------|-------------|
| `FILEUPLOAD_DB_CONNECTION` | `mysql` | Database connection |
| `FILEUPLOAD_DB_TABLE` | `file_uploads` | Upload logs table |
| `FILEUPLOAD_DB_LOGGING` | `false` | Enable DB logging |

### 19. Development Settings
| Variable | Default | Description |
|----------|---------|-------------|
| `FILEUPLOAD_TEST_MODE` | `false` | Enable test mode |
| `FILEUPLOAD_TEST_DIRECTORY` | `tests/uploads` | Test directory |
| `FILEUPLOAD_DETAILED_ERRORS` | `false` | Detailed error reporting |

## 🚀 Quick Setup Examples

### Basic Local Setup
```env
FILEUPLOAD_DRIVER=local
FILEUPLOAD_LOCAL_ROOT=uploads
FILEUPLOAD_LOCAL_URL=/uploads
FILEUPLOAD_MAX_SIZE=10485760
FILEUPLOAD_PROCESSING_ENABLED=true
```

### Production S3 Setup
```env
FILEUPLOAD_DRIVER=s3
FILEUPLOAD_S3_BUCKET=my-app-uploads
FILEUPLOAD_S3_REGION=us-east-1
FILEUPLOAD_S3_KEY=AKIAIOSFODNN7EXAMPLE
FILEUPLOAD_S3_SECRET=wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY
FILEUPLOAD_S3_VISIBILITY=public
```

### High Security Setup
```env
FILEUPLOAD_VALIDATE_HEADERS=true
FILEUPLOAD_SANITIZE_FILENAMES=true
FILEUPLOAD_VIRUS_SCAN=true
FILEUPLOAD_RATE_LIMITING=true
FILEUPLOAD_RATE_LIMIT_PER_HOUR=50
FILEUPLOAD_FORBIDDEN_EXTENSIONS=php,exe,bat,sh,js,html,asp,jsp
```

### Performance Optimized Setup
```env
FILEUPLOAD_CACHE_ENABLED=true
FILEUPLOAD_CACHE_DRIVER=redis
FILEUPLOAD_GZIP_ENABLED=true
FILEUPLOAD_MEMORY_LIMIT=512
FILEUPLOAD_CDN_ENABLED=true
FILEUPLOAD_CDN_URL=https://cdn.example.com
```

## 📚 Additional Resources

- **Complete Configuration**: See `.env.example` for all variables with comments
- **Documentation**: Check `README.md` for usage examples
- **Step-by-Step Guide**: See `USAGE.md` for detailed setup instructions
- **Testing**: Use `demo-dependency-check.php` to verify configuration
- **Feature Testing**: Use `test-fileupload.php` to test all features
