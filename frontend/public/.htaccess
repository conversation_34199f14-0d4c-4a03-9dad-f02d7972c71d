RewriteEngine On

# Handle Authorization Header
RewriteCond %{HTTP:Authorization} .
RewriteRule .* - [E=HTTP_AUTHORIZATION:%{HTTP:Authorization}]

# Dynamic image transformation - Universal N-level nested directory support
# Handle ANY number of nested directories with intelligent rules
# Supports both explicit mode and default mode (fit):
# - /uploads/P/h/P/300x300/fit=file.png (explicit mode)
# - /uploads/P/h/P/300x300/file.png (default to fit mode)

# Rule 1: Handle media folder with explicit mode
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^uploads/media/(.+)/(\d+)x(\d+)/([a-z]+)=(.+)$ transform.php?path=$1/$5&width=$2&height=$3&mode=$4 [L,QSA]

# Rule 2: Handle media folder with default mode (fit)
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^uploads/media/(.+)/(\d+)x(\d+)/(.+)$ transform.php?path=$1/$4&width=$2&height=$3&mode=fit [L,QSA]

# Serve static assets directly
RewriteCond %{REQUEST_FILENAME} -f
RewriteCond %{REQUEST_URI} \.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ [NC]
RewriteRule ^(.*)$ - [L]

# Redirect Trailing Slashes If Not A Folder...
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_URI} (.+)/$
RewriteRule ^ %1 [L,R=301]

# Send Requests To Front Controller...
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_FILENAME} !-f
RewriteRule ^ index.php [L]

# Handle Authorization Header
SetEnvIf Authorization "(.*)" HTTP_AUTHORIZATION=$1

# Security Headers
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
</IfModule>

# Prevent access to sensitive files
<Files ".env">
    Order allow,deny
    Deny from all
</Files>

<Files "composer.json">
    Order allow,deny
    Deny from all
</Files>

<Files "composer.lock">
    Order allow,deny
    Deny from all
</Files> 