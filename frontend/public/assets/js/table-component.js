let TableGridManager = (function () {
    'use strict';

    class TableGridManager {
        constructor(config) {
            this.config = {
                tableId: '',
                gridEnabled: false,
                defaultView: 'table',
                clientSideSearch: true,
                searchId: '',
                ajaxUrl: '',
                currentData: [],
                ...config
            };

            this.currentLimit = null;
            this.currentPage = 1;
            this.currentSort = {};

            this.init();
        }

        init() {
            const perPageSelect = document.getElementById('perPageSelect');
            if (perPageSelect) {
                this.currentLimit = parseInt(perPageSelect.value);
            }

            const defaultView = localStorage.getItem(`preferredView_${window.location.pathname}`) || this.config.defaultView

            this.setupEventListeners();
            this.loadPreferredView(defaultView);
            this.setupAjaxHandlers();
        }
    
        setupEventListeners() {
            const viewToggles = document.querySelectorAll('.view-toggle');
            let activeToggle = null;

            const toggleHandler = (e) => {
                e.preventDefault();
                const clickedBtn = e.currentTarget;
                
                // If this button is already active, do nothing
                if (clickedBtn === activeToggle) return;
                
                // Remove active class from all buttons
                viewToggles.forEach(btn => {
                    btn.classList.remove('active', 'bg-gray-200');
                });
                
                // Add active class to clicked button
                clickedBtn.classList.add('active', 'bg-gray-200');
                
                // Update active toggle
                activeToggle = clickedBtn;
                
                // Load the selected view
                this.loadPreferredView(clickedBtn.dataset.view);
            };

            // Initialize event listeners
            viewToggles.forEach(btn => {
                btn.addEventListener('click', toggleHandler);
                
                // Set initial active button based on default view
                if (btn.dataset.view === (localStorage.getItem(`preferredView_${window.location.pathname}`) || this.config.defaultView)) {
                    btn.classList.add('active', 'bg-gray-200');
                    activeToggle = btn;
                }
            });

            const perPageSelect = document.getElementById('perPageSelect');
            if (perPageSelect) {
                perPageSelect.addEventListener('change', (e) => {
                    this.currentLimit = parseInt(e.target.value);
                    this.currentPage = 1;
                    this.loadData({ limit: this.currentLimit, page: 1 });
                });
            }

            this.setupSearchHandler();

            const handlePaginationClick = (e) => {
                const previousBtn = e.target.closest('.previous-pagination');
                const nextBtn = e.target.closest('.next-pagination');

                if (!previousBtn && !nextBtn) return;

                e.preventDefault();
                const url = (previousBtn || nextBtn).getAttribute('data-url');

                if (url && url !== 'javascript:void(0)') {
                    try {
                        const urlObj = new URL(url, window.location.origin);
                        const page = urlObj.searchParams.get('page');
                        if (page) {
                            this.currentPage = parseInt(page);
                            this.loadData({ page: this.currentPage });
                        }
                    } catch (error) {
                        console.error('Error parsing pagination URL:', error);
                    }
                }
            };

            document.removeEventListener('click', this._paginationClickHandler);
            this._paginationClickHandler = handlePaginationClick.bind(this);
            document.addEventListener('click', this._paginationClickHandler);
        }

        setupAjaxHandlers() {
            document.querySelectorAll('[data-sort]').forEach(header => {
                header.addEventListener('click', () => {
                    const field = header.dataset.sort;
                    const currentDirection = this.currentSort.field === field ? this.currentSort.direction : 'asc';
                    const newDirection = currentDirection === 'asc' ? 'desc' : 'asc';

                    this.currentSort = { field, direction: newDirection };
                    const sortParam = newDirection === 'desc' ? `-${field}` : field;

                    this.loadData({ sort: sortParam });
                });
            });

            document.addEventListener('click', (e) => {
                if (e.target.closest('[data-delete]')) {
                    const id = e.target.closest('[data-delete]').dataset.delete;
                    this.deleteItem(id);
                }

                if (e.target.closest('[data-preview]')) {
                    const id = e.target.closest('[data-preview]').dataset.preview;
                    this.previewItem(id);
                }
            });
        }

        setupSearchHandler() {
            if (!this.config.clientSideSearch) return;

            const searchInput = document.getElementById(this.config.searchId);
            if (!searchInput) return;

            const performSearch = this.debounce((searchValue) => {
                const tableView = document.querySelector('.table-view');
                const gridView = document.querySelector('.grid-view');
                const isTableView = tableView && !tableView.classList.contains('hidden');
                let visibleCount = 0;

                if (isTableView && tableView) {
                    const rows = tableView.querySelectorAll('tbody tr.searchable-row');
                    rows.forEach(row => {
                        const text = row.textContent.toLowerCase();
                        const isVisible = searchValue === '' || text.includes(searchValue);
                        row.style.display = isVisible ? '' : 'none';
                        if (isVisible) visibleCount++;
                    });
                }

                if (!isTableView && gridView) {
                    const cards = gridView.querySelectorAll('.grid-item:not(.col-span-full)');
                    cards.forEach(card => {
                        const text = card.textContent.toLowerCase();
                        const isVisible = searchValue === '' || text.includes(searchValue);
                        card.style.display = isVisible ? '' : 'none';
                        if (isVisible) visibleCount++;
                    });
                }

                const existingMsg = document.querySelector('.no-search-results');
                if (searchValue !== '' && visibleCount === 0) {
                    if (!existingMsg) {
                        const msg = document.createElement('div');
                        msg.className = 'no-search-results col-span-full text-center text-gray-500 py-8';
                        msg.innerText = 'No results found for your search.';

                        if (isTableView && tableView) {
                            const tbody = tableView.querySelector('tbody');
                            const tr = document.createElement('tr');
                            const td = document.createElement('td');
                            td.colSpan = tbody.querySelector('tr')?.cells.length || 1;
                            td.className = 'px-6 py-8 text-center text-gray-500';
                            td.textContent = msg.innerText;
                            tr.appendChild(td);
                            tr.className = 'no-search-results';
                            tbody.appendChild(tr);
                        } else if (gridView) {
                            gridView.appendChild(msg);
                        }
                    }
                } else if (existingMsg) {
                    existingMsg.remove();
                }

            }, 300);

            searchInput.addEventListener('input', function () {
                performSearch(this.value.toLowerCase().trim());
            });

            searchInput.addEventListener('keydown', function (e) {
                if (e.key === 'Escape') {
                    this.value = '';
                    performSearch('');
                }
            });

            if (searchInput.value.trim() !== '') {
                performSearch(searchInput.value.toLowerCase().trim());
            }
        }

        async loadData(params = {}) {
            try {
                const url = new URL(baseUrl + this.config.ajaxUrl);
                const searchParams = new URLSearchParams();

                const requestParams = {
                    page: this.currentPage,
                    limit: this.currentLimit,
                    ...this.currentSort,
                    ...params
                };

                Object.entries(requestParams).forEach(([key, value]) => {
                    if (value !== undefined && value !== null) {
                        searchParams.append(key, value);
                    }
                });

                url.search = searchParams.toString();

                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'Accept': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    }
                });

                if (!response.ok) throw new Error('Network response was not ok');

                const data = await response.json();

                if (!data.success) {
                    this.showToast(data.message, 'error');
                    return;
                }
                this.config.currentData = data.pagination.data;
                this.updateUI(data);
            } catch (error) {
                console.error('Error loading data:', error);
            }
        }

        updateUI(data) {
            const container = document.querySelector('.dataRender');

            if (!container) {
                console.error('Container element not found');
                return;
            }

            const scrollPosition = window.scrollY;
            container.innerHTML = '';

            try {
                container.innerHTML = data.html || '';

                const pagination = document.querySelector('.pagination');
                if (pagination && data.pagination) {
                    pagination.innerHTML = data.pagination;
                }

                this.initializeComponents();
                this.initializeLayoutUI();

                window.scrollTo(0, scrollPosition);
            } catch (error) {
                console.error('Error updating UI:', error);
            }
        }

        initializeComponents() {
            this.setupEventListeners();
            this.setupAjaxHandlers();

            if (typeof bootstrap !== 'undefined') {
                const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
                tooltipTriggerList.map(el => new bootstrap.Tooltip(el));

                const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
                popoverTriggerList.map(el => new bootstrap.Popover(el));
            }
        }

        initializeLayoutUI() {
            if (window.lucide && typeof lucide.createIcons === 'function') {
                lucide.createIcons();
            }

            if (typeof window.initFlowbite === 'function') {
                // alert('Hello');
                window.initFlowbite();
            }
        }

        showToast(message, type = 'info') {
            const toast = document.createElement('div');
            toast.className = `toast toast-${type}`;
            toast.textContent = message;
            
            const container = document.getElementById('toast-container') || document.body;
            container.appendChild(toast);
            
            setTimeout(() => {
                toast.remove();
            }, 5000);
        }

        loadPreferredView(view) {
            this.loadData({ view: view, page: 1 });
            this.saveViewPreference(view);
        }

        saveViewPreference(view) {
            try {
                localStorage.setItem(`preferredView_${window.location.pathname}`, view);
                document.cookie = `preferredView=${encodeURIComponent(view)}; path=/`;
            } catch (e) {
                console.error('Error saving view preference:', e);
            }
        }

        debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }
    }

    return TableGridManager;
})();

if (typeof window !== 'undefined') {
    window.TableGridManager = TableGridManager;
}
