class AjaxHandler {
    constructor() {
        this.csrfToken = document.querySelector('meta[name="csrf-token"]')?.content;
        this.initializeForms();
    }

    initializeForms() {
        // Handle all forms with data-ajax-form attribute
        document.querySelectorAll('[data-ajax-form]').forEach(form => {
            // Store initial form values
            this.storeInitialValues(form);
            // Initialize form state
            this.updateSubmitButtonState(form);
        });


        // Handle form submission
        document.addEventListener('submit', async (e) => {
            const form = e.target.closest('[data-ajax-form]');
            if (!form) return;
            
            e.preventDefault();
            e.stopPropagation();
            form.setAttribute('novalidate', '');
            
            // Validate form before submission
            if (!this.validateForm(form)) {
                return;
            }
            
            await this.handleFormSubmit(form);
        });

        // Handle input changes
        document.addEventListener('input', (e) => {
            const input = e.target;
            const form = input.closest('[data-ajax-form]');
            
            if (form) {
                this.clearFieldValidation(input);
                this.updateSubmitButtonState(form);
                
                // Validate required fields on blur
                if (input.hasAttribute('required') && !input.value.trim()) {
                    this.showFieldError(input, 'This field is required');
                }
            }
        });
    }


    storeInitialValues(form) {
        const formData = new FormData(form);
        const initialValues = {};
        
        for (let [key] of formData.entries()) {
            const input = form.querySelector(`[name="${key}"]`);
            if (input) {
                initialValues[key] = input.value;
            }
        }
        
        form.dataset.initialValues = JSON.stringify(initialValues);
    }


    hasFormChanged(form) {
        if (!form.dataset.initialValues) return true;
        
        const initialValues = JSON.parse(form.dataset.initialValues);
        const formData = new FormData(form);
        
        for (let [key, value] of formData.entries()) {
            const input = form.querySelector(`[name="${key}"]`);
            if (input && input.type !== 'file' && initialValues[key] !== value) {
                return true;
            }
        }
        
        return false;
    }


    updateSubmitButtonState(form) {
        const submitButton = form.querySelector('[type="submit"]');
        if (!submitButton) return;
        
        // Always enable the button if it's a delete action
        if (form.action.includes('delete')) {
            submitButton.disabled = false;
            return;
        }
        
        const hasChanged = this.hasFormChanged(form);
        const isValid = this.isFormValid(form);
        
        submitButton.disabled = !hasChanged || !isValid;
    }


    isFormValid(form) {
        let isValid = true;
        const requiredFields = form.querySelectorAll('[required]');
        
        requiredFields.forEach(field => {
            if (!field.value.trim()) {
                isValid = false;
            }
        });
        
        return isValid;
    }


    validateForm(form) {
        let isValid = true;
        const requiredFields = form.querySelectorAll('[required]');
        
        this.clearFormErrors(form);
        
        requiredFields.forEach(field => {
            if (!field.value.trim()) {
                this.showFieldError(field, 'This field is required');
                isValid = false;
            }
            
            if (field.type === 'email' && field.value) {
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (!emailRegex.test(field.value)) {
                    this.showFieldError(field, 'Please enter a valid email address');
                    isValid = false;
                }
            }
            
            if (field.minLength > 0 && field.value.length < field.minLength) {
                this.showFieldError(field, `Minimum length is ${field.minLength} characters`);
                isValid = false;
            }
            
            if (field.maxLength > 0 && field.value.length > field.maxLength) {
                this.showFieldError(field, `Maximum length is ${field.maxLength} characters`);
                isValid = false;
            }
        });
        
        this.updateSubmitButtonState(form);
        return isValid;
    }


    async handleFormSubmit(form) {
        const submitButton = form.querySelector('[type="submit"]');
        const formData = new FormData(form);

        const url = form.getAttribute('action') || window.location.href;
        const method = form.getAttribute('method')?.toUpperCase() || 'POST';

        if (this.csrfToken && !formData.has('_token')) {
            formData.append('_token', this.csrfToken);
        }

        try {
            this.setButtonLoading(submitButton, true);
            submitButton.disabled = true;

            const response = await fetch(url, {
                method: method,
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Accept': 'application/json'
                },
                credentials: 'same-origin'
            });

            const data = await response.json();

            if (!response.ok) {
                // Handle validation errors
                if (response.status === 422 && data.errors) {
                    this.displayFormErrors(form, data.errors, data.data);
                    throw new Error('Please fix the validation errors');
                }
                // Handle other server errors
                throw new Error(data.message || 'An error occurred');
            }

            this.showToast(data.message || 'Operation successful', 'success');

            // Update initial values after successful submission
            this.storeInitialValues(form);
            this.updateSubmitButtonState(form);

            if (data.redirect) {
                window.location.href = data.redirect;
                return;
            }

            const callback = form.dataset.callback;
            if (callback && typeof window[callback] === 'function') {
                window[callback](data);
            }

            if (form.hasAttribute('data-reset-on-success')) {

                const closeButtons = document.querySelectorAll('.modelClose');
                if (closeButtons.length) {
                    closeButtons.forEach(button => {
                        button.click();
                    });
                    
                    // Dispatch a custom event after a short delay to ensure modal is closed
                    setTimeout(() => {
                        const tableId = form.dataset.tableId;
                        if (tableId) {
                            const table = document.getElementById(tableId);
                            if (table) {
                                // Check if table already has a TableGridManager instance
                                if (!table._tableGridManager) {
                                    // Initialize only if not already initialized
                                    table._tableGridManager = new window.TableGridManager({ 
                                        tableId: tableId,
                                        gridEnabled: false,
                                        defaultView: 'table',
                                        clientSideSearch: true,
                                        searchId: '',
                                        ajaxUrl: form.dataset.ajaxUrl
                                    });
                                }
                            }
                        }
                    }, 100);
                }

                form.reset();
                this.clearFormErrors(form);
                this.storeInitialValues(form);

                const modal = form.closest('.modal');
                if (modal) {
                    modal.classList.remove('show');
                    modal.setAttribute('aria-hidden', 'true');
                    modal.removeAttribute('style');
                    document.body.classList.remove('modal-open'); 
                }
            }

        } catch (error) {
            this.showToast(error.message || 'An error occurred', 'error');
            
            if (error.errors) {
                this.displayFormErrors(form, error.errors);
            }
        } finally {
            this.setButtonLoading(submitButton, false);
            this.updateSubmitButtonState(form);
        }
    }


    // ... (keep the existing methods below as they are)
    clearFieldValidation(field) {
        field.classList.remove('is-invalid');
        const errorElement = field.parentNode.querySelector('.invalid-feedback');
        if (errorElement) {
            errorElement.remove();
        }
    }
    
    clearFormErrors(form) {
        form.querySelectorAll('.is-invalid').forEach(el => el.classList.remove('is-invalid'));
        form.querySelectorAll('.invalid-feedback').forEach(el => el.remove());
    }
    
    showFieldError(field, message) {
        field.classList.add('is-invalid');
        const errorDiv = document.createElement('div');
        errorDiv.className = 'invalid-feedback';
        errorDiv.textContent = message;
        field.parentNode.insertBefore(errorDiv, field.nextSibling);
        
        if (field.scrollIntoView) {
            field.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
    }

    setButtonLoading(button, isLoading) {
        if (!button) return;
        
        if (isLoading) {
            button.setAttribute('data-original-text', button.innerHTML);
            button.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Loading...';
            button.disabled = true;
        } else {
            const originalText = button.getAttribute('data-original-text');
            if (originalText) {
                button.innerHTML = originalText;
                button.removeAttribute('data-original-text');
            }
            button.disabled = !this.hasFormChanged(button.closest('form'));
        }
    }

    showToast(message, type = 'info') {
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        toast.textContent = message;
        
        const container = document.getElementById('toast-container') || document.body;
        container.appendChild(toast);
        
        setTimeout(() => {
            toast.remove();
        }, 5000);
    }

    displayFormErrors(form, errors, data = {}) {
        this.clearFormErrors(form);
    
        // Track which fields have been processed to prevent duplicates
        const processedFields = new Set();
    
        // First, process errors from the data object if it exists
        if (data && typeof data === 'object' && !Array.isArray(data)) {
            Object.entries(data).forEach(([field, messages]) => {
                if (processedFields.has(field)) return;
                
                const input = form.querySelector(`[name="${field}"]`);
                if (input) {
                    const message = Array.isArray(messages) ? messages[0] : messages;
                    this.showFieldError(input, message);
                    processedFields.add(field);
                    
                    if (!form.querySelector('.is-invalid:focus')) {
                        input.focus();
                    }
                }
            });
        }
    
        // Then process errors from the errors object if it's an object
        if (errors && typeof errors === 'object' && !Array.isArray(errors)) {
            Object.entries(errors).forEach(([field, message]) => {
                // Skip if we've already processed this field
                if (processedFields.has(field)) return;
                
                const input = form.querySelector(`[name="${field}"]`);
                if (input) {
                    const errorMessage = Array.isArray(message) ? message[0] : message;
                    this.showFieldError(input, errorMessage);
                    processedFields.add(field);
                    
                    if (!form.querySelector('.is-invalid:focus')) {
                        input.focus();
                    }
                }
            });
        }
    
        // If we have a simple string error and no fields were processed
        if (processedFields.size === 0 && typeof errors === 'string') {
            const firstInput = form.querySelector('input, select, textarea');
            if (firstInput) {
                this.showFieldError(firstInput, errors);
                firstInput.focus();
            } else {
                this.showToast(errors, 'error');
            }
        }
        
        this.updateSubmitButtonState(form);
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.ajaxHandler = new AjaxHandler();
});