/* Form Validation Styles */
.invalid-feedback {
    display: block;
    margin-top: 0.25rem;
    font-size: 0.875rem;
    color: #dc2626; /* Red-600 */
}

.is-invalid {
    border-color: #dc2626 !important; /* Red-600 */
    padding-right: calc(1.5em + 0.75rem);
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc2626'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc2626' stroke='none'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

/* Disabled State */
:disabled,
[disabled],
[disabled] * {
    cursor: not-allowed !important;
    opacity: 0.7 !important;
}

/* Button States */
button:disabled,
button[disabled],
input[type="submit"]:disabled,
input[type="button"]:disabled,
input[type="reset"]:disabled {
    background-color: #e5e7eb !important; /* Gray-200 */
    border-color: #d1d5db !important; /* Gray-300 */
    color: #9ca3af !important; /* Gray-400 */
    box-shadow: none !important;
}

/* Form Input Focus State */
input:not([disabled]):not([readonly]):not(.is-invalid):focus,
select:not([disabled]):not([readonly]):not(.is-invalid):focus,
textarea:not([disabled]):not([readonly]):not(.is-invalid):focus,
[contenteditable]:not([disabled]):not([readonly]):not(.is-invalid):focus {
    border-color: #3b82f6; /* Blue-500 */
    box-shadow: 0 0 0 0.2rem rgba(59, 130, 246, 0.25);
    outline: 0;
}

/* Loading State */
.btn-loading {
    position: relative;
    color: transparent !important;
    pointer-events: none;
}

.btn-loading::after {
    content: '';
    position: absolute;
    width: 1rem;
    height: 1rem;
    top: 50%;
    left: 50%;
    margin: -0.5rem 0 0 -0.5rem;
    border: 2px solid #ffffff;
    border-right-color: transparent;
    border-radius: 50%;
    animation: button-spinner 0.75s linear infinite;
}

@keyframes button-spinner {
    to {
        transform: rotate(360deg);
    }
}

/* Toast Notifications */
.toast {
    position: fixed;
    top: 1rem;
    right: 1rem;
    padding: 0.75rem 1.25rem;
    border-radius: 0.375rem;
    color: white;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    z-index: 50;
    max-width: 24rem;
    animation: slideInRight 0.3s ease-out;
}

.toast-success {
    background-color: #10b981; /* Emerald-500 */
}

.toast-error {
    background-color: #ef4444; /* Red-500 */
}

.toast-info {
    background-color: #3b82f6; /* Blue-500 */
}

.toast-warning {
    background-color: #f59e0b; /* Amber-500 */
}