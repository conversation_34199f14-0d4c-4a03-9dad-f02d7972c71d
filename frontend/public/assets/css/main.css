
@keyframes slideInRight {
    from { transform: translateX(100%); opacity: 0; }

    to { transform: translateX(0); opacity: 1; }

}

@keyframes slideOutRight {
    from { transform: translateX(0); opacity: 1; }
    to { transform: translateX(100%); opacity: 0; } }

#successMessage { animation: slideInRight 0.5s ease-out; }
#successMessage.hide { animation: slideOutRight 0.5s ease-in forwards; }

/* step-content */
.step-content{ height: calc(100vh - 180px); overflow-y: auto; }
.tab-content{ height: calc(100vh - 380px); overflow-y: auto;}

/* CodeMirror Custom Styling */
.CodeMirror { height: auto !important; min-height: 300px; font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace; font-size: 14px; line-height: 1.6; padding: 10px; background: transparent !important; visibility: visible !important; }
.CodeMirror-gutters { background: transparent !important; border-right: 1px solid #e5e7eb !important; }
.CodeMirror-linenumber { color: #9ca3af !important; }

/* Add some hover effects for the toolbar buttons */
.editor-toolbar button:hover { background-color: #f3f4f6; color: #2563eb; }

/* Custom scrollbar */
.CodeMirror-vscrollbar::-webkit-scrollbar { width: 8px; }
.CodeMirror-vscrollbar::-webkit-scrollbar-track { background: #f1f1f1; }
.CodeMirror-vscrollbar::-webkit-scrollbar-thumb { background: #d1d5db; border-radius: 4px; }
.CodeMirror-vscrollbar::-webkit-scrollbar-thumb:hover { background: #9ca3af; }

.CodeMirror-wrap[data-type="HTML"] .CodeMirror-code > div:nth-child(1) pre.CodeMirror-line span[cm-text=""]:before,
.CodeMirror-wrap[data-type="JSON"] .CodeMirror-code > div:nth-child(1) pre.CodeMirror-line span[cm-text=""]:before,
.CodeMirror-wrap[data-type="CSS"] .CodeMirror-code > div:nth-child(1) pre.CodeMirror-line span[cm-text=""]:before,
.CodeMirror-wrap[data-type="JS"] .CodeMirror-code > div:nth-child(1) pre.CodeMirror-line span[cm-text=""]:before{ position: absolute; left: 10px; top: 2px; color: #999; pointer-events: none; font-family: inherit; }
.CodeMirror-wrap[data-type="HTML"] .CodeMirror-code > div:nth-child(1) pre.CodeMirror-line span[cm-text=""]:before{ content: '<!-- Enter your Template code here -->'; }
.CodeMirror-wrap[data-type="JSON"] .CodeMirror-code > div:nth-child(1) pre.CodeMirror-line span[cm-text=""]:before{ content: '{"Enter your JSON code here"}'; }
.CodeMirror-wrap[data-type="CSS"] .CodeMirror-code > div:nth-child(1) pre.CodeMirror-line span[cm-text=""]:before{ content: '/* Enter your CSS code here */'; }
.CodeMirror-wrap[data-type="JS"] .CodeMirror-code > div:nth-child(1) pre.CodeMirror-line span[cm-text=""]:before{ content: '/* Enter your JavaScript code here */'; }

/* Add these styles to your existing CSS */
.preview-container { height: 100%; min-height: 500px; width: 100%; position: relative; }
.preview-frame { width: 100% !important; height: 100% !important; border: none; display: block; }

select.select2 { opacity: 0 !important; }
.select2-container--default .select2-selection--multiple { border-color: #d1d5db !important; border-radius: 0.5rem !important; min-height: 38px !important; padding: 0 8px; }
.select2-container--default .select2-selection--multiple .select2-selection__choice { background-color: #EEF4FF !important; color: #0C5BE2 !important; border: none !important; border-radius: 999px !important; padding: 2px 8px !important; margin: 4px 4px 4px 0 !important; display: inline-flex !important; align-items: center !important; font-size: 0.875rem !important; }
.select2-container--default .select2-selection--multiple .select2-selection__choice__remove { color: #0C5BE2 !important; margin-right: 4px !important; border-right: none !important; padding: 0 2px !important; font-size: 14px !important; border-radius: 999px !important; display: inline-block !important; align-items: center !important; justify-content: center !important; width: 16px !important; height: 16px !important; left: 4px !important }
.select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover { background-color: transparent !important; color: #0C5BE2 !important; }
.select2-container--default .select2-search--inline .select2-search__field { margin-top: 4px !important; padding-left: 4px !important; }
.select2-dropdown { border-color: #d1d5db !important; border-radius: 0.5rem !important; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1) !important; margin-top: 2px !important; }
.select2-container--default .select2-results__option--highlighted[aria-selected] { background-color: #f3f4f6 !important; color: #111827 !important; }
.select2-container--default .select2-results__option[aria-selected=true] { background-color: #e5e7eb !important; }
.select2-container--default.select2-container--focus .select2-selection--multiple { border-color: #0C5BE2 !important; box-shadow: 0 0 0 1px rgba(12, 91, 226, 0.2) !important; }

.select2-container--default .select2-selection--multiple .select2-selection__choice__display { padding-left: 14px !important; }

/* Custom Radio Button Styling */
.radio-button-group { display: flex; gap: 1rem; }
.radio-button-group input[type="radio"] { display: none; }
.radio-button-group label { display: inline-flex; align-items: center; padding: 0.5rem 1rem; border: 2px solid #E5E7EB; border-radius: 0.5rem; cursor: pointer; font-size: 0.875rem; color: #374151; transition: all 0.2s; }
.radio-button-group input[type="radio"]:checked + label { background-color: #EEF4FF; border-color: #0C5BE2; color: #0C5BE2; }
.radio-button-group label:hover { border-color: #0C5BE2; }

.detailedDescription ul { list-style-type: none; padding: 0; margin: 0; }
.detailedDescription li { display: flex; padding: 1rem; margin-bottom: 0.5rem; border-radius: 0.5rem; background-color: #f9fafb; font-size: 0.875rem; color: #6b7280; position: relative; padding-left: 2.5rem; flex-direction: column; }
.detailedDescription li ::before { content: ""; position: absolute; left: 1rem; top: 1.125rem; width: 1.25rem; height: 1.25rem; background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='20' height='20' fill='none' stroke='%2310b981' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' viewBox='0 0 24 24'%3E%3Cpath d='M5 13l4 4L19 7'%3E%3C/path%3E%3C/svg%3E"); background-position: center; background-repeat: no-repeat; background-size: contain; }
.detailedDescription li strong { display: block; font-size: 0.875rem; font-weight: 500; color: #111827; margin-bottom: 0; }

.ck-rounded-corners .ck.ck-editor__top .ck-sticky-panel .ck-toolbar, .ck.ck-editor__top .ck-sticky-panel .ck-toolbar.ck-rounded-corners { border-radius: 0.5rem !important; border-bottom-left-radius: 0 !important;border-bottom-right-radius: 0 !important; }
.ck.ck-editor__main>.ck-editor__editable:not(.ck-focused){ border-radius: 0  0 0.5rem 0.5rem !important; font-size: 14px !important; line-height: 1.6 !important; }

/* View Toggle */
/* .view-toggle.active{ color:#0C5BE2 !important; } */

.grid-view{ display: grid; }
.grid-view.grid-view-hidden { display: none; opacity: 0 !important; }
.table-view.table-view-hidden { display: none; opacity: 0 !important; }

.view-toggle { position: relative; background: white; border: 1px solid #e5e7eb; transition: all 0.2s ease-in-out; }
.view-toggle:hover { background: #f9fafb; border-color: #d1d5db; }
.view-toggle.active { color: #fff; }
.view-toggle.active:hover { color: #fff; }
.view-toggle.active svg, .view-toggle.active i { color: #fff !important; }
.view-toggle svg, .view-toggle i { color: #6b7280; transition: color 0.2s ease-in-out; }
.view-toggle:hover svg, .view-toggle:hover i { color: #374151; }
.view-toggle:first-child { border-top-left-radius: 0.5rem; border-bottom-left-radius: 0.5rem; }
.view-toggle:last-child { border-top-right-radius: 0.5rem; border-bottom-right-radius: 0.5rem; }
.view-toggle + .view-toggle { border-left: none; }
.view-toggle:focus { outline: none; box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5); }

#previewContent{ height: calc(100vh - 434px); }
#previewFrame{ transition: width 0.3s ease, max-width 0.3s ease, margin 0.3s ease; height: calc(100vh - 434px);}
.editor-fullscreen #previewFrame{ height: calc(100vh - 138px); }
.responsive-btn.active { color: #2563eb; }

/* Page Block */
.block-wrapper:hover .block-controls { display: flex; }


.hidden {
    display: none !important;
}

.grid-view, .table-view {
    transition: opacity 0.3s ease;
}

