<?php

declare(strict_types=1);

// Load Composer's autoloader
require_once __DIR__ . '/../vendor/autoload.php';

use App\Logging\Logger;
use Core\ErrorHandler;
use Core\Router;
use Core\Request;
use Dotenv\Dotenv;

// Load environment variables
$dotenv = Dotenv::createImmutable(__DIR__ . '/../');
$dotenv->load();

// Load helpers before config
require_once __DIR__ . '/../config/helpers.php';

// Debug: Check config structure
// Load application configuration
if (function_exists('config')) {
    $appConfig = require __DIR__ . '/../config/app.php';
}

// Set error reporting
if (config('debug')) {
    error_reporting(E_ALL);
    ini_set('display_errors', '1');
} else {
    error_reporting(0);
    ini_set('display_errors', '0');
}

// Register error handler
ErrorHandler::register();

// Load and initialize services
$initServices = require_once __DIR__ . '/../bootstrap/app.php';
$initServices();

try {
    // Handle asset requests before routing
    $requestUri = $_SERVER['REQUEST_URI'];
    $path = parse_url($requestUri, PHP_URL_PATH);
    $path = ltrim($path, '/');

    // Check if this is an asset request
    if (preg_match('/\.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot|conf)$/i', $path)) {
        $baseDir = dirname(__DIR__);
        $filePath = null;

        if (strpos($path, 'modules/') === 0 && strpos($path, '/Assets/') !== false) {
            // Module asset
            $filePath = $baseDir . '/' . $path;
        } elseif (strpos($path, 'public/assets/') === 0) {
            // Public asset
            $filePath = $baseDir . '/' . $path;
        }

        if ($filePath && file_exists($filePath) && is_file($filePath)) {
            // Get file extension and set appropriate content type
            $extension = strtolower(pathinfo($filePath, PATHINFO_EXTENSION));
            $contentTypes = [
                'css' => 'text/css',
                'js' => 'application/javascript',
                'png' => 'image/png',
                'jpg' => 'image/jpeg',
                'jpeg' => 'image/jpeg',
                'gif' => 'image/gif',
                'svg' => 'image/svg+xml',
                'ico' => 'image/x-icon',
                'woff' => 'font/woff',
                'woff2' => 'font/woff2',
                'ttf' => 'font/ttf',
                'eot' => 'application/vnd.ms-fontobject',
                'conf' => 'application/javascript',
            ];

            $contentType = $contentTypes[$extension] ?? 'application/octet-stream';

            // Set headers
            header('Content-Type: ' . $contentType);
            header('Content-Length: ' . filesize($filePath));

            // Set cache headers
            $expires = 60 * 60 * 24 * 30; // 30 days
            header('Cache-Control: public, max-age=' . $expires);
            header('Expires: ' . gmdate('D, d M Y H:i:s', time() + $expires) . ' GMT');

            // Output the file and exit
            readfile($filePath);
            exit;
        }
    }

    // Example of using config in index.php
    $baseUrl = config('app_url');

    // Initialize request using ServerRequest::fromGlobals() for proper request initialization
    $request = new Request();

    // Initialize router with the request
    try {
        $router = new Router($request);
        // Load application routes
        require_once __DIR__ . '/../routes/web.php';
    } catch (\Throwable $e) {
        // Log the error
        Logger::log('Uncaught Exception: ' . $e->getMessage() . ' in ' . $e->getFile() . ':' . $e->getLine());
    }
    // Dispatch the request
    $router->dispatch();
} catch (\Throwable $e) {

    // Log the error
    Logger::log('Uncaught Exception: ' . $e->getMessage() . ' in ' . $e->getFile() . ':' . $e->getLine());

    // Set the HTTP response code
    http_response_code(500);

    // In debug mode, show detailed error
    if (config('debug')) {
        // Include the error view with exception details
        $errorFile = __DIR__ . '/../views/errors/500.php';
        if (file_exists($errorFile)) {
            // Pass the exception to the view
            $error = $e; // Make exception available in the view scope
            include $errorFile;
        } else {
            // Fallback error display
            echo '<h1>500 Internal Server Error</h1>';
            echo '<p>An error occurred while processing your request.</p>';
            if (config('debug')) {
                echo '<pre>' . htmlspecialchars((string) $e) . '</pre>';
            }
        }
    } else {
        // In production, show a generic error page
        $errorFile = __DIR__ . '/../views/errors/500.php';
        if (file_exists($errorFile)) {
            include $errorFile;
        } else {
            echo 'An error occurred. Please try again later.';
        }
    }

    exit(1);
}
