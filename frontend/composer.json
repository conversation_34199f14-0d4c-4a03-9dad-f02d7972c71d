{"name": "indianic/frontcms", "description": "Lightweight modular OOP PHP framework", "type": "project", "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "license": "MIT", "minimum-stability": "dev", "require": {"php": ">=8.1", "vlucas/phpdotenv": "^5.5", "monolog/monolog": "^3.0", "zordius/lightncandy": "^1.2", "package/grid-table": "0.1", "package/file-upload": "0.1"}, "autoload": {"psr-4": {"Core\\": "core/", "Modules\\": "modules/", "Middleware\\": "middleware/", "App\\": "app/"}}, "repositories": [{"type": "path", "url": "./package/GridTable", "options": {"symlink": true}}, {"type": "path", "url": "./package/FileUpload", "options": {"symlink": true}}]}