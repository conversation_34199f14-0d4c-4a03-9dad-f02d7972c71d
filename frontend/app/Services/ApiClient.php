<?php
namespace App\Services;

use App\Services\HttpClient;
use Exception;
use InvalidArgumentException;

class ApiClient
{
    protected HttpClient $http;
    protected string $baseUrl;
    protected string $cmsBaseUrl;
    protected ?string $apiKey;
    protected ?string $apiToken;
    protected bool $useCmsApi = false;

    public function __construct(string $baseUrl, string $cmsBaseUrl, ?string $apiKey = null, ?string $apiToken = null)
    {
        if (empty($baseUrl) || empty($cmsBaseUrl)) {
            throw new InvalidArgumentException("Base URL and CMS Base URL must be provided.");
        }
        $this->baseUrl = rtrim($baseUrl, '/');
        $this->cmsBaseUrl = rtrim($cmsBaseUrl, '/');
        $this->apiKey = $apiKey;
        $this->apiToken = $apiToken;

        $this->http = new HttpClient([
            'verify' => config('debug'), // Change to true in production
            'timeout' => config('timeout'),
        ]);
    }

    /**
     * Use the CMS API for subsequent requests
     */
    public function useCmsApi(bool $useCms = true): self
    {
        $this->useCmsApi = $useCms;
        return $this;
    }

    /**
     * Get the base URL for the current API type
     */
    protected function getBaseUrl(): string
    {
        return $this->useCmsApi ? $this->cmsBaseUrl : $this->baseUrl;
    }

    public function get(string $endpoint, array $query = [], bool $useCmsApi = null): array
    {
        if ($useCmsApi !== null) {
            $this->useCmsApi($useCmsApi);
        }

        return $this->request('GET', $endpoint, ['query' => $query]);
    }

    public function post(string $endpoint, array $data = [], bool $useCmsApi = null): array
    {
        if ($useCmsApi !== null) {
            $this->useCmsApi($useCmsApi);
        }
        return $this->request('POST', $endpoint, ['json' => $data]);
    }

    public function put(string $endpoint, array $data = [], bool $useCmsApi = false): array
    {
        if ($useCmsApi !== false) {
            $this->useCmsApi($useCmsApi);
        }

        return $this->request('PUT', $endpoint, ['json' => $data]);
    }

    public function delete(string $endpoint, array $data = [], bool $useCmsApi = null): array
    {
        if ($useCmsApi !== null) {
            $this->useCmsApi($useCmsApi);
        }
        return $this->request('DELETE', $endpoint, ['json' => $data]);
    }

    protected function request(string $method, string $endpoint, array $options = []): array
    {
        try {
            $endpoint = ltrim($endpoint, '/');
            $baseUrl = $this->getBaseUrl();
    
            // If endpoint is already a full URL, use it directly
            if (filter_var($endpoint, FILTER_VALIDATE_URL)) {
                $url = $endpoint;
            } else {
                $url = $baseUrl . '/' . $endpoint;
            }
    
            // Validate the final URL
            if (!filter_var($url, FILTER_VALIDATE_URL)) {
                throw new InvalidArgumentException("Invalid URL: '$url'");
            }
    
            // Handle Auth token (if applicable)
            $headers = [];
            if (Auth::token() !== null) {
                $headers['Authorization'] = 'Bearer ' . Auth::token();
            }
    
            $options['headers'] = array_merge($headers, $options['headers'] ?? []);
            $response = $this->http->request($method, $url, $options);
    
            return $this->handleResponse($response);
        } catch (Exception $e) {
            return $this->handleException($e);
        }
    }

    protected function handleResponse(array $response): array
    {
        $content = $response['body'];
        $data = json_decode($content, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new Exception('Invalid JSON response: ' . $content);
        }
        
        return $data;
    }
    
    protected function handleException(Exception $e): array
    {
        $message = $e->getMessage();
        
        // Try to extract JSON error message if available
        if (strpos($message, '{') !== false) {
            $json = json_decode($message, true);
            if (json_last_error() === JSON_ERROR_NONE) {
                $message = $json['message'] ?? $message;
            }
        }
        
        return [
            'error' => true,
            'status' => $e->getCode() ?: 500,
            'message' => $message,
            'data' => []
        ];
    }
}