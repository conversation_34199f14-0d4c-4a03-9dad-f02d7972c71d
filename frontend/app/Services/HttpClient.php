<?php
namespace App\Services;

use Exception;
use InvalidArgumentException;

class HttpClient
{
    protected $ch;
    protected array $defaultHeaders = [
        'Accept' => 'application/json',
        'Content-Type' => 'application/json',
    ];
    protected int $timeout = 30;
    protected bool $verifySSL = false;

    public function __construct(array $config = [])
    {
        if (!extension_loaded('curl')) {
            throw new Exception('cURL extension is required');
        }

        $this->configure($config);
    }

    protected function configure(array $config): void
    {
        $this->timeout = $config['timeout'] ?? $this->timeout;
        $this->verifySSL = $config['verify'] ?? $this->verifySSL;
        
        if (isset($config['headers'])) {
            $this->defaultHeaders = array_merge($this->defaultHeaders, $config['headers']);
        }
    }

    public function request(string $method, string $url, array $options = []): array
    {
        if (!filter_var($url, FILTER_VALIDATE_URL)) {
            throw new InvalidArgumentException("Invalid URL: '$url'");
        }
        
        $this->ch = curl_init();
        
        $headers = $this->prepareHeaders($options['headers'] ?? []);
        
        curl_setopt_array($this->ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_HEADER => false,
            CURLOPT_HTTPHEADER => $headers,
            CURLOPT_TIMEOUT => $this->timeout,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false,
            CURLOPT_CUSTOMREQUEST => strtoupper($method),
        ]);

        $this->setRequestOptions($method, $options, $url);
        
        $response = curl_exec($this->ch);
        $error = curl_error($this->ch);
        $statusCode = curl_getinfo($this->ch, CURLINFO_HTTP_CODE);
        $errno = curl_errno($this->ch);
        
        curl_close($this->ch);
        
        if ($errno) {
            throw new Exception(sprintf('cURL error %s: %s', $errno, $error), $errno);
        }
        
        return [
            'status' => $statusCode,
            'body' => $response,
            'headers' => $this->getResponseHeaders()
        ];
    }

    protected function setRequestOptions(string $method, array $options, string $url): void
    {
        $method = strtoupper($method);
        if (in_array($method, ['POST', 'PUT', 'PATCH'])) {
            if (isset($options['json'])) {
                $data = json_encode($options['json']);
                curl_setopt($this->ch, CURLOPT_POSTFIELDS, $data);
                $this->defaultHeaders['Content-Type'] = 'application/json';
            } elseif (isset($options['form_params'])) {
                curl_setopt($this->ch, CURLOPT_POSTFIELDS, http_build_query($options['form_params']));
                $this->defaultHeaders['Content-Type'] = 'application/x-www-form-urlencoded';
            }
        } elseif ($method === 'GET' && !empty($options['query'])) {
            $url .= (strpos($url, '?') === false ? '?' : '&') . http_build_query($options['query']);
            curl_setopt($this->ch, CURLOPT_URL, $url);
        }
    }

    protected function prepareHeaders(array $headers): array
    {
        $prepared = [];
        $headers = array_merge($this->defaultHeaders, $headers);
        
        foreach ($headers as $key => $value) {
            $prepared[] = "$key: $value";
        }
        
        return $prepared;
    }

    protected function getResponseHeaders(): array
    {
        $headers = [];
        $headerText = curl_getinfo($this->ch, CURLINFO_HEADER_OUT);
        
        foreach (explode("\r\n", $headerText) as $i => $line) {
            if ($i === 0 || empty($line)) {
                continue;
            }
            
            $parts = explode(': ', $line, 2);
            if (count($parts) === 2) {
                $headers[$parts[0]] = $parts[1];
            }
        }
        
        return $headers;
    }

    public function __destruct()
    {
        if (is_resource($this->ch)) {
            curl_close($this->ch);
        }
    }
}
