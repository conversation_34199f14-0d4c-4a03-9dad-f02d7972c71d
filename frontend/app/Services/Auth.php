<?php

namespace App\Services;

use Core\ServiceContainer;

class Auth
{
    private static ?array $user = null;

    /**
     * Check if a user is logged in
     */
    public static function check(): bool
    {
        return isset($_SESSION['user']);
    }

    /**
     * Get the authenticated user
     */
    public static function user($user = null): ?array
    {
        if ($user !== null) {
            // Update the user data in both session and static property
            self::$user = $user;
            $_SESSION['user'] = $user;
            return $user;
        }

        // Return cached user if available
        if (self::$user === null && isset($_SESSION['user'])) {
            self::$user = $_SESSION['user'];
        }
        
        return self::$user;
    }

    /**
     * Attempt to log in a user
     */
    public static function attempt(array $credentials): bool
    {
        // Here you would typically validate credentials against your API
        // This is a simplified example
        $api = ServiceContainer::resolve(ApiClient::class);
        $response = $api->post('login', $credentials);

        if (isset($response['user'])) {
            self::login($response['user'], $response['token']);
            return true;
        }

        return false;
    }

    /**
     * Log in a user
     */
    public static function login(array $user, string $token): void
    {
        $_SESSION['user'] = $user;
        $_SESSION['token'] = $token;
        self::$user = $user;
    }

    /**
     * Log out the current user
     */
    public static function logout(): void
    {
        unset($_SESSION['user'], $_SESSION['token']);
        self::$user = null;
        session_destroy();
    }

    /**
     * Get the auth token
     */
    public static function token(): ?string
    {
        return $_SESSION['token'] ?? null;
    }

    /**
     * Get the user's ID
     */
    public static function id(): ?int
    {
        return self::user()['id'] ?? null;
    }

    public static function isAdmin($type = null) {
        if ($type !== null) {
            $_SESSION['is_admin'] = $type == 1 ? true : false;
        }
        return $_SESSION['is_admin'] ?? false; 
    }
}