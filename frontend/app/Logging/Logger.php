<?php


// app/Logging/Logger.php
namespace App\Logging;

class Logger
{
    protected static $logFile;
    
    public static function initialize()
    {
        $logDir = dirname(__DIR__, 2) . '/storage/logs';
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }
        self::$logFile = $logDir . '/app.log';
    }
    
    public static function log(string $message, string $level = 'info', array $context = [])
    {
        if (!self::$logFile) {
            self::initialize();
        }
        
        $timestamp = date('Y-m-d H:i:s');
        $level = strtoupper($level);
        $contextStr = !empty($context) ? ' ' . json_encode($context) : '';
        $logMessage = "[$timestamp] $level: $message$contextStr" . PHP_EOL;
        
        file_put_contents(self::$logFile, $logMessage, FILE_APPEND | LOCK_EX);
    }
}