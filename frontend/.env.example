# Application Environment
APP_NAME=FrontCMS
APP_ENV=local
APP_DEBUG=true

APP_VIEW_PATH=frontend

APP_VERIFY_URL=false

APP_TIMEOUT=10

# Base URL Configuration
APP_URL=

# API Configuration
# Local Development
API_BASE_URL_V1=
API_BASE_URL_CMS=

# API Security
API_KEY=

# File Uploads
UPLOAD_DIR=uploads
UPLOAD_URL=/frontcms/uploads

# Session Configuration
SESSION_DRIVER=file
SESSION_LIFETIME=120

# Available Modules (comma-separated)
MODULES=block_categories,template_categories,tags,templates,pages,blocks,pages_blocks




# =============================================================================
# FILEUPLOAD PACKAGE - COMPREHENSIVE CONFIGURATION
# =============================================================================

# =============================================================================
# STORAGE DRIVER CONFIGURATION
# =============================================================================

# Storage driver to use: local, s3, gcs, azure
FILEUPLOAD_DRIVER=local

# =============================================================================
# LOCAL STORAGE CONFIGURATION
# =============================================================================

# Root directory for local file storage (relative to project root)
FILEUPLOAD_LOCAL_ROOT=uploads

# Base URL for accessing uploaded files
FILEUPLOAD_LOCAL_URL=/uploads

# Directory permissions for created folders (octal format)
FILEUPLOAD_LOCAL_PERMISSIONS=0755

# =============================================================================
# FILE VALIDATION SETTINGS
# =============================================================================

# Maximum file size in bytes (10MB = 10485760)
FILEUPLOAD_MAX_SIZE=10485760

# Allowed file types (comma-separated MIME types)
FILEUPLOAD_ALLOWED_TYPES=image/jpeg,image/png,image/gif,image/svg+xml,application/pdf,text/plain,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document

# Allowed file extensions (comma-separated)
FILEUPLOAD_ALLOWED_EXTENSIONS=jpg,jpeg,png,gif,svg,pdf,txt,doc,docx,xls,xlsx,ppt,pptx

# Forbidden file extensions for security (comma-separated)
FILEUPLOAD_FORBIDDEN_EXTENSIONS=php,exe,bat,sh,cmd,com,pif,scr,vbs,js,jar,asp,aspx,jsp,html

# Minimum file size in bytes (0 = no minimum)
FILEUPLOAD_MIN_SIZE=0

# Maximum filename length
FILEUPLOAD_MAX_FILENAME_LENGTH=255

# =============================================================================
# IMAGE PROCESSING SETTINGS
# =============================================================================

# Enable/disable image processing
FILEUPLOAD_PROCESSING_ENABLED=true

# Enable/disable automatic image compression for uploaded images
# - true: Compress images using FILEUPLOAD_IMAGE_QUALITY setting
# - false: Store original images without compression
FILEUPLOAD_COMPRESS_IMAGES=true

# Image quality for JPEG compression (1-100)
# Lower values = smaller file size, lower quality
# Higher values = larger file size, better quality
# Recommended values:
# - 95-100: Highest quality (minimal compression)
# - 85-94: High quality (recommended for photos)
# - 70-84: Good quality (balanced size/quality)
# - 50-69: Medium quality (noticeable compression)
# - 1-49: Low quality (high compression)
FILEUPLOAD_IMAGE_QUALITY=85

# Auto-orient images based on EXIF data
# Automatically rotates images based on camera orientation
FILEUPLOAD_AUTO_ORIENT=true

# Strip metadata from images
# Removes EXIF data for privacy and smaller file sizes
FILEUPLOAD_STRIP_METADATA=true

# Progressive JPEG encoding
# Creates images that load progressively (better for web)
FILEUPLOAD_PROGRESSIVE_JPEG=false

# =============================================================================
# THUMBNAIL GENERATION CONFIGURATION
# =============================================================================

# Enable/disable automatic thumbnail generation
# When enabled, thumbnails are created for all uploaded images
FILEUPLOAD_THUMBNAILS_ENABLED=true

# Directory name for storing thumbnails (relative to upload directory)
# Thumbnails will be stored in: FILEUPLOAD_LOCAL_ROOT/FILEUPLOAD_THUMBNAILS_DIRECTORY
FILEUPLOAD_THUMBNAILS_DIRECTORY=thumbnails

# Default thumbnail quality (1-100)
# Quality setting for generated thumbnails
FILEUPLOAD_THUMBNAIL_QUALITY=80

# Thumbnail sizes configuration (JSON format)
# Define multiple thumbnail sizes to be generated automatically
# Each size must have: name, width, height, mode
#
# Available modes:
# - crop: Crop to exact dimensions (may cut off parts of image)
# - fit: Fit within dimensions while maintaining aspect ratio
# - fill: Fill dimensions while maintaining aspect ratio (may crop)
# - stretch: Stretch to exact dimensions (may distort image)
#
# Examples:
# Small thumbnails for lists: {"name":"thumb","width":100,"height":100,"mode":"crop"}
# Medium thumbnails for galleries: {"name":"medium","width":300,"height":200,"mode":"fit"}
# Large thumbnails for previews: {"name":"large","width":800,"height":600,"mode":"fit"}
FILEUPLOAD_THUMBNAIL_SIZES=[{"name":"small","width":150,"height":150,"mode":"crop"},{"name":"medium","width":300,"height":300,"mode":"fit"},{"name":"large","width":600,"height":600,"mode":"fit"}]

# =============================================================================
# AWS S3 STORAGE CONFIGURATION (Optional)
# =============================================================================
# Uncomment and configure these settings if using Amazon S3 storage

# AWS Access Key ID
# FILEUPLOAD_S3_KEY=AKIAIOSFODNN7EXAMPLE

# AWS Secret Access Key
# FILEUPLOAD_S3_SECRET=wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY

# AWS Region
# FILEUPLOAD_S3_REGION=us-east-1

# S3 Bucket Name
# FILEUPLOAD_S3_BUCKET=my-upload-bucket

# S3 Base URL (optional, auto-generated if not provided)
# FILEUPLOAD_S3_URL=https://my-upload-bucket.s3.amazonaws.com

# Custom S3 Endpoint (for S3-compatible services like DigitalOcean Spaces)
# FILEUPLOAD_S3_ENDPOINT=https://nyc3.digitaloceanspaces.com

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================

# Sanitize uploaded filenames (remove special characters)
FILEUPLOAD_SANITIZE_FILENAMES=true

# Generate unique filenames to prevent conflicts
FILEUPLOAD_GENERATE_UNIQUE_NAMES=true

# Enable virus scanning (requires ClamAV)
FILEUPLOAD_VIRUS_SCAN_ENABLED=false

# Path to ClamAV scanner
FILEUPLOAD_CLAMAV_PATH=/usr/bin/clamscan

# =============================================================================
# DYNAMIC TRANSFORMATION CONFIGURATION
# =============================================================================

# Enable dynamic image transformation via URL
FILEUPLOAD_DYNAMIC_TRANSFORM_ENABLED=true

# Cache transformed images (recommended for production)
FILEUPLOAD_CACHE_TRANSFORMS=true

# Cache directory for transformed images
FILEUPLOAD_CACHE_DIR=cache/transforms

# Cache expiration time in seconds (default: 30 days)
FILEUPLOAD_CACHE_EXPIRATION=2592000

# =============================================================================
# ADVANCED CONFIGURATION
# =============================================================================

# Enable debug mode for troubleshooting
FILEUPLOAD_DEBUG=false

# Maximum number of files in batch upload
FILEUPLOAD_MAX_BATCH_SIZE=10

# Temporary directory for processing
FILEUPLOAD_TEMP_DIR=/tmp

# Enable/disable image compression for uploaded images
# - true: Compress uploaded images using FILEUPLOAD_IMAGE_QUALITY setting
# - false: Store original images without any compression
FILEUPLOAD_COMPRESS_IMAGES=true

# Image quality for JPEG compression (1-100)
# - Lower values = smaller file size, lower quality
# - Higher values = larger file size, better quality
# - Only used when FILEUPLOAD_COMPRESS_IMAGES=true
FILEUPLOAD_IMAGE_QUALITY=85

# Maximum image dimensions for processing
FILEUPLOAD_MAX_IMAGE_WIDTH=4000
FILEUPLOAD_MAX_IMAGE_HEIGHT=4000

# Enable/disable automatic image orientation correction
FILEUPLOAD_AUTO_ORIENT=true

# =============================================================================
# THUMBNAIL GENERATION
# =============================================================================

# Enable/disable automatic thumbnail generation
FILEUPLOAD_THUMBNAILS_ENABLED=false

# Directory name for storing thumbnails (relative to upload directory)
FILEUPLOAD_THUMBNAILS_DIRECTORY=thumbnails

# Default thumbnail sizes (JSON format)
FILEUPLOAD_THUMBNAIL_SIZES=[{"name":"small","width":150,"height":150,"mode":"crop"},{"name":"medium","width":300,"height":300,"mode":"fit"},{"name":"large","width":600,"height":600,"mode":"fit"}]

# Individual thumbnail size configuration (alternative to JSON)
FILEUPLOAD_THUMB_SMALL_WIDTH=150
FILEUPLOAD_THUMB_SMALL_HEIGHT=150
FILEUPLOAD_THUMB_SMALL_MODE=crop
FILEUPLOAD_THUMB_MEDIUM_WIDTH=300
FILEUPLOAD_THUMB_MEDIUM_HEIGHT=300
FILEUPLOAD_THUMB_MEDIUM_MODE=crop
FILEUPLOAD_THUMB_LARGE_WIDTH=600
FILEUPLOAD_THUMB_LARGE_HEIGHT=600
FILEUPLOAD_THUMB_LARGE_MODE=fit

# Thumbnail image quality (1-100)
FILEUPLOAD_THUMBNAIL_QUALITY=80

# =============================================================================
# SECURITY SETTINGS
# =============================================================================

# Enable/disable file header validation (recommended: true)
FILEUPLOAD_VALIDATE_HEADERS=true

# Enable/disable filename sanitization
FILEUPLOAD_SANITIZE_FILENAMES=true

# Enable/disable virus scanning (requires ClamAV)
FILEUPLOAD_VIRUS_SCAN=false

# Path to ClamAV scanner (if virus scanning is enabled)
FILEUPLOAD_CLAMAV_PATH=/usr/bin/clamscan

# Enable/disable IP-based rate limiting
FILEUPLOAD_RATE_LIMITING=false

# Maximum uploads per IP per hour (if rate limiting is enabled)
FILEUPLOAD_RATE_LIMIT_PER_HOUR=100

# =============================================================================
# AMAZON S3 CONFIGURATION (if FILEUPLOAD_DRIVER=s3)
# =============================================================================

# S3 bucket name
FILEUPLOAD_S3_BUCKET=your-bucket-name

# S3 region
FILEUPLOAD_S3_REGION=us-east-1

# S3 access key ID
FILEUPLOAD_S3_KEY=your-access-key-id

# S3 secret access key
FILEUPLOAD_S3_SECRET=your-secret-access-key

# S3 endpoint (optional, for S3-compatible services)
FILEUPLOAD_S3_ENDPOINT=

# S3 path prefix (optional)
FILEUPLOAD_S3_PREFIX=uploads

# S3 file visibility: public, private
FILEUPLOAD_S3_VISIBILITY=public

# S3 custom domain for file URLs (optional)
FILEUPLOAD_S3_URL=

# =============================================================================
# GOOGLE CLOUD STORAGE CONFIGURATION (if FILEUPLOAD_DRIVER=gcs)
# =============================================================================

# GCS bucket name
FILEUPLOAD_GCS_BUCKET=your-bucket-name

# GCS project ID
FILEUPLOAD_GCS_PROJECT_ID=your-project-id

# Path to GCS service account key file
FILEUPLOAD_GCS_KEY_FILE=/path/to/service-account-key.json

# GCS path prefix (optional)
FILEUPLOAD_GCS_PREFIX=uploads

# GCS file visibility: public, private
FILEUPLOAD_GCS_VISIBILITY=public

# =============================================================================
# AZURE BLOB STORAGE CONFIGURATION (if FILEUPLOAD_DRIVER=azure)
# =============================================================================

# Azure storage account name
FILEUPLOAD_AZURE_ACCOUNT=your-account-name

# Azure storage account key
FILEUPLOAD_AZURE_KEY=your-account-key

# Azure container name
FILEUPLOAD_AZURE_CONTAINER=uploads

# Azure path prefix (optional)
FILEUPLOAD_AZURE_PREFIX=

# Azure file visibility: public, private
FILEUPLOAD_AZURE_VISIBILITY=public

# =============================================================================
# DYNAMIC IMAGE TRANSFORMATION
# =============================================================================

# Enable/disable dynamic image transformation
FILEUPLOAD_TRANSFORM_ENABLED=true

# Default transformation mode: fit, fix, crop, fill, stretch
FILEUPLOAD_TRANSFORM_DEFAULT_MODE=fit

# Maximum transformation dimensions
FILEUPLOAD_TRANSFORM_MAX_WIDTH=2000
FILEUPLOAD_TRANSFORM_MAX_HEIGHT=2000

# Cache duration for transformed images (in seconds, ******** = 1 year)
FILEUPLOAD_TRANSFORM_CACHE_DURATION=********

# Enable/disable transformation URL signing (for security)
FILEUPLOAD_TRANSFORM_SIGN_URLS=false

# Secret key for URL signing (if enabled)
FILEUPLOAD_TRANSFORM_SECRET_KEY=your-secret-key

# =============================================================================
# LOGGING AND DEBUGGING
# =============================================================================

# Enable/disable debug mode
FILEUPLOAD_DEBUG=false

# Log level: emergency, alert, critical, error, warning, notice, info, debug
FILEUPLOAD_LOG_LEVEL=error

# Log file path (relative to project root)
FILEUPLOAD_LOG_FILE=storage/logs/fileupload.log

# Enable/disable upload statistics logging
FILEUPLOAD_LOG_STATS=false

# =============================================================================
# PERFORMANCE SETTINGS
# =============================================================================

# Enable/disable file caching
FILEUPLOAD_CACHE_ENABLED=true

# Cache driver: file, redis, memcached
FILEUPLOAD_CACHE_DRIVER=file

# Cache duration in seconds (3600 = 1 hour)
FILEUPLOAD_CACHE_DURATION=3600

# Enable/disable gzip compression for responses
FILEUPLOAD_GZIP_ENABLED=true

# Memory limit for image processing (in MB)
FILEUPLOAD_MEMORY_LIMIT=256

# =============================================================================
# CDN CONFIGURATION
# =============================================================================

# CDN base URL (optional, for serving files via CDN)
FILEUPLOAD_CDN_URL=

# Enable/disable CDN for file URLs
FILEUPLOAD_CDN_ENABLED=false

# =============================================================================
# WEBHOOK CONFIGURATION
# =============================================================================

# Enable/disable webhooks for upload events
FILEUPLOAD_WEBHOOKS_ENABLED=false

# Webhook URL for upload success events
FILEUPLOAD_WEBHOOK_SUCCESS_URL=

# Webhook URL for upload failure events
FILEUPLOAD_WEBHOOK_FAILURE_URL=

# Webhook secret for request signing
FILEUPLOAD_WEBHOOK_SECRET=

# =============================================================================
# FOLDER OPERATIONS
# =============================================================================

# Enable/disable folder operations
FILEUPLOAD_FOLDERS_ENABLED=true

# Maximum folder depth allowed
FILEUPLOAD_MAX_FOLDER_DEPTH=10

# Default folder permissions (octal format)
FILEUPLOAD_FOLDER_PERMISSIONS=0755

# Enable/disable recursive folder deletion
FILEUPLOAD_ALLOW_RECURSIVE_DELETE=true

# =============================================================================
# BACKUP CONFIGURATION
# =============================================================================

# Enable/disable automatic backups
FILEUPLOAD_BACKUP_ENABLED=false

# Backup storage driver: local, s3, gcs, azure
FILEUPLOAD_BACKUP_DRIVER=local

# Backup directory (for local backups)
FILEUPLOAD_BACKUP_PATH=backups

# Backup retention period in days
FILEUPLOAD_BACKUP_RETENTION_DAYS=30

# =============================================================================
# CUSTOM CONFIGURATION
# =============================================================================

# Custom upload directory structure pattern
# Available variables: {year}, {month}, {day}, {user_id}, {random}
FILEUPLOAD_DIRECTORY_PATTERN=uploads/{year}/{month}

# Enable/disable unique filename generation
FILEUPLOAD_GENERATE_UNIQUE_NAMES=true

# Filename pattern for unique names
# Available variables: {original}, {timestamp}, {random}, {hash}
FILEUPLOAD_FILENAME_PATTERN={timestamp}_{random}_{original}

# =============================================================================
# INTEGRATION SETTINGS
# =============================================================================

# Database connection for logging (optional)
FILEUPLOAD_DB_CONNECTION=mysql

# Table name for upload logs
FILEUPLOAD_DB_TABLE=file_uploads

# Enable/disable database logging
FILEUPLOAD_DB_LOGGING=false

# =============================================================================
# DEVELOPMENT SETTINGS
# =============================================================================

# Enable/disable test mode (for development)
FILEUPLOAD_TEST_MODE=false

# Test upload directory (used in test mode)
FILEUPLOAD_TEST_DIRECTORY=tests/uploads

# Enable/disable detailed error reporting
FILEUPLOAD_DETAILED_ERRORS=false