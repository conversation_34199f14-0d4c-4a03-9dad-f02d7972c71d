# Application Environment
APP_NAME=FrontCMS
APP_ENV=local
APP_DEBUG=true

APP_VIEW_PATH=frontend

APP_VERIFY_URL=false

APP_TIMEOUT=10

# Base URL Configuration
APP_URL=

# API Configuration
# Local Development
API_BASE_URL_V1=
API_BASE_URL_CMS=

# API Security
API_KEY=

# File Uploads
UPLOAD_DIR=uploads
UPLOAD_URL=/frontcms/uploads

# Session Configuration
SESSION_DRIVER=file
SESSION_LIFETIME=120

# Available Modules (comma-separated)
MODULES=block_categories,template_categories,tags,templates,pages,blocks,pages_blocks


# File Upload Package Configuration
FILEUPLOAD_DRIVER=local
FILEUPLOAD_LOCAL_ROOT=/var/www/html/cmsfront-new/frontend/public/uploads/media
FILEUPLOAD_LOCAL_URL=/uploads/media
FILEUPLOAD_MAX_SIZE=10485760
FILEUPLOAD_ALLOWED_TYPES=image/jpeg,image/png,image/gif,image/webp,image/svg+xml,application/pdf,text/plain,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document,video/mp4,video/quicktime,video/x-msvideo
FILEUPLOAD_ALLOWED_EXTENSIONS=jpg,jpeg,png,gif,webp,svg,pdf,doc,docx,txt,mp4,mov,avi
FILEUPLOAD_FORBIDDEN_EXTENSIONS=php,exe,bat,sh,js,html
FILEUPLOAD_PROCESSING_ENABLED=true
FILEUPLOAD_COMPRESS_IMAGES=true
FILEUPLOAD_THUMBNAILS_ENABLED=false
FILEUPLOAD_THUMBNAILS_DIRECTORY=thumbnails
FILEUPLOAD_IMAGE_QUALITY=50
FILEUPLOAD_THUMBNAIL_QUALITY=80
FILEUPLOAD_THUMBNAIL_SIZES=[{"name":"small","width":150,"height":150,"mode":"crop"},{"name":"medium","width":300,"height":300,"mode":"fit"},{"name":"large","width":600,"height":600,"mode":"fit"}]