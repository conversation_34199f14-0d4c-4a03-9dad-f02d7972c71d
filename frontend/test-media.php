<?php

require_once __DIR__ . '/vendor/autoload.php';

// Load environment variables
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
$dotenv->load();

// Test MediaController instantiation
try {
    echo "Testing MediaController...\n";

    // Test autoloading
    if (class_exists('Modules\Media\Controllers\MediaController')) {
        echo "✓ MediaController class found\n";
    } else {
        echo "✗ MediaController class not found\n";
        exit(1);
    }

    if (class_exists('Modules\Media\Services\MediaService')) {
        echo "✓ MediaService class found\n";
    } else {
        echo "✗ MediaService class not found\n";
        exit(1);
    }

    if (class_exists('Package\FileUpload\FileUploadManager')) {
        echo "✓ FileUploadManager class found\n";
    } else {
        echo "✗ FileUploadManager class not found\n";
        exit(1);
    }

    // Test MediaService instantiation
    $mediaService = new \Modules\Media\Services\MediaService();
    echo "✓ MediaService instantiated successfully\n";

    // Test getMediaList method
    $result = $mediaService->getMediaList();
    if ($result['success']) {
        echo "✓ getMediaList method works\n";
        echo "  - Total folders: " . $result['data']['total_folders'] . "\n";
        echo "  - Total files: " . $result['data']['total_files'] . "\n";
    } else {
        echo "✗ getMediaList method failed: " . $result['message'] . "\n";
    }

    // Test folder creation
    $folderResult = $mediaService->createFolder('test-folder');
    if ($folderResult['success']) {
        echo "✓ createFolder method works\n";

        // Clean up - delete the test folder
        $deleteResult = $mediaService->deleteItem('test-folder', 'folder');
        if ($deleteResult['success']) {
            echo "✓ deleteItem method works\n";
        }
    } else {
        echo "✗ createFolder method failed: " . $folderResult['message'] . "\n";
    }

    echo "\nAll tests passed! Media module is working correctly.\n";
} catch (Exception $e) {
    echo "✗ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
    exit(1);
}
