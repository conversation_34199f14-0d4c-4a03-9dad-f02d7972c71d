# Automatic Thumbnail Generation Guide

This guide explains how to use the automatic thumbnail generation feature in the FileUpload package, which is controlled entirely by environment variables.

## 🎯 Overview

The FileUpload package can automatically generate thumbnails when images are uploaded. This feature is controlled by environment variables and requires no manual intervention once configured.

## ⚙️ Configuration

### 1. Enable Processing and Thumbnails

Add these environment variables to your `.env` file:

```env
# Enable image processing
FILEUPLOAD_PROCESSING_ENABLED=true

# Enable automatic thumbnail generation
FILEUPLOAD_THUMBNAILS_ENABLED=true

# Configure thumbnail settings
FILEUPLOAD_THUMBNAILS_DIRECTORY=thumbnails
FILEUPLOAD_THUMBNAIL_QUALITY=80
FILEUPLOAD_IMAGE_QUALITY=85
```

### 2. Configure Thumbnail Sizes

You have two options for configuring thumbnail sizes:

#### Option A: JSON Format (Recommended)
```env
FILEUPLOAD_THUMBNAIL_SIZES=[{"name":"small","width":150,"height":150,"mode":"crop"},{"name":"medium","width":300,"height":300,"mode":"fit"},{"name":"large","width":600,"height":600,"mode":"fit"}]
```

#### Option B: Individual Variables
```env
# Small thumbnail
FILEUPLOAD_THUMB_SMALL_WIDTH=150
FILEUPLOAD_THUMB_SMALL_HEIGHT=150
FILEUPLOAD_THUMB_SMALL_MODE=crop

# Medium thumbnail
FILEUPLOAD_THUMB_MEDIUM_WIDTH=300
FILEUPLOAD_THUMB_MEDIUM_HEIGHT=300
FILEUPLOAD_THUMB_MEDIUM_MODE=crop

# Large thumbnail
FILEUPLOAD_THUMB_LARGE_WIDTH=600
FILEUPLOAD_THUMB_LARGE_HEIGHT=600
FILEUPLOAD_THUMB_LARGE_MODE=fit
```

## 🔧 Implementation in Code

### Automatic Setup (Recommended)

The FileUpload package automatically reads environment variables and configures thumbnails:

```php
<?php
require_once 'vendor/autoload.php';

use Package\FileUpload\FileUpload;

// Create uploader instance
$uploader = new FileUpload();

// Add image processor (reads config from environment variables)
$imageProcessor = new \Package\FileUpload\Processing\ImageProcessor();
$uploader->addProcessor($imageProcessor);

// Upload file - thumbnails will be generated automatically
$result = $uploader->upload($_FILES['image']);

if ($result->isSuccess()) {
    echo "File uploaded: " . $result->getPath() . "\n";
    
    // Get generated thumbnails
    $processedFiles = $result->getProcessedFiles();
    foreach ($processedFiles as $type => $fileInfo) {
        if (strpos($type, 'thumbnail_') === 0) {
            echo "Thumbnail {$type}: " . $fileInfo['path'] . "\n";
        }
    }
}
?>
```

### Manual Configuration (Advanced)

You can also configure thumbnails programmatically:

```php
<?php
// Create image processor with custom config
$imageProcessor = new \Package\FileUpload\Processing\ImageProcessor([
    'thumbnails' => [
        'enabled' => true,
        'directory' => 'thumbs',
        'quality' => 90,
        'sizes' => [
            'tiny' => ['width' => 50, 'height' => 50, 'mode' => 'crop'],
            'small' => ['width' => 150, 'height' => 150, 'mode' => 'crop'],
            'medium' => ['width' => 300, 'height' => 300, 'mode' => 'fit'],
        ]
    ]
]);

$uploader->addProcessor($imageProcessor);
?>
```

## 📏 Thumbnail Modes

The package supports different thumbnail generation modes:

- **crop**: Crop the image to exact dimensions (may cut off parts)
- **fit**: Scale to fit within dimensions, maintaining aspect ratio
- **stretch**: Stretch to exact dimensions (may distort image)
- **fill**: Fill dimensions, may crop to maintain aspect ratio
- **fix**: Crop to exact dimensions from center

## 📁 File Structure

When thumbnails are generated, they are stored in the configured directory:

```
uploads/
├── original-image.jpg          # Original uploaded file
└── thumbnails/                 # Thumbnail directory
    ├── original-image_small.jpg    # 150x150 thumbnail
    ├── original-image_medium.jpg   # 300x300 thumbnail
    └── original-image_large.jpg    # 600x600 thumbnail
```

## 🧪 Testing

### 1. Check Configuration

Visit the test page to verify your configuration:
```
http://your-domain/test-fileupload.php
```

The page will show:
- ✅ Current thumbnail settings from environment variables
- ✅ Configured thumbnail sizes
- ✅ Processing status

### 2. Upload Test

1. Upload an image using the test form
2. Check the "Generated Thumbnails" section in the results
3. Verify thumbnail files are created in the uploads directory

### 3. Verify Environment Variables

The test page displays all relevant environment variables:
- `FILEUPLOAD_PROCESSING_ENABLED`
- `FILEUPLOAD_THUMBNAILS_ENABLED`
- `FILEUPLOAD_THUMBNAILS_DIRECTORY`
- `FILEUPLOAD_THUMBNAIL_QUALITY`
- `FILEUPLOAD_IMAGE_QUALITY`
- `FILEUPLOAD_THUMBNAIL_SIZES`

## 🔍 Troubleshooting

### Thumbnails Not Generated

1. **Check Processing Enabled**:
   ```env
   FILEUPLOAD_PROCESSING_ENABLED=true
   ```

2. **Check Thumbnails Enabled**:
   ```env
   FILEUPLOAD_THUMBNAILS_ENABLED=true
   ```

3. **Verify GD Extension**:
   ```bash
   php -m | grep -i gd
   ```

4. **Check Directory Permissions**:
   ```bash
   chmod 755 uploads
   chmod 755 uploads/thumbnails
   ```

### Invalid Thumbnail Sizes

If using JSON format, ensure valid JSON:
```env
# ✅ Correct
FILEUPLOAD_THUMBNAIL_SIZES=[{"name":"small","width":150,"height":150,"mode":"crop"}]

# ❌ Incorrect (missing quotes)
FILEUPLOAD_THUMBNAIL_SIZES=[{name:small,width:150,height:150,mode:crop}]
```

### Memory Issues

For large images, increase memory limit:
```env
FILEUPLOAD_MEMORY_LIMIT=512
```

Or in php.ini:
```ini
memory_limit = 512M
```

## 📊 Performance Tips

1. **Optimize Quality Settings**:
   ```env
   FILEUPLOAD_IMAGE_QUALITY=85      # Good balance
   FILEUPLOAD_THUMBNAIL_QUALITY=80  # Smaller file sizes
   ```

2. **Limit Thumbnail Sizes**:
   - Only generate sizes you actually use
   - Consider using dynamic transformation for other sizes

3. **Use Appropriate Modes**:
   - `crop` for profile pictures, avatars
   - `fit` for product images, galleries
   - `stretch` rarely recommended

## 🎯 Best Practices

1. **Environment-Based Configuration**: Always use environment variables for configuration
2. **Consistent Naming**: Use descriptive names for thumbnail sizes
3. **Quality Balance**: Balance file size vs. image quality
4. **Directory Organization**: Keep thumbnails in subdirectories
5. **Error Handling**: Always check upload results and handle errors

## 📚 Related Documentation

- **Complete Configuration**: See `.env.example` for all options
- **API Reference**: Check `README.md` for method documentation
- **Testing Guide**: Use `test-fileupload.php` for feature testing
- **Environment Variables**: See `ENV-VARIABLES.md` for complete reference

## 🎉 Example Complete Configuration

Here's a complete `.env` configuration for automatic thumbnails:

```env
# Image Processing
FILEUPLOAD_PROCESSING_ENABLED=true
FILEUPLOAD_IMAGE_QUALITY=85

# Automatic Thumbnails
FILEUPLOAD_THUMBNAILS_ENABLED=true
FILEUPLOAD_THUMBNAILS_DIRECTORY=thumbnails
FILEUPLOAD_THUMBNAIL_QUALITY=80

# Thumbnail Sizes (JSON format)
FILEUPLOAD_THUMBNAIL_SIZES=[{"name":"thumb","width":100,"height":100,"mode":"crop"},{"name":"small","width":200,"height":200,"mode":"crop"},{"name":"medium","width":400,"height":400,"mode":"fit"},{"name":"large","width":800,"height":800,"mode":"fit"}]

# File Validation
FILEUPLOAD_MAX_SIZE=10485760
FILEUPLOAD_ALLOWED_TYPES=image/jpeg,image/png,image/gif,image/webp
FILEUPLOAD_FORBIDDEN_EXTENSIONS=php,exe,bat,sh,js
```

With this configuration, every uploaded image will automatically generate 4 thumbnails without any additional code!
