<?php

/**
 * Dynamic Image Transformation Endpoint - Package Entry Point
 *
 * This file is automatically made available when the FileUpload package is installed via Composer.
 * It acts as a clean entry point that delegates all processing to the package.
 *
 * Supported URL formats:
 * - /uploads/1000x1000/fix=Dashboard2.jpg (explicit mode)
 * - /uploads/1000x1000/Dashboard2.jpg (default to fit mode)
 * - /uploads/P/h/P/300x300/fit=file.png (nested with explicit mode)
 * - /uploads/P/h/P/300x300/file.png (nested with default mode)
 */

use Package\FileUpload\Services\ImageTransformService;

// Auto-detect the correct autoload path
$autoloadPaths = [
    __DIR__ . '/../../../vendor/autoload.php',  // When installed as package
    __DIR__ . '/../../vendor/autoload.php',     // When in package directory
    __DIR__ . '/../vendor/autoload.php',        // Alternative structure
    __DIR__ . '/vendor/autoload.php',           // Root level
];

foreach ($autoloadPaths as $autoloadPath) {
    if (file_exists($autoloadPath)) {
        require_once $autoloadPath;
        break;
    }
}

// Auto-detect environment file
$envPaths = [
    __DIR__ . '/../../../.env',     // When installed as package
    __DIR__ . '/../../.env',        // When in package directory
    __DIR__ . '/../.env',           // Alternative structure
    __DIR__ . '/.env',              // Root level
];

foreach ($envPaths as $envPath) {
    if (file_exists($envPath)) {
        $envFile = file_get_contents($envPath);
        $lines = explode("\n", $envFile);

        foreach ($lines as $line) {
            $line = trim($line);
            if (empty($line) || strpos($line, '#') === 0) {
                continue;
            }

            if (strpos($line, '=') !== false) {
                list($key, $value) = explode('=', $line, 2);
                $_ENV[trim($key)] = trim($value);
            }
        }
        break;
    }
}

try {
    // Use the service layer instead of direct controller access
    $transformService = new ImageTransformService();
    $transformService->handleRequest();
} catch (\Exception $e) {
    error_log("Image transformation error: " . $e->getMessage());

    header('HTTP/1.0 500 Internal Server Error');
    header('Content-Type: text/plain');
    echo 'Image transformation failed';
    exit;
}
