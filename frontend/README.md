# FileUpload Package - Complete Guide

A comprehensive and scalable file upload package with support for multiple storage drivers, validation, dynamic image processing, and folder operations.

## 🚀 Quick Start

### 1. System Requirements Check

Before installation, run the dependency check:

```bash
# Open in browser
http://your-domain/demo-dependency-check.php
```

**Required:**

- PHP 8.0+
- GD Extension
- Composer
- Writable uploads directory

### 2. Installation

The package is already included in this project. If installing elsewhere:

```bash
composer require package/file-upload
```

### 3. Basic Configuration

Create or update your `.env` file:

```env
# Storage Configuration
FILEUPLOAD_DRIVER=local
FILEUPLOAD_LOCAL_ROOT=uploads

# Thumbnail Settings
FILEUPLOAD_THUMBNAILS_ENABLED=false
FILEUPLOAD_THUMBNAILS_DIRECTORY=thumbnails

# Validation Settings
FILEUPLOAD_MAX_SIZE=10485760
FILEUPLOAD_ALLOWED_TYPES=jpg,jpeg,png,gif,pdf,doc,docx
```

### 4. Test the Installation

Open the test page in your browser:

```bash
http://your-domain/test-fileupload.php
```

## 📁 Features

### ✅ File Upload

- Multiple file format support
- Size and type validation
- Secure file handling
- Custom directory structure

### ✅ Dynamic Image Transformation

- On-the-fly image resizing
- Multiple transformation modes
- Universal URL support
- No permanent storage required

### ✅ Folder Operations

- Create directories
- Delete directories (with contents)
- List directory contents
- Nested directory support

### ✅ Multiple Storage Drivers

- Local filesystem (default)
- Amazon S3
- Google Cloud Storage
- Azure Blob Storage

## 🎯 Usage Examples

### Basic File Upload

```php
<?php
require_once 'vendor/autoload.php';

use Package\FileUpload\FileUpload;

$uploader = new FileUpload();

// Upload file
$result = $uploader->upload($_FILES['file'], [
    'directory' => 'uploads/documents'
]);

if ($result->isSuccess()) {
    echo "File uploaded: " . $result->getPath();
    echo "File URL: " . $result->getUrl();
} else {
    echo "Upload failed: " . $result->getError();
}
?>
```

### Dynamic Image Transformation

```php
<?php
$uploader = new FileUpload();

// Upload image
$result = $uploader->upload($_FILES['image'], [
    'directory' => 'uploads/gallery'
]);

if ($result->isSuccess()) {
    $originalPath = $result->getPath();

    // Generate different sizes
    $thumbnail = $uploader->getDynamicUrl($originalPath, 150, 150, 'crop');
    $medium = $uploader->getDynamicUrl($originalPath, 400, 300, 'fit');
    $large = $uploader->getDynamicUrl($originalPath, 800, 600, 'fit');

    // Use in HTML
    echo '<img src="' . $thumbnail . '" alt="Thumbnail">';
    echo '<img src="' . $medium . '" alt="Medium">';
    echo '<img src="' . $large . '" alt="Large">';
}
?>
```

### Folder Operations

```php
<?php
$uploader = new FileUpload();

// Create folder
$result = $uploader->createFolder('uploads/new-gallery');
if ($result->isSuccess()) {
    echo "Folder created: " . $result->getPath();
}

// List folder contents
$result = $uploader->listFolder('uploads');
if ($result->isSuccess()) {
    $contents = $result->getContents();
    foreach ($contents as $item) {
        echo $item['name'] . " (" . $item['type'] . ")\n";
    }
}

// Delete folder
$result = $uploader->deleteFolder('uploads/old-gallery');
if ($result->isSuccess()) {
    echo "Folder deleted successfully";
}
?>
```

## 🖼️ Dynamic Image Transformation

### URL Formats

The package supports multiple URL formats:

#### 1. Explicit Mode Format

```
/uploads/directory/WIDTHxHEIGHT/MODE=filename.jpg
```

**Example:** `/uploads/gallery/300x200/fit=image.jpg`

#### 2. Default Mode Format (Cleaner URLs)

```
/uploads/directory/WIDTHxHEIGHT/filename.jpg
```

**Example:** `/uploads/gallery/300x200/image.jpg` (defaults to 'fit' mode)

#### 3. Query Parameter Format

```
/transform.php?path=full/path&w=WIDTH&h=HEIGHT&m=MODE
```

**Example:** `/transform.php?path=uploads/gallery/image.jpg&w=300&h=200&m=fit`

### Transformation Modes

- **fit** (default): Scale to fit within dimensions, maintaining aspect ratio
- **fix**: Crop to exact dimensions from center
- **crop**: Crop to exact dimensions, maintaining aspect ratio
- **fill**: Fill dimensions, may crop to maintain aspect ratio
- **stretch**: Stretch to exact dimensions (may distort)

### Universal Directory Support

Works with unlimited nested directory levels:

```bash
✅ /uploads/300x200/image.jpg                    # Simple
✅ /uploads/gallery/300x200/image.jpg            # 1 level
✅ /uploads/users/john/avatar/300x200/image.jpg  # 3 levels
✅ /uploads/M/A/Y/U/R/300x200/image.png         # 5 levels
✅ /uploads/very/deep/nested/structure/300x200/image.jpg  # Unlimited
```

## 📂 Folder Operations

### Create Folders

```php
// Create single folder
$result = $uploader->createFolder('uploads/products');

// Create nested folders
$result = $uploader->createFolder('uploads/gallery/2024/january');
```

### List Folder Contents

```php
$result = $uploader->listFolder('uploads');
if ($result->isSuccess()) {
    foreach ($result->getContents() as $item) {
        echo $item['name'] . " - " . $item['type'] . "\n";
    }
}
```

### Delete Folders

```php
// Delete empty folder
$result = $uploader->deleteFolder('uploads/empty-folder');

// Delete folder with contents (use with caution)
$result = $uploader->deleteFolder('uploads/old-gallery');
```

## ⚙️ Configuration

### Environment Variables

Copy `.env.example` to `.env` and configure according to your needs:

```bash
cp .env.example .env
```

**Essential Configuration:**

```env
# Storage Driver
FILEUPLOAD_DRIVER=local                    # local, s3, gcs, azure

# Local Storage
FILEUPLOAD_LOCAL_ROOT=uploads              # Root directory for uploads
FILEUPLOAD_LOCAL_URL=/uploads              # Base URL for file access

# File Validation
FILEUPLOAD_MAX_SIZE=10485760              # Max file size in bytes (10MB)
FILEUPLOAD_ALLOWED_TYPES=image/jpeg,image/png,image/gif,application/pdf
FILEUPLOAD_FORBIDDEN_EXTENSIONS=php,exe,bat,sh,js

# Image Processing
FILEUPLOAD_PROCESSING_ENABLED=true        # Enable image processing
FILEUPLOAD_IMAGE_QUALITY=85               # JPEG quality (1-100)

# Security
FILEUPLOAD_VALIDATE_HEADERS=true          # Validate file headers
FILEUPLOAD_SANITIZE_FILENAMES=true        # Sanitize filenames

# Dynamic Transformation
FILEUPLOAD_TRANSFORM_ENABLED=true         # Enable URL transformations
FILEUPLOAD_TRANSFORM_DEFAULT_MODE=fit     # Default transformation mode

# Folder Operations
FILEUPLOAD_FOLDERS_ENABLED=true           # Enable folder operations
FILEUPLOAD_MAX_FOLDER_DEPTH=10            # Maximum folder nesting
```

**Cloud Storage (S3 Example):**

```env
FILEUPLOAD_DRIVER=s3
FILEUPLOAD_S3_BUCKET=your-bucket-name
FILEUPLOAD_S3_REGION=us-east-1
FILEUPLOAD_S3_KEY=your-access-key-id
FILEUPLOAD_S3_SECRET=your-secret-access-key
FILEUPLOAD_S3_VISIBILITY=public
```

**Complete Configuration:**
See `.env.example` for all 100+ available configuration options including:

- Multiple storage drivers (S3, Google Cloud, Azure)
- Advanced security settings
- Performance optimization
- Webhook integration
- Backup configuration
- Custom directory patterns
- And much more...

### Custom Configuration

```php
$uploader = new FileUpload([
    'max_size' => 5 * 1024 * 1024,  // 5MB
    'allowed_types' => ['jpg', 'png', 'gif'],
    'directory' => 'uploads/custom'
]);
```

## 🧪 Testing

### 1. Dependency Check

```bash
http://your-domain/demo-dependency-check.php
```

### 2. Full Feature Test

```bash
http://your-domain/test-fileupload.php
```

### 3. Manual Testing

Test dynamic image transformation:

```bash
# Upload an image first, then test these URLs:
http://your-domain/uploads/300x200/fit=your-image.jpg
http://your-domain/uploads/300x200/your-image.jpg
http://your-domain/transform.php?path=uploads/your-image.jpg&w=300&h=200&m=fit
```

## 🔧 Troubleshooting

### Common Issues

1. **"Transform endpoint not working"**

   - Check if `transform.php` exists in your frontend directory
   - Verify `.htaccess` rules are properly configured
   - Ensure mod_rewrite is enabled

2. **"Permission denied" errors**

   - Make sure uploads directory is writable: `chmod 755 uploads`
   - Check PHP user permissions

3. **"GD extension not found"**

   - Install GD extension: `sudo apt-get install php-gd` (Ubuntu/Debian)
   - Restart web server after installation

4. **"File too large" errors**
   - Check `upload_max_filesize` and `post_max_size` in php.ini
   - Update `FILEUPLOAD_MAX_SIZE` in .env file

### Debug Mode

Enable debug mode for detailed error information:

```php
$uploader = new FileUpload(['debug' => true]);
```

## 📚 API Reference

### FileUpload Class

#### Upload Methods

- `upload($file, $options = [])` - Upload a file
- `uploadMultiple($files, $options = [])` - Upload multiple files

#### Folder Methods

- `createFolder($path)` - Create a directory
- `deleteFolder($path)` - Delete a directory
- `listFolder($path)` - List directory contents

#### URL Generation

- `getDynamicUrl($path, $width, $height, $mode, $useQuery, $useDefault)` - Generate transformation URL

#### Configuration

- `setConfig($config)` - Update configuration
- `getConfig()` - Get current configuration

### Result Classes

#### UploadResult

- `isSuccess()` - Check if upload succeeded
- `getPath()` - Get file path
- `getUrl()` - Get file URL
- `getError()` - Get error message

#### FolderResult

- `isSuccess()` - Check if operation succeeded
- `getPath()` - Get folder path
- `getContents()` - Get folder contents (for list operations)
- `getMessage()` - Get result message

## 📄 License

MIT License - see LICENSE file for details.

## 🤝 Support

For issues and questions:

1. Check the troubleshooting section above
2. Run the dependency check tool
3. Test with the provided test pages
4. Review the configuration settings
