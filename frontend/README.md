## For Ubuntu

```
sudo nano /etc/apache2/sites-available/frontcms.local.conf
```

## For Mac
```
sudo nano /opt/homebrew/etc/httpd/vhosts/frontcms.local.conf
```

## Virtual Host

```
    <VirtualHost *:80>
        ServerName frontcms.local
        DocumentRoot "/home/<USER>/projects/cmsfront/frontend/public"

        <Directory "/home/<USER>/projects/cmsfront/frontend/public">
            AllowOverride All
            Require all granted
        </Directory>

        ErrorLog ${APACHE_LOG_DIR}/frontcms_error.log
        CustomLog ${APACHE_LOG_DIR}/frontcms_access.log combined
    </VirtualHost>
```

## Enable the Site

### Ubuntu :

```
sudo a2ensite frontcms.local.conf
sudo systemctl reload apache2
```

### Mac : 

```
sudo apachectl restart
```

## Add Local Host Mapping

```
sudo nano /etc/hosts
127.0.0.1   frontcms.local
```


## Restart Apache

### Ubuntu

```
sudo systemctl restart apache2
```

### macOS

```
sudo apachectl restart
```

## Provide permission

```
sudo chmod -R 775 <cmsfront/frontend>
```

## Test in Browser

```
http://frontcms.local
```
