<?php

require_once __DIR__ . '/vendor/autoload.php';

// Load environment variables
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
$dotenv->load();

echo "=== COMPLETE MEDIA MODULE FUNCTIONALITY TEST ===\n\n";

// Test configuration
$baseUrl = 'http://frontcmsdev.local';
$testResults = [];
$testCount = 0;
$passCount = 0;

function runTest($testName, $testFunction)
{
    global $testResults, $testCount, $passCount;
    $testCount++;

    echo "--- Test $testCount: $testName ---\n";

    try {
        $result = $testFunction();
        if ($result['success']) {
            echo "✓ PASS: " . $result['message'] . "\n";
            $passCount++;
            $testResults[] = ['name' => $testName, 'status' => 'PASS', 'message' => $result['message']];
        } else {
            echo "✗ FAIL: " . $result['message'] . "\n";
            $testResults[] = ['name' => $testName, 'status' => 'FAIL', 'message' => $result['message']];
        }
    } catch (Exception $e) {
        echo "✗ ERROR: " . $e->getMessage() . "\n";
        $testResults[] = ['name' => $testName, 'status' => 'ERROR', 'message' => $e->getMessage()];
    }

    echo "\n";
}

// Helper function for HTTP requests
function makeRequest($url, $method = 'GET', $data = null, $headers = [])
{
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);

    $defaultHeaders = ['X-Requested-With: XMLHttpRequest'];
    curl_setopt($ch, CURLOPT_HTTPHEADER, array_merge($defaultHeaders, $headers));

    if ($method === 'POST') {
        curl_setopt($ch, CURLOPT_POST, true);
        if ($data) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        }
    }

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    return ['response' => $response, 'http_code' => $httpCode];
}

// Test 1: Environment Configuration
runTest("Environment Configuration Loading", function () {
    $requiredVars = [
        'FILEUPLOAD_ALLOWED_TYPES',
        'FILEUPLOAD_ALLOWED_EXTENSIONS',
        'FILEUPLOAD_MAX_SIZE',
        'FILEUPLOAD_LOCAL_ROOT',
        'FILEUPLOAD_LOCAL_URL'
    ];

    $missing = [];
    foreach ($requiredVars as $var) {
        if (empty($_ENV[$var])) {
            $missing[] = $var;
        }
    }

    if (empty($missing)) {
        return ['success' => true, 'message' => 'All required environment variables are loaded'];
    } else {
        return ['success' => false, 'message' => 'Missing environment variables: ' . implode(', ', $missing)];
    }
});

// Test 2: Media Module Access
runTest("Media Module Page Access", function () use ($baseUrl) {
    $result = makeRequest("$baseUrl/media");

    if ($result['http_code'] === 200 || $result['http_code'] === 302) {
        return ['success' => true, 'message' => "Media module accessible (HTTP {$result['http_code']})"];
    } else {
        return ['success' => false, 'message' => "Media module not accessible (HTTP {$result['http_code']})"];
    }
});

// Test 3: Create Test Folder
$testFolderName = 'complete-test-' . date('H-i-s');
$testFolderPath = '';

runTest("Folder Creation", function () use ($baseUrl, $testFolderName, &$testFolderPath) {
    $data = json_encode(['name' => $testFolderName, 'parent' => '']);
    $result = makeRequest("$baseUrl/media/create-folder", 'POST', $data, ['Content-Type: application/json']);

    $response = json_decode($result['response'], true);

    if ($response && $response['success']) {
        $testFolderPath = $response['data']['path'];
        return ['success' => true, 'message' => "Folder created: {$response['data']['name']}"];
    } else {
        $error = $response['message'] ?? 'Unknown error';
        return ['success' => false, 'message' => "Folder creation failed: $error"];
    }
});

// Test 4: File Upload - PDF
runTest("PDF File Upload", function () use ($baseUrl, $testFolderPath) {
    // Create test PDF
    $pdfContent = "%PDF-1.4\n1 0 obj\n<<\n/Type /Catalog\n/Pages 2 0 R\n>>\nendobj\n2 0 obj\n<<\n/Type /Pages\n/Kids [3 0 R]\n/Count 1\n>>\nendobj\n3 0 obj\n<<\n/Type /Page\n/Parent 2 0 R\n/MediaBox [0 0 612 792]\n/Contents 4 0 R\n>>\nendobj\n4 0 obj\n<<\n/Length 44\n>>\nstream\nBT\n/F1 12 Tf\n100 700 Td\n(Test PDF Document) Tj\nET\nendstream\nendobj\nxref\n0 5\n0000000000 65535 f \n0000000009 00000 n \n0000000058 00000 n \n0000000115 00000 n \n0000000206 00000 n \ntrailer\n<<\n/Size 5\n/Root 1 0 R\n>>\nstartxref\n300\n%%EOF";

    $tempFile = '/tmp/test-complete.pdf';
    file_put_contents($tempFile, $pdfContent);

    $postData = [
        'file' => new CURLFile($tempFile, 'application/pdf', 'test-complete.pdf'),
        'folder' => $testFolderPath
    ];

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, "$baseUrl/media/upload");
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
    curl_setopt($ch, CURLOPT_HTTPHEADER, ['X-Requested-With: XMLHttpRequest']);

    $response = curl_exec($ch);
    curl_close($ch);
    @unlink($tempFile);

    $result = json_decode($response, true);

    if ($result && $result['success']) {
        return ['success' => true, 'message' => "PDF uploaded: {$result['data']['name']}"];
    } else {
        $error = $result['message'] ?? 'Unknown error';
        return ['success' => false, 'message' => "PDF upload failed: $error"];
    }
});

// Test 5: File Upload - Image
runTest("Image File Upload", function () use ($baseUrl, $testFolderPath) {
    // Create test PNG
    $image = imagecreate(100, 100);
    $backgroundColor = imagecolorallocate($image, 255, 0, 0);
    $textColor = imagecolorallocate($image, 255, 255, 255);
    imagestring($image, 5, 20, 40, 'TEST', $textColor);

    $tempFile = '/tmp/test-complete.png';
    imagepng($image, $tempFile);
    imagedestroy($image);

    $postData = [
        'file' => new CURLFile($tempFile, 'image/png', 'test-complete.png'),
        'folder' => $testFolderPath
    ];

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, "$baseUrl/media/upload");
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
    curl_setopt($ch, CURLOPT_HTTPHEADER, ['X-Requested-With: XMLHttpRequest']);

    $response = curl_exec($ch);
    curl_close($ch);
    @unlink($tempFile);

    $result = json_decode($response, true);

    if ($result && $result['success']) {
        return ['success' => true, 'message' => "Image uploaded: {$result['data']['name']}"];
    } else {
        $error = $result['message'] ?? 'Unknown error';
        return ['success' => false, 'message' => "Image upload failed: $error"];
    }
});

// Test 6: File Upload - Text
runTest("Text File Upload", function () use ($baseUrl, $testFolderPath) {
    $textContent = "This is a comprehensive test file for the media module.\n\nIt contains multiple lines to test text file functionality.\n\nCreated at: " . date('Y-m-d H:i:s');

    $tempFile = '/tmp/test-complete.txt';
    file_put_contents($tempFile, $textContent);

    $postData = [
        'file' => new CURLFile($tempFile, 'text/plain', 'test-complete.txt'),
        'folder' => $testFolderPath
    ];

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, "$baseUrl/media/upload");
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
    curl_setopt($ch, CURLOPT_HTTPHEADER, ['X-Requested-With: XMLHttpRequest']);

    $response = curl_exec($ch);
    curl_close($ch);
    @unlink($tempFile);

    $result = json_decode($response, true);

    if ($result && $result['success']) {
        return ['success' => true, 'message' => "Text file uploaded: {$result['data']['name']}"];
    } else {
        $error = $result['message'] ?? 'Unknown error';
        return ['success' => false, 'message' => "Text upload failed: $error"];
    }
});

// Test 7: Invalid File Upload (Security Test)
runTest("Invalid File Upload (Security)", function () use ($baseUrl, $testFolderPath) {
    $phpContent = '<?php echo "This should be rejected for security"; ?>';

    $tempFile = '/tmp/test-malicious.php';
    file_put_contents($tempFile, $phpContent);

    $postData = [
        'file' => new CURLFile($tempFile, 'application/x-php', 'test-malicious.php'),
        'folder' => $testFolderPath
    ];

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, "$baseUrl/media/upload");
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
    curl_setopt($ch, CURLOPT_HTTPHEADER, ['X-Requested-With: XMLHttpRequest']);

    $response = curl_exec($ch);
    curl_close($ch);
    @unlink($tempFile);

    $result = json_decode($response, true);

    if ($result && !$result['success']) {
        return ['success' => true, 'message' => "Security test passed: PHP file correctly rejected"];
    } else {
        return ['success' => false, 'message' => "Security test failed: PHP file was accepted"];
    }
});

// Test 8: Media Listing
runTest("Media Listing", function () use ($baseUrl, $testFolderPath) {
    $result = makeRequest("$baseUrl/media?action=list&folder=" . urlencode($testFolderPath));

    $response = json_decode($result['response'], true);

    if ($response && $response['success']) {
        $fileCount = count($response['data']['files']);
        return ['success' => true, 'message' => "Media listing successful: $fileCount files found"];
    } else {
        $error = $response['message'] ?? 'Unknown error';
        return ['success' => false, 'message' => "Media listing failed: $error"];
    }
});

// Test 9: File Accessibility
runTest("File Web Accessibility", function () use ($baseUrl, $testFolderPath) {
    // Get the file list first
    $listResult = makeRequest("$baseUrl/media?action=list&folder=" . urlencode($testFolderPath));
    $listResponse = json_decode($listResult['response'], true);

    if (!$listResponse || !$listResponse['success'] || empty($listResponse['data']['files'])) {
        return ['success' => false, 'message' => 'No files found to test accessibility'];
    }

    $testFile = $listResponse['data']['files'][0];
    $fileUrl = $baseUrl . $testFile['url'];

    $result = makeRequest($fileUrl);

    if ($result['http_code'] === 200) {
        return ['success' => true, 'message' => "File accessible: {$testFile['name']}"];
    } else {
        return ['success' => false, 'message' => "File not accessible (HTTP {$result['http_code']}): {$testFile['name']}"];
    }
});

// Test 10: Dynamic Image Transformation
runTest("Dynamic Image Transformation", function () use ($baseUrl, $testFolderPath) {
    // Get the file list to find an image
    $listResult = makeRequest("$baseUrl/media?action=list&folder=" . urlencode($testFolderPath));
    $listResponse = json_decode($listResult['response'], true);

    if (!$listResponse || !$listResponse['success']) {
        return ['success' => false, 'message' => 'Could not get file list'];
    }

    $imageFile = null;
    foreach ($listResponse['data']['files'] as $file) {
        if (in_array($file['extension'], ['png', 'jpg', 'jpeg', 'gif'])) {
            $imageFile = $file;
            break;
        }
    }

    if (!$imageFile) {
        return ['success' => false, 'message' => 'No image file found for transformation test'];
    }

    // Test dynamic transformation URL
    $transformUrl = $baseUrl . '/uploads/media/' . $testFolderPath . '/200x200/' . $imageFile['name'];
    $result = makeRequest($transformUrl);

    if ($result['http_code'] === 200) {
        return ['success' => true, 'message' => "Dynamic transformation working: {$imageFile['name']}"];
    } else {
        return ['success' => false, 'message' => "Dynamic transformation failed (HTTP {$result['http_code']})"];
    }
});

// Test 11: File Counting Accuracy
runTest("File Counting Accuracy", function () use ($baseUrl) {
    $result = makeRequest("$baseUrl/media?action=list&folder=");
    $response = json_decode($result['response'], true);

    if (!$response || !$response['success']) {
        return ['success' => false, 'message' => 'Could not get folder list'];
    }

    $testFolder = null;
    foreach ($response['data']['folders'] as $folder) {
        if (strpos($folder['name'], 'complete-test-') === 0) {
            $testFolder = $folder;
            break;
        }
    }

    if (!$testFolder) {
        return ['success' => false, 'message' => 'Test folder not found in listing'];
    }

    // Get actual files in folder
    $folderResult = makeRequest("$baseUrl/media?action=list&folder=" . urlencode($testFolder['path']));
    $folderResponse = json_decode($folderResult['response'], true);

    if (!$folderResponse || !$folderResponse['success']) {
        return ['success' => false, 'message' => 'Could not get folder contents'];
    }

    $actualFileCount = count($folderResponse['data']['files']);
    $reportedFileCount = $testFolder['file_count'];

    if ($actualFileCount === $reportedFileCount) {
        return ['success' => true, 'message' => "File count accurate: $actualFileCount files"];
    } else {
        return ['success' => false, 'message' => "File count mismatch: reported $reportedFileCount, actual $actualFileCount"];
    }
});

// Test 12: Thumbnail Generation (if enabled)
runTest("Thumbnail Generation", function () use ($baseUrl, $testFolderPath) {
    $thumbnailsEnabled = $_ENV['FILEUPLOAD_THUMBNAILS_ENABLED'] ?? 'false';

    if (strtolower($thumbnailsEnabled) !== 'true') {
        return ['success' => true, 'message' => 'Thumbnails disabled in environment - test skipped'];
    }

    // Check if thumbnails were generated for uploaded image
    $uploadDir = $_ENV['FILEUPLOAD_LOCAL_ROOT'] ?? '';
    if (empty($uploadDir)) {
        return ['success' => false, 'message' => 'Upload directory not configured'];
    }

    $folderPath = $uploadDir . '/' . $testFolderPath;
    if (!is_dir($folderPath)) {
        return ['success' => false, 'message' => 'Test folder not found on disk'];
    }

    $thumbnailFound = false;
    $files = scandir($folderPath);
    foreach ($files as $file) {
        if (strpos($file, '_small.') !== false || strpos($file, '_medium.') !== false || strpos($file, '_large.') !== false) {
            $thumbnailFound = true;
            break;
        }
    }

    if ($thumbnailFound) {
        return ['success' => true, 'message' => 'Thumbnails generated successfully'];
    } else {
        return ['success' => false, 'message' => 'No thumbnails found (may be normal if no images uploaded)'];
    }
});

// Test 13: File Rename
$renamedFileName = '';
runTest("File Rename", function () use ($baseUrl, $testFolderPath, &$renamedFileName) {
    // Get a file to rename
    $listResult = makeRequest("$baseUrl/media?action=list&folder=" . urlencode($testFolderPath));
    $listResponse = json_decode($listResult['response'], true);

    if (!$listResponse || !$listResponse['success'] || empty($listResponse['data']['files'])) {
        return ['success' => false, 'message' => 'No files found to rename'];
    }

    $fileToRename = $listResponse['data']['files'][0];
    $oldName = pathinfo($fileToRename['name'], PATHINFO_FILENAME);
    $extension = pathinfo($fileToRename['name'], PATHINFO_EXTENSION);
    $newName = $oldName . '-renamed';
    $renamedFileName = $newName . '.' . $extension;

    $data = json_encode([
        'path' => $fileToRename['path'],
        'newName' => $newName
    ]);

    $result = makeRequest("$baseUrl/media/rename", 'POST', $data, ['Content-Type: application/json']);
    $response = json_decode($result['response'], true);

    if ($response && $response['success']) {
        return ['success' => true, 'message' => "File renamed: {$fileToRename['name']} → $renamedFileName"];
    } else {
        $error = $response['message'] ?? 'Unknown error';
        return ['success' => false, 'message' => "File rename failed: $error"];
    }
});

// Test 14: Environment Variable Validation
runTest("Environment Variable Validation", function () {
    $allowedTypes = explode(',', $_ENV['FILEUPLOAD_ALLOWED_TYPES'] ?? '');
    $allowedExtensions = explode(',', $_ENV['FILEUPLOAD_ALLOWED_EXTENSIONS'] ?? '');
    $maxSize = (int)($_ENV['FILEUPLOAD_MAX_SIZE'] ?? 0);

    $issues = [];

    if (count($allowedTypes) < 3) {
        $issues[] = 'Too few allowed file types';
    }

    if (count($allowedExtensions) < 3) {
        $issues[] = 'Too few allowed extensions';
    }

    if ($maxSize < 1024 * 1024) { // Less than 1MB
        $issues[] = 'Max file size too small';
    }

    if (empty($issues)) {
        return ['success' => true, 'message' => 'Environment validation passed'];
    } else {
        return ['success' => false, 'message' => 'Environment issues: ' . implode(', ', $issues)];
    }
});

// Test 15: Cleanup Test
runTest("Cleanup Test Files", function () use ($baseUrl, $testFolderPath) {
    // Delete the test folder and all its contents
    $data = json_encode(['path' => $testFolderPath]);
    $result = makeRequest("$baseUrl/media/delete", 'POST', $data, ['Content-Type: application/json']);
    $response = json_decode($result['response'], true);

    if ($response && $response['success']) {
        return ['success' => true, 'message' => 'Test files cleaned up successfully'];
    } else {
        $error = $response['message'] ?? 'Unknown error';
        return ['success' => false, 'message' => "Cleanup failed: $error (manual cleanup may be needed)"];
    }
});

// Generate Test Report
echo "\n" . str_repeat("=", 80) . "\n";
echo "COMPREHENSIVE TEST REPORT\n";
echo str_repeat("=", 80) . "\n\n";

echo "Environment Configuration:\n";
echo "- Allowed Types: " . ($_ENV['FILEUPLOAD_ALLOWED_TYPES'] ?? 'NOT SET') . "\n";
echo "- Allowed Extensions: " . ($_ENV['FILEUPLOAD_ALLOWED_EXTENSIONS'] ?? 'NOT SET') . "\n";
echo "- Max File Size: " . number_format((int)($_ENV['FILEUPLOAD_MAX_SIZE'] ?? 0) / 1024 / 1024, 1) . " MB\n";
echo "- Thumbnails Enabled: " . ($_ENV['FILEUPLOAD_THUMBNAILS_ENABLED'] ?? 'false') . "\n";
echo "- Upload Root: " . ($_ENV['FILEUPLOAD_LOCAL_ROOT'] ?? 'NOT SET') . "\n";
echo "- Upload URL: " . ($_ENV['FILEUPLOAD_LOCAL_URL'] ?? 'NOT SET') . "\n\n";

echo "Test Results Summary:\n";
echo "- Total Tests: $testCount\n";
echo "- Passed: $passCount\n";
echo "- Failed: " . ($testCount - $passCount) . "\n";
echo "- Success Rate: " . round(($passCount / $testCount) * 100, 1) . "%\n\n";

echo "Detailed Results:\n";
foreach ($testResults as $i => $test) {
    $status = $test['status'] === 'PASS' ? '✓' : '✗';
    echo sprintf("%2d. %s %s - %s\n", $i + 1, $status, $test['name'], $test['message']);
}

echo "\n" . str_repeat("=", 80) . "\n";

if ($passCount === $testCount) {
    echo "🎉 ALL TESTS PASSED! Media Module is fully functional.\n";
} else {
    echo "⚠️  Some tests failed. Please review the results above.\n";
}

echo str_repeat("=", 80) . "\n";
