<?php

use Core\Router;
use Core\Response;
use Modules\Auth\Controllers\LoginController;

$router->get('/', [LoginController::class, 'showLoginForm']);

// Greeting route
$router->get('greeting', function () {
    Response::json([
        'message' => "Hello,  Welcome to the Front CMS.",
    ]);
});

// Health check route
$router->get('health', function () {
    Response::json([
        'status' => 'OK',
        'uptime' => time() - $_SERVER['REQUEST_TIME'],
        'timestamp' => date('c'),
        'version' => '1.0.0',
        'debug' => config('debug')
    ]);
});

/**
 * @var mixed
 * Module routes
 */
$modulesPath = __DIR__ . '/../modules';
$modules = array_diff(scandir($modulesPath), ['.', '..']);

foreach ($modules as $module) {
    $routesFile = "{$modulesPath}/{$module}/routes.php";
    if (is_dir("{$modulesPath}/{$module}") && file_exists($routesFile)) {
        require $routesFile;
    }
}