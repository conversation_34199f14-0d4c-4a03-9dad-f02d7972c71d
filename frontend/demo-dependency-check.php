<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FileUpload Package - Dependency Check</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 1200px; margin: 0 auto; padding: 20px; background: #f5f5f5; }
        .container { background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { background: #d4edda; color: #155724; padding: 15px; border-radius: 4px; margin: 10px 0; }
        .warning { background: #fff3cd; color: #856404; padding: 15px; border-radius: 4px; margin: 10px 0; }
        .error { background: #f8d7da; color: #721c24; padding: 15px; border-radius: 4px; margin: 10px 0; }
        .info { background: #e8f4f8; padding: 20px; border-radius: 8px; margin: 20px 0; }
        .check-item { display: flex; align-items: center; padding: 10px; border-bottom: 1px solid #eee; }
        .check-icon { width: 30px; height: 30px; margin-right: 15px; text-align: center; line-height: 30px; border-radius: 50%; }
        .check-icon.success { background: #d4edda; color: #155724; }
        .check-icon.error { background: #f8d7da; color: #721c24; }
        .check-icon.warning { background: #fff3cd; color: #856404; }
        .version-info { font-family: monospace; background: #f8f9fa; padding: 5px 10px; border-radius: 3px; }
        .requirement { margin: 20px 0; }
        .requirement h3 { margin: 0 0 10px 0; color: #333; }
        .feature-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin: 20px 0; }
        .feature-card { border: 1px solid #ddd; padding: 20px; border-radius: 8px; background: #f9f9f9; }
        .feature-card h4 { margin: 0 0 10px 0; color: #333; }
        .demo-section { margin: 30px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 FileUpload Package - System Check</h1>
        <p><strong>Comprehensive dependency and feature verification for the FileUpload package</strong></p>

        <?php
        // System requirements check
        $checks = [];
        $overallStatus = true;

        // PHP Version Check
        $phpVersion = PHP_VERSION;
        $phpRequired = '8.0.0';
        $phpOk = version_compare($phpVersion, $phpRequired, '>=');
        $checks[] = [
            'name' => 'PHP Version',
            'status' => $phpOk,
            'message' => $phpOk ? "PHP $phpVersion (✓ >= $phpRequired)" : "PHP $phpVersion (✗ < $phpRequired required)",
            'required' => true
        ];
        if (!$phpOk) $overallStatus = false;

        // GD Extension Check
        $gdLoaded = extension_loaded('gd');
        $checks[] = [
            'name' => 'GD Extension',
            'status' => $gdLoaded,
            'message' => $gdLoaded ? 'GD extension is loaded' : 'GD extension is NOT loaded',
            'required' => true
        ];
        if (!$gdLoaded) $overallStatus = false;

        // Composer Check
        $composerExists = file_exists('vendor/autoload.php');
        $checks[] = [
            'name' => 'Composer Autoload',
            'status' => $composerExists,
            'message' => $composerExists ? 'Composer autoload found' : 'Composer autoload NOT found',
            'required' => true
        ];
        if (!$composerExists) $overallStatus = false;

        // Package Check
        $packageExists = file_exists('package/FileUpload/src/FileUpload.php');
        $checks[] = [
            'name' => 'FileUpload Package',
            'status' => $packageExists,
            'message' => $packageExists ? 'FileUpload package found' : 'FileUpload package NOT found',
            'required' => true
        ];
        if (!$packageExists) $overallStatus = false;

        // Transform File Check
        $transformExists = file_exists('transform.php');
        $checks[] = [
            'name' => 'Transform Endpoint',
            'status' => $transformExists,
            'message' => $transformExists ? 'transform.php endpoint found' : 'transform.php endpoint NOT found',
            'required' => true
        ];
        if (!$transformExists) $overallStatus = false;

        // Uploads Directory Check
        $uploadsExists = is_dir('uploads');
        $uploadsWritable = $uploadsExists && is_writable('uploads');
        $checks[] = [
            'name' => 'Uploads Directory',
            'status' => $uploadsWritable,
            'message' => $uploadsWritable ? 'uploads/ directory exists and is writable' : 
                        ($uploadsExists ? 'uploads/ directory exists but is NOT writable' : 'uploads/ directory does NOT exist'),
            'required' => true
        ];
        if (!$uploadsWritable) $overallStatus = false;

        // .htaccess Check
        $htaccessExists = file_exists('.htaccess');
        $checks[] = [
            'name' => '.htaccess File',
            'status' => $htaccessExists,
            'message' => $htaccessExists ? '.htaccess file found' : '.htaccess file NOT found',
            'required' => false
        ];

        // Environment File Check
        $envExists = file_exists('.env');
        $checks[] = [
            'name' => 'Environment File',
            'status' => $envExists,
            'message' => $envExists ? '.env file found' : '.env file NOT found',
            'required' => false
        ];

        // Memory Limit Check
        $memoryLimit = ini_get('memory_limit');
        $memoryBytes = $memoryLimit === '-1' ? PHP_INT_MAX : (int)$memoryLimit * 1024 * 1024;
        $memoryOk = $memoryBytes >= 128 * 1024 * 1024; // 128MB minimum
        $checks[] = [
            'name' => 'Memory Limit',
            'status' => $memoryOk,
            'message' => "Memory limit: $memoryLimit " . ($memoryOk ? '(✓ >= 128M)' : '(⚠ < 128M recommended)'),
            'required' => false
        ];

        // File Upload Settings
        $uploadMaxFilesize = ini_get('upload_max_filesize');
        $postMaxSize = ini_get('post_max_size');
        $checks[] = [
            'name' => 'Upload Settings',
            'status' => true,
            'message' => "Max file size: $uploadMaxFilesize, Post max size: $postMaxSize",
            'required' => false
        ];
        ?>

        <!-- Overall Status -->
        <?php if ($overallStatus): ?>
            <div class="success">
                <h3>✅ System Ready!</h3>
                <p>All required dependencies are satisfied. The FileUpload package is ready to use.</p>
            </div>
        <?php else: ?>
            <div class="error">
                <h3>❌ System Issues Detected</h3>
                <p>Some required dependencies are missing. Please resolve the issues below before using the package.</p>
            </div>
        <?php endif; ?>

        <!-- Detailed Checks -->
        <div class="requirement">
            <h2>📋 System Requirements</h2>
            <?php foreach ($checks as $check): ?>
                <div class="check-item">
                    <div class="check-icon <?php echo $check['status'] ? 'success' : ($check['required'] ? 'error' : 'warning'); ?>">
                        <?php echo $check['status'] ? '✓' : ($check['required'] ? '✗' : '⚠'); ?>
                    </div>
                    <div>
                        <strong><?php echo htmlspecialchars($check['name']); ?></strong>
                        <?php if ($check['required']): ?>
                            <span style="color: #dc3545; font-size: 12px;">(Required)</span>
                        <?php endif; ?>
                        <br>
                        <span class="version-info"><?php echo htmlspecialchars($check['message']); ?></span>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>

        <!-- Feature Demonstration -->
        <?php if ($overallStatus): ?>
            <div class="demo-section">
                <h2>🎯 Package Features</h2>
                <div class="feature-grid">
                    <div class="feature-card">
                        <h4>📁 File Upload</h4>
                        <p>Upload files with validation and multiple storage drivers support.</p>
                        <a href="test-fileupload.php" target="_blank">→ Test File Upload</a>
                    </div>
                    
                    <div class="feature-card">
                        <h4>🖼️ Dynamic Image Transform</h4>
                        <p>On-the-fly image resizing and transformation via URL parameters.</p>
                        <code>/uploads/300x200/fit=image.jpg</code>
                    </div>
                    
                    <div class="feature-card">
                        <h4>📂 Folder Operations</h4>
                        <p>Create, delete, and manage folders independently of file uploads.</p>
                        <a href="test-fileupload.php#folders" target="_blank">→ Test Folder Operations</a>
                    </div>
                    
                    <div class="feature-card">
                        <h4>🔧 Multiple Storage Drivers</h4>
                        <p>Local filesystem, Amazon S3, Google Cloud, Azure Blob Storage.</p>
                        <span class="version-info">Currently: Local Storage</span>
                    </div>
                </div>
            </div>

            <!-- Quick Test -->
            <div class="info">
                <h3>🧪 Quick Package Test</h3>
                <?php
                try {
                    require_once 'vendor/autoload.php';
                    $uploader = new \Package\FileUpload\FileUpload();
                    echo '<div class="success">✅ FileUpload package loaded successfully!</div>';
                    
                    // Test URL generation
                    $testUrl = $uploader->getDynamicUrl('uploads/test.jpg', 300, 200, 'fit');
                    echo '<p><strong>Sample dynamic URL:</strong> <code>' . htmlspecialchars($testUrl) . '</code></p>';
                    
                } catch (\Exception $e) {
                    echo '<div class="error">❌ Package loading failed: ' . htmlspecialchars($e->getMessage()) . '</div>';
                }
                ?>
            </div>
        <?php endif; ?>

        <!-- Next Steps -->
        <div class="info">
            <h3>📚 Next Steps</h3>
            <ol>
                <li><strong>Read Documentation:</strong> Check <code>README.md</code> for detailed usage instructions</li>
                <li><strong>Test Upload:</strong> Use <a href="test-fileupload.php">test-fileupload.php</a> to test file uploads</li>
                <li><strong>Configure Storage:</strong> Update <code>.env</code> file for different storage drivers</li>
                <li><strong>Customize Settings:</strong> Modify <code>config/fileupload.php</code> for your needs</li>
                <li><strong>Integration:</strong> Follow the usage examples in the documentation</li>
            </ol>
        </div>

        <!-- System Information -->
        <div class="demo-section">
            <h3>💻 System Information</h3>
            <div style="font-family: monospace; background: #f8f9fa; padding: 15px; border-radius: 4px;">
                <strong>PHP Version:</strong> <?php echo PHP_VERSION; ?><br>
                <strong>Server:</strong> <?php echo $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown'; ?><br>
                <strong>Document Root:</strong> <?php echo $_SERVER['DOCUMENT_ROOT'] ?? 'Unknown'; ?><br>
                <strong>Current Directory:</strong> <?php echo __DIR__; ?><br>
                <strong>Memory Limit:</strong> <?php echo ini_get('memory_limit'); ?><br>
                <strong>Max Execution Time:</strong> <?php echo ini_get('max_execution_time'); ?>s<br>
                <strong>Upload Max Filesize:</strong> <?php echo ini_get('upload_max_filesize'); ?><br>
                <strong>Post Max Size:</strong> <?php echo ini_get('post_max_size'); ?>
            </div>
        </div>
    </div>
</body>
</html>
