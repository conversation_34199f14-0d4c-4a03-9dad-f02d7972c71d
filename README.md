# CMS Frontend Project

A modern content management system built with Laravel 12 backend and Vite + Tailwind CSS frontend.

## 🚀 Technologies

- **Backend**: <PERSON><PERSON> 12
- **Frontend**: Tailwind CSS, Flowbite
- **Database**: MySQL in Laravel
- **Authentication**: Laravel Sanctum

## 📋 Prerequisites

- PHP 8.2+
- Composer 2.0+
- MySQL 8.0+

## 🛠️ Installation

### 1. Clone the repository

```bash
git clone https://gitlab.indianic.com/vipinsarathe/cmsfront.git
cd cmsfront
```

### 2. Backend Setup

```bash
# Navigate to backend directory
cd backend

# Install PHP dependencies
composer install

# Copy environment file and generate application key
cp .env.example .env
php artisan key:generate

# Configure your database in the .env file
# DB_CONNECTION=mysql
# DB_HOST=127.0.0.1
# DB_PORT=3306
# DB_DATABASE=your_database
# DB_USERNAME=your_username
# DB_PASSWORD=your_password

# Run database migrations and seeders
php artisan migrate --seed

# Generate storage link
php artisan storage:link

# Start the local development server
php artisan serve
```

### 3. Frontend Setup

```bash
# Navigate to frontend directory
# Install PHP dependencies
composer install

# Copy environment file and generate application key
cp .env.example .env
```

### 4. Environment Variables

Create a `.env` file in the root of both `backend` and `frontend` directories if they don't exist. Use the respective `.env.example` files as a reference.

## 🏗️ Project Structure

```
cmsfront/
├── backend/              # Laravel backend
│   ├── app/             # Application code
│   ├── config/          # Configuration files
│   ├── database/        # Migrations and seeders
│   ├── routes/          # API routes
│   └── ...
└── frontend/            # Vite + Tailwind frontend
    ├── app/                        # Optional app-wide services or helpers
    │   └── Helpers/
    │       └── ResponseHelper.php
    │
    ├── config/                     # Config files
    │   └── app.php
    │
    ├── core/                       # Core framework classes
    │   ├── Controller.php
    │   ├── Middleware.php
    │   ├── Request.php
    │   ├── Response.php
    │   ├── Router.php
    │   ├── ServiceContainer.php
    │   ├── View.php
    │   └── ErrorHandler.php
    │
    ├── middleware/      # Global and route middleware
    │   └── AuthMiddleware.php
    │
    ├── modules/        # Feature modules
    │   ├── Auth/
    │   │   ├── Controllers/
    │   │   │   └── AuthController.php
    │   │   ├── Services/
    │   │   │   └── AuthService.php
    │   │   ├── Views/
    │   │   │   └── login.php
    │   │   └── routes.php
    │   │
    │   └── User/
    │       ├── Controllers/
    │       │   └── UserController.php
    │       ├── Services/
    │       │   └── UserService.php
    │       ├── Views/
    │       │   ├── index.php
    │       │   └── partials/
    │       │       ├── header.php
    │       │       └── footer.php
    │       └── routes.php
    │
    ├── public/        # Publicly accessible directory
    │   ├── assets/    # JS/CSS for AJAX, jQuery etc.
    │   └── index.php  # Entry point
    │
    ├── routes/        # Central route registry
    │   └── web.php
    │
    ├── storage/        # Logs, sessions (if needed), cache
    │   └── logs/
    │       └── app.log
    │
    ├── .htaccess       # For Apache (public folder rewrite)
    ├── composer.json   # Composer config
    └── .env.example
```
