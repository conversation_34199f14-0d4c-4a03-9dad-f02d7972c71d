<?php

use App\Http\Controllers\ImageUploadController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

use App\Http\Controllers\Auth\AdminAuthController;
use App\Http\Controllers\Auth\UserAuthController;
use App\Http\Controllers\AdminController;
use App\Http\Controllers\BlockCategoryController;
use App\Http\Controllers\GlobalAssetsController;
use App\Http\Controllers\TemplateController;
use App\Http\Controllers\UserController;
use App\Http\Middleware\ApiErrorHandler;
use App\Http\Middleware\ApiRateLimiter;
use App\Http\Middleware\ApiCache;

// API Health Check
Route::get('/health', function () {
    return response()->json([
        'status' => 'healthy',
        'version' => 'v1',
        'timestamp' => now()->toISOString(),
        'environment' => app()->environment()
    ]);
});

// API v1 Routes
Route::group([
    'prefix' => 'v1',
    'middleware' => [ApiErrorHandler::class, ApiRateLimiter::class . ':1000:60']
], function () {

    Route::group(['prefix' => 'cms'], function () {

        // Authentication Routes (with stricter rate limiting)
        Route::middleware(ApiRateLimiter::class . ':5:1')->group(function () {
            Route::post('/user/register', [UserAuthController::class, 'register']);
            Route::post('/user/login', [UserAuthController::class, 'login']);
            Route::post('/admin/register', [AdminAuthController::class, 'register']);
            Route::post('/admin/login', [AdminAuthController::class, 'login']);
        });

        // User Routes (authenticated)
        Route::middleware(['auth:api', ApiRateLimiter::class . ':60:1'])->group(function () {
            Route::post('/user/logout', [UserAuthController::class, 'logout']);
            Route::post('/user/refresh-token', [UserAuthController::class, 'refreshToken']);

            // Cached profile endpoint
            Route::middleware(ApiCache::class . ':1800')->group(function () {
                Route::get('/user/profile', [UserAuthController::class, 'profile']);
            });

            Route::get('/users', [UserController::class, 'index']);
            Route::get('/users/{id}', [UserController::class, 'show']);
            Route::put('/users/{id}', [UserController::class, 'update']);
            Route::delete('/users/{id}', [UserController::class, 'destroy']);
        });

        // Admin Routes (authenticated)
        Route::middleware(['auth:admin', ApiRateLimiter::class . ':100:1'])->group(function () {
            Route::post('/admin/logout', [AdminAuthController::class, 'logout']);
            Route::post('/admin/refresh-token', [AdminAuthController::class, 'refreshToken']);

            // Cached admin profile
            Route::middleware(ApiCache::class . ':1800')->group(function () {
                Route::get('/admin/profile', [AdminAuthController::class, 'profile']);
            });

            Route::get('/admins', [AdminController::class, 'index']);
            Route::get('/admins/{id}', [AdminController::class, 'show']);
            Route::put('/admins/{id}', [AdminController::class, 'update']);
            Route::delete('/admins/{id}', [AdminController::class, 'destroy']);

            Route::post('/upload-images', [ImageUploadController::class, 'uploadMultiple']);

            // Cached read-only endpoints
            Route::middleware(ApiCache::class . ':3600')->group(function () {
                Route::get('/block-categories', [BlockCategoryController::class, 'index']);
                Route::get('/block-list-by-categories', [BlockCategoryController::class, 'getBlocks']);
                Route::get('/blocks/{blockId}', [BlockCategoryController::class, 'getBlockById']);
                Route::get('templates', [TemplateController::class, 'getTemplateData']);
                Route::get('templates/{templateId}', [TemplateController::class, 'getTemplateDetails']);
            });

            // Global Assets Routes
            Route::apiResource('global-assets', GlobalAssetsController::class);
        });
    });
});

// Legacy support - redirect old routes to v1
Route::group(['prefix' => 'cms'], function () {
    Route::any('{any}', function () {
        return response()->json([
            'status' => 'error',
            'message' => 'This API version is deprecated. Please use /api/v1/cms/ instead.',
            'redirect_to' => '/api/v1/cms/'
        ], 410);
    })->where('any', '.*');
});
