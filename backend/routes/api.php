<?php

use App\Http\Controllers\ImageUploadController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

use App\Http\Controllers\Auth\AdminAuthController;
use App\Http\Controllers\Auth\UserAuthController;
use App\Http\Controllers\AdminController;
use App\Http\Controllers\BlockCategoryController;
use App\Http\Controllers\GlobalAssetsController;
use App\Http\Controllers\TemplateController;
use App\Http\Controllers\UserController;


Route::group(['prefix' => 'cms'], function () {

    // User Routes
    Route::post('/user/register', [UserAuthController::class, 'register']);
    Route::post('/user/login', [UserAuthController::class, 'login']);

    Route::middleware('auth:api')->group(function () {
        Route::post('/user/logout', [UserAuthController::class, 'logout']);
        Route::get('/user/profile', [UserAuthController::class, 'profile']);
        Route::get('/users', [UserController::class, 'index']);
        Route::get('/users/{id}', [UserController::class, 'show']);
        Route::put('/users/{id}', [UserController::class, 'update']);
        Route::delete('/users/{id}', [UserController::class, 'destroy']);
    });

    // Admin Routes
    Route::post('/admin/register', [AdminAuthController::class, 'register']);
    Route::post('/admin/login', [AdminAuthController::class, 'login']);

    Route::middleware('auth:admin')->group(function () {
        Route::post('/admin/logout', [AdminAuthController::class, 'logout']);
        Route::get('/admin/profile', [AdminAuthController::class, 'profile']);
        Route::get('/admins', [AdminController::class, 'index']);
        Route::get('/admins/{id}', [AdminController::class, 'show']);
        Route::put('/admins/{id}', [AdminController::class, 'update']);
        Route::delete('/admins/{id}', [AdminController::class, 'destroy']);
        Route::post('/upload-images', [ImageUploadController::class, 'uploadMultiple']);
        Route::get('/block-categories', [BlockCategoryController::class, 'index']);
        Route::get('/block-list-by-categories', [BlockCategoryController::class, 'getBlocks']);

        Route::get('templates', [TemplateController::class, 'getTemplateData']);
        Route::get('templates/{templateId}', [TemplateController::class, 'getTemplateDetails']);
        
        // Global Assets Routes
        Route::apiResource('global-assets', GlobalAssetsController::class);
    });
});
