<?php

namespace Indianic\RestApi\Builders;

use Illuminate\Database\Query\Builder;
use Illuminate\Support\Str;
use Indianic\RestApi\contracts\QueryBuilder;

/**
 * Class Join
 *
 * This class handles applying 'JOIN' clauses to a database query builder.
 */
class Join extends QueryBuilder
{

    /**
     * Apply the 'JOIN' clauses to the query builder.
     *
     * This method retrieves the join tables and applies the appropriate 'JOIN'
     * type (inner, left, right) and the related conditions to the query builder.
     *
     * @param Builder $builder The query builder instance to apply 'JOIN' clauses to.
     * @return void
     */
    public function run(Builder $builder): void
    {
        $this->getJoinTables()->each(function($join) use ($builder) {

            if(Str::contains($join, ':')) {
                [$table, $relation] = explode(':', $join);
                $joinType = $this->getJoinType($table);
                $table = trim($table, '=');

                if(Str::contains($relation, ',')) {
                    [$currentTableKey, $referenceTableKey] = explode(',', $relation);
                    $currentTableKey = $table.'.'.$currentTableKey;
                    $referenceTableKey = $builder->from.'.'.$referenceTableKey;
                } else {
                    $currentTableKey = $table.'.'.Str::singular($builder->from).'_id';
                    $referenceTableKey = $builder->from.'.id';
                }
            } else {
                $joinType = $this->getJoinType($join);
                $table = trim($join, '=');
                $currentTableKey = $table.'.'.Str::singular($builder->from).'_id';
                $referenceTableKey = $builder->from.'.id';
            }

            match ($joinType) {
                'left' => $builder->leftJoin($table, $currentTableKey, $referenceTableKey),
                'right' => $builder->rightJoin($table, $currentTableKey, $referenceTableKey),
                default => $builder->join($table, $currentTableKey, $referenceTableKey)
            };
        });
    }

    /**
     * Determine the type of join to be applied based on the table name.
     *
     * @param string $table The table name to analyze for join type.
     * @return string The join type ('left', 'right', or 'inner').
     */
    protected function getJoinType($table):string
    {
        if (Str::startsWith($table, '=')) {
            $joinType = 'left';
        } else if (Str::endsWith($table, '=')) {
            $joinType = 'right';
        } else {
            $joinType = 'inner';
        }
        return $joinType;
    }

    /**
     * Retrieve the join tables from the request.
     *
     * This method fetches the tables that should be joined from the request data.
     *
     * @return \Illuminate\Support\Collection A collection of join tables.
     */
    public function getJoinTables(): \Illuminate\Support\Collection
    {
        $include = $this->request->getRequestData(config('rest-api.parameters.join'));

        if (is_string($include)) {
            return collect();
        }

        return collect($include);
    }
}
