<?php

namespace Indianic\RestApi\Builders;

use Illuminate\Database\Query\Builder;
use Illuminate\Support\Str;
use Indianic\RestApi\contracts\QueryBuilder;

/**
 * Class Sort
 *
 * This class is responsible for applying sorting to a database query based on request data.
 */
class Sort extends QueryBuilder
{

    /**
     * Apply sorting to the query builder.
     *
     * This method retrieves sorting fields from the request and applies them to the query in ascending or descending order.
     * Fields prefixed with '-' indicate descending order.
     *
     * @param Builder $builder The query builder instance to apply sorting to.
     * @return void
     */
    public function run(Builder $builder): void
    {
        // Get the sorting fields and apply them to the query builder
        $this->getSortingFields()->each(function ($field) use ($builder) {
            $direction = 'asc';

            // If the field starts with '-', set the direction to descending
            if (Str::startsWith($field, '-')) {
                $direction = 'desc';
                $field = ltrim($field, '-');   // Remove '-' from the field name
            }
            $builder->orderBy($field, $direction);
        });
    }

    /**
     * Retrieve sorting fields from the request data.
     *
     * This method fetches the sorting fields (comma-separated) from the request,
     * and converts them into a collection for easier processing.
     *
     * @return \Illuminate\Support\Collection A collection of sorting fields.
     */
    protected function getSortingFields(): \Illuminate\Support\Collection
    {
        $sort = $this->request->getRequestData(config('rest-api.parameters.sort'));
        if (is_string($sort)) {
            $sort = explode(',', $sort);
        }
        return collect($sort);
    }
}
