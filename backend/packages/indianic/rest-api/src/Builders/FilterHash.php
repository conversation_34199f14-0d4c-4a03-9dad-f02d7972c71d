<?php

namespace Indianic\RestApi\Builders;

use Illuminate\Database\Query\Builder;
use Illuminate\Support\Str;
use Indianic\RestApi\contracts\QueryBuilder;

/**
 * Class Filter
 *
 * This class is responsible for applying filters to a database query builder
 * instance. It processes the filter fields from the request and dynamically
 * builds query conditions based on the provided filter data.
 */
class FilterHash extends QueryBuilder
{

    protected array $operators = [
        'equal'            => ['sql_operator' => '=', 'accept_values' => true, 'apply_to' => ['string', 'number', 'datetime']],
        'not_equal'        => ['sql_operator' => '!=', 'accept_values' => true, 'apply_to' => ['string', 'number', 'datetime']],
        'in'               => ['sql_operator' => 'IN', 'accept_values' => true, 'apply_to' => ['string', 'number', 'datetime']],
        'not_in'           => ['sql_operator' => 'NOT IN', 'accept_values' => true, 'apply_to' => ['string', 'number', 'datetime']],
        'less'             => ['sql_operator' => '<', 'accept_values' => true, 'apply_to' => ['number', 'datetime']],
        'less_or_equal'    => ['sql_operator' => '<=', 'accept_values' => true, 'apply_to' => ['number', 'datetime']],
        'greater'          => ['sql_operator' => '>', 'accept_values' => true, 'apply_to' => ['number', 'datetime']],
        'greater_or_equal' => ['sql_operator' => '>=', 'accept_values' => true, 'apply_to' => ['number', 'datetime']],
        'between'          => ['sql_operator' => 'BETWEEN', 'accept_values' => true, 'apply_to' => ['number', 'datetime']],
        'not_between'      => ['sql_operator' => 'NOT BETWEEN', 'accept_values' => true, 'apply_to' => ['number', 'datetime']],
        'begins_with'      => ['sql_operator' => 'LIKE', 'prepend' => '%', 'accept_values' => true, 'apply_to' => ['string']],
        'not_begins_with'  => ['sql_operator' => 'NOT LIKE', 'prepend' => '%', 'accept_values' => true, 'apply_to' => ['string']],
        'contains'         => ['sql_operator' => 'LIKE', 'append' => '%', 'prepend' => '%', 'accept_values' => true, 'apply_to' => ['string']],
        'not_contains'     => ['sql_operator' => 'NOT LIKE', 'append' => '%', 'prepend' => '%', 'accept_values' => true, 'apply_to' => ['string']],
        'end_with'        => ['sql_operator' => 'LIKE', 'append' => '%', 'accept_values' => true, 'apply_to' => ['string']],
        'not_end_with'    => ['sql_operator' => 'NOT LIKE', 'append' => '%', 'accept_values' => true, 'apply_to' => ['string']],
        'is_empty'         => ['sql_operator' => '=', 'accept_values' => false, 'apply_to' => ['string']],
        'is_not_empty'     => ['sql_operator' => '!=', 'accept_values' => false, 'apply_to' => ['string']],
        'is_null'          => ['sql_operator' => 'NULL', 'accept_values' => false, 'apply_to' => ['string', 'number', 'datetime']],
        'is_not_null'      => ['sql_operator' => 'NOT NULL', 'accept_values' => false, 'apply_to' => ['string', 'number', 'datetime']]
    ];

    protected array $needs_array = ['IN', 'NOT IN', 'BETWEEN', 'NOT BETWEEN'];

    /**
     * Run the filter logic on the provided query builder.
     *
     * This method retrieves filter fields from the request and applies them to the
     * query builder. It handles multiple values for a single field by using 'LIKE'
     * queries with the 'OR' condition.
     *
     * @param Builder $builder The query builder instance to apply filters to.
     * @return void
     */
    public function run(Builder $builder): void
    {
        $filterHash = $this->getFilterHashFieldData();
        if (!empty($filterHash)) {
            $this->loopThroughRules($filterHash['rules'], $builder, $filterHash['condition']);
        }
    }

    protected function getFilterHashFieldData(): array
    {
        $fieldData = [];
        $filterHash = $this->request->getRequestData('filterHash');
        if (is_array($filterHash) && !empty($filterHash) && !empty($filterHash['rules'])) {
            $fieldData = $filterHash;
        }
        return $fieldData;
    }

    protected function loopThroughRules(array $rules, Builder $builder, $queryCondition = 'AND')
    {
        foreach ($rules as $rule) {
            if ($this->isNested($rule)) {
                $this->createNestedQuery($builder, $rule, $queryCondition);
            } else {
                $this->makeQuery($builder, $rule, $queryCondition);
            }
        }
    }

    protected function makeQuery(Builder $builder, $rule, $queryCondition = 'AND')
    {
        if (!$this->isValidRule($rule)) {
            ds($rule);
            return;
        }

        $sqlOperator = $this->operators[$rule['operator']]['sql_operator'];

        $value = $this->appendOperatorIfRequired(
            $rule['operator'],
            $this->getValueForQueryFromRule($rule)
        );

        if ($this->operatorRequiresArray($sqlOperator)) {
            $this->makeQueryWhenArray($builder, $rule, $sqlOperator, $value, $queryCondition);
        } elseif ($this->operatorIsNull($sqlOperator)) {
            $this->makeQueryWhenNull($builder, $rule, $sqlOperator, $queryCondition);
        } else {
            $builder->where($rule['field'], $sqlOperator, $value, $queryCondition);
        }
    }

    protected function createNestedQuery(Builder $builder, $rule, $condition)
    {
        $condition = $this->getValidCondition($condition);

        $builder->whereNested(function ($query) use ($rule, $condition) {

            foreach ($rule['rules'] as $loopRule) {
                if ($this->isNested($loopRule)) {
                    $this->createNestedQuery($query, $loopRule, $rule['condition']);
                } else {
                    $this->makeQuery($query, $loopRule, $rule['condition']);
                }
            }

        }, $condition);
    }

    protected function makeQueryWhenArray(Builder $query, array $rule, string $sqlOperator, array $value, $condition)
    {
        if (in_array($sqlOperator, ['IN', 'NOT IN'])) {

            if ($sqlOperator == 'NOT IN') {
                $query->whereNotIn($rule['field'], $value, $condition);
            } else {
                $query->whereIn($rule['field'], $value, $condition);
            }

        } elseif (in_array($sqlOperator, ['BETWEEN', 'NOT BETWEEN'])) {

            if ($sqlOperator == 'NOT BETWEEN') {
                $query->whereNotBetween($rule['field'], $value, $condition );
            } else {
                $query->whereBetween($rule['field'], $value, $condition);
            }

        }
    }

    protected function makeQueryWhenNull(Builder $query, array $rule, string $sqlOperator, $condition)
    {
        if ($sqlOperator == 'NULL') {
            $query->whereNull($rule['field'], $condition);
        } elseif ($sqlOperator == 'NOT NULL') {
            return $query->whereNotNull($rule['field'], $condition);
        }
    }

    protected function isNested($rule): bool
    {
        if (isset($rule['rules']) && is_array($rule['rules']) && !empty($rule['rules'])) {
            return true;
        }

        return false;
    }

    protected function getValidCondition($condition = 'and'): string
    {
        if (!in_array(strtolower($condition), ['and', 'or'])) {
            $condition = 'and';
        }
        return strtolower($condition);
    }

    protected function isValidRule(array $rule): bool
    {
        if (!isset($rule['operator'], $rule['field'])) {
            return false;
        }

        if (!isset($this->operators[$rule['operator']])) {
            return false;
        }

        return true;
    }

    protected function getValueForQueryFromRule(array $rule)
    {
        $value = $rule['value'];
        if (in_array($rule['operator'], ['between', 'not_between'])) {
            $value = [$rule['min'], $rule['max']];
        } else if (in_array($rule['operator'], ['in', 'not_in'])) {
            $value = explode(',', $rule['value']);
        } else if (in_array($rule['operator'], ['is_empty', 'is_not_empty'])) {
            $value = '';
        } else if (in_array($rule['operator'], ['is_null', 'is_not_null'])) {
            $value = null;
        }
        return $value;
    }

    protected function appendOperatorIfRequired($operator, $value)
    {
        if (!$this->operatorRequiresArray($operator)) {

            $sqlOperator = $this->operators[$operator];

            if (isset($sqlOperator['append'])) {
                $value = $sqlOperator['append'] . $value;
            }

            if (isset($sqlOperator['prepend'])) {
                $value = $value . $sqlOperator['prepend'];
            }
        }

        return $value;
    }

    protected function operatorRequiresArray($operator): bool
    {
        return in_array($operator, $this->needs_array);
    }

    protected function operatorIsNull($operator): bool
    {
        return in_array($operator, ['NULL', 'NOT NULL']);
    }
}
