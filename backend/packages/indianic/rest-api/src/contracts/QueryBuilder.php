<?php

namespace Indianic\RestApi\contracts;

use Illuminate\Database\Query\Builder;
use Indianic\RestApi\RestAPIRequest;


/**
 * Class QueryBuilder
 *
 * This abstract class defines the structure for query builders in the RestAPI.
 * It serves as a base for creating specific query builders that handle
 * database queries based on the provided request parameters.
 */
abstract class QueryBuilder
{
    /**
     * Factory method to create an instance of the query builder.
     *
     * This method initializes a new instance of a subclass using the provided RestAPIRequest.
     *
     * @param RestAPIRequest $request The request containing query parameters.
     * @return static An instance of the subclass implementing the query builder.
     */
    public static function make(RestAPIRequest $request): static
    {
        return new static($request);
    }

    /**
     * QueryBuilder constructor.
     *
     * This constructor stores the RestAPIRequest for use in query operations.
     *
     * @param RestAPIRequest $request The request object containing the query parameters.
     */
    public function __construct(protected RestAPIRequest $request)
    {
        // Store the request for later use in building queries
    }

    /**
     * Apply the query logic to the query builder.
     *
     * This abstract method must be implemented by subclasses to define the logic
     * for building the query using the query builder.
     *
     * @param Builder $builder The query builder instance to apply the logic to.
     */
    abstract public function run(Builder $builder): void;
}
