<?php

namespace Indianic\RestApi\Helpers;

use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;

class ApiResponse
{
    const VERSION = 'v1';

    /**
     * Standard success response format
     */
    public static function success(
        mixed $data = [],
        string|null $message = null,
        int $statusCode = 200,
        array $meta = []
    ): JsonResponse {
        $response = [
            'status' => 'success',
            'version' => self::VERSION,
            'timestamp' => now()->toISOString(),
            'data' => $data,
            'message' => $message,
        ];

        if (!empty($meta)) {
            $response['meta'] = $meta;
        }

        return response()->json($response, $statusCode);
    }

    /**
     * Standard error response format
     */
    public static function error(
        string $message,
        int $statusCode = 400,
        mixed $errorCode = null,
        array $details = []
    ): JsonResponse {
        $response = [
            'status' => 'error',
            'version' => self::VERSION,
            'timestamp' => now()->toISOString(),
            'error' => [
                'code' => $errorCode ?? $statusCode,
                'message' => $message,
                'type' => self::getErrorType($statusCode)
            ]
        ];

        if (!empty($details)) {
            $response['error']['details'] = $details;
        }

        // Log error for monitoring
        Log::error('API Error', [
            'status_code' => $statusCode,
            'error_code' => $errorCode,
            'message' => $message,
            'details' => $details
        ]);

        return response()->json($response, $statusCode);
    }

    /**
     * Validation error response
     */
    public static function validationError(
        string $message = null,
        array $errors = []
    ): JsonResponse {
        return response()->json([
            'status' => 'error',
            'version' => self::VERSION,
            'timestamp' => now()->toISOString(),
            'error' => [
                'code' => 422,
                'message' => $message ?? 'Validation failed.',
                'type' => 'validation_error',
                'validation_errors' => $errors
            ]
        ], 422);
    }

    /**
     * Paginated response format
     */
    public static function paginated(
        $paginatedData,
        string $message = null
    ): JsonResponse {
        return self::success(
            $paginatedData->items(),
            $message,
            200,
            [
                'pagination' => [
                    'current_page' => $paginatedData->currentPage(),
                    'last_page' => $paginatedData->lastPage(),
                    'per_page' => $paginatedData->perPage(),
                    'total' => $paginatedData->total(),
                    'from' => $paginatedData->firstItem(),
                    'to' => $paginatedData->lastItem(),
                ]
            ]
        );
    }

    /**
     * Get error type based on status code
     */
    private static function getErrorType(int $statusCode): string
    {
        return match ($statusCode) {
            400 => 'bad_request',
            401 => 'unauthorized',
            403 => 'forbidden',
            404 => 'not_found',
            422 => 'validation_error',
            429 => 'rate_limit_exceeded',
            500 => 'internal_server_error',
            503 => 'service_unavailable',
            default => 'unknown_error'
        };
    }
}
