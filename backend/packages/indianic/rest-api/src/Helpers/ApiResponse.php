<?php
namespace Indianic\RestApi\Helpers;


class ApiResponse
{
    public static function success(mixed $data = [], string|null $message = null): \Illuminate\Http\JsonResponse
    {
        return response()->json([
            'status' => 'success',
            'data' => $data,
            'message' => $message
        ], 200);
    }

    public static function error(string $message, int $statusCode = 400, mixed $errorCode = null): \Illuminate\Http\JsonResponse
    {
        return response()->json([
            'status' => 'error',
            'error_code' => $errorCode ?? $statusCode,
            'message' => $message
        ], $statusCode);
    }

    public static function validationError($message, $errors): \Illuminate\Http\JsonResponse
    {
        return response()->json([
            'status' => 'error',
            'error_code' => 422,
            'message' => $message ?? 'Validation failed.',
            'errors' => $errors
        ], 422);
    }
}
