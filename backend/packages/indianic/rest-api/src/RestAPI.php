<?php

namespace Indianic\RestApi;

use Illuminate\Contracts\Database\Query\Builder;
use Illuminate\Pagination\Paginator;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Traits\ForwardsCalls;
use Indianic\RestApi\contracts\QueryBuilder;
use Indianic\RestApi\Traits\RestAPIUtils;
use Indianic\RestApi\RestAPIValidator;
use JetBrains\PhpStorm\ArrayShape;

/**
 * Class RestAPI
 *
 * This class serves as a wrapper around a database query builder,
 * providing a fluent interface for handling RESTful API requests.
 *
 * @method delete(string $id) Deletes a record by ID.
 * @method where(string $string, $id) Adds a where clause to the query.
 */
class RestAPI
{
    use ForwardsCalls, RestAPIUtils;

    private RestAPIRequest $request;  // Holds the request object.

    private Collection $builders; // Collection of query builders.

    /**
     * Constructor to initialize the RestAPI instance.
     *
     * @param Builder $subject The underlying query builder.
     * @param RestAPIRequest|null $request Optional request object.
     */
    public function __construct(
        protected Builder $subject,
        ?RestAPIRequest $request = null
    ) {
        $this->request = $request
            ? RestAPIRequest::fromRequest($request)
            : app(RestAPIRequest::class);

        $this->builders = collect();
    }

    /**
     * Static method to create an instance of RestAPI.
     *
     * @param Builder $subject The underlying query builder.
     * @param RestAPIRequest|null $request Optional request object.
     * @return static
     */
    public static function for(
        Builder $subject,
        ?RestAPIRequest $request = null
    ): static {
        return new static($subject, $request);
    }

    /**
     * Parse the request and process all registered query builders.
     */
    private function runBuilders(): void
    {
        $this->builders->each(fn(QueryBuilder $builder) => $builder->run($this->subject));
    }

    /**
     * Retrieve data based on the current query and request parameters.
     *
     * @return array|Paginator
     */
    public function getData(): array|\Illuminate\Contracts\Pagination\Paginator
    {
        $this->runBuilders();
        $perPage = $this->request->getRequestData(config('rest-api.parameters.limit', 'limit'), 1000);
        $page = $this->request->getRequestData(config('rest-api.parameters.page', 'page'), 1);
        return $this->subject->simplePaginate(perPage: $perPage, page: $page);
    }

    /**
     * Store new data in the database.
     *
     * @return int The last insert id of the insert operation.
     */
    public function storeData(): int
    {
        $data = $this->getValidFields($this->subject->from, $this->request->all());
        return $this->subject->insertGetId($data);
    }

    /**
     * Update an existing row identified by its ID.
     *
     * @param mixed $id The ID of the row to update.
     * @return int The number of affected rows.
     */
    public function updateRow(mixed $id): int
    {
        $data = $this->getValidFields($this->subject->from, $this->request->post());
        return $this->subject->where(
            $this->getIdKey($id),
            $this->getIdVal($id)
        )->update($data);
    }

    /**
     * Update data based on the current query and request parameters.
     *
     * @return int The number of affected rows.
     */
    public function updateData(): int
    {
        $this->runBuilders();
        return $this->subject->update($this->request->getRequestData('data'));
    }

    /**
     * Delete data based on the current query.
     *
     * @return int The number of affected rows.
     */
    public function deleteData(): int
    {
        $this->runBuilders();
        return $this->subject->delete();
    }

    public function first(mixed $id): mixed
    {
        $this->runBuilders();
        return $this->subject->where(
            $this->getIdKey($id, true),
            $this->getIdVal($id)
        )->first();
    }

    public function count(): int
    {
        $this->runBuilders();
        return $this->subject->count();
    }

    public function avg(string $fieldName): mixed
    {
        $this->runBuilders();
        return $this->subject->avg($fieldName);
    }

    public function max(string $fieldName): mixed
    {
        $this->runBuilders();
        return $this->subject->max($fieldName);
    }

    public function exists(): bool
    {
        $this->runBuilders();
        return $this->subject->exists();
    }

    public function getValidationRulesFor(string $method)
    {
        $validationRule = false;
        $tableName = $this->subject->from;
        $configValidation = config('rest-api-validations');

        if (!is_null($configValidation)) {
            // Check if 'table-validation' and the given table name exist
            if (isset($configValidation['table-validation'][$tableName])) {
                // Check if the method (e.g., GET, POST) exists for the table
                if (isset($configValidation['table-validation'][$tableName][$method])) {
                    $validationId = $configValidation['table-validation'][$tableName][$method] ?? null;

                    // Check if the validation ID exists in 'validations'
                    if ($validationId && isset($configValidation['validations'][$validationId])) {
                        $rules = $configValidation['validations'][$validationId] ?? [];

                        // Encode the rules to JSON format
                        $validationRule = json_encode($rules);
                    } else {
                        // Handle missing validation ID or missing rules for the validation ID
                        $validationRule = json_encode([]);
                    }
                } else {
                    // Handle missing method in the table configuration
                    $validationRule = json_encode([]);
                }
            } else {
                // Handle missing table name in the configuration
                $validationRule = json_encode([]);
            }
        } else {
            // Handle missing configuration file
            $validationRule = json_encode([]);
        }
        return $validationRule;
    }

    public function validateRequest(string $method): void
    {
        $validationRule = $this->getValidationRulesFor($method);
        if ($validationRule) {
            RestAPIValidator::validate($this->request->all(), json_decode($validationRule));
        }
    }

    public function getIdKey(mixed $id, bool $withTableName = false): string
    {
        $tablePrefix = $withTableName ? $this->subject->from . '.' : '';
        return $tablePrefix . $this->getIdKeyAndVal($id)['key'];
    }

    public function getIdVal(mixed $id): mixed
    {
        return $this->getIdKeyAndVal($id)['val'];
    }

    #[ArrayShape(['key' => "string", 'val' => "mixed|string"])]
    private function getIdKeyAndVal(mixed $id): array
    {
        $key = 'id';
        $val = $id;

        if (str_contains($id, ':')) {
            [$key, $val] = explode(':', $id);
        }

        return [
            'key' => $key,
            'val' => $val
        ];
    }

    private function getValidFields(string $tableName, ?array $data = []): array
    {
        $validKeys = collect(Schema::getColumnListing($tableName))->toArray(); // Uses the default connection
        return collect($data)->filter(function ($value, $key) use ($validKeys) {
            return in_array($key, $validKeys);
        })->all();
    }

    /**
     * Handle dynamic method calls to the query builder.
     *
     * @param string $method The name of the method.
     * @param array $parameters The parameters for the method.
     * @return mixed The result of the method call or $this for chaining.
     */
    public function __call(string $method, $parameters)
    {
        $result = $this->forwardCallTo($this->subject, $method, $parameters);  // Forward the call to the query builder.

        // If the forwarded method call is part of a chain, return $this to keep the chain going.
        if ($result === $this->subject) {
            return $this;
        }

        return $result;
    }
}
