<?php

namespace Indianic\RestApi;

use Illuminate\Contracts\Debug\ExceptionHandler;
use Illuminate\Foundation\Application;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\ServiceProvider;
use Indianic\RestApi\Exceptions\RestAPIExceptionsHandler;
use Indianic\RestApi\FieldType\DateTypeField;
use Indianic\RestApi\FieldType\DropdownTypeField;
use Indianic\RestApi\FieldType\EmailTypeField;
use Indianic\RestApi\FieldType\NumberTypeField;
use Indianic\RestApi\FieldType\PhoneTypeField;
use Indianic\RestApi\FieldType\TextTypeField;
use Illuminate\Support\Facades\Validator;

/**
 * Class RestAPIServiceProvider
 *
 * This service provider is responsible for registering the REST API's
 * services, middleware, and routes within the Laravel application.
 */
class RestAPIServiceProvider extends ServiceProvider
{
    /**
     * Register services in the container.
     *
     * This method binds various services, including a custom exception handler
     * and request handling for REST API requests.
     */
    public function register(): void
    {
        // Bind the custom exception handler for REST API exceptions.
        $this->app->singleton(ExceptionHandler::class, RestAPIExceptionsHandler::class);

        // Bind the REST API request handler to the application.
        $this->app->bind(RestAPIRequest::class, function (Application $app) {
            return RestAPIRequest::fromRequest($app['request']);
        });

        // Merge the REST API configuration file into the application's configuration.
        $this->mergeConfigFrom(__DIR__ . '/../config/rest-api.php', 'rest-api');

        $this->app->singleton(RestAPIValidator::class, function () {
            return new RestAPIValidator();
        });
    }

    /**
     * Bootstrap services.
     *
     * This method publishes configuration files, defines route bindings,
     * and groups routes under specific middleware.
     */
    public function boot(): void
    {
        // Publish the REST API configuration file to the application's config directory.
        $this->publishes([
            __DIR__ . '/../config/rest-api.php' => config_path('rest-api.php')
        ], 'rest-api');


        // Only load routes if the API service is not disabled
        if (!config('rest-api.disable_api_service', false)) {
            Route::bind('table', function (string $tableName): RestAPI {
                $subject = DB::table($tableName); // Use the default database connection
                return RestAPI::for($subject);
            });

            Route::prefix(config('rest-api.prefix', 'api/v1'))
                ->group(fn() => $this->loadRoutesFrom(__DIR__ . '/../routes/api.php'));
        }


        /**Validations */
        RestAPIValidator::extend(new TextTypeField());
        RestAPIValidator::extend(new NumberTypeField());
        RestAPIValidator::extend(new EmailTypeField());
        RestAPIValidator::extend(new PhoneTypeField());
        RestAPIValidator::extend(new DateTypeField());
        RestAPIValidator::extend(new DropdownTypeField());

        Validator::extend('phone', function ($attribute, $value, $parameters, $validator) {
            return preg_match('%^(?:(?:\(?(?:00|\+)([1-4]\d\d|[1-9]\d?)\)?)?[\-\.\ \\\/]?)?((?:\(?\d{1,}\)?[\-\.\ \\\/]?){0,})(?:[\-\.\ \\\/]?(?:#|ext\.?|extension|x)[\-\.\ \\\/]?(\d+))?$%i', $value) && strlen($value) >= 10;
        });

        Validator::replacer('phone', function ($message, $attribute, $rule, $parameters) {
            return str_replace(':attribute', $attribute, ':attribute is invalid phone number');
        });
    }
}
