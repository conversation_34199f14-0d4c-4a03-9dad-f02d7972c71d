<?php

namespace Indianic\RestApi;

use Filament\Forms\Components\Group;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Get;
use Filament\Forms\Set;

trait FormHelper
{
    public static function renderFormFields(string $id, string $label): Repeater
    {
        return Repeater::make($id)
            ->label($label)
            ->columns(3)
            ->minItems(1)
            ->reorderable()
            ->deletable(fn($component) => count($component->getState()) > 1)
            ->schema([
                TextInput::make('request_parameter_field')
                    ->label('Request Parameter Name')
                    ->required(),

                Select::make('request_parameter_field_type')
                    ->label('Field Type')
                    ->options(RestAPIValidator::FieldTypes())
                    ->native(false)
                    ->live(),

                Group::make()->schema([
                    Toggle::make('required')
                ])->extraAttributes(['class' => 'flex items-center h-full mt-4']),

                Group::make()
                    ->columnSpanFull()
                    ->schema(self::getFieldTypesOptionsFields()),
            ]);
    }

    public static function getFieldTypesOptionsFields(): array
    {
        $fieldTypeSchema = [];
        foreach (RestAPIValidator::FieldTypes() as $key => $val) {
            $formFields = RestAPIValidator::getFormField($key);
            foreach ($formFields as $field) {
                $field->statePath($key);
                $field->visible(fn(Get $get) => $get('request_parameter_field_type') === $key);
            }
            $fieldTypeSchema = array_merge($fieldTypeSchema, $formFields);
        }
        return $fieldTypeSchema;
    }
}
