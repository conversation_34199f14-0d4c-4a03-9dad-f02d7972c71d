<?php

namespace Indianic\RestApi;

use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;
use Indianic\RestApi\FieldType\DummyField;
use Indianic\RestApi\FieldType\FieldType;

class RestAPIValidator
{
    use FormHelper;

    private array $fieldTypes = [];

    private array $fieldTypeFields = [];

    private array $objects = [];

    public static function extend(FieldType $fieldType): void
    {
        app(RestAPIValidator::class)->register($fieldType);
    }

    public function register(FieldType $fieldType): void
    {
        if (isset($this->fieldTypes[$fieldType->getID()])) {
            throw new \Exception('The field type ' . $fieldType->getID() . ' is already exist, please use different name');
        }
        $this->fieldTypes[$fieldType->getID()] = $fieldType->getLabel();
        $this->objects[$fieldType->getID()] = $fieldType;
    }

    public static function FieldTypes(): array
    {
        return app(RestAPIValidator::class)->getFieldTypes();
    }

    public static function getFormField(string|null $id): array
    {
        return app(RestAPIValidator::class)->getFieldsById($id);
    }

    private function getObjectByID(string $id): FieldType
    {
        return $this->objects[$id] ?? new DummyField();
    }

    private function getFieldTypes(): array
    {
        return $this->fieldTypes;
    }

    private function getFieldsById(string|null $id): array
    {
        return $this->fieldTypeFields[$id] ?? [];
    }

    /**
     * @throws ValidationException
     */
    private function validateRequestData(array $data, array $rules): void
    {
        $validatorRules = [];
        foreach ($rules as $rule) {
            $validatorRules[$rule->request_parameter_field] = $this->getRuleForField(json_decode(json_encode($rule), true));
        }
        Validator::make($data, $validatorRules)->validate();
    }

    private function getRuleForField(array $rule): array
    {
        $fieldType = $rule['request_parameter_field_type'];
        $fieldValidatorRules = $rule['required'] ? ['required'] : [];

        if (!$fieldType) {
            return $fieldValidatorRules;
        }

        return [
            ...$fieldValidatorRules,
            ...$this->getObjectByID($fieldType)->getRuleForValidator($rule[$fieldType])
        ];
    }

    public static function parseSaveFieldData(&$rules): void
    {
        $preservedFields = ['request_parameter_field', 'request_parameter_field_type', 'required'];
        foreach ($rules as $index => $rule) {
            foreach ($rule as $key => $val) {
                if ($key !== $rule['request_parameter_field_type'] && !in_array($key, $preservedFields)) {
                    unset($rules[$index][$key]);
                }
            }
        }
    }

    public static function validate(array $data, array $rules): void
    {
        if (empty($rules)) {
            return;
        }

        app(RestAPIValidator::class)
            ->validateRequestData($data, $rules);
    }
}
