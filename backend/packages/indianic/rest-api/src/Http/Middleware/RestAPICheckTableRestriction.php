<?php

namespace Indianic\RestApi\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Symfony\Component\HttpFoundation\Response;

class RestAPICheckTableRestriction
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle($request, Closure $next)
    {
        // Get the table and method, and ensure they are strings
        $table = $request->route()->originalParameter('table');
        $method = strtolower($request->getMethod());
        $config = config('rest-api-restricted-apis');
        // Check if the configuration file exists
        if (is_null($config)) {
            abort_if(1, 500, 'Configuration file "rest-api-restricted-apis.php" not found.');
        }
        // Check if the table exists in the config
        if (isset($config[$table])) {
            // Check if the method exists for the table and is restricted
            if (!empty($config[$table][$method]) && $config[$table][$method] === 1) {
                abort_if(1, 403, 'Access Forbidden.');
            }
        }

        // Allow the request to proceed
        return $next($request);
    }
}
