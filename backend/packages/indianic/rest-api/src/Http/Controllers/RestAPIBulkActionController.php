<?php
namespace Indianic\RestApi\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Client\Request;
use Illuminate\Http\JsonResponse;
use Indianic\RestApi\Helpers\ApiResponse;
use Indianic\RestApi\RestAPI;
use Illuminate\Support\Facades\Response;
use Indianic\RestApi\RestAPIValidator;


/**
 * Class RestAPIBulkActionController
 *
 * This controller handles bulk update and delete actions for the RestAPI.
 */
class RestAPIBulkActionController extends Controller
{

    /**
     * Perform a bulk insert resources in storage.
     *
     * This method creates a new record by passing the request data to the RestAPI instance.
     *
     * @param RestAPI $restAPI The RestAPI instance for storing the data.
     * @return JsonResponse A JSON response with a status message.
     */
    public function store(RestAPI $restAPI, \Illuminate\Http\Request $request): JsonResponse
    {
        $data = $request->post();
        $validationRule = $restAPI->getValidationRulesFor('post');
        if ($validationRule) {
            foreach ($data as $row) {
                RestAPIValidator::validate($row, json_decode($validationRule));
            }
        }

        $restAPI->insert($data);
        return ApiResponse::success(message: 'Given resources created successfully.');
    }

    /**
     * Perform a bulk update on filtered records.
     *
     * This method applies any filters provided in the request to the RestAPI instance
     * and performs a bulk update operation on the matching records.
     *
     * @param RestAPI $restAPI The RestAPI instance for performing the bulk update operation.
     * @return JsonResponse A JSON response with the number of updated records.
     */
    public function update(RestAPI $restAPI): JsonResponse
    {
        $count = $restAPI->withFilters()->updateData();

        return ApiResponse::success([
            'rows_updated' => $count
        ], 'Resources updated successfully.');
    }

    /**
     * Perform a bulk delete on filtered records.
     *
     * This method applies any filters provided in the request to the RestAPI instance
     * and performs a bulk delete operation on the matching records.
     *
     * @param RestAPI $restAPI The RestAPI instance for performing the bulk delete operation.
     * @return JsonResponse A JSON response with the number of deleted records.
     */
    public function destroy(RestAPI $restAPI): JsonResponse
    {
        $count = $restAPI->withFilters()->deleteData();

        return ApiResponse::success([
            'rows_deleted' => $count
        ], 'Resources deleted successfully.');
    }

}
