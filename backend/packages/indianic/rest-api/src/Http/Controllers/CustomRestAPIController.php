<?php
namespace Indianic\RestApi\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Indianic\RestApi\Helpers\ApiResponse;
use Indianic\RestApi\RestAPI;
use Indianic\RestApi\RestAPIRequest;
use Indianic\RestApi\RestAPIValidator;
use Illuminate\Support\Str;


/**
 * Class CustomRestAPIController
 *
 * This controller handles the custom api created through admin panel.
 */
class CustomRestAPIController extends Controller
{

    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request, $slug)
    {
        $requestData = $request->all();
        $customAPI = $requestData['api_data'];
        $customAPIRules = $requestData['rules'];
        if (!empty($customAPIRules)) {
            RestAPIValidator::validate($requestData, json_decode(json_encode($customAPIRules)));
        }
        
        return match ($requestData['api_data']['type']) {
            'sql' => $this->processSqlAPI($customAPI['sql_query'], $requestData),
            'rest_api' => $this->processRestAPI($customAPI, $requestData),
        };       
    }

    public function processSqlAPI(string $sqlQuery, array $requestData): JsonResponse
    {
        $data = [];
        $sqlQuery = Str::squish($this->getReplacedFilterValue($sqlQuery, $requestData));
        if (Str::startsWith(strtoupper($sqlQuery), 'SELECT')) {
            $message = 'Data retrieved successfully.';
            $data = DB::select($sqlQuery);
        } else if (Str::startsWith(strtoupper($sqlQuery), 'UPDATE')) {
            $message = 'Data updated successfully.';
            $data['rows_updated'] = DB::update($sqlQuery);
        } else if (Str::startsWith(strtoupper($sqlQuery), 'DELETE')) {
            $message = 'Data deleted successfully.';
            $data['rows_deleted'] = DB::delete($sqlQuery);
        } else {
            $message = 'Mysql query executed successfully.';
            $data['result'] = DB::statement($sqlQuery);
        }

        return ApiResponse::success($data, $message);
    }

    public function processRestAPI($customAPI, $requestData): JsonResponse
    {
        $requestData['fields'] = $customAPI['fields'];

        if ($customAPI['limit']) {
            $requestData['limit'] = $customAPI['limit'];
        }

        if ($customAPI['group_by']) {
            $requestData['group_by'] = $customAPI['group_by'];
        }

        if ($customAPI['sort_by']) {
            $requestData['sort'] = $customAPI['sort_by'];
        }

        $joins = $this->getJoinTableData($customAPI['join']);
        if (!$joins->isEmpty()) {
            $requestData['join'] = $joins->all();
        }

        $filterHash = $customAPI['filter'];
        // && is_string($customAPI['filter']) ? json_decode($customAPI['filter'], true) : [];
        if (!empty($filterHash) && !empty($filterHash['rules'])) {
            $this->replaceFilterDataValues($filterHash['rules'], $requestData);
            $requestData['filterHash'] = $filterHash;
        }

        $request = new RestAPIRequest();
        $request->merge($requestData);

        $subject = DB::table($customAPI['table']);
        $restAPI = RestAPI::for($subject, $request)
            ->withSelectFields()
            ->withSorting()
            ->withFilterHash()
            ->withJoin()
            ->withGroupBy();
            
        $data = $restAPI->getData();
        return ApiResponse::success($data, 'Data retrieved successfully.');
    }

    public function replaceFilterDataValues(&$rules, $requestData): void
    {
        foreach ($rules as &$rule) {

            if (isset($rule['value'])) {
                $rule['value'] = $this->getReplacedFilterValue($rule['value'], $requestData);
            }

            if (isset($rule['min'])) {
                $rule['min'] = $this->getReplacedFilterValue($rule['min'], $requestData);
            }

            if (isset($rule['max'])) {
                $rule['max'] = $this->getReplacedFilterValue($rule['max'], $requestData);
            }

            if (isset($rule['rules'])) {
                $this->replaceFilterDataValues($rule['rules'], $requestData);
            }
        }
    }

    protected function getReplacedFilterValue($filterVal, $requestData)
    {
        foreach ($requestData as $field => $val) {
            if (is_string($val)) {
                $filterVal = str_replace(':'.$field, $val, $filterVal);
            }
        }
        return $filterVal;
    }

    public function getJoinTableData($joins): \Illuminate\Support\Collection
    {
        $requestData = collect();
        foreach ($joins as $join) {
            $tableName = $join['join_table_name'];
            if ($join['join_table_name'] == 'LEFT') {
                $tableName = '=' . $tableName;
            } else if ($join['join_table_name'] == 'RIGHT') {
                $tableName = $tableName . '=';
            }
            $tableWithJoin = ($tableName . ':' . $join['join_primary_key'] . ',' . $join['join_foreign_key']);
            $requestData->push($tableWithJoin);
        }
        return $requestData;
    }

}
