<?php
namespace Indianic\RestApi\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Indianic\RestApi\Helpers\ApiResponse;
use Indianic\RestApi\RestAPI;

/**
 * Class RestAPIAggregatesController
 *
 * This controller handles various aggregate functions (count, max, avg, exists)
 * for the RestAPI class.
 */
class RestAPIAggregatesController extends Controller
{

    /**
     * Count the number of records.
     *
     * This method calls the `count` method of the RestAPI class to return the total count of records.
     *
     * @param RestAPI $restAPI The RestAPI instance for performing the count operation.
     * @return JsonResponse A JSON response with a status message.
     */
    public function count(RestAPI $restAPI): JsonResponse
    {
        $restAPI->withFilters();
        $count = $restAPI->count();

        return ApiResponse::success([
            'count' => $count
        ], 'Data aggregated successfully.');
    }

    /**
     * Get the maximum value of a specified field.
     *
     * This method retrieves the maximum value for the provided field from the RestAPI instance.
     *
     * @param RestAPI $restAPI The RestAPI instance for performing the max operation.
     * @param string $fieldName The name of the field to retrieve the maximum value from.
     * @return JsonResponse A JSON response with a status message.
     */
    public function max(RestAPI $restAPI, string $fieldName): JsonResponse
    {
        $restAPI->withFilters();
        $max = $restAPI->max($fieldName);

        return ApiResponse::success([
            'max' => $max
        ], 'Data aggregated successfully.');
    }

    /**
     * Get the average value of a specified field.
     *
     * This method retrieves the average value for the provided field from the RestAPI instance.
     *
     * @param RestAPI $restAPI The RestAPI instance for performing the average operation.
     * @param string $fieldName The name of the field to retrieve the average value from.
     * @return JsonResponse A JSON response with a status message.
     */
    public function avg(RestAPI $restAPI, string $fieldName): JsonResponse
    {
        $restAPI->withFilters();
        $avg = $restAPI->avg($fieldName);

        return ApiResponse::success([
            'avg' => $avg
        ], 'Data aggregated successfully.');
    }

    /**
     * Check if any records exist.
     *
     * This method checks if any records exist using the `exists` method of the RestAPI instance.
     *
     * @param RestAPI $restAPI The RestAPI instance for performing the existence check.
     * @return JsonResponse A JSON response with a status message.
     */
    public function exists(RestAPI $restAPI): JsonResponse
    {
        $restAPI->withFilters();
        $exists = $restAPI->exists();

        return ApiResponse::success([
            'exists' => $exists
        ], 'Data aggregated successfully.');
    }

}
