<?php

namespace Indianic\RestApi\FieldType;

class PhoneTypeField extends EmailTypeField implements FieldType
{

    public function getID(): string
    {
        return 'phone';
    }

    public function getLabel(): string
    {
        return 'Phone';
    }

    public function getRuleForValidator(array $data): array
    {
        $rules = ['phone'];
        if ($data['unique']) {
            $rules[] = "unique:{$data['table']},{$data['column']}";
        }
        return $rules;
    }
}
