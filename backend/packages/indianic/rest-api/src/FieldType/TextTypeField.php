<?php

namespace Indianic\RestApi\FieldType;

class TextTypeField implements FieldType
{

    public function getID(): string
    {
        return 'text';
    }

    public function getLabel(): string
    {
        return 'Text';
    }

    public function getFields(): array
    {
        return [];
    }

    public function getRuleForValidator(array $data): array
    {
        return ["between:{$data['min_length']},{$data['max_length']}"];
    }
}
