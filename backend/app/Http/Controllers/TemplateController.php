<?php

namespace App\Http\Controllers;

use App\Models\Template;
use App\Models\TemplateCategory;
use App\Models\Page;
use Indianic\RestApi\Helpers\ApiResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class TemplateController extends Controller
{
    // Get template data with category name and page count
    public function getTemplateData(Request $request)
    {
        try {
            $search = $request->input('search');
            $perPage = $request->input('limit', 10); // Default to 10 if per_page is not provided
            $sort = $request->input('sort', 'id');
            $sortField = str_contains($sort, '-') ? str_replace('-', '', $sort) : $sort;
            $sortOrder = str_contains($sort, '-') ? 'desc' : 'asc';

            // Query Templates with the relevant filters
            $query = Template::with(['categories'])->withCount('templatePages');

            // Apply search filter if provided
            if ($search) {
                $query->where('name', 'like', '%' . $search . '%')
                    ->orWhere('short_description', 'like', '%' . $search . '%')
                    ->orWhere('detailed_description', 'like', '%' . $search . '%');
            }

            // Apply sorting
            if ($sortField === 'template_pages_count') {
                $query->orderBy('template_pages_count', $sortOrder);
            } else {
                $query->orderBy($sortField, $sortOrder);
            }

            // Paginate results
            $templates = $query->paginate($perPage);

            return ApiResponse::success(
                $templates,
                'Templates fetched successfully'
            );
        } catch (\Exception $e) {
            Log::error('Error fetching templates: ' . $e->getMessage());
            return ApiResponse::error(
                'Failed to fetch templates',
                500,
                'INTERNAL_SERVER_ERROR'
            );
        }
    }

    // API 2: Get all template details with pages and page blocks
    public function getTemplateDetails(Request $request, $templateId)
    {
        try {
            // Fetch the template with its pages and page blocks, filter by page_id if provided
            $pageId = $request->input('page_id');

            $template = Template::with([
                'categories',
                'templatePages' => function ($query) use ($pageId) {
                    if ($pageId) {
                        $query->where('id', $pageId);
                    }
                    $query->with('templatePageBlocks');
                },
                'globalAssets'
            ])
                ->findOrFail($templateId);

            // Group global assets by asset_type
            $groupedAssets = $template->globalAssets
                ->groupBy('asset_type')
                ->map(function ($assets) {
                    return $assets->map(function ($asset) {
                        return [
                            'id' => $asset->id,
                            'value' => $asset->asset_value,
                        ];
                    })->unique('value')->values();
                });

            // Replace the original globalAssets with the grouped version
            $template->setRelation('globalAssets', $groupedAssets);

            return ApiResponse::success(
                $template,
                'Template details fetched successfully'
            );
        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return ApiResponse::error(
                'Template not found',
                404,
                'RESOURCE_NOT_FOUND'
            );
        } catch (\Exception $e) {
            Log::error('Error fetching template details: ' . $e->getMessage());
            return ApiResponse::error(
                'Failed to fetch template details',
                500,
                'INTERNAL_SERVER_ERROR'
            );
        }
    }
}
