<?php

namespace App\Http\Controllers;

use App\Models\Template;
use App\Models\TemplateCategory;
use App\Models\Page;
use App\Traits\ApiResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class TemplateController extends Controller
{
    use ApiResponse;
    // Get template data with category name and page count
    public function getTemplateData(Request $request)
    {
        print_r('ss');
        exit;;
        $search = $request->input('search');
        $perPage = $request->input('limit', 10); // Default to 10 if per_page is not provided
        $sort = $request->input('sort');
        $sortField = str_contains($sort, '-') ? str_replace('-', '', $sort) : $sort;
        $sortOrder = str_contains($sort, '-') ? 'desc' : 'asc';
        dd('ss');
        exit;
        // Query Templates with the relevant filters
        $query = Template::with(['categories'])->withCount('templatePages');
        dd($query);
        if ($sortField === 'template_pages_count') {
            $query->orderBy('template_pages_count', $sortOrder);
        } else {
            $query->orderBy($sortField, $sortOrder);
        }

        // Paginate results
        $templates = $query->paginate($perPage);

        // Append the page count attribute
        $templates->getCollection()->transform(function ($template) {
            $template->template_pages_count = $template->template_pages_count;
            return $template;
        });

        return $this->successResponse('Templates fetched successfully', ['data' => $templates]);
    }

    // API 2: Get all template details with pages and page blocks
    public function getTemplateDetails(Request $request, $templateId)
    {
        // Fetch the template with its pages and page blocks, filter by page_id if provided
        $pageId = $request->input('page_id');

        $template = Template::with([
            'categories',
            'templatePages' => function ($query) use ($pageId) {
                if ($pageId) {
                    $query->where('id', $pageId);
                }
                $query->with('templatePageBlocks');
            },
            'globalAssets'
        ])
            ->findOrFail($templateId);

        // Group global assets by asset_type
        $groupedAssets = $template->globalAssets
            ->groupBy('asset_type')
            ->map(function ($assets) {
                return $assets->map(function ($asset) {
                    return [
                        'id' => $asset->id,
                        'value' => $asset->asset_value,
                    ];
                })->unique('value')->values();
            });

        // Replace the original globalAssets with the grouped version
        $template->setRelation('globalAssets', $groupedAssets);

        return $this->successResponse('Template details fetched successfully', ['data' => $template], 200);
    }
}
