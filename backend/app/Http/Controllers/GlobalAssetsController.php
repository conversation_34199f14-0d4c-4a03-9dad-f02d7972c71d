<?php

namespace App\Http\Controllers;

use App\Models\GlobalAssets;
use App\Models\Template;
use App\Traits\ApiResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;

class GlobalAssetsController extends Controller
{
    use ApiResponse;

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $perPage = $request->input('per_page', 10);
        $search = $request->input('search');
        $assetType = $request->input('asset_type');

        $sort = $request->input('sort');
        $sortField = str_contains($sort, '-') ? str_replace('-', '', $sort) : $sort;
        $sortOrder = str_contains($sort, '-') ? 'desc' : 'asc';

        $query = GlobalAssets::query()
            ->when($search, function ($query, $search) {
                return $query->where('asset_value', 'like', "%{$search}%");
            })
            ->when($assetType, function ($query, $assetType) {
                return $query->where('asset_type', $assetType);
            })
            ->with(['templates' => function ($query) {
                $query->select('templates.id', 'templates.name');
            }]);

        $query->orderBy($sortField, $sortOrder);

        $assets = $query->paginate($perPage);

        return $this->successResponse('Global assets retrieved successfully', ['data' => $assets]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'asset_type' => 'required|string|max:100',
            'asset_value' => 'required|string',
            'template_ids' => 'nullable|array',
            'template_ids.*' => 'exists:templates,id',
        ]);

        try {
            DB::beginTransaction();

            $asset = GlobalAssets::create([
                'asset_type' => $validated['asset_type'],
                'asset_value' => $validated['asset_value'],
            ]);

            if (!empty($validated['template_ids'])) {
                $asset->templates()->sync($validated['template_ids']);
            }

            DB::commit();

            $asset->load('templates');
            return $this->successResponse('Global asset created successfully', ['data' => $asset], 201);
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->errorResponse('Failed to create global asset', $e->getMessage(), 500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show($id)
    {
        $asset = GlobalAssets::with(['templates' => function ($query) {
            $query->select('templates.id', 'templates.name');
        }])->findOrFail($id);

        return $this->successResponse('Global asset retrieved successfully', ['data' => $asset]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, $id)
    {
        $validated = $request->validate([
            'asset_type' => 'sometimes|required|string|max:100',
            'asset_value' => 'sometimes|required|string',
            'template_ids' => 'nullable|array',
            'template_ids.*' => 'exists:templates,id',
        ]);

        try {
            DB::beginTransaction();

            $asset = GlobalAssets::findOrFail($id);

            $asset->update([
                'asset_type' => $validated['asset_type'] ?? $asset->asset_type,
                'asset_value' => $validated['asset_value'] ?? $asset->asset_value,
            ]);

            if (array_key_exists('template_ids', $validated)) {
                $asset->templates()->sync($validated['template_ids']);
            }

            DB::commit();

            $asset->load('templates');
            return $this->successResponse('Global asset updated successfully', ['data' => $asset]);
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->errorResponse('Failed to update global asset', $e->getMessage(), 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($id)
    {
        try {
            $asset = GlobalAssets::findOrFail($id);

            DB::beginTransaction();

            // Detach all relationships first
            $asset->templates()->detach();

            // Delete the asset
            $asset->delete();

            DB::commit();

            return $this->successResponse('Global asset deleted successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->errorResponse('Failed to delete global asset', $e->getMessage(), 500);
        }
    }
}
