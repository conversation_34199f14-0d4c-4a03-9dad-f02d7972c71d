<?php

namespace App\Http\Controllers;

use App\Models\BlockCategory;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class BlockCategoryController extends Controller
{
    public function index(Request $request)
    {
        $categoryId = $request->input('category_id');
        $search = $request->input('search');
        $sortField = $request->input('sort_field', 'id'); // default sorting by 'id'
        $sortOrder = $request->input('sort_order', 'desc'); // default 'desc'
        $perPage = $request->input('per_page', 10);

        $query = BlockCategory::with(['blocks']);

        // Filtering
        if (!empty($categoryId)) {
            $query->where('id', $categoryId);
        }

        // Search by category name
        if (!empty($search)) {
            $query->where('name', 'like', '%' . $search . '%');
        }

        // Sorting
        if (in_array($sortField, ['id', 'name']) && in_array(strtolower($sortOrder), ['asc', 'desc'])) {
            $query->orderBy($sortField, $sortOrder);
        }

        // Paginate
        $categories = $query->paginate($perPage);

        // Format the response
        $result = $categories->through(function ($category) {
            return [
                'category_id' => $category->id,
                'category_name' => $category->name,
                'category_image' => $category->image,
                'block_count' => $category->blocks->count(),
                // 'blocks' => $category->blocks->map(function ($block) {
                //     return [
                //         'block_id' => $block->id,
                //         'block_name' => $block->name,
                //         'block_slug' => $block->slug,
                //         'block_description' => $block->description,
                //         'block_image' => $block->image,
                //     ];
                // }),
            ];
        });

        return response()->json([
            'data' => $result,
            // 'current_page' => $categories->currentPage(),
            // 'last_page' => $categories->lastPage(),
            // 'per_page' => $categories->perPage(),
            // 'total' => $categories->total(),
        ]);
    }

    public function getBlocks(Request $request)
    {
        
        // Inputs
        $categoryId = $request->input('category_id');
        $search     = $request->input('search');
        $sortBy     = $request->input('sort_by', 'blocks.id');
        $sortOrder  = $request->input('sort_order', 'asc');
        $perPage    = $request->input('per_page', 10);

        // Base query
        $query = DB::table('blocks')
            ->select(
                'blocks.*',
                'block_categories_mapping.category_id',
                'block_categories.name as category_name',
                DB::raw('GROUP_CONCAT(DISTINCT tags.tag SEPARATOR ", ") as tag_names')
            )
            ->leftJoin('block_categories_mapping', 'block_categories_mapping.block_id', '=', 'blocks.id')
            ->leftJoin('block_categories', 'block_categories.id', '=', 'block_categories_mapping.category_id')
            ->leftJoin('common_tags', function ($join) {
                $join->on('common_tags.model_id', '=', 'blocks.id')
                    ->where('common_tags.model_type', '=', 'App\Models\Block');
            })
            ->leftJoin('tags', 'tags.id', '=', 'common_tags.tag_id')
            ->groupBy('blocks.id', 'blocks.name', 'blocks.slug', 'blocks.description', 'blocks.image', 'block_categories_mapping.category_id', 'block_categories.name');

        // Filter by category
        if (!empty($categoryId)) {
            $query->where('block_categories_mapping.category_id', $categoryId);
        }

        // Search filter
        if (!empty($search)) {
            $query->where(function ($q) use ($search) {
                $q->where('blocks.name', 'like', "%$search%")
                    ->orWhere('blocks.description', 'like', "%$search%");
            });
        }

        // Sorting
        $query->orderBy($sortBy, $sortOrder);

        // Paginate
        $blocks = $query->paginate($perPage);

        return response()->json($blocks);
    }
}
