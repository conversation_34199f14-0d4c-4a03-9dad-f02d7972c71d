<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Services\AuthService;
use App\Http\Requests\Auth\RegisterRequest;
use App\Http\Requests\Auth\LoginRequest;
use Indianic\RestApi\Helpers\ApiResponse;
use Illuminate\Http\Request;
use Illuminate\Auth\AuthenticationException;

class UserAuthController extends Controller
{
    protected AuthService $authService;

    public function __construct(AuthService $authService)
    {
        $this->authService = $authService;
    }

    /**
     * Register a new user
     */
    public function register(RegisterRequest $request)
    {
        try {
            $result = $this->authService->register($request->validated(), User::class);

            return ApiResponse::success(
                $result,
                'User registered successfully.',
                201
            );
        } catch (\Exception $e) {
            return ApiResponse::error(
                'Registration failed. Please try again.',
                500,
                'REGISTRATION_FAILED'
            );
        }
    }
    /**
     * Authenticate user
     */
    public function login(LoginRequest $request)
    {
        try {
            $result = $this->authService->login($request->validated(), User::class);

            return ApiResponse::success(
                $result,
                'User logged in successfully.'
            );
        } catch (AuthenticationException $e) {
            return ApiResponse::error(
                $e->getMessage(),
                401,
                'AUTHENTICATION_FAILED'
            );
        } catch (\Exception $e) {
            return ApiResponse::error(
                'Login failed. Please try again.',
                500,
                'LOGIN_FAILED'
            );
        }
    }

    /**
     * Logout user
     */
    public function logout(Request $request)
    {
        try {
            $this->authService->logout($request->user());

            return ApiResponse::success(
                [],
                'User logged out successfully.'
            );
        } catch (\Exception $e) {
            return ApiResponse::error(
                'Logout failed. Please try again.',
                500,
                'LOGOUT_FAILED'
            );
        }
    }

    /**
     * Get user profile
     */
    public function profile(Request $request)
    {
        try {
            $user = $request->user();

            return ApiResponse::success(
                [
                    'id' => $user->id,
                    'name' => $user->name,
                    'email' => $user->email,
                    'email_verified_at' => $user->email_verified_at?->toISOString(),
                    'last_login_at' => $user->last_login_at?->toISOString(),
                    'created_at' => $user->created_at->toISOString(),
                    'updated_at' => $user->updated_at->toISOString(),
                ],
                'User profile retrieved successfully.'
            );
        } catch (\Exception $e) {
            return ApiResponse::error(
                'Failed to retrieve user profile.',
                500,
                'PROFILE_RETRIEVAL_FAILED'
            );
        }
    }

    /**
     * Refresh user token
     */
    public function refreshToken(Request $request)
    {
        try {
            $result = $this->authService->refreshToken($request->user());

            return ApiResponse::success(
                $result,
                'Token refreshed successfully.'
            );
        } catch (\Exception $e) {
            return ApiResponse::error(
                'Token refresh failed.',
                500,
                'TOKEN_REFRESH_FAILED'
            );
        }
    }
}
