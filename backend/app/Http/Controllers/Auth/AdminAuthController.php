<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\Admin;
use App\Services\AuthService;
use App\Traits\ApiResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;
use Illuminate\Validation\Rules;

class AdminAuthController extends Controller
{
    use ApiResponse;

    protected AuthService $authService;

    public function __construct(AuthService $authService)
    {
        $this->authService = $authService;
    }

    public function register(Request $request)
    {
        try {
            $validatedData = $request->validate([
                'name' => ['required', 'string', 'max:255'],
                'email' => ['required', 'string', 'email', 'max:255', 'unique:admins'],
                'password' => ['required', 'confirmed', Rules\Password::defaults()],
            ]);

            $result = $this->authService->register($validatedData, Admin::class);

            return $this->successResponse(
                AuthResponseMessages::REGISTRATION_SUCCESSFUL,
                $result,
                201
            );
        } catch (ValidationException $e) {
            return $this->validationErrorResponse(
                AuthResponseMessages::VALIDATION_FAILED,
                $e->errors()
            );
        } catch (\Exception $e) {
            return $this->errorResponse(
                AuthResponseMessages::REGISTRATION_FAILED,
                AuthResponseMessages::REGISTRATION_ERROR,
                500
            );
        }
    }
    public function login(Request $request)
    {
        try {
            $credentials = $request->validate([
                'email' => 'required|email',
                'password' => 'required',
            ]);

            $result = $this->authService->login($credentials, Admin::class);

            return $this->successResponse(
                AuthResponseMessages::LOGIN_SUCCESSFUL,
                $result
            );
        } catch (ValidationException $e) {
            return $this->validationErrorResponse(
                AuthResponseMessages::VALIDATION_FAILED,
                $e->errors()
            );
        } catch (\Exception $e) {
            return $this->errorResponse(
                AuthResponseMessages::LOGIN_FAILED,
                AuthResponseMessages::LOGIN_ERROR,
                500
            );
        }
    }

    public function logout(Request $request)
    {
        try {
            $request->user()->currentAccessToken()->delete();

            return $this->successResponse(AuthResponseMessages::LOGOUT_SUCCESSFUL);
        } catch (\Exception $e) {
            return $this->errorResponse(
                AuthResponseMessages::LOGOUT_FAILED,
                AuthResponseMessages::LOGOUT_ERROR,
                500
            );
        }
    }

    public function profile(Request $request)
    {
        try {
            $user = $request->user();
            $userDetails['data'] = [
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email,
                // Add any additional user details you want to include
            ];

            return $this->successResponse(AuthResponseMessages::LOGGED_IN_USER_SUCCESSFUL, $userDetails);
        } catch (\Exception $e) {
            return $this->errorResponse(
                AuthResponseMessages::LOGGED_IN_USER_FAILED,
                AuthResponseMessages::LOGGED_IN_USER_ERROR,
                500
            );
        }
    }
}
