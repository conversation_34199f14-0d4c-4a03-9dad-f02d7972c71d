<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class ImageUploadController extends Controller
{
    public function uploadMultiple(Request $request)
    {

        // Validate the request
        $request->validate([
            'images' => 'required|array',
            'images.*' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048',
            'folder' => 'required|string|max:255',
        ]);

        $images = $request->file('images');
        $folder = Str::slug($request->input('folder')); // clean folder name

        $paths = [];

        // Store the files in the specified folder in the 'public' disk
        foreach ($images as $image) {
            $path = $image->store("uploads/{$folder}", 'public');
            $paths[] = $path;
        }

        return response()->json([
            'message' => 'Images uploaded successfully',
            'paths' => $paths,
            'urls' => collect($paths)->map(fn($path) => asset("storage/{$path}")),
        ]);
    }
}
