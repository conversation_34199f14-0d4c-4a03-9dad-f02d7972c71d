<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\RateLimiter;
use Indianic\RestApi\Helpers\ApiResponse;

class ApiRateLimiter
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next, string $limit = '60:1'): mixed
    {
        [$maxAttempts, $decayMinutes] = explode(':', $limit);
        
        $key = $this->resolveRequestSignature($request);
        
        if (RateLimiter::tooManyAttempts($key, $maxAttempts)) {
            $retryAfter = RateLimiter::availableIn($key);
            
            return ApiResponse::error(
                "Too many requests. Please try again in {$retryAfter} seconds.",
                429,
                'RATE_LIMIT_EXCEEDED',
                [
                    'retry_after' => $retryAfter,
                    'limit' => $maxAttempts,
                    'window' => $decayMinutes . ' minutes'
                ]
            )->withHeaders([
                'X-RateLimit-Limit' => $maxAttempts,
                'X-RateLimit-Remaining' => 0,
                'X-RateLimit-Reset' => now()->addMinutes($decayMinutes)->timestamp,
                'Retry-After' => $retryAfter,
            ]);
        }
        
        RateLimiter::hit($key, $decayMinutes * 60);
        
        $response = $next($request);
        
        // Add rate limit headers to successful responses
        $remaining = RateLimiter::remaining($key, $maxAttempts);
        
        return $response->withHeaders([
            'X-RateLimit-Limit' => $maxAttempts,
            'X-RateLimit-Remaining' => $remaining,
            'X-RateLimit-Reset' => now()->addMinutes($decayMinutes)->timestamp,
        ]);
    }
    
    /**
     * Resolve the request signature for rate limiting
     */
    protected function resolveRequestSignature(Request $request): string
    {
        $user = $request->user();
        
        if ($user) {
            return 'api:user:' . $user->id;
        }
        
        return 'api:ip:' . $request->ip();
    }
}
