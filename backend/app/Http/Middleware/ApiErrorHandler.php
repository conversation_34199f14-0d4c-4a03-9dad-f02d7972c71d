<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\ValidationException;
use Illuminate\Auth\AuthenticationException;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\HttpKernel\Exception\MethodNotAllowedHttpException;
use Indianic\RestApi\Helpers\ApiResponse;
use Illuminate\Support\Facades\Log;

class ApiErrorHandler
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response|JsonResponse
    {
        try {
            return $next($request);
        } catch (\Throwable $e) {
            return $this->handleException($request, $e);
        }
    }

    /**
     * Handle exceptions and return appropriate API responses
     */
    protected function handleException(Request $request, \Throwable $e): JsonResponse
    {
        // Log the exception
        Log::error('API Exception', [
            'exception' => get_class($e),
            'message' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'url' => $request->fullUrl(),
            'method' => $request->method(),
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent(),
        ]);

        // Handle specific exception types
        return match (true) {
            $e instanceof ValidationException => $this->handleValidationException($e),
            $e instanceof AuthenticationException => $this->handleAuthenticationException($e),
            $e instanceof AuthorizationException => $this->handleAuthorizationException($e),
            $e instanceof ModelNotFoundException => $this->handleModelNotFoundException($e),
            $e instanceof NotFoundHttpException => $this->handleNotFoundHttpException($e),
            $e instanceof MethodNotAllowedHttpException => $this->handleMethodNotAllowedException($e),
            default => $this->handleGenericException($e)
        };
    }

    protected function handleValidationException(ValidationException $e): JsonResponse
    {
        return ApiResponse::validationError(
            'The given data was invalid.',
            $e->errors()
        );
    }

    protected function handleAuthenticationException(AuthenticationException $e): JsonResponse
    {
        return ApiResponse::error(
            'Authentication required.',
            401,
            'AUTHENTICATION_REQUIRED'
        );
    }

    protected function handleAuthorizationException(AuthorizationException $e): JsonResponse
    {
        return ApiResponse::error(
            'You do not have permission to perform this action.',
            403,
            'INSUFFICIENT_PERMISSIONS'
        );
    }

    protected function handleModelNotFoundException(ModelNotFoundException $e): JsonResponse
    {
        $model = class_basename($e->getModel());
        return ApiResponse::error(
            "The requested {$model} was not found.",
            404,
            'RESOURCE_NOT_FOUND'
        );
    }

    protected function handleNotFoundHttpException(NotFoundHttpException $e): JsonResponse
    {
        return ApiResponse::error(
            'The requested endpoint was not found.',
            404,
            'ENDPOINT_NOT_FOUND'
        );
    }

    protected function handleMethodNotAllowedException(MethodNotAllowedHttpException $e): JsonResponse
    {
        return ApiResponse::error(
            'The HTTP method is not allowed for this endpoint.',
            405,
            'METHOD_NOT_ALLOWED'
        );
    }

    protected function handleGenericException(\Throwable $e): JsonResponse
    {
        $message = config('app.debug')
            ? $e->getMessage()
            : 'An unexpected error occurred.';

        $details = config('app.debug') ? [
            'exception' => get_class($e),
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'trace' => $e->getTraceAsString()
        ] : [];

        return ApiResponse::error(
            $message,
            500,
            'INTERNAL_SERVER_ERROR',
            $details
        );
    }
}
