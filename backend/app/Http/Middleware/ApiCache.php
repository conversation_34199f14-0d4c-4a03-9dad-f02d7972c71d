<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class ApiCache
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next, int $ttl = null): mixed
    {
        // Only cache GET requests
        if ($request->method() !== 'GET') {
            return $next($request);
        }

        // Skip caching if disabled
        if (!config('api.caching.enabled', true)) {
            return $next($request);
        }

        $cacheKey = $this->generateCacheKey($request);
        $cacheTtl = $ttl ?? config('api.caching.ttl', 3600);

        // Try to get cached response
        $cachedResponse = Cache::get($cacheKey);
        
        if ($cachedResponse) {
            Log::debug('API Cache Hit', ['key' => $cacheKey]);
            
            return response()->json($cachedResponse['data'], $cachedResponse['status'])
                ->withHeaders(array_merge($cachedResponse['headers'], [
                    'X-Cache' => 'HIT',
                    'X-Cache-Key' => $cacheKey,
                ]));
        }

        // Process request
        $response = $next($request);

        // Cache successful responses
        if ($response->isSuccessful() && $response instanceof \Illuminate\Http\JsonResponse) {
            $cacheData = [
                'data' => $response->getData(true),
                'status' => $response->getStatusCode(),
                'headers' => $this->getCacheableHeaders($response),
            ];

            Cache::put($cacheKey, $cacheData, $cacheTtl);
            
            Log::debug('API Cache Miss - Cached', ['key' => $cacheKey, 'ttl' => $cacheTtl]);
            
            $response->withHeaders([
                'X-Cache' => 'MISS',
                'X-Cache-Key' => $cacheKey,
                'X-Cache-TTL' => $cacheTtl,
            ]);
        }

        return $response;
    }

    /**
     * Generate cache key for the request
     */
    protected function generateCacheKey(Request $request): string
    {
        $user = $request->user();
        $userId = $user ? $user->id : 'guest';
        
        $key = sprintf(
            'api:cache:%s:%s:%s:%s',
            $userId,
            $request->path(),
            $request->method(),
            md5($request->getQueryString() ?? '')
        );

        return $key;
    }

    /**
     * Get headers that should be cached
     */
    protected function getCacheableHeaders($response): array
    {
        $cacheableHeaders = [
            'Content-Type',
            'X-RateLimit-Limit',
            'X-RateLimit-Remaining',
        ];

        $headers = [];
        foreach ($cacheableHeaders as $header) {
            if ($response->headers->has($header)) {
                $headers[$header] = $response->headers->get($header);
            }
        }

        return $headers;
    }
}
