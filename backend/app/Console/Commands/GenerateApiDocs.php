<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Str;

class GenerateApiDocs extends Command
{
    protected $signature = 'api:docs {--format=json : Output format (json|yaml)}';
    protected $description = 'Generate OpenAPI documentation for the API';

    public function handle(): int
    {
        $format = $this->option('format');
        $routes = $this->getApiRoutes();
        $documentation = $this->generateOpenApiSpec($routes);

        $filename = $format === 'yaml' ? 'api-docs.yaml' : 'api-docs.json';
        $path = storage_path("app/docs/{$filename}");

        // Ensure directory exists
        if (!is_dir(dirname($path))) {
            mkdir(dirname($path), 0755, true);
        }

        if ($format === 'yaml') {
            file_put_contents($path, yaml_emit($documentation));
        } else {
            file_put_contents($path, json_encode($documentation, JSON_PRETTY_PRINT));
        }

        $this->info("API documentation generated: {$path}");
        return 0;
    }

    protected function getApiRoutes(): array
    {
        $routes = [];
        
        foreach (Route::getRoutes() as $route) {
            $uri = $route->uri();
            
            // Only include API routes
            if (!Str::startsWith($uri, 'api/v1/')) {
                continue;
            }

            $routes[] = [
                'uri' => $uri,
                'methods' => $route->methods(),
                'name' => $route->getName(),
                'action' => $route->getActionName(),
                'middleware' => $route->middleware(),
                'parameters' => $route->parameterNames(),
            ];
        }

        return $routes;
    }

    protected function generateOpenApiSpec(array $routes): array
    {
        $config = config('api.documentation');
        
        $spec = [
            'openapi' => '3.0.0',
            'info' => [
                'title' => $config['title'],
                'description' => $config['description'],
                'version' => $config['version'],
                'contact' => $config['contact'],
            ],
            'servers' => $config['servers'],
            'paths' => [],
            'components' => [
                'securitySchemes' => [
                    'bearerAuth' => [
                        'type' => 'http',
                        'scheme' => 'bearer',
                        'bearerFormat' => 'JWT',
                    ],
                ],
                'schemas' => $this->getCommonSchemas(),
            ],
        ];

        foreach ($routes as $route) {
            $path = '/' . ltrim($route['uri'], '/');
            $path = preg_replace('/\{([^}]+)\}/', '{\1}', $path);

            foreach ($route['methods'] as $method) {
                if (in_array($method, ['HEAD', 'OPTIONS'])) {
                    continue;
                }

                $spec['paths'][$path][strtolower($method)] = $this->generatePathSpec($route, $method);
            }
        }

        return $spec;
    }

    protected function generatePathSpec(array $route, string $method): array
    {
        $spec = [
            'summary' => $this->generateSummary($route, $method),
            'tags' => [$this->getTagFromRoute($route)],
            'responses' => [
                '200' => [
                    'description' => 'Successful response',
                    'content' => [
                        'application/json' => [
                            'schema' => ['$ref' => '#/components/schemas/SuccessResponse'],
                        ],
                    ],
                ],
                '400' => [
                    'description' => 'Bad request',
                    'content' => [
                        'application/json' => [
                            'schema' => ['$ref' => '#/components/schemas/ErrorResponse'],
                        ],
                    ],
                ],
                '401' => [
                    'description' => 'Unauthorized',
                    'content' => [
                        'application/json' => [
                            'schema' => ['$ref' => '#/components/schemas/ErrorResponse'],
                        ],
                    ],
                ],
                '422' => [
                    'description' => 'Validation error',
                    'content' => [
                        'application/json' => [
                            'schema' => ['$ref' => '#/components/schemas/ValidationErrorResponse'],
                        ],
                    ],
                ],
            ],
        ];

        // Add security if route requires authentication
        if (in_array('auth:api', $route['middleware']) || in_array('auth:admin', $route['middleware'])) {
            $spec['security'] = [['bearerAuth' => []]];
        }

        // Add parameters for route parameters
        if (!empty($route['parameters'])) {
            $spec['parameters'] = [];
            foreach ($route['parameters'] as $parameter) {
                $spec['parameters'][] = [
                    'name' => $parameter,
                    'in' => 'path',
                    'required' => true,
                    'schema' => ['type' => 'string'],
                ];
            }
        }

        // Add request body for POST/PUT/PATCH methods
        if (in_array($method, ['POST', 'PUT', 'PATCH'])) {
            $spec['requestBody'] = [
                'required' => true,
                'content' => [
                    'application/json' => [
                        'schema' => ['type' => 'object'],
                    ],
                ],
            ];
        }

        return $spec;
    }

    protected function generateSummary(array $route, string $method): string
    {
        $action = class_basename($route['action']);
        $method = strtoupper($method);
        
        return "{$method} {$action}";
    }

    protected function getTagFromRoute(array $route): string
    {
        $uri = $route['uri'];
        
        if (Str::contains($uri, 'user')) return 'Users';
        if (Str::contains($uri, 'admin')) return 'Admins';
        if (Str::contains($uri, 'template')) return 'Templates';
        if (Str::contains($uri, 'block')) return 'Blocks';
        if (Str::contains($uri, 'asset')) return 'Assets';
        
        return 'General';
    }

    protected function getCommonSchemas(): array
    {
        return [
            'SuccessResponse' => [
                'type' => 'object',
                'properties' => [
                    'status' => ['type' => 'string', 'example' => 'success'],
                    'version' => ['type' => 'string', 'example' => 'v1'],
                    'timestamp' => ['type' => 'string', 'format' => 'date-time'],
                    'data' => ['type' => 'object'],
                    'message' => ['type' => 'string'],
                    'meta' => ['type' => 'object'],
                ],
            ],
            'ErrorResponse' => [
                'type' => 'object',
                'properties' => [
                    'status' => ['type' => 'string', 'example' => 'error'],
                    'version' => ['type' => 'string', 'example' => 'v1'],
                    'timestamp' => ['type' => 'string', 'format' => 'date-time'],
                    'error' => [
                        'type' => 'object',
                        'properties' => [
                            'code' => ['type' => 'integer'],
                            'message' => ['type' => 'string'],
                            'type' => ['type' => 'string'],
                            'details' => ['type' => 'object'],
                        ],
                    ],
                ],
            ],
            'ValidationErrorResponse' => [
                'type' => 'object',
                'properties' => [
                    'status' => ['type' => 'string', 'example' => 'error'],
                    'version' => ['type' => 'string', 'example' => 'v1'],
                    'timestamp' => ['type' => 'string', 'format' => 'date-time'],
                    'error' => [
                        'type' => 'object',
                        'properties' => [
                            'code' => ['type' => 'integer', 'example' => 422],
                            'message' => ['type' => 'string'],
                            'type' => ['type' => 'string', 'example' => 'validation_error'],
                            'validation_errors' => ['type' => 'object'],
                        ],
                    ],
                ],
            ],
        ];
    }
}
