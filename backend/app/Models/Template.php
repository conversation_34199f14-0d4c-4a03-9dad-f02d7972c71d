<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Template extends Model
{
    protected $table = 'templates';

    // Define relationships
    public function categories()
    {
        return $this->belongsToMany(TemplateCategory::class, 'template_categories_mapping', 'template_id', 'template_category_id')->withPivot('id');
    }

    public function pages()
    {
        return $this->hasMany(Page::class, 'template_id');
    }

    // Add the page count accessor (for pagination)
    public function getPagesCountAttribute()
    {
        return $this->pages()->count();
    }

    /**
     * Get the global assets associated with this template.
     */
    public function globalAssets()
    {
        return $this->belongsToMany(
            GlobalAssets::class,
            'global_assets_mapping',
            'template_id',
            'global_assets_id'
        );
    }
}
