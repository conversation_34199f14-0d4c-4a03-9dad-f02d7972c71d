<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Page extends Model
{
    protected $table = 'pages';

    // Define the relationship with template
    public function template()
    {
        return $this->belongsTo(Template::class, 'template_id');
    }

    // Define the relationship with page_blocks
    public function pageBlocks()
    {
        return $this->hasMany(PageBlock::class);
    }
}
