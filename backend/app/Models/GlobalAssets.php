<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class GlobalAssets extends Model
{
    protected $table = 'global_assets';

    protected $fillable = [
        'asset_type',
        'asset_value'
    ];

    /**
     * Get the templates that belong to this global asset.
     */
    public function templates(): BelongsToMany
    {
        return $this->belongsToMany(
            Template::class,
            'global_assets_mapping',
            'global_assets_id',
            'template_id'
        );
    }
}
