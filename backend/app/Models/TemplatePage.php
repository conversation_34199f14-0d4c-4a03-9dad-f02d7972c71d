<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class TemplatePage extends Model
{
    protected $table = 'template_pages';

    // Define the relationship with template
    public function template()
    {
        return $this->belongsTo(Template::class, 'template_id');
    }

    // Define the relationship with page_blocks
    public function templatePageBlocks()
    {
        return $this->hasMany(TemplatePageBlock::class);
    }
}
