<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class GlobalAssetsMapping extends Model
{
    protected $table = 'global_assets_mapping';
    public $timestamps = false;

    protected $fillable = [
        'global_assets_id',
        'template_id'
    ];

    public function globalAsset()
    {
        return $this->belongsTo(GlobalAssets::class, 'global_assets_id');
    }

    public function template()
    {
        return $this->belongsTo(Template::class);
    }
}
