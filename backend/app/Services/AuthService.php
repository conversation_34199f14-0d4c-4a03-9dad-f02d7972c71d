<?php

namespace App\Services;

use App\Http\Controllers\Auth\AuthResponseMessages;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Validation\ValidationException;
use Illuminate\Auth\AuthenticationException;
use Carbon\Carbon;

class AuthService
{
    /**
     * Register a new user
     */
    public function register(array $validatedData, string $modelClass): array
    {
        $user = $modelClass::create([
            'name' => $validatedData['name'],
            'email' => $validatedData['email'],
            'password' => Hash::make($validatedData['password']),
            'email_verified_at' => now(), // Auto-verify for now
        ]);

        $token = $user->createToken('auth-token', ['*'], now()->addDays(30))->plainTextToken;

        return [
            'user' => $this->formatUserData($user),
            'token' => $token,
            'token_type' => 'Bearer',
            'expires_at' => now()->addDays(30)->toISOString()
        ];
    }

    /**
     * Authenticate user and generate token
     */
    public function login(array $credentials, string $modelClass): array
    {
        $email = $credentials['email'];

        // Rate limiting
        $key = 'login.' . $email;
        if (RateLimiter::tooManyAttempts($key, 5)) {
            $seconds = RateLimiter::availableIn($key);
            throw new AuthenticationException(
                "Too many login attempts. Please try again in {$seconds} seconds."
            );
        }

        $user = $modelClass::where('email', $email)->first();

        if (!$user || !Hash::check($credentials['password'], $user->password)) {
            RateLimiter::hit($key, 300); // 5 minutes lockout
            throw new AuthenticationException('Invalid credentials provided.');
        }

        // Clear rate limiting on successful login
        RateLimiter::clear($key);

        // Revoke existing tokens (optional - for single session)
        // $user->tokens()->delete();

        $token = $user->createToken('auth-token', ['*'], now()->addDays(30))->plainTextToken;

        // Update last login
        $user->update(['last_login_at' => now()]);

        return [
            'user' => $this->formatUserData($user),
            'token' => $token,
            'token_type' => 'Bearer',
            'expires_at' => now()->addDays(30)->toISOString()
        ];
    }

    /**
     * Logout user and revoke token
     */
    public function logout($user): bool
    {
        $user->currentAccessToken()->delete();
        return true;
    }

    /**
     * Refresh user token
     */
    public function refreshToken($user): array
    {
        // Revoke current token
        $user->currentAccessToken()->delete();

        // Create new token
        $token = $user->createToken('auth-token', ['*'], now()->addDays(30))->plainTextToken;

        return [
            'token' => $token,
            'token_type' => 'Bearer',
            'expires_at' => now()->addDays(30)->toISOString()
        ];
    }

    /**
     * Format user data for API response
     */
    private function formatUserData($user): array
    {
        return [
            'id' => $user->id,
            'name' => $user->name,
            'email' => $user->email,
            'email_verified_at' => $user->email_verified_at?->toISOString(),
            'last_login_at' => $user->last_login_at?->toISOString(),
            'created_at' => $user->created_at->toISOString(),
            'updated_at' => $user->updated_at->toISOString(),
        ];
    }
}
