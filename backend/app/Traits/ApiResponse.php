<?php

namespace App\Traits;

trait ApiResponse
{
    protected function successResponse(string $message, array $data = [], int $status = 200): \Illuminate\Http\JsonResponse
    {
        return response()->json([
            'status' => "success",
            'message' => $message,
            ...$data
        ], $status);
    }

    protected function errorResponse(string $message, string $error, int $status = 400): \Illuminate\Http\JsonResponse
    {
        return response()->json([
            'status' => "error",
            'message' => $message,
            'error' => $error
        ], $status);
    }

    protected function validationErrorResponse(string $message, array $errors, int $status = 422): \Illuminate\Http\JsonResponse
    {
        return response()->json([
            'status' => false,
            'message' => $message,
            'errors' => $errors
        ], $status);
    }
}
