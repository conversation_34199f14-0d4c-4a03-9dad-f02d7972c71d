<?php

return [

    /*
    |--------------------------------------------------------------------------
    | API Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains configuration options for the API including
    | versioning, documentation, security, and performance settings.
    |
    */

    'version' => env('API_VERSION', 'v1'),

    'documentation' => [
        'enabled' => env('API_DOCS_ENABLED', true),
        'title' => env('API_DOCS_TITLE', 'CMS API Documentation'),
        'description' => 'RESTful API for the CMS application',
        'version' => env('API_VERSION', 'v1'),
        'contact' => [
            'name' => 'API Support',
            'email' => env('API_CONTACT_EMAIL', '<EMAIL>'),
        ],
        'servers' => [
            [
                'url' => env('APP_URL', 'http://localhost') . '/api/v1',
                'description' => 'Development server',
            ],
        ],
    ],

    'security' => [
        'api_key_rotation' => [
            'enabled' => env('API_KEY_ROTATION_ENABLED', false),
            'interval_days' => env('API_KEY_ROTATION_INTERVAL', 30),
        ],
        'token_expiry' => [
            'access_token' => env('API_ACCESS_TOKEN_EXPIRY', 30), // days
            'refresh_token' => env('API_REFRESH_TOKEN_EXPIRY', 90), // days
        ],
    ],

    'rate_limiting' => [
        'global' => env('API_RATE_LIMIT_GLOBAL', '1000:60'), // 1000 requests per hour
        'auth' => env('API_RATE_LIMIT_AUTH', '5:1'), // 5 login attempts per minute
        'user' => env('API_RATE_LIMIT_USER', '60:1'), // 60 requests per minute for authenticated users
        'guest' => env('API_RATE_LIMIT_GUEST', '30:1'), // 30 requests per minute for guests
    ],

    'caching' => [
        'enabled' => env('API_CACHE_ENABLED', true),
        'ttl' => env('API_CACHE_TTL', 3600), // 1 hour
        'tags' => [
            'users' => 'api:users',
            'templates' => 'api:templates',
            'blocks' => 'api:blocks',
            'assets' => 'api:assets',
        ],
    ],

    'pagination' => [
        'default_per_page' => env('API_DEFAULT_PER_PAGE', 15),
        'max_per_page' => env('API_MAX_PER_PAGE', 100),
    ],

    'validation' => [
        'strict_mode' => env('API_VALIDATION_STRICT', true),
        'custom_rules_path' => app_path('Rules'),
    ],

    'logging' => [
        'enabled' => env('API_LOGGING_ENABLED', true),
        'log_requests' => env('API_LOG_REQUESTS', false),
        'log_responses' => env('API_LOG_RESPONSES', false),
        'log_errors' => env('API_LOG_ERRORS', true),
    ],

    'features' => [
        'health_check' => env('API_HEALTH_CHECK_ENABLED', true),
        'metrics' => env('API_METRICS_ENABLED', false),
        'debug_mode' => env('API_DEBUG_MODE', false),
    ],

];
