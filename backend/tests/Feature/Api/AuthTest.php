<?php

namespace Tests\Feature\Api;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class AuthTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected string $apiBase = '/api/v1/cms';

    public function test_user_can_register(): void
    {
        $userData = [
            'name' => $this->faker->name,
            'email' => $this->faker->unique()->safeEmail,
            'password' => 'password123',
            'password_confirmation' => 'password123',
        ];

        $response = $this->postJson("{$this->apiBase}/user/register", $userData);

        $response->assertStatus(201)
            ->assertJsonStructure([
                'status',
                'version',
                'timestamp',
                'data' => [
                    'user' => [
                        'id',
                        'name',
                        'email',
                        'email_verified_at',
                        'created_at',
                        'updated_at',
                    ],
                    'token',
                    'token_type',
                    'expires_at',
                ],
                'message',
            ]);

        $this->assertDatabaseHas('users', [
            'email' => $userData['email'],
        ]);
    }

    public function test_user_can_login(): void
    {
        $user = User::factory()->create([
            'password' => bcrypt('password123'),
        ]);

        $response = $this->postJson("{$this->apiBase}/user/login", [
            'email' => $user->email,
            'password' => 'password123',
        ]);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status',
                'version',
                'timestamp',
                'data' => [
                    'user',
                    'token',
                    'token_type',
                    'expires_at',
                ],
                'message',
            ]);
    }

    public function test_user_login_with_invalid_credentials(): void
    {
        $response = $this->postJson("{$this->apiBase}/user/login", [
            'email' => '<EMAIL>',
            'password' => 'wrongpassword',
        ]);

        $response->assertStatus(401)
            ->assertJsonStructure([
                'status',
                'version',
                'timestamp',
                'error' => [
                    'code',
                    'message',
                    'type',
                ],
            ]);
    }

    public function test_user_can_logout(): void
    {
        $user = User::factory()->create();
        $token = $user->createToken('test-token')->plainTextToken;

        $response = $this->withHeaders([
            'Authorization' => "Bearer {$token}",
        ])->postJson("{$this->apiBase}/user/logout");

        $response->assertStatus(200)
            ->assertJson([
                'status' => 'success',
                'message' => 'User logged out successfully.',
            ]);
    }

    public function test_user_can_get_profile(): void
    {
        $user = User::factory()->create();
        $token = $user->createToken('test-token')->plainTextToken;

        $response = $this->withHeaders([
            'Authorization' => "Bearer {$token}",
        ])->getJson("{$this->apiBase}/user/profile");

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status',
                'version',
                'timestamp',
                'data' => [
                    'id',
                    'name',
                    'email',
                    'email_verified_at',
                    'created_at',
                    'updated_at',
                ],
                'message',
            ]);
    }

    public function test_registration_validation_errors(): void
    {
        $response = $this->postJson("{$this->apiBase}/user/register", [
            'name' => '',
            'email' => 'invalid-email',
            'password' => '123',
        ]);

        $response->assertStatus(422)
            ->assertJsonStructure([
                'status',
                'version',
                'timestamp',
                'error' => [
                    'code',
                    'message',
                    'type',
                    'validation_errors',
                ],
            ]);
    }

    public function test_rate_limiting_on_login(): void
    {
        $loginData = [
            'email' => '<EMAIL>',
            'password' => 'wrongpassword',
        ];

        // Make multiple failed login attempts
        for ($i = 0; $i < 6; $i++) {
            $response = $this->postJson("{$this->apiBase}/user/login", $loginData);
        }

        // The 6th attempt should be rate limited
        $response->assertStatus(429)
            ->assertJsonStructure([
                'status',
                'version',
                'timestamp',
                'error' => [
                    'code',
                    'message',
                    'type',
                    'details' => [
                        'retry_after',
                        'limit',
                        'window',
                    ],
                ],
            ]);
    }
}
