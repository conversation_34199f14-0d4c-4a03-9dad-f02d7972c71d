# API Testing Guide

## 🧪 **Testing the Improved API Structure**

This guide helps you test all the implemented API improvements.

---

## 🚀 **Quick Start Testing**

### **1. Health Check**
```bash
curl -X GET http://localhost/api/health
```

**Expected Response:**
```json
{
  "status": "healthy",
  "version": "v1",
  "timestamp": "2024-01-01T12:00:00.000Z",
  "environment": "local"
}
```

### **2. User Registration**
```bash
curl -X POST http://localhost/api/v1/cms/user/register \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Test User",
    "email": "<EMAIL>",
    "password": "password123",
    "password_confirmation": "password123"
  }'
```

**Expected Response:**
```json
{
  "status": "success",
  "version": "v1",
  "timestamp": "2024-01-01T12:00:00.000Z",
  "data": {
    "user": {
      "id": 1,
      "name": "Test User",
      "email": "<EMAIL>",
      "email_verified_at": "2024-01-01T12:00:00.000Z",
      "created_at": "2024-01-01T12:00:00.000Z",
      "updated_at": "2024-01-01T12:00:00.000Z"
    },
    "token": "1|abc123...",
    "token_type": "Bearer",
    "expires_at": "2024-02-01T12:00:00.000Z"
  },
  "message": "User registered successfully."
}
```

### **3. User Login**
```bash
curl -X POST http://localhost/api/v1/cms/user/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123"
  }'
```

### **4. Authenticated Request**
```bash
# Replace YOUR_TOKEN with the token from login response
curl -X GET http://localhost/api/v1/cms/user/profile \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json"
```

---

## 🔍 **Testing Specific Features**

### **Rate Limiting Test**
```bash
# Make multiple rapid requests to trigger rate limiting
for i in {1..6}; do
  curl -X POST http://localhost/api/v1/cms/user/login \
    -H "Content-Type: application/json" \
    -d '{"email":"<EMAIL>","password":"wrong"}' \
    -w "\nStatus: %{http_code}\n"
done
```

**Expected:** 6th request should return 429 (Too Many Requests)

### **Validation Error Test**
```bash
curl -X POST http://localhost/api/v1/cms/user/register \
  -H "Content-Type: application/json" \
  -d '{
    "name": "",
    "email": "invalid-email",
    "password": "123"
  }'
```

**Expected Response:**
```json
{
  "status": "error",
  "version": "v1",
  "timestamp": "2024-01-01T12:00:00.000Z",
  "error": {
    "code": 422,
    "message": "The given data was invalid.",
    "type": "validation_error",
    "validation_errors": {
      "name": ["The name field is required."],
      "email": ["The email must be a valid email address."],
      "password": ["The password must be at least 8 characters."]
    }
  }
}
```

### **Cache Headers Test**
```bash
# First request (cache miss)
curl -X GET http://localhost/api/v1/cms/user/profile \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -I

# Second request (cache hit)
curl -X GET http://localhost/api/v1/cms/user/profile \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -I
```

**Look for headers:**
- `X-Cache: MISS` (first request)
- `X-Cache: HIT` (second request)
- `X-RateLimit-Limit: 60`
- `X-RateLimit-Remaining: 59`

### **Legacy Route Test**
```bash
curl -X GET http://localhost/api/cms/user/profile
```

**Expected:** 410 Gone with deprecation message

---

## 🧪 **Running Automated Tests**

### **Setup Test Database**
```bash
cd backend
cp .env .env.testing
# Edit .env.testing to use test database
php artisan migrate --env=testing
```

### **Run API Tests**
```bash
php artisan test tests/Feature/Api/AuthTest.php
```

### **Run All Tests**
```bash
php artisan test
```

---

## 📊 **Performance Testing**

### **Load Testing with Apache Bench**
```bash
# Test rate limiting
ab -n 100 -c 10 -H "Content-Type: application/json" \
  -p post_data.json \
  http://localhost/api/v1/cms/user/login

# Test cached endpoints
ab -n 100 -c 10 \
  -H "Authorization: Bearer YOUR_TOKEN" \
  http://localhost/api/v1/cms/user/profile
```

### **Create post_data.json**
```json
{"email":"<EMAIL>","password":"password123"}
```

---

## 📚 **Generate Documentation**

### **Generate OpenAPI Docs**
```bash
php artisan api:docs --format=json
php artisan api:docs --format=yaml
```

**Files created:**
- `storage/app/docs/api-docs.json`
- `storage/app/docs/api-docs.yaml`

---

## 🔧 **Configuration Testing**

### **Environment Variables to Set**
```env
# API Configuration
API_VERSION=v1
API_DOCS_ENABLED=true
API_CACHE_ENABLED=true
API_RATE_LIMIT_GLOBAL=1000:60
API_RATE_LIMIT_AUTH=5:1
API_RATE_LIMIT_USER=60:1

# CORS Configuration
FRONTEND_URL=http://localhost:3000
ADMIN_URL=http://localhost:3001

# Security
API_ACCESS_TOKEN_EXPIRY=30
API_REFRESH_TOKEN_EXPIRY=90
```

### **Test Configuration Loading**
```bash
php artisan config:show api
php artisan config:show cors
```

---

## 🐛 **Debugging**

### **Check Logs**
```bash
tail -f storage/logs/laravel.log
```

### **Clear Cache**
```bash
php artisan cache:clear
php artisan config:clear
php artisan route:clear
```

### **Debug Routes**
```bash
php artisan route:list --path=api
```

---

## ✅ **Success Criteria**

### **All tests should pass:**
- ✅ Health check returns 200
- ✅ User registration works
- ✅ User login returns token
- ✅ Authenticated requests work
- ✅ Rate limiting triggers at 6th attempt
- ✅ Validation errors return 422
- ✅ Cache headers present
- ✅ Legacy routes return 410
- ✅ Automated tests pass
- ✅ Documentation generates

### **Performance benchmarks:**
- ✅ Response time < 200ms
- ✅ Rate limiting works correctly
- ✅ Cache improves response time
- ✅ No memory leaks

---

## 🎯 **Troubleshooting**

### **Common Issues:**

**1. Middleware not working:**
```bash
php artisan route:list --middleware
```

**2. Database connection:**
```bash
php artisan migrate:status
```

**3. Token issues:**
```bash
php artisan sanctum:prune-expired
```

**4. Cache problems:**
```bash
php artisan cache:clear
redis-cli flushall  # if using Redis
```

---

## 🎉 **Next Steps**

After successful testing:

1. **Deploy to staging** environment
2. **Update frontend** to use new API structure
3. **Monitor performance** metrics
4. **Set up API monitoring**
5. **Create user documentation**

**Your API is now production-ready with enterprise-grade features!** 🚀
