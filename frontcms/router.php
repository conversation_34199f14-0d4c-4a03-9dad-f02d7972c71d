<?php
require_once __DIR__ . '/config/config.php';

// Request info
$requestUri = $_SERVER['REQUEST_URI'];
$basePath = rtrim(BASE_URL, '/') . '/';

// Separate path and query string
$pathParts = explode('?', $requestUri, 2);
$pathWithoutQuery = $pathParts[0];
$queryString = $pathParts[1] ?? '';

// Clean path
$path = trim(str_replace($basePath, '', $pathWithoutQuery), '/');

// Debug logs
error_log("Request URI: $requestUri");
error_log("Base Path: $basePath");
error_log("Path: $path");
error_log("Query String: $queryString");

// Route config
$sections = [
    'dashboard'      => 'views/dashboard/index.php',
    'templates'      => 'views/templates/index.php',
    'global_assets'  => 'views/global_assets/index.php',
    'blocks'         => 'views/blocks/index.php',
    'pages'          => 'views/pages/index.php',
    'tags'           => 'views/tags/index.php',
    'media'          => 'views/media/index.php',
    'login'          => 'views/auth/login.php',
    'register'       => 'views/auth/register.php',
    'profile'        => 'views/auth/profile.php',
    'logout'         => 'views/auth/logout.php',
];

$directViews = [
    'blocks/list'        => 'views/blocks/list.php',
    'blocks/form'        => 'views/blocks/form.php',
    'blocks/view'        => 'views/blocks/view.php',
    'pages/form'         => 'views/pages/form.php',
    'pages/view'         => 'views/pages/view.php',
    'pages/preview'      => 'views/pages/preview.php',
    'templates/builder'  => 'views/templates/builder.php',
    'templates/preview'  => 'views/templates/preview.php',
];

// Determine file path
$filePath = '';

// 1. Direct view under views/
if (strpos($path, 'views/') === 0 && file_exists(__DIR__ . '/' . $path)) {
    $filePath = __DIR__ . '/' . $path;

// 2. Mapped direct view
} elseif (isset($directViews[$path]) && file_exists(__DIR__ . '/' . $directViews[$path])) {
    $filePath = __DIR__ . '/' . $directViews[$path];

// 3. Section index fallback
} else {
    $firstSegment = explode('/', $path)[0] ?? '';
    if (isset($sections[$firstSegment])) {
        $filePath = __DIR__ . '/' . $sections[$firstSegment];

// 4. Try dynamic paths
    } else {
        $dynamicPaths = [
            "views/$path.php",
            "views/$path",
            $path
        ];
        foreach ($dynamicPaths as $candidate) {
            $fullPath = __DIR__ . '/' . $candidate;
            if (file_exists($fullPath)) {
                $filePath = $fullPath;
                break;
            }
        }
    }
}

// Load valid file or show 404
if ($filePath && file_exists($filePath)) {
    error_log("Loading file: $filePath");

    if (!empty($queryString)) {
        $_SERVER['QUERY_STRING'] = $queryString;
        parse_str($queryString, $_GET);
        error_log("Parsed GET parameters: " . print_r($_GET, true));
    }

    include_once $filePath;
    exit();
}

// Fallback: 404 page
$notFoundPage = __DIR__ . '/views/errors/404.php';
http_response_code(404);
if (file_exists($notFoundPage)) {
    error_log("404 Not Found - Loading 404 page");
    include_once $notFoundPage;
} else {
    error_log("404 Not Found - No 404 page found");
    echo "<h1>404 - Page Not Found</h1>";
}
exit();
