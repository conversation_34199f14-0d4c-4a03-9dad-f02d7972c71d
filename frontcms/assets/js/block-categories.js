
/**
 * Category Image Preview
 */
document.addEventListener('DOMContentLoaded', function() {
    // Function to set default preview
    function setDefaultPreview(previewId) {
        const preview = document.getElementById(previewId);
        if (preview) {
            preview.innerHTML = `
                <div class="text-center">
                    <p class="text-sm font-medium text-gray-600 mb-2">Click to Upload Image</p>
                    <p class="text-xs text-gray-500 mb-3">PNG, JPG or GIF (max 2MB)</p>
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mx-auto text-gray-400 lucide lucide-image-up-icon lucide-image-up"><path d="M10.3 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2v10l-3.1-3.1a2 2 0 0 0-2.814.014L6 21"/><path d="m14 19.5 3-3 3 3"/><path d="M17 22v-5.5"/><circle cx="9" cy="9" r="2"/></svg>
                </div>
            `;
        }
    }

    // Function to set image preview
    function setImagePreview(previewId, imageUrl) {
        const preview = document.getElementById(previewId);
        if (preview) {
            preview.innerHTML = `<img src="${imageUrl}" alt="Category Preview" class="w-full h-full object-contain">`;
        }
    }

    // Function to handle image preview
    function setupImagePreview(inputId, previewId) {
        const input = document.getElementById(inputId);
        const preview = document.getElementById(previewId);

        if (input && preview) {
            input.addEventListener('change', function(e) {
                const file = e.target.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        setImagePreview(previewId, e.target.result);
                    };
                    reader.readAsDataURL(file);
                } else {
                    setDefaultPreview(previewId);
                }
            });
        }
    }

    // Setup image preview for create modal
    setupImagePreview('createCategoryImage', 'createImagePreview');

    // Setup image preview for edit modal
    setupImagePreview('editCategoryImage', 'editImagePreview');

    // Handle edit button click to populate edit modal
    const editButtons = document.querySelectorAll('.edit-category-btn');
    editButtons.forEach(button => {
        button.addEventListener('click', function() {
            const categoryId = this.dataset.id;
            const categoryName = this.dataset.name;
            const imageUrl = this.dataset.image;
            
            // Populate form fields
            document.getElementById('editCategoryId').value = categoryId;
            document.getElementById('editCategoryName').value = categoryName;
            
            // Set image preview if exists
            if (imageUrl) {
                setImagePreview('editImagePreview', imageUrl);
            } else {
                setDefaultPreview('editImagePreview');
            }
        });
    });

    // Handle delete button click to populate delete modal
    const deleteButtons = document.querySelectorAll('.delete-category-btn');
    deleteButtons.forEach(button => {
        button.addEventListener('click', function() {
            const categoryId = this.dataset.id;
            const blockCount = parseInt(this.dataset.blockCount) || 0;
            
            // Set form values
            document.getElementById('deleteCategoryId').value = categoryId;
            document.getElementById('deleteCategoryBlockCount').value = blockCount;
            
            // Show/hide appropriate message and delete button
            const withBlocksMsg = document.getElementById('deleteMessageWithBlocks');
            const noBlocksMsg = document.getElementById('deleteMessageNoBlocks');
            const deleteBtn = document.getElementById('deleteCategoryBtn');
            
            if (blockCount > 0) {
                withBlocksMsg.classList.remove('hidden');
                noBlocksMsg.classList.add('hidden');
                deleteBtn.disabled = true;
                document.getElementById('blockCountText').textContent = blockCount + (blockCount === 1 ? ' block' : ' blocks');
            } else {
                withBlocksMsg.classList.add('hidden');
                noBlocksMsg.classList.remove('hidden');
                deleteBtn.disabled = false;
            }
        });
    });
});
