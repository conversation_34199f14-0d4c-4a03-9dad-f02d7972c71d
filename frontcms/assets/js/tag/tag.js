document.addEventListener('DOMContentLoaded', function () {
    // Initialize Flowbite
    if (typeof initFlowbite === 'function') {
        initFlowbite();
    } else if (typeof Flowbite !== 'undefined') {
        new Flowbite();
    }

    // Handle edit button clicks
    document.addEventListener('click', function (e) {
        if (e.target.closest('[data-modal-target="editTagModal"]')) {
            const button = e.target.closest('[data-modal-target="editTagModal"]');
            const tagId = button.dataset.id;
            const tagName = button.dataset.tag;

            // Set form values
            document.getElementById('editTagId').value = tagId;
            document.getElementById('editTagName').value = tagName;
        }
    });

    // Handle delete button clicks
    document.addEventListener('click', function (e) {
        if (e.target.closest('[data-modal-target="deleteTagModal"]')) {
            const button = e.target.closest('[data-modal-target="deleteTagModal"]');
            const tagId = button.dataset.id;

            // Set the tag ID in the delete confirmation modal
            document.getElementById('deleteTagId').value = tagId;
        }
    });

    // Initialize Lucide icons
    if (typeof lucide !== "undefined" && typeof lucide.createIcons === "function") {
        lucide.createIcons();
    }
});