function storeSelectedCategory(categoryId, categoryName) {
    console.log('Storing category:', { categoryId, categoryName });
    try {
        localStorage.setItem('selectedCategoryId', categoryId.toString());
        localStorage.setItem('selectedCategoryName', categoryName);
        console.log('Stored successfully');
        console.log('Current localStorage:', {
            id: localStorage.getItem('selectedCategoryId'),
            name: localStorage.getItem('selectedCategoryName')
        });
    } catch (error) {
        console.error('Error storing category:', error);
    }
}

/**
 * Success Message System
 */

// Check URL for success message parameter
function checkUrlForSuccessMessage() {
    const urlParams = new URLSearchParams(window.location.search);
    const successMessage = urlParams.get('success');
    if (successMessage) {
        showSuccessMessage(successMessage);
    }
}

// Display success message notification
function showSuccessMessage(message) {
    // Remove existing message if present
    hideSuccessMessage();
    
    // Create message element
    const successDiv = document.createElement('div');
    successDiv.id = 'successMessage';
    successDiv.className = 'fixed top-4 right-4 bg-white border-l-4 border-green-500 rounded-lg shadow-lg p-4 mb-4 transform transition-all duration-500 ease-in-out opacity-100 translate-x-0 flex items-center space-x-3 min-w-[300px] z-50';
    
    // Create icon
    const iconDiv = document.createElement('div');
    iconDiv.className = 'flex-shrink-0';
    iconDiv.innerHTML = '<svg class="h-5 w-5 text-green-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" /></svg>';
    
    // Create message text
    const messageDiv = document.createElement('div');
    messageDiv.className = 'flex-1';
    const messagePara = document.createElement('p');
    messagePara.className = 'text-sm font-medium text-green-600';
    messagePara.textContent = message;
    messageDiv.appendChild(messagePara);
    
    // Create close button
    const closeButton = document.createElement('button');
    closeButton.className = 'text-gray-400 hover:text-gray-500 focus:outline-none';
    closeButton.onclick = hideSuccessMessage;
    closeButton.innerHTML = '<svg class="h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" /></svg>';
    
    // Assemble and add to document
    successDiv.appendChild(iconDiv);
    successDiv.appendChild(messageDiv);
    successDiv.appendChild(closeButton);
    document.body.appendChild(successDiv);
    
    // Auto-hide after 5 seconds
    setTimeout(hideSuccessMessage, 5000);
    
    // Remove success parameter from URL
    cleanSuccessParamFromUrl();
}

// Hide and remove success message
function hideSuccessMessage() {
    const message = document.getElementById('successMessage');
    if (!message) return;
    
    // Animate out
    message.classList.add('opacity-0', '-translate-x-4');
    
    // Remove from DOM after animation completes
    setTimeout(() => {
        if (message && message.parentNode) {
            message.remove();
        }
    }, 500);
    
    // Clean URL
    cleanSuccessParamFromUrl();
}

// Remove success parameter from URL without page refresh
function cleanSuccessParamFromUrl() {
    const url = new URL(window.location.href);
    if (url.searchParams.has('success')) {
        url.searchParams.delete('success');
        window.history.replaceState({}, '', url);
    }
}

function initializeCustomTabs(){
    const tabs = document.querySelectorAll('.custom-tabs');
    const tabPanels = document.querySelectorAll('.custom-tabpanel');

    tabs.forEach(tab => {
        tab.addEventListener('click', function() {
            // Update tab states
            tabs.forEach(t => {
                const isSelected = t === this;
                t.setAttribute('aria-selected', isSelected);
                t.classList.toggle('bg-blue-50', isSelected);
                t.classList.toggle('text-blue-600', isSelected);
                t.classList.toggle('text-gray-500', !isSelected);
            });

            // Show selected panel
            tabPanels.forEach(panel => {
                panel.classList.toggle('hidden', panel.id !== this.id);
            });
        });
    });

    // Activate first tab
    tabs[0]?.click();
}


document.addEventListener('DOMContentLoaded', function() {
    initializeCustomTabs();
});