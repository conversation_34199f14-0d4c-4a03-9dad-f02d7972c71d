// Global editors object
const editors = {
    template: null,
    content: null,
    css: null,
    javascript: null,
    dependencies: null
};

// Global state
let editorsInitialized = false;
let currentStep = 1;
const totalSteps = 2;
let isDarkMode = false;

function initializeStepNavigation() {
    updateSteps();

    // Button Event Listeners for Steps
    document.getElementById('nextBtn')?.addEventListener('click', () => {
        if (currentStep < totalSteps) {
            // Validate required fields in step 1
            if (currentStep === 1) {
                let hasError = false;
                
                // Clear all previous errors first
                ['blockName', 'blockDescription', 'blockSlug', 'blockCategories'].forEach(fieldId => {
                    const field = document.getElementById(fieldId);
                    if (field) clearFieldError(field);
                });

                // Validate Block Name
                const blockNameField = document.getElementById('blockName');
                const blockName = blockNameField.value.trim();
                if (!blockName) {
                    showFieldError(blockNameField, 'Block Name is required');
                    hasError = true;
                }

                // Validate Block Description
                const blockDescField = document.getElementById('blockDescription');
                const blockDescription = blockDescField.value.trim();
                if (!blockDescription) {
                    showFieldError(blockDescField, 'Block Description is required');
                    hasError = true;
                }

                // Validate Block Slug
                const blockSlugField = document.getElementById('blockSlug');
                const blockSlug = blockSlugField.value.trim();
                if (!blockSlug) {
                    showFieldError(blockSlugField, 'Block Slug is required');
                    hasError = true;
                }

                // Validate Block Categories
                const blockCategoriesField = document.getElementById('blockCategories');
                const blockCategories = Array.from(blockCategoriesField.selectedOptions).map(opt => opt.value);
                if (blockCategories.length === 0) {
                    showFieldError(blockCategoriesField, 'Please select at least one category');
                    hasError = true;
                }
                
                if (hasError) {
                    showNotification('Please fill in all required fields before continuing.', 'error');
                    return;
                }
            }
            
            currentStep++;
            updateSteps();
        }
    });

    document.getElementById('prevBtn')?.addEventListener('click', () => {
        if (currentStep > 1) {
            currentStep--;
            updateSteps();
        }
    });
}

function updateSteps() {
    // Update step indicators
    document.querySelectorAll('.step-item').forEach((item, index) => {
        const stepNum = index + 1;
        const outerCircle = item.querySelector('[class^="absolute -inset"]');
        const innerCircle = item.querySelector('[class^="relative flex"]');
        const numberSpan = innerCircle.querySelector('span');
        const title = item.querySelector('h3');
        const desc = item.querySelector('p');
        const connector = item.querySelector('[class^="absolute left-4"]');

        if (stepNum < currentStep) {
            // Completed state
            outerCircle.className = 'absolute -inset-0.5 bg-green-100 rounded-full';
            innerCircle.className = 'relative flex items-center justify-center w-8 h-8 bg-green-500 rounded-full';
            numberSpan.innerHTML = '<svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>';
            title.className = 'text-base font-semibold text-gray-900';
            desc.className = 'text-sm text-gray-500';
            if (connector) connector.className = 'absolute left-4 top-8 h-full w-0.5 bg-green-200 -ml-px';
        } else if (stepNum === currentStep) {
            // Active state
            outerCircle.className = 'absolute -inset-0.5 bg-blue-100 rounded-full';
            innerCircle.className = 'relative flex items-center justify-center w-8 h-8 bg-blue-600 rounded-full';
            numberSpan.innerHTML = stepNum;
            numberSpan.className = 'text-white text-sm';
            title.className = 'text-base font-semibold text-gray-900';
            desc.className = 'text-sm text-gray-500';
            if (connector) connector.className = 'absolute left-4 top-8 h-full w-0.5 bg-gray-200 -ml-px';
        } else {
            // Disabled state
            outerCircle.className = 'absolute -inset-0.5 bg-gray-200 rounded-full';
            innerCircle.className = 'relative flex items-center justify-center w-8 h-8 bg-gray-100 rounded-full';
            numberSpan.innerHTML = stepNum;
            numberSpan.className = 'text-gray-500 text-sm';
            title.className = 'text-base font-semibold text-gray-400';
            desc.className = 'text-sm text-gray-400';
            if (connector) connector.className = 'absolute left-4 top-8 h-full w-0.5 bg-gray-200 -ml-px';
        }
    });

    // Show/hide content
    document.querySelectorAll('.step-content').forEach((content, index) => {
        if (index + 1 === currentStep) {
            content.classList.remove('hidden');

            // Initialize editors when reaching step 2
            if (currentStep === 2 && !editorsInitialized) {
                setTimeout(() => {
                    initializeBlockContent();
                    editorsInitialized = true;
                }, 10);
            }

            // Update preview when reaching step 2
            if (currentStep === 2 && editorsInitialized) {
                setTimeout(() => {
                    updatePreview();
                }, 10);
            }
        } else {
            content.classList.add('hidden');
        }
    });

    // Update buttons
    const prevBtn = document.getElementById('prevBtn');
    const nextBtn = document.getElementById('nextBtn');
    const submitBtn = document.getElementById('submitBtn');
    
    if (prevBtn && nextBtn) {
        prevBtn.disabled = currentStep === 1;
        prevBtn.style.opacity = currentStep === 1 ? '0.5' : '1';
        nextBtn.textContent = currentStep === totalSteps ? 'Finish' : 'Continue';
        
        // Show/hide submit button based on current step
        if (submitBtn) {
            if (currentStep === totalSteps) {
                submitBtn.classList.remove('hidden');
                nextBtn.classList.add('hidden');
            } else {
                submitBtn.classList.add('hidden');
                nextBtn.classList.remove('hidden');
            }
        }
    }
}

function initializeBlockContent() {
    // Get existing values from textareas - ensure we're getting the actual content
    const tmplCodeElement = document.getElementById('blockTmplCode');
    const jsonCodeElement = document.getElementById('blockJsonCode');
    const cssCodeElement = document.getElementById('blockCssCode');
    const jsCodeElement = document.getElementById('blockJsCode');
    
    // Default template code
    const defaultTmplCode = ``;

    const defaultJsonCode = ``;

    const defaultCssCode = ``;

    const defaultJsCode = ``;

    // Get actual content or use defaults if empty
    const tmplCode = tmplCodeElement.value.trim() !== '' ? tmplCodeElement.value : defaultTmplCode;
    const jsonCode = jsonCodeElement.value.trim() !== '' ? jsonCodeElement.value : defaultJsonCode;
    const cssCode = cssCodeElement.value.trim() !== '' ? cssCodeElement.value : defaultCssCode;
    const jsCode = jsCodeElement.value.trim() !== '' ? jsCodeElement.value : defaultJsCode;
    

    // Initialize CodeMirror editors with existing values
    editors.template = CodeMirror(document.getElementById('templateEditor'), {
        mode: 'htmlmixed',
        theme: 'default',
        lineNumbers: true,
        autoCloseTags: true,
        autoCloseBrackets: true,
        foldGutter: true,
        gutters: ['CodeMirror-linenumbers', 'CodeMirror-foldgutter'],
        lineWrapping: true,
        matchBrackets: true,
        value: tmplCode
    });

    editors.content = CodeMirror(document.getElementById('contentEditor'), {
        mode: 'application/json',
        theme: 'default',
        lineNumbers: true,
        autoCloseBrackets: true,
        foldGutter: true,
        gutters: ['CodeMirror-linenumbers', 'CodeMirror-foldgutter'],
        lineWrapping: true,
        matchBrackets: true,
        value: jsonCode
    });

    editors.css = CodeMirror(document.getElementById('cssEditor'), {
        mode: 'css',
        theme: 'default',
        lineNumbers: true,
        autoCloseBrackets: true,
        foldGutter: true,
        gutters: ['CodeMirror-linenumbers', 'CodeMirror-foldgutter'],
        lineWrapping: true,
        matchBrackets: true,
        value: cssCode
    });

    editors.javascript = CodeMirror(document.getElementById('javascriptEditor'), {
        mode: 'javascript',
        theme: 'default',
        lineNumbers: true,
        autoCloseBrackets: true,
        foldGutter: true,
        gutters: ['CodeMirror-linenumbers', 'CodeMirror-foldgutter'],
        lineWrapping: true,
        matchBrackets: true,
        value: jsCode
    });

    // Add form submit event listener to ensure values are updated before submission
    document.querySelector('.block-form')?.addEventListener('submit', function() {
        document.getElementById('blockTmplCode').value = editors.template.getValue();
        document.getElementById('blockJsonCode').value = editors.content.getValue();
        document.getElementById('blockCssCode').value = editors.css.getValue();
        document.getElementById('blockJsCode').value = editors.javascript.getValue();   
        
    });
    document.querySelector('#templateEditor .CodeMirror').setAttribute('data-type', 'HTML');
    document.querySelector('#contentEditor .CodeMirror').setAttribute('data-type', 'JSON');
    document.querySelector('#cssEditor .CodeMirror').setAttribute('data-type', 'CSS');
    document.querySelector('#javascriptEditor .CodeMirror').setAttribute('data-type', 'JS');
    
    // Initialize tabs
    initializeEditorTabs();
    
    // Add event listener for preview tab specifically
    document.getElementById('preview-tab').addEventListener('click', function() {
        // Show preview controls when preview tab is clicked
        const previewControls = [
            document.getElementById('resetPreview'),
            document.getElementById('themeToggle'),
            document.querySelector('[data-device="desktop"]'),
            document.querySelector('[data-device="tablet"]'),
            document.querySelector('[data-device="mobile"]')
        ];
        
        previewControls.forEach(control => {
            if (control) control.classList.remove('hidden');
        });

        // Update the preview content when the preview tab is clicked
        if (editorsInitialized) {
            updatePreview();
        }
    });
}

function initializeEditorTabs() {
    const tabs = document.querySelectorAll('[role="tab"]');
    const tabPanels = document.querySelectorAll('[role="tabpanel"]');

    tabs.forEach(tab => {
        tab.addEventListener('click', function() {
            const targetId = this.getAttribute('data-editor-target').substring(1);
            
            // Update tab states
            tabs.forEach(t => {
                const isSelected = t === this;
                t.setAttribute('aria-selected', isSelected);
                t.classList.toggle('bg-blue-50', isSelected);
                t.classList.toggle('text-blue-600', isSelected);
                t.classList.toggle('text-gray-500', !isSelected);
            });

            // Show selected panel
            tabPanels.forEach(panel => {
                panel.classList.toggle('hidden', panel.id !== targetId);
            });

            // Toggle preview controls visibility
            const previewControls = [
                document.getElementById('resetPreview'),
                document.getElementById('themeToggle'),
                document.querySelector('[data-device="desktop"]'),
                document.querySelector('[data-device="tablet"]'),
                document.querySelector('[data-device="mobile"]')
            ];
            
            if (targetId === 'preview') {
                // Show preview controls when preview tab is active
                previewControls.forEach(control => {
                    if (control) control.classList.remove('hidden');
                });
            } else {
                // Hide preview controls when other tabs are active
                previewControls.forEach(control => {
                    if (control) control.classList.add('hidden');
                });
            }

            // Refresh editor if exists
            const editor = editors[targetId];
            if (editor) {
                setTimeout(() => editor.refresh(), 1);
            }
        });
    });

    // Activate first tab
    tabs[0]?.click();
}


function initializePreviewControls() {
    const deviceButtons = document.querySelectorAll('[data-device]');
    const themeToggle = document.getElementById('themeToggle');
    const fullscreenButton = document.getElementById('previewFullscreenBtn');
    const resetButton = document.getElementById('resetPreview');
    const previewContent = document.getElementById('previewContent');

    // Device Switcher
    deviceButtons.forEach(button => {
        button.addEventListener('click', function() {
            const device = this.dataset.device;
            const previewFrame = document.getElementById('previewFrame');

            // Update button states
            deviceButtons.forEach(btn => {
                const isActive = btn === this;
                btn.classList.toggle('bg-white', isActive);
                btn.classList.toggle('shadow-sm', isActive);
                btn.classList.toggle('text-blue-600', isActive);
                btn.classList.toggle('text-gray-400', !isActive);
            });

            // Update preview width - apply to iframe instead of container
            switch(device) {
                case 'mobile':
                    previewFrame.style.maxWidth = '375px';
                    previewFrame.style.margin = 'auto';
                    previewContent.style.margin = 'auto';
                    break;
                case 'tablet':
                    previewFrame.style.maxWidth = '768px';
                    previewFrame.style.margin = 'auto';
                    previewContent.style.margin = 'auto';
                    break;
                default:
                    previewFrame.style.maxWidth = '100%';
                    previewFrame.style.margin = '0';
                    previewContent.style.margin = '0';
            }

            if (editorsInitialized) {
                updatePreview();
            }
        });
    });

    // Theme Toggle
    if (themeToggle) {
        themeToggle.addEventListener('click', function() {
            isDarkMode = !isDarkMode;
            
            // Update icon
            this.innerHTML = isDarkMode ? `
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"/>
                </svg>
            ` : `
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"/>
                </svg>
            `;

            // Update the preview
            if (editorsInitialized) {
                updatePreview();
                
                // After preview is updated, toggle the dark class using classList.toggle
                setTimeout(() => {
                    const previewFrame = document.getElementById('previewFrame');
                    if (previewFrame && previewFrame.contentDocument) {
                        try {
                            const iframeDocument = previewFrame.contentDocument;
                            // Use classList.toggle with a force parameter to ensure it matches isDarkMode
                            iframeDocument.documentElement.classList.toggle('dark', isDarkMode);
                            
                            console.log('Dark mode toggled:', isDarkMode, 
                                iframeDocument.documentElement.classList.contains('dark'));
                            
                            // Optionally store the preference in localStorage
                            try {
                                previewFrame.contentWindow.localStorage.setItem('darkMode', isDarkMode);
                            } catch (e) {
                                console.log('Could not save dark mode to iframe localStorage:', e);
                            }
                        } catch (error) {
                            console.error('Error toggling dark mode:', error);
                        }
                    }
                }, 100); // Small delay to ensure iframe is loaded
            }
        });
    }

    // Fullscreen Functionality
    if (fullscreenButton) {
        fullscreenButton.addEventListener('click', function() {
            const previewContainer = fullscreenButton.closest('.preview-window');
            
            if (!document.fullscreenElement) {
                // Enter fullscreen
                if (previewContainer.requestFullscreen) {
                    previewContainer.requestFullscreen();
                } else if (previewContainer.webkitRequestFullscreen) {
                    previewContainer.webkitRequestFullscreen();
                } else if (previewContainer.msRequestFullscreen) {
                    previewContainer.msRequestFullscreen();
                }
                
                previewContainer.classList.add('fixed', 'inset-0', 'z-50', 'bg-white');
                previewContent.classList.add('h-screen');
            } else {
                // Exit fullscreen
                if (document.exitFullscreen) {
                    document.exitFullscreen();
                } else if (document.webkitExitFullscreen) {
                    document.webkitExitFullscreen();
                } else if (document.msExitFullscreen) {
                    document.msExitFullscreen();
                }
                
                previewContainer.classList.remove('fixed', 'inset-0', 'z-50', 'bg-white');
                previewContent.classList.remove('h-screen');
            }
        });

        // Handle fullscreen change
        document.addEventListener('fullscreenchange', function() {
            if (!document.fullscreenElement) {
                const previewContainer = document.querySelector('.preview-window');
                if (previewContainer) {
                    previewContainer.classList.remove('fixed', 'inset-0', 'z-50', 'bg-white');
                    previewContent.classList.remove('h-screen');
                }
            }
        });
    }

    // Reset Preview
    if (resetButton) {
        resetButton.addEventListener('click', function() {
            // Reset device view
            const desktopButton = document.querySelector('[data-device="desktop"]');
            if (desktopButton) {
                desktopButton.click();
            }

            // Reset theme
            if (isDarkMode) {
                themeToggle?.click();
            }

            // Update preview
            if (editorsInitialized) {
                updatePreview();
            }
        });
    }
}

function initializeImageUpload() {
    const fileInput = document.getElementById('blockPoster');
    const preview = document.getElementById('preview');
    const uploadPlaceholder = document.getElementById('uploadPlaceholder');
    const imagePreview = document.getElementById('imagePreview');
    const removeImage = document.getElementById('removeImage');
    const browseButton = document.getElementById('browseButton');
    const blockImage = document.getElementById('blockImage');
    const dropZone = uploadPlaceholder?.parentElement;

    if (!fileInput || !preview || !uploadPlaceholder || !imagePreview || !removeImage || !blockImage || !dropZone || !browseButton) {
        console.error('Some image upload elements are missing');
        return;
    }

    // Browse Button Click Handler
    browseButton.addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        fileInput.click();
    });

    // Dropzone Click Handler
    dropZone.addEventListener('click', function(e) {
        if (!imagePreview.contains(e.target) && e.target !== removeImage) {
            fileInput.click();
        }
    });

    // Prevent default drag behaviors
    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
        dropZone.addEventListener(eventName, preventDefaults, false);
        document.body.addEventListener(eventName, preventDefaults, false);
    });

    // Highlight drop zone when item is dragged over it
    ['dragenter', 'dragover'].forEach(eventName => {
        dropZone.addEventListener(eventName, highlight, false);
    });

    ['dragleave', 'drop'].forEach(eventName => {
        dropZone.addEventListener(eventName, unhighlight, false);
    });

    function preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }

    function highlight() {
        dropZone.classList.add('border-blue-500', 'bg-blue-50/50');
    }

    function unhighlight() {
        dropZone.classList.remove('border-blue-500', 'bg-blue-50/50');
    }

    // Handle dropped files
    dropZone.addEventListener('drop', function(e) {
        const dt = e.dataTransfer;
        const files = dt.files;
        handleFiles(files);
    });

    // Handle selected files
    fileInput.addEventListener('change', function() {
        handleFiles(this.files);
    });

    function handleFiles(files) {
        if (files.length === 0) return;

        const file = files[0];

        // Validate file type
        if (!file.type.startsWith('image/')) {
            showError('Please upload an image file');
            return;
        }

        // Show loading state
        imagePreview.classList.add('opacity-50');
        uploadPlaceholder.classList.add('hidden');
        imagePreview.classList.remove('hidden');

        // Create FormData
        const formData = new FormData();
        formData.append('image', file);
        formData.append('action', 'upload_block_image');

        // Upload to server
        fetch(window.location.href, {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Update preview and hidden input
                preview.src = data.path;
                blockImage.value = data.path;
                
                // Show preview
                uploadPlaceholder.classList.add('hidden');
                imagePreview.classList.remove('hidden', 'opacity-50');
            } else {
                throw new Error(data.message || 'Upload failed');
            }
        })
        .catch(error => {
            showError(error.message);
            // Reset UI
            uploadPlaceholder.classList.remove('hidden');
            imagePreview.classList.add('hidden');
            imagePreview.classList.remove('opacity-50');
        });
    }

    // Remove image handler
    removeImage.addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();

        // Clear file input
        fileInput.value = '';
        blockImage.value = '';
        
        // Reset preview
        preview.src = '';
        uploadPlaceholder.classList.remove('hidden');
        imagePreview.classList.add('hidden');

        // Add fade out animation
        imagePreview.classList.add('opacity-0');
        setTimeout(() => {
            imagePreview.classList.remove('opacity-0');
        }, 300);
    });

    function showError(message) {
        // Create error notification
        const notification = document.createElement('div');
        notification.className = 'fixed bottom-4 right-4 bg-red-500 text-white px-6 py-3 rounded-lg shadow-lg transform transition-all duration-300 translate-y-0';
        notification.textContent = message;

        // Add to document
        document.body.appendChild(notification);

        // Remove after 3 seconds
        setTimeout(() => {
            notification.classList.add('translate-y-full', 'opacity-0');
            setTimeout(() => {
                notification.remove();
            }, 300);
        }, 3000);
    }
}

function initializeCategoryManagement() {
    const addCategoryBtn = document.getElementById('addCategoryBtn');
    const cancelCategoryBtn = document.getElementById('cancelCategoryBtn');
    const saveCategoryBtn = document.getElementById('saveCategoryBtn');
    const categorySelectContainer = document.getElementById('categorySelectContainer');
    const newCategoryContainer = document.getElementById('newCategoryContainer');
    const newCategoryInput = document.getElementById('newCategory');
    const blockCategory = document.getElementById('blockCategory');
    const categoryPillsContainer = document.getElementById('categoryPillsContainer');

    // Initialize existing categories
    function initializeCategories() {
        if (!blockCategory || !categoryPillsContainer) return;

        // Clear existing pills
        categoryPillsContainer.innerHTML = '';

        // Get all options except the first placeholder
        const options = Array.from(blockCategory.options).slice(1);
        options.forEach(option => {
            addCategoryPill(option.text, option.value);
        });
    }

    // Create and add category pill
    function addCategoryPill(name, value) {
        const pill = document.createElement('div');
        pill.className = 'inline-flex items-center gap-1 px-3 py-1 bg-blue-50 text-blue-700 rounded-full text-sm';
        pill.innerHTML = `
            <span>${name}</span>
            <button type="button" class="delete-category" data-value="${value}">
                <svg class="w-4 h-4 text-blue-500 hover:text-red-500 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        `;

        // Add delete functionality
        const deleteBtn = pill.querySelector('.delete-category');
        deleteBtn.addEventListener('click', function(e) {
            e.stopPropagation();
            const valueToDelete = this.dataset.value;
            
            // Remove from select
            const optionToRemove = Array.from(blockCategory.options)
                .find(option => option.value === valueToDelete);
            if (optionToRemove) {
                blockCategory.removeChild(optionToRemove);
            }

            // Remove pill with animation
            pill.classList.add('scale-95', 'opacity-0');
            setTimeout(() => {
                pill.remove();
            }, 150);

            // Show notification
            showNotification('Category removed');
        });

        // Add pill to container with animation
        pill.style.opacity = '0';
        pill.style.transform = 'scale(0.95)';
        categoryPillsContainer.appendChild(pill);
        
        // Trigger animation
        setTimeout(() => {
            pill.style.opacity = '1';
            pill.style.transform = 'scale(1)';
            pill.style.transition = 'all 150ms ease-in-out';
        }, 10);
    }

    // Show/Hide new category form
    if (addCategoryBtn) {
        addCategoryBtn.addEventListener('click', function() {
            categorySelectContainer.classList.add('hidden');
            addCategoryBtn.classList.add('hidden');
            newCategoryContainer.classList.remove('hidden');
            
            // Focus input with animation
            newCategoryInput.classList.add('ring-2', 'ring-blue-500');
            newCategoryInput.focus();
            setTimeout(() => {
                newCategoryInput.classList.remove('ring-2', 'ring-blue-500');
            }, 300);
        });
    }

    // Cancel new category
    if (cancelCategoryBtn) {
        cancelCategoryBtn.addEventListener('click', function() {
            resetCategoryForm();
        });
    }

    // Save new category
    if (saveCategoryBtn && newCategoryInput && blockCategory) {
        saveCategoryBtn.addEventListener('click', function() {
            const newCategory = newCategoryInput.value.trim();
            
            if (!newCategory) {
                showError('Please enter a category name');
                newCategoryInput.focus();
                return;
            }

            // Check if category already exists
            const exists = Array.from(blockCategory.options).some(option => 
                option.value.toLowerCase() === newCategory.toLowerCase() ||
                option.text.toLowerCase() === newCategory.toLowerCase()
            );

            if (exists) {
                showError('This category already exists');
                newCategoryInput.focus();
                return;
            }

            // Add to select element
            const option = new Option(newCategory, newCategory.toLowerCase());
            blockCategory.add(option);
            
            // Add pill
            addCategoryPill(newCategory, newCategory.toLowerCase());

            // Reset form
            resetCategoryForm();
            
            // Show success notification
            showNotification('Category added successfully');
        });

        // Add keyboard support
        newCategoryInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                saveCategoryBtn.click();
            }
            if (e.key === 'Escape') {
                resetCategoryForm();
            }
        });
    }

    function resetCategoryForm() {
        categorySelectContainer.classList.remove('hidden');
        addCategoryBtn.classList.remove('hidden');
        newCategoryContainer.classList.add('hidden');
        newCategoryInput.value = '';
    }

    function showError(message) {
        const notification = document.createElement('div');
        notification.className = 'fixed bottom-4 right-4 bg-red-500 text-white px-6 py-3 rounded-lg shadow-lg transform transition-all duration-300';
        notification.textContent = message;

        document.body.appendChild(notification);

        setTimeout(() => {
            notification.classList.add('translate-y-full', 'opacity-0');
            setTimeout(() => notification.remove(), 300);
        }, 3000);
    }

    function showNotification(message) {
        const notification = document.createElement('div');
        notification.className = 'fixed bottom-4 right-4 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg transform transition-all duration-300';
        notification.textContent = message;

        document.body.appendChild(notification);

        setTimeout(() => {
            notification.classList.add('translate-y-full', 'opacity-0');
            setTimeout(() => notification.remove(), 300);
        }, 3000);
    }

    // Initialize categories on load
    initializeCategories();
}

function initializeSlugGeneration() {
    const blockName = document.getElementById('blockName');
    const blockSlug = document.getElementById('blockSlug');

    if (!blockName || !blockSlug) return;

    let typingTimer;
    const doneTypingInterval = 500; // Wait for 500ms after user stops typing

    blockName.addEventListener('input', function(e) {
        clearTimeout(typingTimer);
        
        // Add loading indicator
        blockSlug.classList.add('opacity-50');
        
        // Set timer to generate slug
        typingTimer = setTimeout(() => {
            const slug = generateSlug(e.target.value);
            
            // Animate slug update
            blockSlug.classList.add('opacity-0');
            setTimeout(() => {
                blockSlug.value = slug;
                blockSlug.classList.remove('opacity-0', 'opacity-50');
            }, 150);
        }, doneTypingInterval);
    });

    function generateSlug(text) {
        return text
            .toString()
            .toLowerCase()
            .trim()
            .replace(/\s+/g, '-')        // Replace spaces with -
            .replace(/[^\w\-]+/g, '')    // Remove all non-word chars
            .replace(/\-\-+/g, '-')      // Replace multiple - with single -
            .replace(/^-+/, '')          // Trim - from start of text
            .replace(/-+$/, '');         // Trim - from end of text
    }
}

function initializeAssistantTabs() {
    const assistantTabs = document.querySelectorAll('[data-assistant-target]');
    const assistantPanels = document.querySelectorAll('.assistant-panel');
    let activeTab = null;

    function switchTab(tab) {
        const targetId = tab.getAttribute('data-assistant-target').substring(1);
        const targetPanel = document.getElementById(targetId);

        if (!targetPanel) return;

        // Remove active states from all tabs
        assistantTabs.forEach(t => {
            t.classList.remove('bg-blue-50', 'text-blue-600');
            t.classList.add('text-gray-500');
        });

        // Add active state to clicked tab
        tab.classList.remove('text-gray-500');
        tab.classList.add('bg-blue-50', 'text-blue-600');

        // Hide all panels with fade out
        assistantPanels.forEach(panel => {
            if (panel !== targetPanel) {
                panel.classList.add('opacity-0');
                setTimeout(() => {
                    panel.classList.add('hidden');
                    panel.classList.remove('opacity-0');
                }, 150);
            }
        });

        // Show target panel with fade in
        setTimeout(() => {
            targetPanel.classList.remove('hidden');
            setTimeout(() => {
                targetPanel.classList.remove('opacity-0');
            }, 10);
        }, 150);

        activeTab = tab;
    }

    // Add click handlers to tabs
    assistantTabs.forEach(tab => {
        tab.addEventListener('click', function() {
            if (this === activeTab) return;
            switchTab(this);
        });

        // Add hover effects
        tab.addEventListener('mouseenter', function() {
            if (this !== activeTab) {
                this.classList.add('bg-gray-50');
            }
        });

        tab.addEventListener('mouseleave', function() {
            if (this !== activeTab) {
                this.classList.remove('bg-gray-50');
            }
        });
    });

    // Initialize first tab
    if (assistantTabs.length > 0) {
        switchTab(assistantTabs[0]);
    }

    // Add keyboard navigation
    document.addEventListener('keydown', function(e) {
        if (!activeTab) return;

        if (e.altKey && e.key === 'ArrowRight') {
            e.preventDefault();
            const currentIndex = Array.from(assistantTabs).indexOf(activeTab);
            const nextTab = assistantTabs[currentIndex + 1] || assistantTabs[0];
            switchTab(nextTab);
        }
        else if (e.altKey && e.key === 'ArrowLeft') {
            e.preventDefault();
            const currentIndex = Array.from(assistantTabs).indexOf(activeTab);
            const prevTab = assistantTabs[currentIndex - 1] || assistantTabs[assistantTabs.length - 1];
            switchTab(prevTab);
        }
    });

    // Add panel-specific functionality
    initializeAIPanel();
    initializeStructurePanel();
    initializeHistoryPanel();
}

function initializeAIPanel() {
    const aiPanel = document.getElementById('ai-panel');
    if (!aiPanel) return;

    // AI Provider selection
    const providerSelect = aiPanel.querySelector('select');
    if (providerSelect) {
        providerSelect.addEventListener('change', function() {
            // Add animation for selection change
            this.classList.add('ring-2', 'ring-blue-500');
            setTimeout(() => {
                this.classList.remove('ring-2', 'ring-blue-500');
            }, 300);
        });
    }

    // Custom prompt handling
    const promptTextarea = aiPanel.querySelector('textarea');
    const generateButton = aiPanel.querySelector('button:last-child');
    
    if (promptTextarea && generateButton) {
        let typingTimer;
        
        promptTextarea.addEventListener('input', function() {
            clearTimeout(typingTimer);
            typingTimer = setTimeout(() => {
                generateButton.disabled = this.value.trim().length === 0;
                generateButton.classList.toggle('opacity-50', this.value.trim().length === 0);
            }, 300);
        });

        generateButton.addEventListener('click', function() {
            if (!promptTextarea.value.trim()) return;

            // Add loading state
            this.disabled = true;
            this.innerHTML = `
                <svg class="animate-spin h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Generating...
            `;

            // Simulate AI response (replace with actual AI integration)
            setTimeout(() => {
                this.disabled = false;
                this.innerHTML = `
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
                    </svg>
                    Generate Response
                `;
                showNotification('AI response generated successfully');
            }, 2000);
        });
    }
}

function initializeStructurePanel() {
    const structurePanel = document.getElementById('structure-panel');
    if (!structurePanel) return;

    // Add hover effects to structure items
    const items = structurePanel.querySelectorAll('.p-3');
    items.forEach(item => {
        item.addEventListener('mouseenter', function() {
            this.classList.add('bg-gray-50');
        });
        item.addEventListener('mouseleave', function() {
            this.classList.remove('bg-gray-50');
        });
    });
}

function initializeHistoryPanel() {
    const historyPanel = document.getElementById('history-panel');
    if (!historyPanel) return;

    // Add hover effects and click handlers to history items
    const historyItems = historyPanel.querySelectorAll('.px-4.py-3');
    historyItems.forEach(item => {
        item.addEventListener('mouseenter', function() {
            this.classList.add('bg-gray-50');
        });
        item.addEventListener('mouseleave', function() {
            this.classList.remove('bg-gray-50');
        });

        // Add restore functionality
        const restoreButton = item.querySelector('button');
        if (restoreButton) {
            restoreButton.addEventListener('click', function(e) {
                e.stopPropagation();
                
                // Add loading state
                this.classList.add('animate-spin');
                
                // Simulate restore (replace with actual restore logic)
                setTimeout(() => {
                    this.classList.remove('animate-spin');
                    showNotification('Version restored successfully');
                }, 1000);
            });
        }
    });
}

function showNotification(message, type = 'success') {
    const notification = document.createElement('div');
    notification.style.opacity = '0';
    notification.className = `fixed top-4 right-4 p-4 rounded-lg text-white z-50 transition-all duration-300 ${type === 'success' ? 'bg-green-500' : 'bg-red-500'}`;
    notification.textContent = message;
    document.body.appendChild(notification);

    // Trigger reflow
    notification.offsetHeight;

    // Fade in
    notification.style.opacity = '1';
    
    setTimeout(() => {
        // Fade out
        notification.style.opacity = '0';
        setTimeout(() => notification.remove(), 300);
    }, 3000);
}

function showFieldError(field, message) {
    // Remove any existing error message
    const existingError = field.parentElement.querySelector('.error-message');
    if (existingError) {
        existingError.remove();
    }

    // Add error class to the input
    field.classList.add('border-red-500');

    // Create and append error message
    const errorDiv = document.createElement('div');
    errorDiv.className = 'error-message text-red-500 text-sm mt-1';
    errorDiv.textContent = message;
    field.parentElement.appendChild(errorDiv);
}

function clearFieldError(field) {
    // Remove error class from input
    field.classList.remove('border-red-500');

    // Remove error message if exists
    const errorMessage = field.parentElement.querySelector('.error-message');
    if (errorMessage) {
        errorMessage.remove();
    }
}

function initializePromptTabs() {
    const promptTabs = document.querySelectorAll('[data-prompt-target]');
    const promptPanels = document.querySelectorAll('#customprompt, #imageprompt, #htmlprompt');
    let activePromptTab = null;

    function switchPromptTab(tab) {
        const targetId = tab.getAttribute('data-prompt-target').substring(1);
        const targetPanel = document.getElementById(targetId);

        if (!targetPanel) return;

        // Remove active states from all tabs
        promptTabs.forEach(t => {
            // Remove active classes from tabs
            t.classList.remove('bg-blue-50', 'text-blue-600');
            t.classList.add('text-gray-500');
            // Remove any hover effect classes
            t.classList.remove('bg-gray-50');
        });

        // Add active state to clicked tab
        tab.classList.remove('text-gray-500');
        tab.classList.add('bg-blue-50', 'text-blue-600');

        // Hide all content panels
        promptPanels.forEach(panel => {
            panel.classList.add('hidden');
        });

        // Show target panel
        targetPanel.classList.remove('hidden');

        activePromptTab = tab;
    }

    // Add click handlers to tabs
    promptTabs.forEach(tab => {
        tab.addEventListener('click', function(e) {
            e.preventDefault(); // Prevent default action
            switchPromptTab(this);
        });

        // Add hover effects
        tab.addEventListener('mouseenter', function() {
            if (this !== activePromptTab) {
                this.classList.add('bg-gray-50');
            }
        });

        tab.addEventListener('mouseleave', function() {
            if (this !== activePromptTab) {
                this.classList.remove('bg-gray-50');
            }
        });
    });

    // Initialize first tab
    if (promptTabs.length > 0) {
        switchPromptTab(promptTabs[0]);
    }
}


// JavaScript
let uploadedFile = null;

function showSection(sectionId) {
    // Hide all sections
    document.getElementById('promptTypeSelection').classList.add('hidden');
    document.getElementById('textPromptInput').classList.add('hidden');
    document.getElementById('imageUploadInput').classList.add('hidden');
    document.getElementById('codeSnippetInput').classList.add('hidden');
    
    // Show selected section
    document.getElementById(sectionId).classList.remove('hidden');
    
    // Update modal title
    const titles = {
        'textPromptInput': 'Text Prompt',
        'imageUploadInput': 'Image Upload',
        'codeSnippetInput': 'Code Snippet'
    };
    document.getElementById('modalTitle').textContent = titles[sectionId];
}

function backToPromptSelection() {
    // Hide all input sections
    document.getElementById('textPromptInput').classList.add('hidden');
    document.getElementById('imageUploadInput').classList.add('hidden');
    document.getElementById('codeSnippetInput').classList.add('hidden');
    
    // Show prompt type selection
    document.getElementById('promptTypeSelection').classList.remove('hidden');
    document.getElementById('modalTitle').textContent = 'Select Prompt Type';
    
    // Reset forms
    resetForms();
}

function resetForms() {
    document.getElementById('prompt').value = '';
    document.getElementById('dropzone-file').value = '';
    document.getElementById('imagePreview').classList.add('hidden');
    document.getElementById('uploadedImage').src = '';
    document.getElementById('codeSnippet').value = '';
    document.getElementById('language').value = 'html';
    uploadedFile = null;
}

function handleImageUpload(event) {
    const file = event.target.files[0];
    if (file) {
        uploadedFile = file;
        const reader = new FileReader();
        reader.onload = function(e) {
            document.getElementById('uploadedImage').src = e.target.result;
            document.getElementById('imagePreview').classList.remove('hidden');
        };
        reader.readAsDataURL(file);
    }
}

function savePrompt(type) {
    let promptData = {};
    
    switch(type) {
        case 'text':
            const promptText = document.getElementById('prompt').value;
            if (promptText.trim() === '') {
                alert('Please enter a prompt');
                return;
            }
            promptData = {
                type: 'text',
                content: promptText
            };
            break;
            
        case 'image':
            if (!uploadedFile) {
                alert('Please upload an image');
                return;
            }
            promptData = {
                type: 'image',
                file: uploadedFile
            };
            break;
            
        case 'code':
            const language = document.getElementById('language').value;
            const codeSnippet = document.getElementById('codeSnippet').value;
            if (codeSnippet.trim() === '') {
                alert('Please enter code snippet');
                return;
            }
            promptData = {
                type: 'code',
                language: language,
                content: codeSnippet
            };
            break;
    }
    
    console.log('Prompt saved:', promptData);
    
    // Get the modal element
    const modalElement = document.getElementById('promptModal');
    
    // Hide the modal
    if (window.closeModal) {
        window.closeModal();
    }
    
    // Reset forms
    setTimeout(() => {
        backToPromptSelection();
        resetForms();
    }, 300);
}

// Initialize modal functionality
document.addEventListener('DOMContentLoaded', function() {
    const modalElement = document.getElementById('promptModal');
    
    // Only initialize modal functionality if the modal element exists

    if (modalElement) {
        // Function to show modal
        window.showModal = function() {
            modalElement.classList.remove('hidden');
            document.body.classList.add('overflow-hidden');
        };

        // Function to hide modal
        window.closeModal = function() {
            modalElement.classList.add('hidden');
            document.body.classList.remove('overflow-hidden');
        };
        // Setup click handlers for modal triggers
        document.querySelectorAll('[data-modal-toggle="promptModal"]').forEach(button => {
            button.addEventListener('click', () => {
                window.showModal();
            });
        });
        // Setup click handlers for modal close buttons
        document.querySelectorAll('[data-modal-hide="promptModal"]').forEach(button => {
            button.addEventListener('click', () => {
                window.closeModal();
            });
        });

        // Close modal when clicking outside
        modalElement.addEventListener('click', (e) => {
            if (e.target === modalElement) {
                window.closeModal();
            }
        });     

        // Close modal with Escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && !modalElement.classList.contains('hidden')) {
                window.closeModal();
            }
        });

    } else {
        // Define empty functions if modal doesn't exist to prevent errors
        window.showModal = function() { console.log('Modal element not found'); };
        window.closeModal = function() { console.log('Modal element not found'); };
    }
});

// Add this to your existing JavaScript file
function initializeEditorFullscreen() {
    const fullscreenBtn = document.getElementById('editorFullscreenBtn');
    const editorContainer = document.querySelector('.leftColumnfullscreen'); // Left Column - Code Editor

    if (fullscreenBtn && editorContainer) {
        fullscreenBtn.addEventListener('click', function() {
            if (!document.fullscreenElement) {
                // Enter fullscreen
                if (editorContainer.requestFullscreen) {
                    editorContainer.requestFullscreen();
                } else if (editorContainer.webkitRequestFullscreen) {
                    editorContainer.webkitRequestFullscreen();
                } else if (editorContainer.mozRequestFullScreen) {
                    editorContainer.mozRequestFullScreen();
                } else if (editorContainer.msRequestFullscreen) {
                    editorContainer.msRequestFullscreen();
                }

                // Add fullscreen styles with scroll support
                editorContainer.classList.add('fixed', 'inset-0', 'z-50', 'bg-white', 'p-4', 'overflow-y-auto');
                
                // Add styles for editor content
                const editorContent = editorContainer.querySelector('.editor-content');
                if (editorContent) {
                    editorContent.classList.add('h-full', 'overflow-y-auto');
                }

                // Refresh CodeMirror editors
                Object.values(editors).forEach(editor => {
                    if (editor) {
                        setTimeout(() => {
                            editor.refresh();
                            // Adjust editor height for fullscreen
                            editor.setSize(null, 'calc(100vh - 200px)'); // Adjust the value based on your header/toolbar height
                        }, 100);
                    }
                });

                // Add custom styles for fullscreen mode
                const style = document.createElement('style');
                style.id = 'fullscreen-styles';
                style.textContent = `
                    .CodeMirror-fullscreen {
                        height: calc(100vh - 200px) !important;
                    }
                    .editor-tab-content {
                        height: calc(100vh - 80px) !important;
                        overflow: auto;
                    }
                    .tab-panel-fullscreen {
                        height: calc(100vh - 150px);
                        overflow-y: auto;
                    }
                `;
                document.head.appendChild(style);

            } else {
                // Exit fullscreen
                if (document.exitFullscreen) {
                    document.exitFullscreen();
                } else if (document.webkitExitFullscreen) {
                    document.webkitExitFullscreen();
                } else if (document.mozCancelFullScreen) {
                    document.mozCancelFullScreen();
                } else if (document.msExitFullscreen) {
                    document.msExitFullscreen();
                }
            }
        });

        // Handle fullscreen change event
        document.addEventListener('fullscreenchange', function() {
            if (!document.fullscreenElement) {
                // Remove fullscreen styles when exiting fullscreen
                editorContainer.classList.remove('fixed', 'inset-0', 'z-50', 'bg-white', 'p-4', 'overflow-y-auto');
                
                const editorContent = editorContainer.querySelector('.editor-content');
                if (editorContent) {
                    editorContent.classList.remove('h-full', 'overflow-y-auto');
                }

                // Reset CodeMirror editors size
                Object.values(editors).forEach(editor => {
                    if (editor) {
                        setTimeout(() => {
                            editor.refresh();
                            editor.setSize(null, '400px'); // Reset to original height
                        }, 100);
                    }
                });

                // Remove custom fullscreen styles
                const fullscreenStyles = document.getElementById('fullscreen-styles');
                if (fullscreenStyles) {
                    fullscreenStyles.remove();
                }
            }
        });
    }
}

function initializeDependencies() {
    const addJsDepBtn = document.getElementById('addJsDepBtn');
    const addCssDepBtn = document.getElementById('addCssDepBtn');
    const jsDependencies = document.getElementById('jsDependencies');
    const cssDependencies = document.getElementById('cssDependencies');
    const blockDependencies = document.getElementById('blockDependencies');
    const blockForm = document.querySelector('.block-form');

    function createDependencyInput(type, value = '') {
        const div = document.createElement('div');
        div.className = 'flex items-center gap-2 opacity-0 transform scale-95 transition-all duration-200';
        
        div.innerHTML = `
            <input type="text" 
                class="flex-1 px-3 py-2 border rounded-lg text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500" 
                placeholder="Enter ${type} CDN URL"
                value="${value}">
            <button class="p-1.5 text-red-500 hover:text-red-600 rounded-md hover:bg-red-50" title="Remove">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                </svg>
            </button>
        `;

        // Add remove functionality
        div.querySelector('button').addEventListener('click', function() {
            div.classList.add('opacity-0', 'scale-95');
            setTimeout(() => {
                div.remove();
                updateDependenciesField();
            }, 200);
        });

        // Add input change handler
        div.querySelector('input').addEventListener('input', function() {
            updateDependenciesField();
        });

        return div;
    }

    function addDependency(type, container, value = '') {
        const newInput = createDependencyInput(type, value);
        container.appendChild(newInput);
        
        // Trigger animation
        setTimeout(() => {
            newInput.classList.remove('opacity-0', 'scale-95');
        }, 10);

        // Focus the new input if it's empty
        const input = newInput.querySelector('input');
        if (!value) {
            input.focus();
        }

        // Add validation
        input.addEventListener('input', function() {
            const isValid = /^(https?:)?\/\/.*\.(js|css)$/.test(this.value);
            this.classList.toggle('border-red-500', !isValid && this.value);
            updateDependenciesField();
        });

        // Trigger initial validation if there's a value
        if (value) {
            const event = new Event('input');
            input.dispatchEvent(event);
        }
    }

    function updateDependenciesField() {
        const dependencies = {
            js: [],
            css: []
        };

        // Collect JavaScript dependencies
        jsDependencies.querySelectorAll('input').forEach(input => {
            const value = input.value.trim();
            if (value && /^(https?:)?\/\/.*\.js$/.test(value)) {
                dependencies.js.push(value);
            }
        });

        // Collect CSS dependencies
        cssDependencies.querySelectorAll('input').forEach(input => {
            const value = input.value.trim();
            if (value && /^(https?:)?\/\/.*\.css$/.test(value)) {
                dependencies.css.push(value);
            }
        });

        // Update the hidden textarea
        blockDependencies.value = JSON.stringify(dependencies);
    }

    // Add form submit handler
    blockForm?.addEventListener('submit', function(e) {
        // Update dependencies before form submission
        updateDependenciesField();

        // Validate dependencies
        const deps = JSON.parse(blockDependencies.value);
        const invalidJs = deps.js.some(url => !/^(https?:)?\/\/.*\.js$/.test(url));
        const invalidCss = deps.css.some(url => !/^(https?:)?\/\/.*\.css$/.test(url));

        if (invalidJs || invalidCss) {
            e.preventDefault();
            alert('Please ensure all dependency URLs are valid and end with .js or .css');
            return false;
        }
    });

    // Add button click handlers
    addJsDepBtn?.addEventListener('click', () => addDependency('JavaScript', jsDependencies));
    addCssDepBtn?.addEventListener('click', () => addDependency('CSS', cssDependencies));

    // Initialize existing dependencies if any
    if (blockDependencies.value) {
        try {
            const existingDeps = JSON.parse(blockDependencies.value);
            
            // Clear existing inputs first
            jsDependencies.innerHTML = '';
            cssDependencies.innerHTML = '';
            
            // Add existing JavaScript dependencies
            if (existingDeps.js && Array.isArray(existingDeps.js)) {
                existingDeps.js.forEach(url => {
                    addDependency('JavaScript', jsDependencies, url);
                });
            }

            // Add existing CSS dependencies
            if (existingDeps.css && Array.isArray(existingDeps.css)) {
                existingDeps.css.forEach(url => {
                    addDependency('CSS', cssDependencies, url);
                });
            }
        } catch (e) {
            console.error('Error parsing existing dependencies:', e);
        }
    }
}


document.addEventListener('DOMContentLoaded', function() {
    // Initialize all functionalities
    initializeStepNavigation();
    
    // Initialize delete modal
    const deleteModal = document.getElementById('deleteBlockModal');
    if (deleteModal) {
        const options = {
            placement: 'center',
            backdrop: 'dynamic',
            backdropClasses: 'bg-gray-900 bg-opacity-50 fixed inset-0 z-40',
            closable: true,
            onHide: () => {
                console.log('modal is hidden');
            },
            onShow: () => {
                console.log('modal is shown');
            },
            onToggle: () => {
                console.log('modal has been toggled');
            }
        };

        const modal = new Modal(deleteModal, options);

        // Initialize modal triggers
        const modalToggles = document.querySelectorAll('[data-modal-toggle="deleteBlockModal"]');
        modalToggles.forEach(toggle => {
            toggle.addEventListener('click', () => {
                modal.toggle();
            });
        });

        // Initialize modal hide triggers
        const modalHides = document.querySelectorAll('[data-modal-hide="deleteBlockModal"]');
        modalHides.forEach(hide => {
            hide.addEventListener('click', () => {
                modal.hide();
            });
        });
    }
    initializeImageUpload();
    initializeCategoryManagement();
    initializeSlugGeneration();
    initializeAssistantTabs();
    initializePreviewControls();
    initializePromptTabs(); 
    initializeDependencies();
    initializeEditorFullscreen();
});