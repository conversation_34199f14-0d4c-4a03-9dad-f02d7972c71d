// Replace the script part in paste.txt with this code
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Flowbite
    if (typeof initFlowbite === 'function') {
        initFlowbite();
    } else if (typeof Flowbite !== 'undefined') {
        new Flowbite();
    }
    
    // Handle edit button clicks
    document.addEventListener('click', function(e) {
        const editButton = e.target.closest('[data-modal-target="editAssetModal"]');
        if (editButton) {
            const assetId = editButton.getAttribute('data-id');
            const assetType = editButton.getAttribute('data-type');
            const assetValue = editButton.getAttribute('data-value');
            const templateId = editButton.getAttribute('data-template-id');
            
            // Set form values
            document.getElementById('editAssetId').value = assetId;
            document.getElementById('editAssetType').value = assetType;
            document.getElementById('editAssetValue').value = assetValue;
            document.getElementById('editAssetTemplateId').value = templateId;
        }
    });

    // Handle delete button clicks
    document.addEventListener('click', function(e) {
        const deleteButton = e.target.closest('[data-modal-target="deleteAssetModal"]');
        if (deleteButton) {
            const assetId = deleteButton.getAttribute('data-id');
            
            // Set the asset ID in the delete confirmation modal
            document.getElementById('deleteAssetId').value = assetId;
        }
    });

    // Initialize Lucide icons
    if (typeof lucide !== "undefined" && typeof lucide.createIcons === "function") {
        lucide.createIcons();
    }
});