
@keyframes slideInRight {
    from { transform: translateX(100%); opacity: 0; }

    to { transform: translateX(0); opacity: 1; }

}

@keyframes slideOutRight {
    from { transform: translateX(0); opacity: 1; }
    to { transform: translateX(100%); opacity: 0; } }

#successMessage { animation: slideInRight 0.5s ease-out; }
#successMessage.hide { animation: slideOutRight 0.5s ease-in forwards; }

/* step-content */
.step-content{ height: calc(100vh - 180px); overflow-y: auto; }
.tab-content{ height: calc(100vh - 380px); overflow-y: auto;}

/* CodeMirror Custom Styling */
.CodeMirror { height: auto !important; min-height: 300px; font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace; font-size: 14px; line-height: 1.6; padding: 10px; background: transparent !important; visibility: visible !important; }
.CodeMirror-gutters { background: transparent !important; border-right: 1px solid #e5e7eb !important; }
.CodeMirror-linenumber { color: #9ca3af !important; }

/* Add some hover effects for the toolbar buttons */
.editor-toolbar button:hover { background-color: #f3f4f6; color: #2563eb; }

/* Custom scrollbar */
.CodeMirror-vscrollbar::-webkit-scrollbar { width: 8px; }
.CodeMirror-vscrollbar::-webkit-scrollbar-track { background: #f1f1f1; }
.CodeMirror-vscrollbar::-webkit-scrollbar-thumb { background: #d1d5db; border-radius: 4px; }
.CodeMirror-vscrollbar::-webkit-scrollbar-thumb:hover { background: #9ca3af; }

.CodeMirror-wrap[data-type="HTML"] .CodeMirror-code > div:nth-child(1) pre.CodeMirror-line span[cm-text=""]:before,
.CodeMirror-wrap[data-type="JSON"] .CodeMirror-code > div:nth-child(1) pre.CodeMirror-line span[cm-text=""]:before,
.CodeMirror-wrap[data-type="CSS"] .CodeMirror-code > div:nth-child(1) pre.CodeMirror-line span[cm-text=""]:before,
.CodeMirror-wrap[data-type="JS"] .CodeMirror-code > div:nth-child(1) pre.CodeMirror-line span[cm-text=""]:before{ position: absolute; left: 10px; top: 2px; color: #999; pointer-events: none; font-family: inherit; }
.CodeMirror-wrap[data-type="HTML"] .CodeMirror-code > div:nth-child(1) pre.CodeMirror-line span[cm-text=""]:before{ content: '<!-- Enter your Template code here -->'; }
.CodeMirror-wrap[data-type="JSON"] .CodeMirror-code > div:nth-child(1) pre.CodeMirror-line span[cm-text=""]:before{ content: '{"Enter your JSON code here"}'; }
.CodeMirror-wrap[data-type="CSS"] .CodeMirror-code > div:nth-child(1) pre.CodeMirror-line span[cm-text=""]:before{ content: '/* Enter your CSS code here */'; }
.CodeMirror-wrap[data-type="JS"] .CodeMirror-code > div:nth-child(1) pre.CodeMirror-line span[cm-text=""]:before{ content: '/* Enter your JavaScript code here */'; }

/* Add these styles to your existing CSS */
.preview-container { height: 100%; min-height: 500px; width: 100%; position: relative; }
.preview-frame { width: 100% !important; height: 100% !important; border: none; display: block; }

select.select2 { opacity: 0 !important; }
.select2-container--default .select2-selection--multiple { border-color: #d1d5db !important; border-radius: 0.5rem !important; min-height: 38px !important; padding: 0 8px; }
.select2-container--default .select2-selection--multiple .select2-selection__choice { background-color: #EEF4FF !important; color: #0C5BE2 !important; border: none !important; border-radius: 999px !important; padding: 2px 8px !important; margin: 4px 4px 4px 0 !important; display: inline-flex !important; align-items: center !important; font-size: 0.875rem !important; }
.select2-container--default .select2-selection--multiple .select2-selection__choice__remove { color: #0C5BE2 !important; margin-right: 4px !important; border-right: none !important; padding: 0 2px !important; font-size: 14px !important; border-radius: 999px !important; display: inline-block !important; align-items: center !important; justify-content: center !important; width: 16px !important; height: 16px !important; left: 4px !important }
.select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover { background-color: transparent !important; color: #0C5BE2 !important; }
.select2-container--default .select2-search--inline .select2-search__field { margin-top: 4px !important; padding-left: 4px !important; }
.select2-dropdown { border-color: #d1d5db !important; border-radius: 0.5rem !important; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1) !important; margin-top: 2px !important; }
.select2-container--default .select2-results__option--highlighted[aria-selected] { background-color: #f3f4f6 !important; color: #111827 !important; }
.select2-container--default .select2-results__option[aria-selected=true] { background-color: #e5e7eb !important; }
.select2-container--default.select2-container--focus .select2-selection--multiple { border-color: #0C5BE2 !important; box-shadow: 0 0 0 1px rgba(12, 91, 226, 0.2) !important; }

.select2-container--default .select2-selection--multiple .select2-selection__choice__display { padding-left: 14px !important; }

/* Custom Radio Button Styling */
.radio-button-group { display: flex; gap: 1rem; }
.radio-button-group input[type="radio"] { display: none; }
.radio-button-group label { display: inline-flex; align-items: center; padding: 0.5rem 1rem; border: 2px solid #E5E7EB; border-radius: 0.5rem; cursor: pointer; font-size: 0.875rem; color: #374151; transition: all 0.2s; }
.radio-button-group input[type="radio"]:checked + label { background-color: #EEF4FF; border-color: #0C5BE2; color: #0C5BE2; }
.radio-button-group label:hover { border-color: #0C5BE2; }

.detailedDescription ul { list-style-type: none; padding: 0; margin: 0; }
.detailedDescription li { display: flex; padding: 1rem; margin-bottom: 0.5rem; border-radius: 0.5rem; background-color: #f9fafb; font-size: 0.875rem; color: #6b7280; position: relative; padding-left: 2.5rem; flex-direction: column; }
.detailedDescription li ::before { content: ""; position: absolute; left: 1rem; top: 1.125rem; width: 1.25rem; height: 1.25rem; background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='20' height='20' fill='none' stroke='%2310b981' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' viewBox='0 0 24 24'%3E%3Cpath d='M5 13l4 4L19 7'%3E%3C/path%3E%3C/svg%3E"); background-position: center; background-repeat: no-repeat; background-size: contain; }
.detailedDescription li strong { display: block; font-size: 0.875rem; font-weight: 500; color: #111827; margin-bottom: 0; }

.ck-rounded-corners .ck.ck-editor__top .ck-sticky-panel .ck-toolbar, .ck.ck-editor__top .ck-sticky-panel .ck-toolbar.ck-rounded-corners { border-radius: 0.5rem !important; border-bottom-left-radius: 0 !important;border-bottom-right-radius: 0 !important; }
.ck.ck-editor__main>.ck-editor__editable:not(.ck-focused){ border-radius: 0  0 0.5rem 0.5rem !important; font-size: 14px !important; line-height: 1.6 !important; }

/* View Toggle */
/* .view-toggle.active{ color:#0C5BE2 !important; } */

.grid-view{ display: grid; }
.grid-view.grid-view-hidden { display: none; opacity: 0 !important; }
.table-view.table-view-hidden { display: none; opacity: 0 !important; }

.view-toggle { position: relative; background: white; border: 1px solid #e5e7eb; transition: all 0.2s ease-in-out; }
.view-toggle:hover { background: #f9fafb; border-color: #d1d5db; }
.view-toggle.active { color: #fff; }
.view-toggle.active:hover { color: #fff; }
.view-toggle.active svg, .view-toggle.active i { color: #fff !important; }
.view-toggle svg, .view-toggle i { color: #6b7280; transition: color 0.2s ease-in-out; }
.view-toggle:hover svg, .view-toggle:hover i { color: #374151; }
.view-toggle:first-child { border-top-left-radius: 0.5rem; border-bottom-left-radius: 0.5rem; }
.view-toggle:last-child { border-top-right-radius: 0.5rem; border-bottom-right-radius: 0.5rem; }
.view-toggle + .view-toggle { border-left: none; }
.view-toggle:focus { outline: none; box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5); }

#previewContent{ height: calc(100vh - 434px); }
#previewFrame{ transition: width 0.3s ease, max-width 0.3s ease, margin 0.3s ease; height: calc(100vh - 434px);}
.editor-fullscreen #previewFrame{ height: calc(100vh - 138px); }
.responsive-btn.active { color: #2563eb; }

/* Page Block */
.block-wrapper:hover .block-controls { display: flex; }


  /* Apply these classes to your HTML structure */
  /* .gridTableWrapper{ padding: 0 !important; overflow: inherit !important;}
  .gridTableWrapper .gridTableOverflowWrapper{ border: 0 !important;}
  .gridTableWrapper table thead{ display: none;}
  .gridTableWrapper table tbody { display: grid; grid-template-columns: repeat(auto-fill, minmax(437px, 1fr)); gap: 20px; padding: 20px; }
  .gridTableWrapper table tr { display: block; position: relative; background: white; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 10px rgba(0,0,0,0.1); transition: transform 0.3s ease; width: 100%; }
  .gridTableWrapper table tr:hover { transform: translateY(-5px); }
  .gridTableWrapper table tr td{ display: inline-flex; width: 100%; }
  .gridTableWrapper table tr td.template-image{ padding-bottom: 15px; }
  .gridTableWrapper table tr td.template-image > div{ width: 100%;}
  .gridTableWrapper table tr td.template-image > div > div{ padding: 10px 10px 0;}
  .gridTableWrapper table tr td.template-image > div > div > div.font-semibold { font-size: 20px; line-height: 1; padding-bottom: 10px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;}
  .gridTableWrapper table tr td.template-image img{ min-height: 280px; max-height: 280px; object-fit: cover; width: auto;}
  
  .gridTableWrapper table tr td.cell_imagewrapper{ width: 100%; }
  .gridTableWrapper table tr td.cell_imagewrapper .image-container{ width: 100%;}
  .gridTableWrapper table tr td.cell_imagewrapper .image-container img{min-height: 280px; max-height: 280px; object-fit: contain; width: 100%;}
  
  .gridTableWrapper table tr td.cell_id__name{ flex-direction: column; padding: 14px 12px;}
  .gridTableWrapper table tr td.cell_id__name .font-semibold { font-size: 20px; line-height: 1; padding-bottom: 5px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;}

  .gridTableWrapper table tr td.categories{ width: 100%; display: none; }
  .gridTableWrapper table tr td.page_count, .gridTableWrapper table tr td.block_count{ position: absolute; top: 10px; right: 10px; display: inline-flex; background: #fff; width: auto; border-radius: 10px; padding: 0 8px; }
  .gridTableWrapper table tr td.page_count::before{ content: "Pages: "; padding-right: 5px;}
  .gridTableWrapper table tr td.block_count::before{ content: "Block: "; padding-right: 5px;}
  .gridTableWrapper table tr td.updated_at{display: none;}
  .gridTableWrapper table tr td.created_at{width: 50%; padding: 0 10px !important; display: none;}
  .gridTableWrapper table tr td.actionColumn{ width: auto; padding: 0 !important; justify-content: end; position: absolute; right: 10px; bottom: 8px;} 
  .gridTableWrapper table tr td.cell_slug{ display: none;}
  .gridTableWrapper table tr td.cell_description{ display: none;}
  .gridTableWrapper table tr td.cell_created_at{ display: none;}
  .gridTableWrapper table tr td.cell_updated_at{ display: none;}
   */

/* Improved Grid Table Wrapper Styles */
/* .gridTableWrapper { padding: 0 !important; overflow: inherit !important; }
.gridTableWrapper .gridTableOverflowWrapper { border: 0 !important; }
.gridTableWrapper table thead { display: none; }
.gridTableWrapper table tbody { display: grid; grid-template-columns: repeat(auto-fill, minmax(320px, 1fr)); gap: 24px; padding: 24px; }
.gridTableWrapper table tr { display: block; position: relative; background: white; border-radius: 12px; overflow: hidden; box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08); transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1); width: 100%; border: 1px solid #f1f5f9; }
.gridTableWrapper table tr td { display: inline-flex; width: 100%; }
.gridTableWrapper table tr td.cell_imagewrapper { width: 100%; padding: 0; }
.gridTableWrapper table tr td.cell_imagewrapper > a { width: 100%; display: block; }
.gridTableWrapper table tr td.cell_imagewrapper > a > div svg { width: 48px; height: 48px; color: #94a3b8; }
.gridTableWrapper table tr td.cell_id__name { flex-direction: column; padding: 16px 16px 16px; background: white; }
.gridTableWrapper table tr td.cell_id__name .font-medium { font-size: 16px; line-height: 1.3; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; color: #1e293b; padding-bottom: 5px; }
.gridTableWrapper table tr td.cell_id__name .text-sm { font-size: 13px; color: #64748b;}
.gridTableWrapper table tr td.categories, .gridTableWrapper table tr td.cell_slug, .gridTableWrapper table tr td.cell_description, .gridTableWrapper table tr td.cell_created_at, 
.gridTableWrapper table tr td.cell_updated_at, .gridTableWrapper table tr td.updated_at, .gridTableWrapper table tr td.created_at { display: none; }
.gridTableWrapper table tr td.page_count, .gridTableWrapper table tr td.block_count { position: absolute; top: 12px; right: 12px; display: inline-flex; background: rgba(255, 255, 255, 0.95); backdrop-filter: blur(10px); width: auto; border-radius: 20px; padding: 6px 12px; font-size: 12px; font-weight: 600; color: #475569; box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1); border: 1px solid rgba(255, 255, 255, 0.2); }
.gridTableWrapper table tr td.page_count::before { content: "Pages: "; padding-right: 4px; color: #64748b; }
.gridTableWrapper table tr td.block_count::before { content: "Blocks: "; padding-right: 4px; color: #64748b; }
.gridTableWrapper table tr td.actionColumn { width: auto; padding: 0 !important; justify-content: end; position: absolute; right: 12px; bottom: 12px; gap: 8px; }
.gridTableWrapper table tr td.actionColumn button, .gridTableWrapper table tr td.actionColumn a { background: rgba(255, 255, 255, 0.95); backdrop-filter: blur(10px); padding: 8px;}
.gridTableWrapper table tr td.actionColumn button:hover, .gridTableWrapper table tr td.actionColumn a:hover { background: white;  }
.gridTableWrapper table tr td.actionColumn button svg, .gridTableWrapper table tr td.actionColumn a svg { width: 16px; height: 16px; color: #64748b; }
.gridTableWrapper table tr td.actionColumn button:hover svg, .gridTableWrapper table tr td.actionColumn a:hover svg { color: #475569; }
.gridTableWrapper table tr td.actionColumn button[data-modal-target="deleteBlockModal"]:hover svg { color: #dc2626; }

@media (max-width: 768px) { 
    .gridTableWrapper table tbody { grid-template-columns: 1fr; gap: 16px; padding: 16px; }
    .gridTableWrapper table tr td.cell_imagewrapper .image-container { height: 180px; }
    .gridTableWrapper table tr td.cell_id__name { padding: 16px 12px 12px; }
    .gridTableWrapper table tr td.cell_id__name .font-medium { font-size: 16px; }
}

@media (min-width: 1200px) { 
    .gridTableWrapper table tbody { grid-template-columns: repeat(auto-fill, minmax(360px, 1fr)); }
}

.gridTableWrapper table tr::before { content: ''; position: absolute; top: 0; left: 0; right: 0; height: 3px; background: linear-gradient(90deg, #3b82f6, #8b5cf6, #06b6d4); opacity: 0; transition: opacity 0.3s ease; }
.gridTableWrapper table tr:hover::before { opacity: 1; }
.gridTableWrapper table tr td.cell_imagewrapper .image-container img { transition: all ease-in 0.5s; }
.gridTableWrapper table tr.searchable-row:hover td.cell_imagewrapper .image-container img {transform: translateY(calc(-100% + 240px));}
.gridTableWrapper table tr td.cell_imagewrapper .image-container { width: 100%; position: relative; height: 240px; overflow: hidden; }
.gridTableWrapper table tr td.cell_imagewrapper .image-placeholder{ display: flex; align-items: center; justify-content: center; width: 100%; position: relative; height: 240px; overflow: hidden; background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%); }
.gridTableWrapper table tr td.cell_imagewrapper .image-container img { width: 100%; position: absolute; left: 0; right: 0; top: 0; height: auto; bottom: 0; animation: transition linear 0.5s; }
.gridTableWrapper table tr td.cell_imagewrapper .image-container img { transition: all ease-in 0.5s; }
.gridTableWrapper table tr td.cell_id__name .font-semibold{font-size: 16px; line-height: 1;padding-bottom: 0px;margin-bottom: 0;}
.gridTableWrapper table tr td.actionColumn { width: auto; padding: 0 !important; justify-content: end; position: absolute; right: 12px; bottom: 10px; z-index: 9; } */