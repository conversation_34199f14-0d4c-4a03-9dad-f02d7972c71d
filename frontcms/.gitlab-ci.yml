stages:
  - deploy

variables:
  GIT_STRATEGY: none
  # Domain mapping
  MAIN: "MAIN"
  BRANCH1: "DEVELOPMENT"
  BRANCH2: "LIVE"
  # Add more branch to domain mappings as needed

.deploy_template: &deploy_template
  stage: deploy
  tags:
    - shell-exec
  before_script:
    - |
      # Determine the domain based on branch
      if [[ "$CI_COMMIT_BRANCH" == "main" ]]; then
        DOMAIN=$MAIN
      elif [[ "$CI_COMMIT_BRANCH" == "branch1" ]]; then
        DOMAIN=$BRANCH1
      elif [[ "$CI_COMMIT_BRANCH" == "branch2" ]]; then
        DOMAIN=$BRANCH2
      else
        DOMAIN=$(echo "$CI_COMMIT_BRANCH" | tr '[:lower:]' '[:upper:]')
      fi

      # Set variables dynamically based on domain
      export SERVER_ACCESS_VAR="${DOMAIN}_SERVER_ACCESS"
      export SERVER_IP_VAR="${DOMAIN}_SERVER_IP"
      export SERVER_USER_VAR="${DOMAIN}_SERVER_USER"
      export DEPLOY_PATH_VAR="${DOMAIN}_DEPLOY_PATH"

    - 'command -v ssh-agent >/dev/null || ( apt-get update -qq && apt-get install -qq openssh-client )'
    - eval $(ssh-agent -s)
    - echo "${!SERVER_ACCESS_VAR}" | base64 --decode > /tmp/ssh_key
    - chmod 600 /tmp/ssh_key
    - ssh-add /tmp/ssh_key
    - rm /tmp/ssh_key
    - mkdir -p ~/.ssh
    - echo -e "Host ${!SERVER_IP_VAR}\n\tStrictHostKeyChecking accept-new\n\tUserKnownHostsFile ~/.ssh/known_hosts\n" > ~/.ssh/config
  script:
    - |
      if ! ssh ${!SERVER_USER_VAR}@${!SERVER_IP_VAR} "cd ${!DEPLOY_PATH_VAR} && git pull origin $CI_COMMIT_BRANCH"; then
        echo "Deployment failed for $DOMAIN"
        exit 1
      fi
    - echo "Deployment successful to ${!DEPLOY_PATH_VAR} for $DOMAIN"
  after_script:
    - rm -f ~/.ssh/config
  retry:
    max: 2
    when:
      - runner_system_failure
      - stuck_or_timeout_failure

# Deploy for main branch
deploy_main:
  <<: *deploy_template
  only:
    - main
  environment:
    name: production
    url: https://${MAIN_SERVER_IP}

# Deploy for other branches with [DEPLOY] tag
deploy_branch:
  <<: *deploy_template
  rules:
    - if: $CI_COMMIT_BRANCH != "main" && $CI_COMMIT_MESSAGE =~ /\[DEPLOY\]/
  environment:
    name: $CI_COMMIT_BRANCH
    url: https://${CI_COMMIT_BRANCH}
