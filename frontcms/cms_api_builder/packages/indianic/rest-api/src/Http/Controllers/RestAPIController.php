<?php
namespace Indianic\RestApi\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Indianic\RestApi\Helpers\ApiResponse;
use Indianic\RestApi\RestAPI;

/**
 * Class RestAPIController
 *
 * This controller handles CRUD operations (Create, Read, Update, Delete) for resources
 * using the RestAPI class.
 */
class RestAPIController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * This method retrieves a list of records by applying filters, joins, order by, paging, grouping and returns the data.
     *
     * @param RestAPI $restAPI The RestAPI instance for retrieving data.
     * @return JsonResponse A JSON response containing the list of records.
     */
    public function index(RestAPI $restAPI): JsonResponse
    {
        $data = $restAPI->withDefault()->getData();
        return ApiResponse::success($data, 'Data retrieved successfully.');
    }

    /**
     * Display a specific resource by ID.
     *
     * This method retrieves a single record based on its ID.
     *
     * @param RestAPI $restAPI The RestAPI instance for retrieving the record.
     * @param mixed $id The ID of the record to retrieve.
     * @return JsonResponse A JSON response containing the record data.
     */
    public function getOne(RestAPI $restAPI, mixed $id): JsonResponse
    {
        $data = $restAPI->withSelectFields()
            ->withJoin()
            ->withGroupBy()
            ->first($id);

        if (!$data) {
            return ApiResponse::error(message: 'Resource not found.', statusCode: 404);
        }

        return ApiResponse::success($data, 'Data retrieved successfully.');
    }

    /**
     * Store a newly created resource in storage.
     *
     * This method creates a new record by passing the request data to the RestAPI instance.
     *
     * @param RestAPI $restAPI The RestAPI instance for storing the data.
     * @return JsonResponse A JSON response with a status message.
     */
    public function store(RestAPI $restAPI): JsonResponse
    {
        $restAPI->validateRequest('post');
        $insertId = $restAPI->storeData();

        return ApiResponse::success([
            'last_insert_id' => $insertId
        ], 'Resource created successfully.');
    }

    /**
     * Update the specified resource in storage.
     *
     * This method updates an existing record based on its ID using the RestAPI instance.
     *
     * @param RestAPI $restAPI The RestAPI instance for updating the data.
     * @param string $id The ID of the record to update.
     * @return JsonResponse A JSON response with a status message.
     */
    public function update(RestAPI $restAPI, string $id): JsonResponse
    {
        $restAPI->validateRequest('put');
        $count = $restAPI->updateRow($id);

        return ApiResponse::success([
            'rows_updated' => $count
        ], 'Resource updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     *
     * This method deletes a record based on its ID using the RestAPI instance.
     *
     * @param RestAPI $restAPI The RestAPI instance for deleting the data.
     * @param string $id The ID of the record to delete.
     * @return JsonResponse A response indicating no content (204 No Content).
     */
    public function destroy(RestAPI $restAPI, mixed $id): JsonResponse
    {
        $count = $restAPI->where(
            $restAPI->getIdKey($id),
            $restAPI->getIdVal($id),
        )->delete();

        return ApiResponse::success([
            'rows_deleted' => $count
        ], 'Resource deleted successfully.');
    }

}
