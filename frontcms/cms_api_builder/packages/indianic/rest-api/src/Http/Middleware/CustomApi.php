<?php

namespace Indianic\RestApi\Http\Middleware;

use Closure;
use Indianic\RestApi\Helpers\ApiResponse;

class CustomApi
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle($request, Closure $next)
    {
        $segment = $request->segments();
        if ($segment[2] == 'custom-api') {
            $config = config('rest-api-custom-apis');
            // Check if the configuration file exists
            if (is_null($config)) {
                abort_if(1, 500, 'Configuration file "rest-api-custom-apis.php" not found.');
            }

            $method = $request->getMethod();
            $slug = $request->route('slug');

            if (!isset($config[$slug]) || empty($config[$slug])) {
                return ApiResponse::error('404 Not found', 404);
            }

            if ($config[$slug]['method'] != $method) {
                return ApiResponse::error('404 Not found', 404);
            }

            /**Check validaion rules */
            $configCustomValidation = config('rest-api-validations');
            if (!is_null($configCustomValidation)) {
                // Include the configuration file

                // Check if 'custom-api' and the slug exist
                if (isset($configCustomValidation['custom-api'][$slug])) {
                    $validationRuleId = $configCustomValidation['custom-api'][$slug] ?? null;
                    // Check if 'validations' and the validation rule ID exist
                    if ($validationRuleId && isset($configCustomValidation['validations'][$validationRuleId])) {
                        // Set rules based on the validation rule ID
                        $request['rules'] = $configCustomValidation['validations'][$validationRuleId] ?? [];
                    } else {
                        // Validation rule ID or validations entry is missing
                        $request['rules'] = [];
                    }
                } else {
                    // 'custom-api' or slug is not found
                    $request['rules'] = [];
                }
            } else {
                // Configuration file does not exist
                $request['rules'] = [];
            }
            $request['api_data'] = $config[$slug];
        }
        // Allow the request to proceed
        return $next($request);
    }
}
