<?php

namespace Indianic\RestApi\Traits;

use Indianic\RestApi\Builders\Filter;
use Indianic\RestApi\Builders\FilterHash;
use Indianic\RestApi\Builders\GroupBy;
use Indianic\RestApi\Builders\Join;
use Indianic\RestApi\Builders\SelectField;
use Indianic\RestApi\Builders\Sort;

/**
 * Trait RestAPIUtils
 *
 * This trait provides utility methods for registering various query
 * building functionalities in the REST API, including sorting,
 * selecting fields, filtering, joining tables, and grouping results.
 */
trait RestAPIUtils
{
    /**
     * Register all default builder, including selecting fields,
     * sorting, filtering, joining, and grouping.
     *
     * @return static Returns the current instance for method chaining.
     */
    public function withDefault(): static
    {
        $this->withSelectFields();
        $this->withSorting();
        $this->withFilters();
        $this->withJoin();
        $this->withGroupBy();
        return $this;
    }

    /**
     * Register sorting builder to the query builder based on the request data.
     *
     * @return static Returns the current instance for method chaining.
     */
    public function withSorting(): static
    {
        $this->builders->put('sort', Sort::make($this->request));
        return $this;
    }

    /**
     * Register select field selection builder to the query builder based on the request data.
     *
     * @return static Returns the current instance for method chaining.
     */
    public function withSelectFields(): static
    {
        $this->builders->put('select', SelectField::make($this->request));
        return $this;
    }

    /**
     * Register filters builder to the query builder based on the request data.
     *
     * @return static Returns the current instance for method chaining.
     */
    public function withFilters(): static
    {
        $this->builders->put('filter', Filter::make($this->request));
        return $this;
    }

    public function withFilterHash(): static
    {
        $this->builders->put('filterHash', FilterHash::make($this->request));
        return $this;
    }

    /**
     * Register join builder to the query builder based on the request data.
     *
     * @return static Returns the current instance for method chaining.
     */
    public function withJoin(): static
    {
        $this->builders->put('join', Join::make($this->request));
        return $this;
    }

    /**
     * Register group by builder to the query builder based on the request data.
     *
     * @return static Returns the current instance for method chaining.
     */
    public function withGroupBy(): static
    {
        $this->builders->put('group_by', GroupBy::make($this->request));
        return $this;
    }

}
