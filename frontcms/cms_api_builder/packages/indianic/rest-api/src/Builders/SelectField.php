<?php

namespace Indianic\RestApi\Builders;

use Illuminate\Database\Query\Builder;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Str;
use Indianic\RestApi\contracts\QueryBuilder;

/**
 * Class SelectField
 *
 * This class handles selecting fields from a database query based on request data.
 */
class SelectField extends QueryBuilder
{

    /**
     * Apply the selected fields to the query builder.
     *
     * This method retrieves the fields to be selected from the request and applies them to the query builder.
     * If no specific fields are selected, it retrieves all fields from the table.
     *
     * @param Builder $builder The query builder instance to apply selected fields to.
     * @return void
     */
    public function run(Builder $builder): void
    {
        $fieldsToBeSelected = [];
        $fields = $this->getSelectFieldsFromRequest();
        if ($fields->isEmpty()) {
            $fieldsToBeSelected = $this->getTableAllFields($builder->from);
        } else {
            foreach ($fields as $table => $tableFields) {
                $table = $table === '_' ? $builder->from : $table;
                $prependedFields = $this->prependFieldsWithTableName($tableFields, $table);
                $extractedFields = $this->getSelectableFields($prependedFields);
                $fieldsToBeSelected = [...$fieldsToBeSelected, ...$extractedFields];
            }
        }

        $filteredFields = $this->filterOutRestrictedFields($fieldsToBeSelected);
        $fieldsWithAlias = $this->processSelectField($filteredFields, $builder->from);
        $builder->select($fieldsWithAlias);
    }

    /**
     * Process and select the appropriate fields for the join table.
     *
     * This method adds the fields to be selected from the joined table, excluding
     * any fields that are marked as hidden in the configuration.
     *
     * @param Builder $builder The query builder instance to add fields to.
     * @param string $table The name of the join table.
     * @return void
     */
    protected function processSelectField(array $fields, string $ignoreTable): array
    {
        foreach ($fields as $index => $field) {
            if(!Str::startsWith($field, $ignoreTable.'.')) {
                $fields[$index] = $this->getAlias($field);
            }
        }
        return $fields;
    }

    /**
     * Generate an alias for the selected field.
     *
     * This method replaces periods (.) in the field name with underscores (_) to create an alias.
     *
     * @param string $field The field name.
     * @return string The field alias.
     */
    public function getAlias(string $field): string
    {
        return $field . ' AS ' . Str::replace('.', '_', $field);
    }

    /**
     * Retrieve selectable fields, including wildcard (*) fields, from the request.
     *
     * @param array $prependedFields The fields with table names prepended.
     * @return array The fully expanded list of selectable fields.
     */
    protected function getSelectableFields(array $prependedFields): array
    {
        $selectableFields = [];
        foreach ($prependedFields as $field) {
            if (Str::endsWith($field, '.*')) {
                $table = rtrim($field, '.*');;
                $selectableFields = array_merge($selectableFields, $this->getTableAllFields($table));
            } else {
                $selectableFields[] = $field;
            }
        }
        return $selectableFields;
    }

    /**
     * Retrieve all fields from the specified table.
     *
     * This method fetches all columns of the table from the database schema and prepends the table name to each field.
     *
     * @param string $table The name of the table.
     * @return array A list of all fields in the table.
     */
    protected function getTableAllFields(string $table): array
    {
        $fieldsToBeSelect = Schema::getColumnListing($table); // Uses the default connection
        return $this->prependFieldsWithTableName($fieldsToBeSelect, $table);
    }

    /**
     * Filter out any restricted fields from the selected fields.
     *
     * @param array $fields The list of fields to be selected.
     * @return array The filtered list of fields with restricted ones removed.
     */
    protected function filterOutRestrictedFields(array $fields): array
    {
        $restrictedFields = config('rest-api.hidden_fields', []);
        return collect($fields)
            ->filter(fn($val) => !in_array($val, $restrictedFields))
            ->all();
    }

    /**
     * Retrieve the select fields from the request data.
     *
     * This method fetches the fields that should be selected from the request, either as a string or array.
     *
     * @return Collection A collection of select fields mapped to their respective tables.
     */
    protected function getSelectFieldsFromRequest(): Collection
    {
        $fieldsData = $this->request->getRequestData(config('rest-api.parameters.fields'));
        $fieldsPerTable = collect(
            is_string($fieldsData)
                ? explode(',', str_replace(' ', '', $fieldsData))
                : $fieldsData
        );

        if ($fieldsPerTable->isEmpty()) {
            return collect();
        }

        $fields = [];
        $fieldsPerTable->each(function ($tableFields, $table) use (&$fields) {
            if (is_numeric($table)) {
                // If the field is in dot notation, we'll grab the table without the field.
                // If the field isn't in dot notation we want the base table. We'll use `_` and replace it later.
                $table = Str::contains($tableFields, '.') ? Str::beforeLast($tableFields, '.') : '_';
            }

            if (! isset($fields[$table])) {
                $fields[$table] = [];
            }

            // If the field is in dot notation, we'll grab the field without the tables:
            $tableFields = array_map(function (string $field) {
                return Str::afterLast($field, '.');
            }, explode(',', $tableFields));

            $fields[$table] = array_merge($fields[$table], $tableFields);
        });

        return collect($fields);
    }

    /**
     * Prepend the table name to the given fields.
     *
     * This method ensures that each field is properly namespaced with its table name.
     *
     * @param array $fields The list of fields.
     * @param string $tableName The name of the table to prepend.
     * @return array The list of fields with table names prepended.
     */
    protected function prependFieldsWithTableName(array $fields, string $tableName): array
    {
        return array_map(function($field) use ($tableName) {
            return $this->prependField($field, $tableName);
        }, $fields);
    }

    /**
     * Prepend the table name to a specific field, if not already present.
     *
     * @param string $field The field name.
     * @param string $table The table name.
     * @return string The field name with the table name prepended.
     */
    protected function prependField(string $field, string $table): string
    {
        if (!Str::contains($field, '.')) {
            $field = "{$table}.{$field}";
        }
        return $field;
    }
}
