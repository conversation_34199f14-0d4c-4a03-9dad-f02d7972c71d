<?php

namespace Indianic\RestApi\Builders;

use Illuminate\Database\Query\Builder;
use Illuminate\Support\Str;
use Indianic\RestApi\contracts\QueryBuilder;

/**
 * Class Filter
 *
 * This class is responsible for applying filters to a database query builder
 * instance. It processes the filter fields from the request and dynamically
 * builds query conditions based on the provided filter data.
 */
class Filter extends QueryBuilder
{

    /**
     * Run the filter logic on the provided query builder.
     *
     * This method retrieves filter fields from the request and applies them to the
     * query builder. It handles multiple values for a single field by using 'LIKE'
     * queries with the 'OR' condition.
     *
     * @param Builder $builder The query builder instance to apply filters to.
     * @return void
     */
    public function run(Builder $builder): void
    {
        $this->getFilterFields()
            ->each(function($val, $fieldName) use ($builder) {
                if (is_array($val) || Str::contains($val, ',')) {
                    $values = is_array($val) ? $val : explode(',', $val);
                    $builder->where(function ($subQuery) use ($fieldName, $values) {
                        foreach ($values as $val) {
                            $subQuery->orWhereLike($fieldName, $val);
                        }
                    });
                } else {
                    $builder->whereLike($fieldName, $val);
                }
            });
    }

    /**
     * Retrieve and process filter fields from the request.
     *
     * This method fetches the filter parameters from the request and processes
     * them into a collection. It ensures that the filter values are in a proper format
     * for further usage in query building.
     *
     * @return \Illuminate\Support\Collection A collection of filter fields.
     */
    protected function getFilterFields(): \Illuminate\Support\Collection
    {
        $filter = $this->request->getRequestData(config('rest-api.parameters.filter'));

        if (is_string($filter)) {
            return collect();
        }

        return collect($filter)
            ->map(fn ($value) => $this->getFilterValue($value));
    }

    /**
     * Process and return the proper filter value.
     *
     * This method normalizes the filter value, handling special cases such as
     * empty values, arrays, and strings with comma-separated values. It also
     * converts 'true' and 'false' strings into their respective boolean values.
     *
     * @param mixed $value The raw filter value from the request.
     * @return mixed The processed filter value.
     */
    private function getFilterValue(mixed $value): mixed
    {
        if (empty($value)) {
            return $value;
        }

        if (is_array($value)) {
            return collect($value)
                ->map(fn ($valueValue) => $this->getFilterValue($valueValue))
                ->all();
        }

        if (Str::contains($value, ',')) {
            return explode(',', $value);
        }

        if ($value === 'true') {
            return true;
        }

        if ($value === 'false') {
            return false;
        }

        return $value;
    }
}
