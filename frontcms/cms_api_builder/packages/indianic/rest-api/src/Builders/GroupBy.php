<?php

namespace Indianic\RestApi\Builders;

use Illuminate\Database\Query\Builder;
use Illuminate\Support\Str;
use Indianic\RestApi\contracts\QueryBuilder;

/**
 * Class GroupBy
 *
 * This class handles applying 'GROUP BY' clauses to a database query builder.
 */
class GroupBy extends QueryBuilder
{

    /**
     * Apply the 'GROUP BY' clause to the query builder.
     *
     * This method retrieves the fields to group by and applies them to the
     * query builder instance.
     *
     * @param Builder $builder The query builder instance to apply 'GROUP BY' to.
     * @return void
     */
    public function run(Builder $builder): void
    {
        $groupBy = $this->getGroupByFields()->all();
        if (!empty($groupBy)) {
            $builder->groupBy($groupBy);
        }
    }

    /**
     * Retrieve the 'GROUP BY' fields from the request.
     *
     * This method fetches the group by fields and converts them into a collection
     * that can be used by the query builder.
     *
     * @return \Illuminate\Support\Collection A collection of group by fields.
     */
    protected function getGroupByFields(): \Illuminate\Support\Collection
    {
        $groupBy = $this->request->getRequestData(config('rest-api.parameters.group_by'));
        if (is_string($groupBy)) {
            $groupBy = explode(',', $groupBy);
        }
        return collect($groupBy);
    }
}
