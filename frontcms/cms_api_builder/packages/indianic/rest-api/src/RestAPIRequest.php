<?php
namespace Indianic\RestApi;

use Illuminate\Http\Request;

/**
 * Class RestAPIRequest
 *
 * Extends the default Laravel Request class to provide additional
 * functionality specific to the REST API.
 */
class RestAPIRequest extends Request
{
    /**
     * Create a new instance of RestAPIRequest from an existing Request.
     *
     * @param Request $request
     * @return self
     */
    public static function fromRequest(Request $request): self
    {
        return static::createFrom($request);
    }

    /**
     * Retrieve input data from the request.
     *
     * This method allows for optional retrieval of data by key,
     * returning a default value if the key is not present.
     *
     * @param string|null $key The key to retrieve data for.
     * @param mixed|null $default The default value to return if key not found.
     * @return mixed
     */
    public function getRequestData(?string $key = null, mixed $default = null): mixed
    {
        return $this->input($key, $default);
    }
}
