<?php

namespace Indianic\RestApi\FieldType;

class EmailTypeField implements FieldType
{

    public function getID(): string
    {
        return 'email';
    }

    public function getLabel(): string
    {
        return 'Email';
    }

    public function getFields(): array
    {
        return [];
    }

    public function getRuleForValidator(array $data): array
    {
        $rule = ['email'];
        if ($data['unique']) {
            $rule[]  = "unique:{$data['table']},{$data['column']}";
        }
        return $rule;
    }
}
