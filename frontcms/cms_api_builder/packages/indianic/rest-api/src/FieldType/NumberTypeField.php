<?php

namespace Indianic\RestApi\FieldType;

class NumberTypeField implements FieldType
{

    public function getID(): string
    {
        return 'number';
    }

    public function getLabel(): string
    {
        return 'Number';
    }



    public function getFields(): array
    {
        return [];
    }

    public function getRuleForValidator(array $data): array
    {
        $rules = ['numeric'];

        if($data['min']) {
            $rules[] = 'min:' . $data['min'];
        }

        if($data['max']) {
            $rules[] = 'max:' . $data['max'];
        }

        return $rules;
    }
}
