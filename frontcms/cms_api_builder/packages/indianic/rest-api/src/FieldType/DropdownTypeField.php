<?php

namespace Indianic\RestApi\FieldType;
use Illuminate\Validation\Rule;

class DropdownTypeField implements FieldType
{

    public function getID(): string
    {
        return 'dropdown';
    }

    public function getLabel(): string
    {
        return 'Dropdown';
    }

    public function getFields(): array
    {
        return [];
    }

    public function getRuleForValidator(array $data): array
    {
        $rules = [];
        switch ($data['limit_list_by']) {
            case 'database':
                $rules[] = "exists:{$data['table']},{$data['field']}";
                break;
            case 'static':
                $allowedList = [];
                foreach ($data['dropdown_options'] as $val) {
                    $allowedList[] = $val['value'];
                }
                $rules[] = Rule::in($allowedList);
                break;
        }
        return $rules;
    }
}
