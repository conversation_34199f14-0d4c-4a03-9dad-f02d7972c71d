<?php
namespace Indianic\RestApi\Exceptions;

use Illuminate\Database\QueryException;
use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Illuminate\Validation\ValidationException;
use Indianic\RestApi\Helpers\ApiResponse;
use <PERSON><PERSON>\Sanctum\Exceptions\MissingAbilityException;
use Throwable;

/**
 * Custom exception handler for REST API requests.
 *
 * This class extends the default Laravel ExceptionHandler and provides
 * custom handling for REST API exceptions, returning JSON responses.
 */
class RestAPIExceptionsHandler extends ExceptionHandler
{
    /**
     * Render an exception into an HTTP response.
     *
     * If the request is determined to be a REST API request, this method
     * will return a JSON response with the exception details. Otherwise,
     * it will fall back to the default exception rendering process.
     *
     * @param \Illuminate\Http\Request $request The incoming request instance.
     * @param \Throwable $e The thrown exception.
     * @return mixed The HTTP response, either JSON for REST API requests or the default.
     * @throws Throwable
     */
    public function render($request, Throwable $e): mixed
    {
        if ($this->isRestAPIRequest($request) && $e instanceof QueryException) {
            return ApiResponse::error($e->getMessage(), 400, $e->getCode());
        } elseif ($this->isRestAPIRequest($request) && $e instanceof ValidationException) {
            return ApiResponse::validationError($e->getMessage(), $e->errors());
        } elseif ($this->isRestAPIRequest($request) && $e instanceof MissingAbilityException) {
            return ApiResponse::error('Access Forbidden.', 403);
        } else {
            return parent::render($request, $e);
        }
    }

    /**
     * Determine if the request is a REST API request.
     *
     * This method checks if the request URL matches the configured REST API prefix.
     *
     * @param \Illuminate\Http\Request $request The incoming request instance.
     * @return bool True if it's a REST API request, false otherwise.
     */
    private function isRestAPIRequest($request): bool
    {
        // Get the REST API prefix from the config, or default to 'rest-api'
        $prefix = config('rest-api.prefix', 'rest-api') . '/*';

        // Check if the request URI matches the API prefix
        return $request->is($prefix);
    }
}
