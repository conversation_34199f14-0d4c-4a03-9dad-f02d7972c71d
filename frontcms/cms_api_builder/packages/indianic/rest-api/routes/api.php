<?php

use Illuminate\Support\Facades\Route;
use Indianic\RestApi\Http\Controllers\RestAPIController;
use Indianic\RestApi\Http\Controllers\RestAPIAggregatesController;
use Indianic\RestApi\Http\Controllers\RestAPIBulkActionController;
use Indianic\RestApi\Http\Controllers\CustomRestAPIController;
use Indianic\RestApi\Http\Middleware\RestAPICheckTableRestriction;
use Indianic\RestApi\Http\Middleware\CustomApi;

    Route::middleware([
        'api',
        RestAPICheckTableRestriction::class,
        CustomApi::class
    ])
    ->group(function () {
        Route::controller(RestAPIAggregatesController::class)
            ->prefix('aggregates')
            ->group(function () {
                Route::get('/{table}/count', 'count');
                Route::get('/{table}/max/{fieldName}', 'max');
                Route::get('/{table}/avg/{fieldName}', 'avg');
                Route::get('/{table}/exists', 'exists');
            });

        Route::controller(RestAPIBulkActionController::class)
            ->prefix('bulk-action')
            ->group(function () {
                Route::post('/{table}', 'store');
                Route::put('/{table}', 'update');
                Route::delete('/{table}', 'destroy');
            });


        Route::any('custom-api/{slug}', CustomRestAPIController::class);


        Route::controller(RestAPIController::class)
            ->group(function () {
                Route::get('/{table}', 'index');
                Route::get('/{table}/{id}', 'getOne');
                Route::post('/{table}', 'store');
                Route::put('/{table}/{id}', 'update');
                Route::delete('/{table}/{id}', 'destroy');
            });
    });
