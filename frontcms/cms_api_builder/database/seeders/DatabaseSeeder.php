<?php

namespace Database\Seeders;

use App\Models\Tag;
use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // User::factory(10)->create();

        // User::factory()->create([
        //     'name' => 'Test User',
        //     'email' => '<EMAIL>',
        // ]);

        // for ($i = 0; $i < 10; $i++) {
        //     $blockCategory = new \App\Models\BlockCategory();
        //     $blockCategory->name = 'Category ' . ($i + 1);
        //     $blockCategory->save();

        //     for ($j = 0; $j < 5; $j++) {
        //         $block = new \App\Models\Block();
        //         $block->name = 'Block ' . ($j + 1) . ' of Category ' . ($i + 1);
        //         $block->slug = 'Block ' . ($j + 1) . ' of Category ' . ($i + 1);
        //         $block->description = 'Description of Block ' . ($j + 1) . ' of Category ' . ($i + 1);
        //         $block->image = 'https://picsum.photos/200/300?random=' . rand(1, 100);
        //         $block->save();

        //         $blockCategory->blocksAttached()->attach($block->id, ['category_id' => $blockCategory->id]);
        //     }
        // }




        // for ($i = 0; $i < 10; $i++) {
        //     $tag = new Tag();
        //     $tag->tag = 'Tag ' . ($i + 1);
        //     $tag->save();

        //     $blockIds = \App\Models\Block::inRandomOrder()->limit(rand(1, 5))->pluck('id')->toArray();
        //     $tag->blocks()->attach($blockIds, ['model_type' => \App\Models\Block::class]);
        // }
    }
}
