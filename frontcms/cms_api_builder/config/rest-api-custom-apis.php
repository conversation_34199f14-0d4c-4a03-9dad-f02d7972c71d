<?php
return [
        'get-block-categories-wise-data' => [
                'name' => 'get Block Categories Wise Data',
                'slug' => 'get-block-categories-wise-data',
                'method' => 'GET',
                'type' => 'sql',
                'table' => null,
                'fields' => [],
                'join' => [
],
                'filter' => [
],
                'sql_query' => 'SELECT 
    bc.id AS category_id,
    bc.name AS category_name,
    COUNT(b.id) AS block_count,
    CASE 
        WHEN COUNT(b.id) = 0 THEN JSON_ARRAY()
        ELSE COALESCE(
            JSON_ARRAYAGG(
                JSON_OBJECT(
                    \'block_id\', b.id,
                    \'block_name\', b.name,
                    \'block_slug\', b.slug,
                    \'block_description\', b.description,
                    \'block_image\', b.image
                )
            ),
            JSON_ARRAY()
        )
    END AS blocks
FROM 
    block_categories bc
LEFT JOIN 
    block_categories_mapping bcm ON bc.id = bcm.category_id
LEFT JOIN 
    blocks b ON bcm.block_id = b.id
GROUP BY 
    bc.id, bc.name
ORDER BY 
    bc.id DESC
LIMIT :limit OFFSET :offset;
',
                'sort_by' => null,
                'limit' => null,
                'group_by' => null,
            ],
    'gettemplates' => [
                'name' => 'getTemplates',
                'slug' => 'gettemplates',
                'method' => 'GET',
                'type' => 'sql',
                'table' => null,
                'fields' => [],
                'join' => [
],
                'filter' => [
],
                'sql_query' => 'SELECT 
    templates.id,
    templates.name,
    templates.short_description,
    template_categories_mapping.template_category_id,
    template_categories.name as template_category_name
FROM 
    templates
LEFT JOIN 
    template_categories_mapping 
    ON template_categories_mapping.template_id = templates.id
LEFT JOIN 
    template_categories 
    ON template_categories.id = template_categories_mapping.template_category_id;',
                'sort_by' => null,
                'limit' => null,
                'group_by' => null,
            ]
];