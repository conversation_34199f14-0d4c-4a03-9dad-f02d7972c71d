<?php

namespace App\Http\Controllers\Auth;

class AuthResponseMessages
{
    public const LOGIN_FAILED = 'Login failed';
    public const VALIDATION_FAILED = 'Validation failed';
    public const REGISTRATION_SUCCESSFUL = 'Registration successful';
    public const REGISTRATION_FAILED = 'Registration failed';
    public const LOGOUT_FAILED = 'Logout failed';
    public const LOGIN_SUCCESSFUL = 'Login successful';
    public const LOGOUT_SUCCESSFUL = 'Logged out successfully';
    public const EMAIL_NOT_FOUND = 'Email not found';
    public const INVALID_PASSWORD = 'Invalid password';
    public const REGISTRATION_ERROR = 'An error occurred during registration';
    public const LOGIN_ERROR = 'An error occurred during login';
    public const LOGOUT_ERROR = 'An error occurred during logout';
    public const LOGGED_IN_USER_SUCCESSFUL = 'Logged in user details';
    public const LOGGED_IN_USER_FAILED = 'Failed to get logged in user details';
    public const LOGGED_IN_USER_ERROR = 'An error occurred while getting logged in user details';
}
