<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class BlockCategory extends Model
{
    protected $table = 'block_categories';

    public function blocks()
    {
        return $this->hasManyThrough(
            Block::class,
            BlockCategoriesMapping::class,
            'category_id', // FK on mapping table
            'id',          // PK on blocks table
            'id',          // PK on categories table
            'block_id'     // FK on mapping table
        )
            ->select([
                'blocks.id',
                'blocks.name',
                'blocks.slug',
                'blocks.description',
                'blocks.image',
                'block_categories_mapping.category_id as laravel_through_key'
            ]);
    }

    public function blocksAttached()
    {
        return $this->belongsToMany(Block::class, 'block_categories_mapping', 'category_id', 'block_id');
    }
}
