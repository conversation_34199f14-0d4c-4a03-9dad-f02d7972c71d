<?php
// /views/demo.php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/PreviewHelper.php';

// Get parameters
$type = $_GET['type'] ?? 'template';
$templateId = $_GET['id'] ?? $_GET['template_id'] ?? null;
$pageSlug = $_GET['page'] ?? 'home';

if (!$templateId) {
    header("HTTP/1.0 400 Bad Request");
    exit("Template ID is required");
}

// Set demo options
$options = [
    'preview_mode' => true,
    'hide_controls' => true,
    'page_slug' => $pageSlug,
    'is_template_page' => false
];

// Render demo
$html = PreviewHelper::renderPreview('template', $templateId, $options);

if (isset($html['error'])) {
    header("HTTP/1.0 404 Not Found");
    exit($html['error']);
}

echo $html;
?>