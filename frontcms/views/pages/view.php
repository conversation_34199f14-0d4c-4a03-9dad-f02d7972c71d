<?php
require_once __DIR__ . '/../../config/config.php';
require_once __DIR__ . '/../../modules/pages/Pages.php';
require_once __DIR__ . '/../../modules/blocks/Blocks.php';

$pages = new Pages();
$blocks = new Blocks();
$error = null;
$success = isset($_GET['success']) ? $_GET['success'] : null;

// Set page title and breadcrumbs
$pageTitle = 'Page Builder';
$breadcrumbs = [
    [
        'label' => 'Pages',
        'url' => BASE_URL . '/views/pages/index.php'
    ],
    [
        'label' => 'Page Builder',
        'url' => '#'
    ]
];

// Get page ID from URL
$pageId = isset($_GET['id']) ? $_GET['id'] : null;
$pageData = null;
$allPages = [];

// Get all pages for the dropdown
try {
    $response = $pages->getList();
    if (isset($response['data']['data']) && is_array($response['data']['data'])) {
        $allPages = $response['data']['data'];
    }
} catch (Exception $e) {
    $error = $e->getMessage();
}

// Get the current page data if ID is provided
if ($pageId) {
    try {
        $response = $pages->getById($pageId);
        if (isset($response['data'])) {
            $pageData = $response['data'];
        } else {
            $error = 'Page not found';
        }
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// Handle form submission for save/publish/preview
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (isset($_POST['save_draft'])) {
            // Save as draft logic
            $data = [
                'status' => 'draft'
            ];
            $pages->update($pageId, $data);
            header("Location: " . BASE_URL . "/pages/view.php?id=" . $pageId . "&success=Page saved as draft");
            exit;
        } 
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}
?>
<?php
// Initialize the variable that will store the Block List HTML output
$globalBlockList = '';

try {
    // Get all blocks
    $response = $blocks->getList();
    
    // Check if the request was successful
    if (isset($response['success']) && $response['success'] === true) {
        // Handle different response structures

        $blocks = [];
        if (isset($response['data']['data']) && is_array($response['data']['data'])) {
            $blocks = $response['data']['data'];
        } elseif (isset($response['data']) && is_array($response['data'])) {
            $blocks = $response['data'];
        }
        
        // Start capturing the HTML in the variable
        ob_start();
        
        // If no blocks found, show a message
        if (empty($blocks)) {
            echo '<div class="text-center py-4">';
            echo '<p class="text-gray-500">No blocks found</p>';
            echo '</div>';
        } else {
            // Display each block
            foreach ($blocks as $block) {
                // Check if required fields exist
                if (!isset($block['id']) || !isset($block['name'])) {
                    continue;
                }
                
                $blockType = isset($block['slug']) ? $block['slug'] : 'default-block';
                $blockName = isset($block['name']) ? $block['name'] : 'Unnamed Block';
                $blockImage = isset($block['image']) ? BASE_URL . $block['image'] : 'https://placehold.co/600x300/e5e7eb/9ca3af?text=' . urlencode($blockName);
                $blockDesc = isset($block['description']) ? $block['description'] : '';
                ?>
                <div class="block-item cursor-move bg-white border border-gray-200 rounded-lg mb-2 hover:border-blue-500 hover:shadow-sm overflow-hidden" 
                draggable="true" 
                data-block-type="<?php echo htmlspecialchars($blockType); ?>" 
                data-block-id="<?php echo htmlspecialchars($block['id']); ?>">
                    <div class="flex items-start relative">
                        <img src="<?php echo htmlspecialchars($blockImage); ?>" alt="Block Image">
                        <p class="hidden"><?php echo htmlspecialchars($blockDesc); ?></p>
                    </div>
                    <h3 class="text-sm font-medium text-gray-900 w-full px-3 py-2 bg-gradient-to-t from-[#f7f7f7] to-white border-t border-gray-100"><?php echo htmlspecialchars($blockName); ?></h3>
                </div>
                <?php
            }
        }
        
        // Store the captured HTML in the variable
        $globalBlockList = ob_get_clean();
        
    } else {
        // If the API request failed, show an error message
        ob_start();
        echo '<div class="text-center py-4 mb-4">';
        echo '<p class="text-red-500">Failed to load blocks from API.</p>';
        if (defined('DEBUG') && DEBUG && isset($response['message'])) {
            echo '<p class="text-xs text-gray-500 mt-1">' . htmlspecialchars($response['message']) . '</p>';
        }
        echo '</div>';
        $globalBlockList = ob_get_clean();
    }
} catch (Exception $e) {
    // If an exception occurred, show an error message
    ob_start();
    echo '<div class="text-center py-4">';
    echo '<p class="text-red-500">Error: ' . htmlspecialchars($e->getMessage()) . '</p>';
    echo '</div>';
    $globalBlockList = ob_get_clean();
}
?>
<?php
// Start output buffering
ob_start();
?>

<div class="container-fluid">
    <?php if ($error): ?>
    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
        <p><?php echo htmlspecialchars($error); ?></p>
    </div>
    <?php endif; ?>

    <!-- Success message will be handled by JavaScript -->

    <!-- Page Builder Header -->
    <div class="border-b border-gray-200 p-3">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-2">
                <label for="pageSelector" class="text-xs font-medium text-gray-700">Page:</label>
                <select id="pageSelector" class="text-xs bg-white border border-gray-300 text-gray-900 rounded focus:ring-blue-500 focus:border-blue-500 p-1.5">
                    <option value="">Select a page</option>
                    <?php foreach ($allPages as $page): ?>
                        <option value="<?php echo htmlspecialchars($page['id']); ?>" <?php echo ($pageId == $page['id']) ? 'selected' : ''; ?>>
                            <?php echo htmlspecialchars($page['name']); ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            
            <div class="flex items-center space-x-2">
                <div class="flex items-center space-x-2 bg-white border border-gray-300 rounded-lg p-0.5">
                    <button id="mobileViewBtn" class="p-0.5 rounded hover:bg-gray-100" title="Mobile View">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z" />
                        </svg>
                    </button>
                    <button id="tabletViewBtn" class="p-0.5 rounded hover:bg-gray-100" title="Tablet View">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                        </svg>
                    </button>
                    <button id="desktopViewBtn" class="p-0.5 rounded bg-blue-100" title="Desktop View">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                        </svg>
                    </button>
                </div>
            </div>
            
            <div class="flex items-center space-x-2">
                <button id="previewBtn" class="text-xs text-white bg-[#0C5BE2] px-2 py-1.5 rounded hover:bg-blue-700 flex items-center gap-1">
                    <svg class="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                    </svg>
                    Preview
                </button>
                
                <button id="publishBtn" class="text-xs text-white bg-emerald-500 px-2 py-1.5 rounded hover:bg-emerald-600 flex items-center gap-1">
                    <svg class="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    Publish
                </button>
            </div>
        </div>
    </div>

    <!-- Main Content Area -->
    <div class="flex h-[calc(100vh-66px)]">
        <!-- Canvas Area -->
        <div class="w-[calc(100%-280px)] ml-auto relative">
            <?php if ($pageId): ?>
                <iframe id="pageCanvas" src="<?php echo BASE_URL; ?>/views/preview.php?type=page&id=<?php echo $pageId; ?>" class="w-full h-full border-0 transition-all duration-300"></iframe>
            <?php else: ?>
                <div class="flex items-center justify-center h-full">
                    <div class="text-center">
                        <div class="inline-flex items-center justify-center w-16 h-16 rounded-full bg-gray-200 mb-4">
                            <svg class="w-8 h-8 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                        </div>
                        <p class="text-lg font-medium text-gray-900">Select a page to start building</p>
                    </div>
                </div>
            <?php endif; ?>
        </div>
        
        <!-- Blocks Panel -->
        <div class="h-full w-72 mr-4 fixed left-[66px] bg-white border-r border-gray-200 flex flex-col z-10">
            <!-- Flowbite Tabs -->
            <div class="border-b border-gray-200 p-2">
                <ul class="flex flex-wrap -mb-px text-xs font-medium text-center" id="blocksTabs" data-tabs-toggle="#blocksTabContent" role="tablist">
                    <li class="mr-2" role="presentation">
                        <button class="inline-flex items-center gap-1.5 px-3 py-1.5 text-xs font-medium rounded-full bg-blue-50 text-blue-600 custom-tabs" id="global-blocks-tab" data-tabs-target="#global-blocks" type="button" role="tab" aria-controls="global-blocks" aria-selected="true">Global Blocks</button>
                    </li>
                    <li class="mr-2" role="presentation">
                        <button class="inline-flex items-center gap-1.5 px-3 py-1.5 text-xs font-medium rounded-full hover:bg-gray-100 text-gray-500 custom-tabs" id="settings-tab" data-tabs-target="#settings" type="button" role="tab" aria-controls="settings" aria-selected="false">Settings</button>
                    </li>
                </ul>
            </div>
            
            <div id="blocksTabContent" class="flex-1 flex flex-col">
                <!-- Global Blocks Tab -->
                <div class="hidden flex-1 flex flex-col custom-tabpanel" id="global-blocks" role="tabpanel" aria-labelledby="global-blocks-tab">
                    <div class="p-3">
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                                <svg class="w-4 h-4 text-gray-500" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 20">
                                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z"/>
                                </svg>
                            </div>
                            <input type="text" id="blockSearch" class="block w-full p-2 pl-10 text-sm text-gray-900 border border-gray-300 rounded-lg bg-gray-50 focus:ring-blue-500 focus:border-blue-500" placeholder="Search blocks...">
                        </div>
                    </div>
                    
                    <div class="flex-1 pl-3">
                        <!-- Block Types -->
                        <div id="blocksList" class="mb-4 h-[calc(100vh-183px)] overflow-auto pr-3">
                            <?php echo $globalBlockList; ?>
                        </div>
                    </div>
                </div>
                
                <!-- Settings Tab -->
                <div class="hidden flex-1 flex flex-col custom-tabpanel" id="settings" role="tabpanel" aria-labelledby="settings-tab">
                    <div class="flex-1 pl-3">
                        <div class="mb-4 h-[calc(100vh-121px)] overflow-auto pr-3 pt-3">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Page Settings</h3>
                            
                            <div class="space-y-4">
                                <!-- Page Status -->
                                <div>
                                    <label for="pageStatus" class="block text-sm font-medium text-gray-700 mb-1">Page Status</label>
                                    <select id="pageStatus" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5">
                                        <option value="draft" <?php echo ($pageData && $pageData['status'] == 'draft') ? 'selected' : ''; ?>>Draft</option>
                                        <option value="published" <?php echo ($pageData && $pageData['status'] == 'published') ? 'selected' : ''; ?>>Published</option>
                                    </select>
                                </div>
                                
                                <!-- Page Template -->
                                <div>
                                    <label for="pageTemplate" class="block text-sm font-medium text-gray-700 mb-1">Page Template</label>
                                    <select id="pageTemplate" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5">
                                        <option value="">Default Template</option>
                                        <option value="landing" <?php echo ($pageData && isset($pageData['template_id']) && $pageData['template_id'] == 'landing') ? 'selected' : ''; ?>>Landing Page</option>
                                        <option value="blog" <?php echo ($pageData && isset($pageData['template_id']) && $pageData['template_id'] == 'blog') ? 'selected' : ''; ?>>Blog Page</option>
                                    </select>
                                </div>
                                
                                <!-- SEO Settings -->
                                <div class="border-t border-gray-200 pt-4 mt-4">
                                    <h4 class="text-md font-medium text-gray-900 mb-3">SEO Settings</h4>
                                    
                                    <div class="space-y-3">
                                        <div>
                                            <label for="metaTitle" class="block text-sm font-medium text-gray-700 mb-1">Meta Title</label>
                                            <input type="text" id="metaTitle" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5" placeholder="Meta title" value="<?php echo ($pageData && isset($pageData['meta_title'])) ? htmlspecialchars($pageData['meta_title']) : ''; ?>">
                                        </div>
                                        
                                        <div>
                                            <label for="metaDescription" class="block text-sm font-medium text-gray-700 mb-1">Meta Description</label>
                                            <textarea id="metaDescription" rows="3" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5" placeholder="Meta description"><?php echo ($pageData && isset($pageData['meta_description'])) ? htmlspecialchars($pageData['meta_description']) : ''; ?></textarea>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Save Button -->
                                <div class="pt-4">
                                    <button type="button" id="saveSettings" class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center">Save Settings</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// FIXED: Track the most recently dropped block to prevent duplicates
let lastDroppedBlockId = null;
let lastDropTime = 0;
let processingBlocks = {};

// FIXED: Page selector change handler
document.getElementById('pageSelector').addEventListener('change', function() {
    const pageId = this.value;
    if (pageId) {
        const iframe = document.getElementById('pageCanvas');
        if (iframe) {
            iframe.src = `<?php echo BASE_URL; ?>/views/preview.php?type=page&id=${pageId}`;
        }
        // Also update URL for navigation
        window.history.replaceState({}, '', `<?php echo BASE_URL; ?>/pages/view?id=${pageId}`);
    } else {
        window.location.href = `<?php echo BASE_URL; ?>/pages/view.php`;
    }
});

// Initialize Flowbite tabs
document.addEventListener('DOMContentLoaded', function() {
    // Make sure the first tab is active by default
    const tabsElement = document.getElementById('blocksTabs');
    if (tabsElement) {
        const activeTab = tabsElement.querySelector('[aria-selected="true"]');
        if (activeTab) {
            const tabTarget = document.getElementById(activeTab.getAttribute('data-tabs-target').replace('#', ''));
            if (tabTarget) {
                tabTarget.classList.remove('hidden');
            }
        }
    }
    
    // Setup drag and drop
    setupDragAndDrop();
});

// FIXED: Setup drag and drop functionality for blocks
function setupDragAndDrop() {
    const blockItems = document.querySelectorAll('.block-item');
    const pageCanvas = document.querySelector('iframe#pageCanvas');
    
    if (!pageCanvas) {
        console.warn('Page canvas iframe not found');
        return;
    }
    
    // Add visual feedback for drag operations
    blockItems.forEach(item => {
        item.setAttribute('draggable', 'true');
        
        item.addEventListener('dragstart', function(e) {
            const blockId = this.getAttribute('data-block-id');
            const blockType = this.getAttribute('data-block-type');
            
            if (!blockId) {
                console.error('Block is missing data-block-id attribute:', this);
                e.preventDefault();
                return;
            }
            
            // Check if this block is already being processed
            if (processingBlocks[blockId]) {
                console.warn('Block is already being processed, cancelling drag');
                e.preventDefault();
                return;
            }
            
            // Add visual feedback
            this.style.opacity = '0.5';
            this.classList.add('dragging');
            
            // FIXED: Set the drag data with proper format
            const dragData = {
                blockId: blockId,
                blockType: blockType,
                id: blockId,  // Add id field as well for compatibility
                name: this.querySelector('h3')?.textContent || 'Unknown Block'
            };
            
            console.log('Starting drag with data:', dragData);
            
            // Set data in multiple formats for compatibility
            e.dataTransfer.setData('application/json', JSON.stringify(dragData));
            e.dataTransfer.setData('text/plain', JSON.stringify(dragData));
            e.dataTransfer.effectAllowed = 'copy';
            
            // Show loading cursor
            document.body.style.cursor = 'grabbing';
            
            // FIXED: Notify iframe about drag start with retry mechanism
            const notifyIframe = () => {
                try {
                    if (pageCanvas.contentWindow) {
                        pageCanvas.contentWindow.postMessage({
                            type: 'dragStart',
                            blockData: dragData
                        }, '*');
                        console.log('Sent dragStart message to iframe');
                    } else {
                        throw new Error('Iframe contentWindow not available');
                    }
                } catch (error) {
                    console.warn('Could not send dragStart message to iframe:', error);
                    // Retry after a short delay
                    setTimeout(notifyIframe, 100);
                }
            };
            notifyIframe();
        });
        
        item.addEventListener('dragend', function() {
            console.log('Drag ended');
            
            // Reset visual feedback
            this.style.opacity = '1';
            this.classList.remove('dragging');
            document.body.style.cursor = 'default';
            
            // FIXED: Notify iframe about drag end
            try {
                if (pageCanvas.contentWindow) {
                    pageCanvas.contentWindow.postMessage({
                        type: 'dragEnd'
                    }, '*');
                    console.log('Sent dragEnd message to iframe');
                }
            } catch (error) {
                console.warn('Could not send dragEnd message to iframe:', error);
            }
        });
        
        // Add hover effects
        item.addEventListener('mouseenter', function() {
            this.style.transform = 'scale(1.02)';
            this.style.transition = 'transform 0.2s ease';
        });
        
        item.addEventListener('mouseleave', function() {
            this.style.transform = 'scale(1)';
        });
    });
    
    // FIXED: Listen for messages from iframe with enhanced error handling
    window.addEventListener('message', function(event) {
        // Validate message origin and structure
        if (!event.data || typeof event.data !== 'object') {
            return;
        }
        
        console.log('Parent received message:', event.data);
        
        if (event.data.type === 'blockDropped' && event.data.blockId) {
            const blockId = event.data.blockId;
            const now = Date.now();
            
            console.log('Block dropped message received:', event.data);
            
            // Prevent duplicate handling of the same block in quick succession
            if (blockId === lastDroppedBlockId && (now - lastDropTime) < 3000) {
                console.log('Ignoring duplicate drop event for block:', blockId);
                return;
            }
            
            // Record this drop event
            lastDroppedBlockId = blockId;
            lastDropTime = now;
            processingBlocks[blockId] = true;
            
            // Show success feedback
            showDropSuccessMessage(blockId);
            
            // Set a timeout to clear this block from processing after 5 seconds
            setTimeout(() => {
                if (processingBlocks[blockId]) {
                    delete processingBlocks[blockId];
                    console.log('Cleared processing flag for block:', blockId);
                }
            }, 5000);
            
            // The iframe handles block creation, so we just need to acknowledge
            console.log('Block drop acknowledged:', blockId);
        }
        
        // Handle iframe ready message
        if (event.data.type === 'iframeReady') {
            console.log('Iframe is ready for communication');
        }
        
        // Handle errors from iframe
        if (event.data.type === 'error') {
            console.error('Error from iframe:', event.data.message);
            showErrorMessage(event.data.message);
        }
    });
    
    // FIXED: Prevent default drag behavior on window
    window.addEventListener('dragover', function(e) {
        e.preventDefault();
        e.dataTransfer.dropEffect = 'copy';
    });
    
    window.addEventListener('drop', function(e) {
        e.preventDefault();
        console.log('Drop event on parent window - preventing default');
    });
}

// Helper function to show drop success message
function showDropSuccessMessage(blockId) {
    const message = document.createElement('div');
    message.className = 'fixed top-4 right-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded z-50';
    message.innerHTML = `
        <div class="flex items-center">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
            Block added successfully!
        </div>
    `;
    document.body.appendChild(message);
    
    setTimeout(() => {
        message.remove();
    }, 3000);
}

// Helper function to show error message
function showErrorMessage(errorMsg) {
    const message = document.createElement('div');
    message.className = 'fixed top-4 right-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded z-50';
    message.innerHTML = `
        <div class="flex items-center">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
            Error: ${errorMsg}
        </div>
    `;
    document.body.appendChild(message);
    
    setTimeout(() => {
        message.remove();
    }, 5000);
}

// FIXED: Publish button handler
document.getElementById('publishBtn').addEventListener('click', function(event) {
    event.preventDefault();
    const pageId = document.getElementById('pageSelector').value;
    if (!pageId) {
        alert('Please select a page first');
        return;
    }
    
    // Send publish request to server
    fetch('<?php echo BASE_URL; ?>/modules/pages/publish_page.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            page_id: pageId
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Page published successfully!');
        } else {
            alert('Failed to publish page: ' + (data.message || 'Unknown error'));
        }
    })
    .catch(error => {
        console.error('Error publishing page:', error);
        alert('Failed to publish page. Please try again later.');
    });
});

// Viewport controls
const pageCanvas = document.getElementById('pageCanvas');
const mobileViewBtn = document.getElementById('mobileViewBtn');
const tabletViewBtn = document.getElementById('tabletViewBtn');
const desktopViewBtn = document.getElementById('desktopViewBtn');

mobileViewBtn.addEventListener('click', function() {
    pageCanvas.style.width = '375px';
    pageCanvas.style.margin = '0 auto';
    
    // Update active state
    mobileViewBtn.classList.add('bg-blue-100');
    tabletViewBtn.classList.remove('bg-blue-100');
    desktopViewBtn.classList.remove('bg-blue-100');
});

tabletViewBtn.addEventListener('click', function() {
    pageCanvas.style.width = '768px';
    pageCanvas.style.margin = '0 auto';
    
    // Update active state
    mobileViewBtn.classList.remove('bg-blue-100');
    tabletViewBtn.classList.add('bg-blue-100');
    desktopViewBtn.classList.remove('bg-blue-100');
});

desktopViewBtn.addEventListener('click', function() {
    pageCanvas.style.width = '100%';
    pageCanvas.style.margin = '0';
    
    // Update active state
    mobileViewBtn.classList.remove('bg-blue-100');
    tabletViewBtn.classList.remove('bg-blue-100');
    desktopViewBtn.classList.add('bg-blue-100');
});

// FIXED: Preview button handler
document.getElementById('previewBtn').addEventListener('click', function() {
    const pageId = document.getElementById('pageSelector').value;
    if (pageId) {
        window.open(`<?php echo BASE_URL; ?>/views/preview.php?type=page&id=${pageId}&preview=true`, '_blank');
    } else {
        alert('Please select a page first');
    }
});

// Save settings button handler
document.getElementById('saveSettings').addEventListener('click', function() {
    const pageId = document.getElementById('pageSelector').value;
    if (!pageId) {
        alert('Please select a page first');
        return;
    }
    
    const status = document.getElementById('pageStatus').value;
    const template = document.getElementById('pageTemplate').value;
    const metaTitle = document.getElementById('metaTitle').value;
    const metaDescription = document.getElementById('metaDescription').value;
    
    // Here you would typically send an API request to save the settings
    alert(`Settings would be saved for page ID: ${pageId}`);
});

// Block search functionality
document.getElementById('blockSearch').addEventListener('input', function() {
    const searchTerm = this.value.toLowerCase();
    const blockItems = document.querySelectorAll('.block-item');
    
    blockItems.forEach(item => {
        const blockName = item.querySelector('h3').textContent.toLowerCase();
        const blockDesc = item.querySelector('p').textContent.toLowerCase();
        
        if (blockName.includes(searchTerm) || blockDesc.includes(searchTerm)) {
            item.style.display = 'block';
        } else {
            item.style.display = 'none';
        }
    });
});

// FIXED: Wait for iframe to load before setting up drag and drop
window.addEventListener('load', function() {
    const iframe = document.getElementById('pageCanvas');
    if (iframe) {
        iframe.addEventListener('load', function() {
            console.log('Iframe loaded, setting up enhanced communication...');
            
            // Give iframe time to initialize
            setTimeout(() => {
                // Test communication
                try {
                    iframe.contentWindow.postMessage({
                        type: 'test',
                        message: 'Parent to iframe communication test'
                    }, '*');
                } catch (error) {
                    console.warn('Initial iframe communication test failed:', error);
                }
            }, 500);
        });
    }
});
</script>

<?php
$content = ob_get_clean();
require_once __DIR__ . '/../layouts/main.php';
?>