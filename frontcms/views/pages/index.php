<?php
require_once __DIR__ . '/../../config/config.php';
require_once __DIR__ . '/../../modules/pages/Pages.php';
require_once __DIR__ . '/../../modules/templates/Templates.php';
require_once __DIR__ . '/../../includes/TableComponent.php';
require_once __DIR__ . '/../../includes/TableHelper.php';

// Get sorting parameter from URL (in format 'sort=column' or 'sort=-column')
$sort = isset($_GET['sort']) ? $_GET['sort'] : 'id';
$direction = 'asc';

// Check if sort has a negative prefix (for descending)
if (substr($sort, 0, 1) === '-') {
    $realSort = substr($sort, 1); // Remove the minus sign
    $direction = 'desc';
} else {
    $realSort = $sort;
}

// Get limit from URL parameter
$items_per_page = isset($_GET['limit']) ? (int)$_GET['limit'] : 10;

// Validate sort parameter 
$allowedSortColumns = ['id', 'name', 'slug', 'template_id', 'status', 'created_at', 'updated_at'];
if (!in_array($realSort, $allowedSortColumns)) {
    $realSort = 'id';
    $sort = ($direction === 'desc') ? '-id' : 'id';
}

$pages = new Pages();
$templates = new Templates();

// Handle form submission for delete
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // Check if it's a delete operation
        if (isset($_POST['delete']) && isset($_POST['id'])) {
            $id = $_POST['id'];
            $result = $pages->delete($id);
            if ($result['success']) {
                header("Location: " . BASE_URL . "/pages?success=Page deleted successfully");
                exit;
            } else {
                $error = $result['message'] ?? 'Failed to delete page';
            }
        }
        
        // Handle create/edit operations
        if (isset($_POST['save_page'])) {
            $data = [
                'name' => $_POST['name'],
                'slug' => $_POST['slug'],
                'description' => !empty($_POST['description']) ? $_POST['description'] : ''
            ];
            
            // Set status based on form selection
            $data['status'] = $_POST['status'] ?? 'inactive';
            
            // Only add template_id if it's not empty
            if (!empty($_POST['template_id'])) {
                $data['template_id'] = $_POST['template_id'];
            }
            
            if (!empty($_POST['id'])) {
                // Edit operation
                $id = $_POST['id'];
                
                // Debug prepared data
                if (isset($_GET['debug']) && $_GET['debug'] == 1) {
                    echo "<div class='p-4 mb-4 text-sm text-gray-800 rounded-lg bg-gray-100'>";
                    echo "<h3>Data being sent to update API:</h3>";
                    echo "<pre>";
                    print_r($data);
                    echo "</pre>";
                    echo "</div>";
                }
                
                $result = $pages->update($id, $data);
                if ($result['success']) {
                    header("Location: " . BASE_URL . "/pages?success=Page updated successfully");
                    exit;
                } else {
                    $error = $result['message'] ?? 'Failed to update page';
                }
            } else {
                // Create operation
                
                // Debug prepared data
                if (isset($_GET['debug']) && $_GET['debug'] == 1) {
                    echo "<div class='p-4 mb-4 text-sm text-gray-800 rounded-lg bg-gray-100'>";
                    echo "<h3>Data being sent to create API:</h3>";
                    echo "<pre>";
                    print_r($data);
                    echo "</pre>";
                    echo "</div>";
                }
                
                $result = $pages->create($data);
                if ($result['success']) {
                    header("Location: " . BASE_URL . "/pages?success=Page created successfully");
                    exit;
                } else {
                    $error = $result['message'] ?? 'Failed to create page';
                }
            }
        }
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// Get pages with sorting and pagination
$result = $pages->getList([
    'page' => isset($_GET['page']) ? (int)$_GET['page'] : 1,
    'sort' => $realSort,
    'direction' => $direction,
    'limit' => $items_per_page
]);

$pagesData = $result['success'] ? ($result['data']['data'] ?? []) : [];

// Simple pagination configuration using the new structure
$pagination = $result['success'] ? $result['data'] : [
    'current_page' => 1,
    'per_page' => 10,
    'from' => null,
    'to' => null,
    'total' => 0,
    'next_page_url' => null,
    'prev_page_url' => null,
    'first_page_url' => null,
    'last_page_url' => null
];

// Get list of templates for dropdown
$templatesList = [];
try {
    $templatesResult = $templates->getList(['limit' => 100]);
    if ($templatesResult['success']) {
        $templatesList = $templatesResult['data']['data'] ?? [];
    }
} catch (Exception $e) {
    // Handle error silently
}

// Set page title and breadcrumbs
$pageTitle = 'Pages';
$breadcrumbs = [
    [
        'label' => 'Pages',
        'url' => BASE_URL . '/pages'
    ]
];

// Start output buffering
ob_start();

// Define table columns
$columns = [
    'name' => [
        'label' => 'Name',
        'sortable' => true
    ],
    'slug' => [
        'label' => 'Slug',
        'sortable' => true
    ],
    'template_id' => [
        'label' => 'Template',
        'sortable' => true,
        'formatter' => function($value) use ($templatesList) {
            // Display template name instead of just ID if available
            if (empty($value)) return '-';
            foreach ($templatesList as $template) {
                if ($template['id'] == $value) {
                    return htmlspecialchars($template['name']);
                }
            }
            return htmlspecialchars($value);
        }
    ],
    'status' => [
        'label' => 'Status',
        'sortable' => true,
        'formatter' => function($value) {
            // For display, we use active/inactive
            $status = ($value === 'active') ? 'Active' : 'Inactive';
            
            return formatStatusBadge($status, [
                'active' => [
                    'bg' => 'bg-green-100',
                    'text' => 'text-green-800'
                ],
                'inactive' => [
                    'bg' => 'bg-red-100',
                    'text' => 'text-red-800'
                ]
            ]);
        }
    ],
    'description' => [
        'label' => 'Description',
        'sortable' => false,
        'formatter' => function($value) {
            return !empty($value) ? htmlspecialchars(substr($value, 0, 50)) . (strlen($value) > 50 ? '...' : '') : '-';
        }
    ],
    'created_at' => [
        'label' => 'Created At',
        'sortable' => true,
        'formatter' => function($value) {
            return formatDate($value);
        }
    ],
    'updated_at' => [
        'label' => 'Updated At',
        'sortable' => true,
        'formatter' => function($value) {
            return formatDate($value);
        }
    ]
];

// Define action buttons
$actionButtons = [
    [
        'label' => 'Create New Page',
        'icon' => 'plus',
        'attributes' => [
            'data-modal-target' => 'createPageModal',
            'data-modal-toggle' => 'createPageModal'
        ]
    ]
];

// Row actions
$rowActions = [
    [
        'icon' => '<path d="M21.174 6.812a1 1 0 0 0-3.986-3.987L3.842 16.174a2 2 0 0 0-.5.83l-1.321 4.352a.5.5 0 0 0 .623.622l4.353-1.32a2 2 0 0 0 .83-.497z"/><path d="m15 5 4 4"/>',
        'tooltip' => 'Edit',
        'type' => 'button',
        'hoverTextColor' => 'hover:text-blue-700 edit-page',
        'attributes' => [
            'data-modal-target' => 'editPageModal',
            'data-modal-toggle' => 'editPageModal',
            'data-id' => function($row) { return $row['id'] ?? ''; },
            'data-name' => function($row) { return $row['name'] ?? ''; },
            'data-slug' => function($row) { return $row['slug'] ?? ''; },
            'data-status' => function($row) { return $row['status'] ?? 'inactive'; },
            'data-template-id' => function($row) { return $row['template_id'] ?? ''; },
            'data-description' => function($row) { return $row['description'] ?? ''; }
        ]
    ],
    [
        'icon' => '<path d="M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0"/><circle cx="12" cy="12" r="3"/>',
        'tooltip' => 'View',
        'type' => 'link',
        'hoverTextColor' => 'hover:text-green-700',     
        'urlFormatter' => function($row) { return BASE_URL . '/pages/view?id=' . ($row['id'] ?? ''); }
    ],
    [
        'icon' => '<path d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/><path d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>',
        'tooltip' => 'Preview',
        'type' => 'link',
        'hoverTextColor' => 'hover:text-purple-700',
        'urlFormatter' => function($row) { return BASE_URL . '/views/preview.php?type=page&id=' . ($row['id'] ?? '') . '&preview=true'; }
    ],
    [
        'icon' => '<path d="M3 6h18"/><path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"/><path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"/>',
        'tooltip' => 'Delete',
        'type' => 'button',
        'hoverTextColor' => 'hover:text-red-700 delete-page',
        'attributes' => [
            'data-modal-target' => 'deletePageModal',
            'data-modal-toggle' => 'deletePageModal',
            'data-id' => function($row) { return $row['id'] ?? ''; }
        ]
    ]
];

// Initialize the table component with pagination - TABLE VIEW ONLY!
$table = new TableComponent($pagesData, $columns, $pagination);
$table->setTableId('pagesTable')
      ->setTitleBreadcrumb('Pages', [
        ['label' => 'Home', 'url' => BASE_URL . '/dashboard'],
        ['label' => 'Pages']
      ])
      ->setSearchConfig(true, 'searchPages')
      ->setActionButtons($actionButtons)
      ->setRowActions($rowActions)
      ->setGridConfig(['enabled' => false]);

// Display the table
echo $table->render();

// Include the modals (rest of your modal code remains the same)
?>
<!-- Create Page Modal -->
<div id="createPageModal" tabindex="-1" aria-hidden="true" class="fixed top-0 left-0 right-0 z-50 hidden w-full p-4 overflow-x-hidden overflow-y-auto md:inset-0 h-[calc(100%-1rem)] max-h-full bg-gray-900 bg-opacity-50">
    <div class="relative w-full max-w-2xl max-h-full mx-auto">
        <!-- Modal content -->
        <div class="relative bg-white rounded-lg shadow-xl">
            <!-- Modal header -->
            <div class="flex items-start justify-between p-4 border-b rounded-t">
                <div class="flex items-center">
                    <div class="flex items-center justify-center h-10 w-10 rounded-full bg-blue-50 text-blue-500 mr-3">
                        <svg class="h-5 w-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z"/>
                            <path d="M14 2v4a2 2 0 0 0 2 2h4"/>
                        </svg>
                    </div>
                    <div>
                        <h3 class="text-lg font-medium text-gray-900">Create New Page</h3>
                        <p class="text-sm text-gray-500">Create a new page with the details below.</p>
                    </div>
                </div>
                <button type="button" class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm p-1.5 ml-auto inline-flex items-center" data-modal-hide="createPageModal">
                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                    </svg>
                </button>
            </div>
            <!-- Modal body -->
            <form id="createPageForm" method="POST" action="<?php echo BASE_URL; ?>/pages">
                <div class="p-6 pb-0">
                    <input type="hidden" name="save_page" value="1">
                    <div class="grid grid-cols-2 gap-4">
                        <div class="mb-4">
                            <label for="create_name" class="block text-sm font-bold text-gray-700 mb-2">Name</label>
                            <input type="text" name="name" id="create_name" required class="block w-full px-4 py-2.5 text-sm text-gray-900 bg-white border border-gray-300 rounded-lg focus:border-blue-500 focus:ring-1 focus:ring-blue-500">
                        </div>
                        <div class="mb-4">
                            <label for="create_slug" class="block text-sm font-bold text-gray-700 mb-2">Slug</label>
                            <input type="text" name="slug" id="create_slug" required class="block w-full px-4 py-2.5 text-sm text-gray-900 bg-white border border-gray-300 rounded-lg focus:border-blue-500 focus:ring-1 focus:ring-blue-500">
                        </div>
                    </div>
                    <div class="grid grid-cols-2 gap-4">
                        <div class="mb-4">
                            <label for="create_status" class="block text-sm font-bold text-gray-700 mb-2">Status</label>
                            <select name="status" id="create_status" required class="block w-full px-4 py-2.5 text-sm text-gray-900 bg-white border border-gray-300 rounded-lg focus:border-blue-500 focus:ring-1 focus:ring-blue-500">
                                <option value="active">Published</option>
                                <option value="inactive" selected>Draft</option>
                            </select>
                        </div>
                        <div class="mb-4">
                            <label for="create_template_id" class="block text-sm font-bold text-gray-700 mb-2">Template (Optional)</label>
                            <select name="template_id" id="create_template_id" class="block w-full px-4 py-2.5 text-sm text-gray-900 bg-white border border-gray-300 rounded-lg focus:border-blue-500 focus:ring-1 focus:ring-blue-500">
                                <option value="">Select Template</option>
                                <?php foreach ($templatesList as $template): ?>
                                    <option value="<?php echo $template['id']; ?>"><?php echo htmlspecialchars($template['name']); ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                    <div class="mb-4 w-full">
                        <label for="create_description" class="block text-sm font-bold text-gray-700 mb-2">Description</label>
                        <textarea name="description" id="create_description" rows="3" class="block w-full px-4 py-2.5 text-sm text-gray-900 bg-white border border-gray-300 rounded-lg focus:border-blue-500 focus:ring-1 focus:ring-blue-500" placeholder="Optional page description..."></textarea>
                    </div>
                </div>
                
                <!-- Modal footer -->
                <div class="flex items-center pb-7 px-6 justify-end space-x-2 rounded-b">
                    <button type="submit" class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2 text-center">Save</button>
                    <button type="button" class="text-gray-500 bg-white hover:bg-gray-100 focus:ring-4 focus:outline-none focus:ring-gray-200 rounded-lg border border-gray-200 text-sm font-medium px-5 py-2 hover:text-gray-900 focus:z-10" data-modal-hide="createPageModal">Cancel</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Page Modal -->
<div id="editPageModal" tabindex="-1" aria-hidden="true" class="fixed top-0 left-0 right-0 z-50 hidden w-full p-4 overflow-x-hidden overflow-y-auto md:inset-0 h-[calc(100%-1rem)] max-h-full bg-gray-900 bg-opacity-50">
    <div class="relative w-full max-w-2xl max-h-full mx-auto">
        <!-- Modal content -->
        <div class="relative bg-white rounded-lg shadow-xl">
            <!-- Modal header -->
            <div class="flex items-start justify-between p-4 border-b rounded-t">
                <div class="flex items-center">
                    <div class="flex items-center justify-center h-10 w-10 rounded-full bg-blue-50 text-blue-500 mr-3">
                        <svg class="h-5 w-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z"/>
                            <path d="M14 2v4a2 2 0 0 0 2 2h4"/>
                        </svg>
                    </div>
                    <div>
                        <h3 class="text-lg font-medium text-gray-900">Edit Page</h3>
                        <p class="text-sm text-gray-500">Edit the details of the selected page.</p>
                    </div>
                </div>
                <button type="button" class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm p-1.5 ml-auto inline-flex items-center" data-modal-hide="editPageModal">
                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                    </svg>
                </button>
            </div>
            <!-- Modal body -->
            <form id="editPageForm" method="POST" action="<?php echo BASE_URL; ?>/pages">
                <div class="p-6 pb-0">
                    <input type="hidden" name="save_page" value="1">
                    <input type="hidden" name="id" id="editPageId">
                    <div class="grid grid-cols-2 gap-4">
                        <div class="mb-4">
                            <label for="editPageName" class="block text-sm font-bold text-gray-700 mb-2">Name</label>
                            <input type="text" name="name" id="editPageName" required class="block w-full px-4 py-2.5 text-sm text-gray-900 bg-white border border-gray-300 rounded-lg focus:border-blue-500 focus:ring-1 focus:ring-blue-500">
                        </div>
                        <div class="mb-4">
                            <label for="editPageSlug" class="block text-sm font-bold text-gray-700 mb-2">Slug</label>
                            <input type="text" name="slug" id="editPageSlug" required class="block w-full px-4 py-2.5 text-sm text-gray-900 bg-white border border-gray-300 rounded-lg focus:border-blue-500 focus:ring-1 focus:ring-blue-500">
                        </div>
                    </div>
                    <div class="grid grid-cols-2 gap-4">
                        <div class="mb-4">
                            <label for="editPageStatus" class="block text-sm font-bold text-gray-700 mb-2">Status</label>
                            <select name="status" id="editPageStatus" required class="block w-full px-4 py-2.5 text-sm text-gray-900 bg-white border border-gray-300 rounded-lg focus:border-blue-500 focus:ring-1 focus:ring-blue-500">
                                <option value="active">Published</option>
                                <option value="inactive">Draft</option>
                            </select>
                        </div>
                        <div class="mb-4">
                            <label for="editPageTemplateId" class="block text-sm font-bold text-gray-700 mb-2">Template (Optional)</label>
                            <select name="template_id" id="editPageTemplateId" class="block w-full px-4 py-2.5 text-sm text-gray-900 bg-white border border-gray-300 rounded-lg focus:border-blue-500 focus:ring-1 focus:ring-blue-500">
                                <option value="">Select Template</option>
                                <?php foreach ($templatesList as $template): ?>
                                    <option value="<?php echo $template['id']; ?>"><?php echo htmlspecialchars($template['name']); ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                    <div class="mb-4">
                        <label for="editPageDescription" class="block text-sm font-bold text-gray-700 mb-2">Description</label>
                        <textarea name="description" id="editPageDescription" rows="3" class="block w-full px-4 py-2.5 text-sm text-gray-900 bg-white border border-gray-300 rounded-lg focus:border-blue-500 focus:ring-1 focus:ring-blue-500" placeholder="Optional page description..."></textarea>
                    </div>
                </div>
                <!-- Modal footer -->
                <div class="flex items-center pb-7 px-6 justify-end space-x-2 rounded-b">
                    <button type="submit" class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2 text-center">Save</button>
                    <button type="button" class="text-gray-500 bg-white hover:bg-gray-100 focus:ring-4 focus:outline-none focus:ring-gray-200 rounded-lg border border-gray-200 text-sm font-medium px-5 py-2 hover:text-gray-900 focus:z-10" data-modal-hide="editPageModal">Cancel</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Page Confirmation Modal -->
<div id="deletePageModal" tabindex="-1" aria-hidden="true" class="fixed top-0 left-0 right-0 z-50 hidden w-full p-4 overflow-x-hidden overflow-y-auto md:inset-0 h-[calc(100%-1rem)] max-h-full bg-gray-900 bg-opacity-50">
    <div class="relative w-full max-w-md max-h-full mx-auto">
        <!-- Modal content -->
        <div class="relative bg-white rounded-lg shadow-xl">
            <!-- Close button -->
            <button type="button" class="absolute top-3 right-2.5 text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm p-1.5 ml-auto inline-flex items-center" data-modal-hide="deletePageModal">
                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                </svg>
            </button>
            <!-- Modal body -->
            <form id="deletePageForm" method="POST" action="<?php echo BASE_URL; ?>/pages">
                <div class="p-6">
                    <input type="hidden" name="delete" value="1">
                    <input type="hidden" name="id" id="deletePageId">
                    <div class="text-center mb-4">
                        <svg class="mx-auto h-12 w-12 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                        </svg>
                    </div>
                    <p class="text-center text-base leading-relaxed text-gray-700 font-medium">
                        Are you sure you want to delete this page?
                    </p>
                    <p class="text-center text-sm text-gray-500 mt-1">
                        This action cannot be undone.
                    </p>
                </div>
                <!-- Modal footer -->
                <div class="flex items-center pt-2 pb-7 justify-center space-x-2 rounded-b">
                    <button type="submit" class="text-white bg-red-700 hover:bg-red-800 focus:ring-4 focus:outline-none focus:ring-red-300 font-medium rounded-lg text-sm px-5 py-2 text-center">Delete</button>
                    <button type="button" class="text-gray-500 bg-white hover:bg-gray-100 focus:ring-4 focus:outline-none focus:ring-gray-200 rounded-lg border border-gray-200 text-sm font-medium px-5 py-2 hover:text-gray-900 focus:z-10" data-modal-hide="deletePageModal">Cancel</button>
                </div>
            </form>
        </div>
    </div>
</div>
<script>
document.addEventListener('DOMContentLoaded', function() {
    
    // Simple modal show/hide functions without Flowbite dependency
    function showModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.classList.remove('hidden');
            modal.setAttribute('aria-hidden', 'false');
            // Add backdrop click to close
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    hideModal(modalId);
                }
            });
        }
    }
    
    function hideModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.classList.add('hidden');
            modal.setAttribute('aria-hidden', 'true');
        }
    }
    
    // Handle edit button clicks
    document.addEventListener('click', function(e) {
        if (e.target.closest('.edit-page')) {
            const button = e.target.closest('.edit-page');
            const pageId = button.dataset.id;
            const pageName = button.dataset.name;
            const pageSlug = button.dataset.slug;
            const pageStatus = button.dataset.status;
            const pageTemplateId = button.dataset.templateId;
            const pageDescription = button.dataset.description;
            
            // Set form values
            document.getElementById('editPageId').value = pageId || '';
            document.getElementById('editPageName').value = pageName || '';
            document.getElementById('editPageSlug').value = pageSlug || '';
            document.getElementById('editPageTemplateId').value = pageTemplateId || '';
            document.getElementById('editPageDescription').value = pageDescription || '';

            // Set status dropdown
            const statusDropdown = document.getElementById('editPageStatus');
            if (statusDropdown) {
                const status = pageStatus === 'active' ? 'active' : 'inactive';
                statusDropdown.value = status;
            }
            
            showModal('editPageModal');
        }
    });

    // Handle delete button clicks
    document.addEventListener('click', function(e) {
        if (e.target.closest('.delete-page')) {
            const button = e.target.closest('.delete-page');
            const pageId = button.dataset.id;
            
            document.getElementById('deletePageId').value = pageId || '';
            showModal('deletePageModal');
        }
    });

    // Handle modal toggle buttons (for create modal)
    document.addEventListener('click', function(e) {
        const toggleButton = e.target.closest('[data-modal-toggle]');
        if (toggleButton) {
            e.preventDefault();
            const modalId = toggleButton.getAttribute('data-modal-toggle');
            showModal(modalId);
        }
    });

    // Handle modal hide buttons
    document.addEventListener('click', function(e) {
        const hideButton = e.target.closest('[data-modal-hide]');
        if (hideButton) {
            e.preventDefault();
            const modalId = hideButton.getAttribute('data-modal-hide');
            hideModal(modalId);
        }
    });

    // Handle ESC key to close modals
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            const openModals = document.querySelectorAll('.fixed:not(.hidden)');
            openModals.forEach(modal => {
                if (modal.id.includes('Modal')) {
                    hideModal(modal.id);
                }
            });
        }
    });

    // Auto-generate slug from name (optional enhancement)
    const nameInputs = document.querySelectorAll('input[name="name"]');
    nameInputs.forEach(nameInput => {
        nameInput.addEventListener('input', function() {
            const slugInput = this.closest('form').querySelector('input[name="slug"]');
            if (slugInput && !slugInput.value) {
                const slug = this.value
                    .toLowerCase()
                    .replace(/[^a-z0-9]+/g, '-')
                    .replace(/^-+|-+$/g, '');
                slugInput.value = slug;
            }
        });
    });
});
</script>

<?php
$content = ob_get_clean();
require_once __DIR__ . '/../layouts/main.php';
?>