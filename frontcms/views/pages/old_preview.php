<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once __DIR__ . '/../../config/config.php';
require_once __DIR__ . '/../../modules/pages/Pages.php';
require_once __DIR__ . '/../../modules/blocks/Blocks.php';
require_once __DIR__ . '/../../modules/templates/Templates.php';
require_once __DIR__ . '/../../vendor/autoload.php';

use LightnCandy\LightnCandy;

// Define Handlebars helper functions (same as in blocks/form.php)
$helpers = [
    'mod' => function($arg1, $arg2) {
        return intval($arg1) % intval($arg2);
    },
    'eq' => function($arg1, $arg2) {
        return $arg1 === $arg2;
    },
    'ne' => function($arg1, $arg2) {
        return $arg1 !== $arg2;
    },
    'neq' => function($arg1, $arg2) {
        return $arg1 !== $arg2;
    },
    'lt' => function($arg1, $arg2) {
        return $arg1 < $arg2;
    },
    'gt' => function($arg1, $arg2) {
        return $arg1 > $arg2;
    },
    'lte' => function($arg1, $arg2) {
        return $arg1 <= $arg2;
    },
    'gte' => function($arg1, $arg2) {
        return $arg1 >= $arg2;
    },
    'and' => function() {
        foreach (func_get_args() as $arg) {
            if (!$arg) return false;
        }
        return true;
    },
    'or' => function() {
        foreach (func_get_args() as $arg) {
            if ($arg) return true;
        }
        return false;
    },
    'not' => function($arg) {
        return !$arg;
    },
    'isEven' => function($arg1) {
        return intval($arg1) % 2 === 0;
    },
    'isOdd' => function($arg1) {
        return intval($arg1) % 2 !== 0;
    }
];

// Function to process Handlebars templates (same as in blocks/form.php)
function processTemplate($template, $jsonData) {
    global $helpers;
    try {
        // Parse JSON data first
        $data = json_decode($jsonData, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            return ['success' => false, 'message' => 'Invalid JSON data: ' . json_last_error_msg()];
        }

        // Compile the template with helper functions
        $phpStr = LightnCandy::compile($template, [
            'flags' => LightnCandy::FLAG_HANDLEBARS | 
                      LightnCandy::FLAG_ERROR_EXCEPTION | 
                      LightnCandy::FLAG_RUNTIMEPARTIAL |
                      LightnCandy::FLAG_NOESCAPE,
            'helpers' => $helpers
        ]);
        
        if (!$phpStr) {
            return ['success' => false, 'message' => 'Failed to compile template'];
        }

        // Prepare the renderer (safer than eval)
        $renderer = LightnCandy::prepare($phpStr);
        if (!$renderer) {
            return ['success' => false, 'message' => 'Failed to prepare template renderer'];
        }
        
        // Render the template with data
        $result = $renderer($data);
        return ['success' => true, 'html' => $result];
    } catch (Exception $e) {
        return ['success' => false, 'message' => 'Template processing error: ' . $e->getMessage()];
    }
}

// Handle AJAX request for template processing
if (isset($_POST['action']) && $_POST['action'] === 'process_template') {
    header('Content-Type: application/json');
    $template = $_POST['template'] ?? '';
    $jsonData = $_POST['json_data'] ?? '{}';
    
    $result = processTemplate($template, $jsonData);
    echo json_encode($result);
    exit;
}

$pages = new Pages();
$blocks = new Blocks();
$templates = new Templates();
$error = null;

// Get page ID from URL
$pageId = isset($_GET['id']) ? $_GET['id'] : null;
$pageData = null;

// Check if we're in preview mode (no editing controls)
$previewMode = isset($_GET['preview']) && $_GET['preview'] === 'true';
$isTemplatePreview = isset($_GET['template']) && $_GET['template'] === 'true';

// Enable preview mode for template previews or when explicitly requested
if ($isTemplatePreview || $previewMode) {
    $previewMode = true;
}

// Get the current page data if ID is provided
if ($pageId) {
    try {
        $response = $pages->getById($pageId);
        if (isset($response['data'])) {
            $pageData = $response['data'];
        } else {
            $error = 'Page not found';
        }
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}
$templateId = $pageData['template_id'];
$templateData = null;
if ($templateId) {
    try {
        // Use the new method to get template with pages
        $response = $templates->get($templateId);
        if (isset($response['data'])) {
            $templateData = $response['data'];
            
        } else {
            $error = 'Template not found';
        }
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// If we have page_blocks module, we would load blocks here
$pageBlocks = [];
$apiResponse = null;
$dependenciesCSS = []; // Array to store CSS dependencies
$dependenciesJS = [];  // Array to store JS dependencies

if ($pageId && isset(MODULES['pages_blocks'])) {
    // Load blocks for this page from the PagesBlocks module
    require_once __DIR__ . '/../../modules/pages_blocks/PagesBlocks.php';
    
    try {
        $pagesBlocks = new PagesBlocks();
        $response = $pagesBlocks->getByPageId($pageId);
        $apiResponse = $response; // Save for debugging
        
        if (DEBUG) {
            error_log('Page Blocks API Response: ' . print_r($response, true));
        }
        
        // Extract blocks from response - handle different response formats
        $pageBlocksData = [];
        
        if (isset($response['success']) && $response['success'] === true && isset($response['data']['data'])) {
            // Format: success->data->data
            $pageBlocksData = $response['data']['data'];
        } elseif (isset($response['data']) && is_array($response['data'])) {
            // Format: data (direct array)
            $pageBlocksData = $response['data'];
        } elseif (isset($response[0]) && is_array($response[0])) {
            // Format: direct array of blocks
            $pageBlocksData = $response;
        }
        
        // Convert to the format we need
        if (!empty($pageBlocksData)) {
            foreach ($pageBlocksData as $block) {
                if (isset($block['block_id'])) {
                    // Get block details from Blocks API
                    $blockResponse = $blocks->get($block['block_id']);
                    
                    if (isset($blockResponse['success']) && $blockResponse['success'] === true && isset($blockResponse['data'])) {
                        $blockData = $blockResponse['data'];
                        
                        // Get template code and JSON data
                        $tmplCode = isset($blockData['tmpl_code']) ? $blockData['tmpl_code'] : '';
                        $jsonCode = isset($block['json_code']) ? $block['json_code'] : 
                                  (isset($blockData['json_code']) ? $blockData['json_code'] : '{}');
                        $cssCode = isset($block['css_code']) ? $block['css_code'] : 
                                 (isset($blockData['css_code']) ? $blockData['css_code'] : '');
                        $jsCode = isset($block['js_code']) ? $block['js_code'] : 
                                (isset($blockData['js_code']) ? $blockData['js_code'] : '');
                        $blockClasses = isset($block['classes']) ? $block['classes'] : 
                                      (isset($blockData['classes']) ? $blockData['classes'] : '');
                        
                        // Handle dependencies
                        $dependencies = isset($blockData['dependencies']) ? $blockData['dependencies'] : '';
                        if (!empty($dependencies)) {
                            $dependenciesArray = json_decode($dependencies, true);
                            if (json_last_error() === JSON_ERROR_NONE) {
                                // Add CSS dependencies
                                if (isset($dependenciesArray['css']) && is_array($dependenciesArray['css'])) {
                                    foreach ($dependenciesArray['css'] as $cssUrl) {
                                        if (!in_array($cssUrl, $dependenciesCSS)) {
                                            $dependenciesCSS[] = $cssUrl;
                                        }
                                    }
                                }
                                // Add JS dependencies
                                if (isset($dependenciesArray['js']) && is_array($dependenciesArray['js'])) {
                                    foreach ($dependenciesArray['js'] as $jsUrl) {
                                        if (!in_array($jsUrl, $dependenciesJS)) {
                                            $dependenciesJS[] = $jsUrl;
                                        }
                                    }
                                }
                            }
                        }
                        
                        // Process the template with JSON data if available using LightnCandy
                        $processedContent = $tmplCode;
                        
                        // If we have JSON data, compile and render the template using LightnCandy
                        if (!empty($jsonCode) && $jsonCode != '{}') {
                            // Debug: Log template and JSON data
                            if (DEBUG) {
                                error_log('Template Code: ' . $tmplCode);
                                error_log('JSON Code: ' . $jsonCode);
                            }
                            
                            // Use the processTemplate function (defined above)
                            $templateResult = processTemplate($tmplCode, $jsonCode);
                            
                            if ($templateResult['success']) {
                                $processedContent = $templateResult['html'];
                                if (DEBUG) {
                                    error_log('Processed Content: ' . $processedContent);
                                }
                            } else {
                                if (DEBUG) {
                                    error_log('Template processing error: ' . $templateResult['message']);
                                }
                                // Keep the original template if there's an error
                                $processedContent = $tmplCode;
                            }
                        }
                        
                        // Add block classes to the processed content
                        if (!empty($blockClasses)) {
                            // Add classes to the outermost element
                            $processedContent = addClassesToHtml($processedContent, $blockClasses);
                        }
                        
                        // Add to page blocks array
                        $pageBlocks[] = [
                            'id' => $block['id'],
                            'block_id' => $block['block_id'],
                            'position' => $block['position'],
                            'type' => isset($blockData['type']) ? $blockData['type'] : 'block',
                            'content' => $processedContent,
                            'css' => $cssCode,
                            'js' => $jsCode,
                            'json_code' => $jsonCode, // Add this for editing
                            'classes' => $blockClasses, // Add this for editing
                            'name' => isset($blockData['name']) ? $blockData['name'] : 'Block'
                        ];
                    }
                }
            }
        }
        
        // Sort blocks by position to ensure correct order
        usort($pageBlocks, function($a, $b) {
            return ($a['position'] ?? 999) - ($b['position'] ?? 999);
        });
        
    } catch (Exception $e) {
        // Log the error
        error_log('Error loading page blocks: ' . $e->getMessage());
    }
}

// Function to add classes to HTML content
function addClassesToHtml($html, $classes) {
    if (empty($classes) || empty($html)) {
        return $html;
    }
    
    // Simple regex to add classes to the first element
    $pattern = '/^(\s*<[^>]+?)(class\s*=\s*["\']([^"\']*)["\'])([^>]*>)/i';
    if (preg_match($pattern, $html, $matches)) {
        // Element already has classes, append new ones
        $existingClasses = $matches[3];
        $newClasses = trim($existingClasses . ' ' . $classes);
        return $matches[1] . 'class="' . $newClasses . '"' . $matches[4] . substr($html, strlen($matches[0]));
    } else {
        // Element doesn't have classes, add them
        $pattern = '/^(\s*<[^>\s]+)([^>]*>)/';
        if (preg_match($pattern, $html, $matches)) {
            return $matches[1] . ' class="' . $classes . '"' . $matches[2] . substr($html, strlen($matches[0]));
        }
    }
    
    return $html;
}

/**
 * Generate color palette from base color
 * Creates 50-950 shades similar to Tailwind CSS
 */
function generateColorPalette($baseColor) {
    // Convert hex to RGB
    $hex = ltrim($baseColor, '#');
    $r = hexdec(substr($hex, 0, 2));
    $g = hexdec(substr($hex, 2, 2));
    $b = hexdec(substr($hex, 4, 2));
    
    // Convert RGB to HSL for better color manipulation
    $hsl = rgbToHsl($r, $g, $b);
    
    $palette = [];
    
    // Define the lightness values for each shade (based on Tailwind's approach)
    $shades = [
        50 => 0.97,   // Very light
        100 => 0.94,  // Light
        200 => 0.87,  // Light
        300 => 0.75,  // Light-medium
        400 => 0.62,  // Medium-light
        500 => 0.50,  // Base (medium)
        600 => 0.38,  // Medium-dark
        700 => 0.25,  // Dark
        800 => 0.15,  // Very dark
        900 => 0.08,  // Very dark
        950 => 0.03   // Almost black
    ];
    
    foreach ($shades as $shade => $lightness) {
        // Adjust saturation for very light and very dark shades
        $saturation = $hsl[1];
        if ($lightness > 0.9) {
            // Reduce saturation for very light shades
            $saturation = $hsl[1] * 0.3;
        } elseif ($lightness < 0.1) {
            // Reduce saturation for very dark shades
            $saturation = $hsl[1] * 0.5;
        }
        
        // Convert back to RGB
        $rgb = hslToRgb($hsl[0], $saturation, $lightness);
        
        // Convert to hex
        $hex = sprintf("#%02x%02x%02x", $rgb[0], $rgb[1], $rgb[2]);
        $palette[$shade] = strtoupper($hex);
    }
    
    return $palette;
}

/**
 * Convert RGB to HSL
 */
function rgbToHsl($r, $g, $b) {
    $r /= 255;
    $g /= 255;
    $b /= 255;
    
    $max = max($r, $g, $b);
    $min = min($r, $g, $b);
    $diff = $max - $min;
    
    // Lightness
    $l = ($max + $min) / 2;
    
    if ($diff == 0) {
        $h = $s = 0; // achromatic
    } else {
        // Saturation
        $s = $l > 0.5 ? $diff / (2 - $max - $min) : $diff / ($max + $min);
        
        // Hue
        switch ($max) {
            case $r:
                $h = (($g - $b) / $diff + ($g < $b ? 6 : 0)) / 6;
                break;
            case $g:
                $h = (($b - $r) / $diff + 2) / 6;
                break;
            case $b:
                $h = (($r - $g) / $diff + 4) / 6;
                break;
        }
    }
    
    return [$h, $s, $l];
}

/**
 * Convert HSL to RGB
 */
function hslToRgb($h, $s, $l) {
    if ($s == 0) {
        $r = $g = $b = $l; // achromatic
    } else {
        $hue2rgb = function($p, $q, $t) {
            if ($t < 0) $t += 1;
            if ($t > 1) $t -= 1;
            if ($t < 1/6) return $p + ($q - $p) * 6 * $t;
            if ($t < 1/2) return $q;
            if ($t < 2/3) return $p + ($q - $p) * (2/3 - $t) * 6;
            return $p;
        };
        
        $q = $l < 0.5 ? $l * (1 + $s) : $l + $s - $l * $s;
        $p = 2 * $l - $q;
        
        $r = $hue2rgb($p, $q, $h + 1/3);
        $g = $hue2rgb($p, $q, $h);
        $b = $hue2rgb($p, $q, $h - 1/3);
    }
    
    return [round($r * 255), round($g * 255), round($b * 255)];
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($pageData['meta_title']) ? htmlspecialchars($pageData['meta_title']) : 'Page Preview'; ?></title>
    <meta name="description" content="<?php echo isset($pageData['meta_description']) ? htmlspecialchars($pageData['meta_description']) : ''; ?>">
    
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=<?php if (isset($templateData['body_font_family'])): ?><?php echo htmlspecialchars($templateData['body_font_family']); ?><?php else: ?>'Inter'<?php endif; ?>:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap" rel="stylesheet">
    
    <!-- Flowbite CSS -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/flowbite/2.2.1/flowbite.min.css" rel="stylesheet" />

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <script>
        tailwind.config = {
            darkMode: 'class', // Enable class-based dark mode
        }
    </script>
    <!-- Block Dependencies CSS -->
    <?php foreach ($dependenciesCSS as $cssUrl): ?>
    <link href="<?php echo htmlspecialchars($cssUrl); ?>" rel="stylesheet" />
    <?php endforeach; ?>
    <!-- Custom CSS for blocks -->
    <style>
        :root {
            <?php
            if (!empty($templateData['tailwind_configuration_code'])) {
                $config = $templateData['tailwind_configuration_code'];
                // Extract the JSON object part
                if (preg_match('/tailwind\.config\s*=\s*({[\s\S]*})/', $config, $matches)) {
                    $jsonStr = $matches[1];
                    // Clean the string
                    $jsonStr = preg_replace('/\\n/', '', $jsonStr);
                    $jsonStr = preg_replace('/\\"/', '"', $jsonStr); // Fix escaped quotes
                    $jsonStr = preg_replace('/\s+/', ' ', $jsonStr); // Normalize whitespace
                    
                    try {
                        $colors = json_decode($jsonStr);
                        if ($colors && isset($colors->theme->extend->colors)) {
                            $primaryBase = $colors->theme->extend->colors->primary;
                            $secondaryBase = $colors->theme->extend->colors->secondary;
                            $accentBase = $colors->theme->extend->colors->accent;
                        } else {
                            throw new Exception('Invalid color structure');
                        }
                    } catch (Exception $e) {
                        error_log('Error parsing colors: ' . $e->getMessage() . '\nJSON: ' . $jsonStr);
                        $primaryBase = '#4F46E5';
                        $secondaryBase = '#0EA5E9';
                        $accentBase = '#F59E0B';
                    }
                } else {
                    $primaryBase = '#4F46E5';
                    $secondaryBase = '#0EA5E9';
                    $accentBase = '#F59E0B';
                }
            } else {
                $primaryBase = '#4F46E5';
                $secondaryBase = '#0EA5E9';
                $accentBase = '#F59E0B';
            }
            
            // Generate color palettes
            $primaryPalette = generateColorPalette($primaryBase);
            $secondaryPalette = generateColorPalette($secondaryBase);
            $accentPalette = generateColorPalette($accentBase);
            
            // Output CSS variables for primary colors
            foreach ($primaryPalette as $shade => $color) {
                echo "        --primary-{$shade}: {$color};\n";
            }
            
            // Output CSS variables for secondary colors
            foreach ($secondaryPalette as $shade => $color) {
                echo "        --secondary-{$shade}: {$color};\n";
            }
            
            // Output CSS variables for accent colors
            foreach ($accentPalette as $shade => $color) {
                echo "        --accent-{$shade}: {$color};\n";
            }
            ?>
        }
        
        /* Primary Color Classes */
        <?php foreach (array_keys($primaryPalette) as $shade): ?>
        .bg-primary-<?php echo $shade; ?> { background-color: var(--primary-<?php echo $shade; ?>); }
        .text-primary-<?php echo $shade; ?> { color: var(--primary-<?php echo $shade; ?>); }
        .border-primary-<?php echo $shade; ?> { border-color: var(--primary-<?php echo $shade; ?>); }
        <?php endforeach; ?>
        
        /* Secondary Color Classes */
        <?php foreach (array_keys($secondaryPalette) as $shade): ?>
        .bg-secondary-<?php echo $shade; ?> { background-color: var(--secondary-<?php echo $shade; ?>); }
        .text-secondary-<?php echo $shade; ?> { color: var(--secondary-<?php echo $shade; ?>); }
        .border-secondary-<?php echo $shade; ?> { border-color: var(--secondary-<?php echo $shade; ?>); }
        <?php endforeach; ?>
        
        /* Accent Color Classes */
        <?php foreach (array_keys($accentPalette) as $shade): ?>
        .bg-accent-<?php echo $shade; ?> { background-color: var(--accent-<?php echo $shade; ?>); }
        .text-accent-<?php echo $shade; ?> { color: var(--accent-<?php echo $shade; ?>); }
        .border-accent-<?php echo $shade; ?> { border-color: var(--accent-<?php echo $shade; ?>); }
        <?php endforeach; ?>
        
        /* Legacy support - map base colors to 500 shade */
        .bg-primary { background-color: var(--primary-500); }
        .bg-secondary { background-color: var(--secondary-500); }
        .bg-accent { background-color: var(--accent-500); }
        .text-primary { color: var(--primary-500); }
        .text-secondary { color: var(--secondary-500); }
        .text-accent { color: var(--accent-500); }
        .border-primary { border-color: var(--primary-500); }
        .border-secondary { border-color: var(--secondary-500); }
        .border-accent { border-color: var(--accent-500); }

        /* Base styles */
        body {
            font-family: <?php if (isset($templateData['body_font_family'])): ?>"<?php echo htmlspecialchars($templateData['body_font_family']); ?>", sans-serif<?php else: ?>'Inter', sans-serif<?php endif; ?>;
            line-height: <?php if (isset($templateData['line_height'])): ?><?php echo htmlspecialchars($templateData['line_height']); ?><?php else: ?>1.5<?php endif; ?>;
            color: #111827;
        }
        
        /* Block wrapper with hover effect */
        .block-wrapper { position: relative; margin: 0; transition: all 0.2s ease; border: 2px dashed transparent; border-radius: 0; padding: 0; }

        <?php if (!$previewMode): ?>
            .block-wrapper:hover { border-color: #3b82f6; background-color: rgba(59, 130, 246, 0.02); } 
            .block-wrapper:hover .block-controls { display: flex; }
        <?php endif; ?>
        
        /* Block edit controls */
        #edit-drawer{ z-index: 10000; }
        .block-controls {
            <?php if ($previewMode): ?>
            display: none !important;
            <?php else: ?>
            display: none; position: absolute; top: 8px; right: 8px; background: rgba(255, 255, 255, 0.95); backdrop-filter: blur(10px); -webkit-backdrop-filter: blur(10px); border: 1px solid rgba(229, 231, 235, 0.8); border-radius: 6px; padding: 4px; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); z-index: 10000; transition: all 0.2s ease;
            <?php endif; ?>
        }
        
        .block-controls:hover { background: rgba(255, 255, 255, 1); box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15); }
        .block-controls button { margin: 0 1px; padding: 8px; background: none; border: none; cursor: pointer; transition: all 0.2s ease; border-radius: 4px; color: #6b7280; }
        .block-controls button:hover { background: rgba(59, 130, 246, 0.1); color: #3b82f6; transform: scale(1.05); }
        .block-controls button:active { transform: scale(0.95); }
        .block-controls button.block-delete:hover { background: rgba(239, 68, 68, 0.1); color: #ef4444; }
        .block-controls button.block-edit:hover { background: rgba(16, 185, 129, 0.1); color: #10b981; }
        
        /* Dynamic Drop Zones */
        .drop-zone {
            <?php if ($previewMode): ?>
            display: none !important;
            <?php else: ?>
            height: 60px; margin: 8px 0; border: 2px dashed #d1d5db; border-radius: 8px; background-color: #f9fafb; display: none; align-items: center; justify-content: center; transition: all 0.3s ease; opacity: 0;
            <?php endif; ?>
        }
        .drop-zone.active { display: flex; opacity: 1; border-color: #3b82f6; background-color: #eff6ff; }
        .drop-zone.drag-over { border-color: #1d4ed8; background-color: #dbeafe; transform: scale(1.02); }
        .drop-zone-text { color: #6b7280; font-size: 14px; display: flex; align-items: center; gap: 8px; }
        .main-drop-area {
            <?php if ($previewMode): ?>
            display: none !important;
            <?php else: ?>
            min-height: 200px; margin: 16px 0; border: 2px dashed #d1d5db; border-radius: 12px; background-color: #f9fafb; display: flex; align-items: center; justify-content: center; transition: all 0.3s ease;
            <?php endif; ?>
        }
        .main-drop-area.drag-over { border-color: #3b82f6; background-color: #eff6ff; transform: scale(1.01); }
        /* Nested form styles */
        .nested-field { border-left: 2px solid #e5e7eb; margin-left: 1rem; padding-left: 1rem; }
        .array-item { background: #f9fafb; border: 1px solid #e5e7eb; border-radius: 0.5rem; padding: 0.75rem; margin-bottom: 0.5rem; }
        .object-field { background: #f3f4f6; border: 1px solid #d1d5db; border-radius: 0.5rem; padding: 1rem; margin-bottom: 1rem; }
        .field-header { cursor: pointer; user-select: none; }
        .field-header:hover { background: rgba(59, 130, 246, 0.05); }
        .add-array-item { font-size: 0.875rem; padding: 0.25rem 0.5rem; border-radius: 0.25rem; transition: all 0.2s; }
        .add-array-item:hover { background: rgba(59, 130, 246, 0.1); }
        .remove-item-btn { width: 1.5rem; height: 1.5rem; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 1rem; font-weight: bold; transition: all 0.2s; }
        .remove-item-btn:hover { background: rgba(239, 68, 68, 0.1); transform: scale(1.1); }

        /* Additional CSS styles remain the same... */
        .block-controls button.block-dark-toggle:hover { background: rgba(99, 102, 241, 0.1); color: #6366f1; }
        .block-controls button.block-dark-toggle.dark-enabled { background: rgba(99, 102, 241, 0.2); color: #4f46e5; }
        /* Custom CSS from blocks */
        <?php
        if (!empty($pageBlocks)) {
            foreach ($pageBlocks as $block) {
                if (!empty($block['css'])) {
                    echo "/* Block ID: {$block['block_id']} */\n";
                    echo $block['css'] . "\n\n";
                }
            }
        }
        ?>
    </style>
</head>
<body>
    <?php if ($error): ?>
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 m-4 rounded">
            <p><?php echo htmlspecialchars($error); ?></p>
        </div>
    <?php endif; ?>

    <?php if (empty($pageData)): ?>
        <div class="flex items-center justify-center h-screen bg-gray-100">
            <div class="text-center">
                <h1 class="text-3xl font-bold text-gray-800 mb-4">Page Not Found</h1>
                <p class="text-gray-600">The requested page could not be found or has not been created yet.</p>
            </div>
        </div>
    <?php else: ?>
        <div class="page-content">
            <?php if (!$previewMode): ?>
            <!-- Top drop zone -->
            <div class="drop-zone" data-position="0">
                <div class="drop-zone-text">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M12 5v14m-7-7h14"/>
                    </svg>
                    Drop block here
                </div>
            </div>
            <?php endif; ?>
            
            <?php if (!empty($pageBlocks)): ?>
                <?php foreach ($pageBlocks as $index => $block): ?>
                    <div class="block-wrapper section-container relative"
                         <?php if (!$previewMode): ?>
                         data-block-id="<?php echo $block['id']; ?>"
                         data-position="<?php echo $block['position'] ?? ($index + 1); ?>"
                         data-block-type="<?php echo $block['type']; ?>"
                         data-original-block-id="<?php echo $block['block_id']; ?>"
                         data-json-code="<?php echo htmlspecialchars($block['json_code']); ?>"
                         data-block-classes="<?php echo htmlspecialchars($block['classes']); ?>"
                         data-block-name="<?php echo htmlspecialchars($block['name']); ?>"
                         data-section="section<?php echo $block['id']; ?>"
                         <?php endif; ?>>
                        <?php if (!$previewMode): ?>
                        <div class="block-controls absolute top-0 right-0 flex space-x-1 bg-white p-1 rounded shadow">
                            <button class="block-edit p-1 text-gray-600 hover:text-green-600" title="Edit Block"
                                    data-drawer-target="edit-drawer" data-drawer-show="edit-drawer" data-drawer-placement="right" aria-controls="edit-drawer">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                </svg>
                            </button>
                            <button class="block-move-up p-1 text-gray-600 hover:text-blue-600" title="Move Block Up">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18" />
                                </svg>
                            </button>
                            <button class="block-move-down p-1 text-gray-600 hover:text-blue-600" title="Move Block Down">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3" />
                                </svg>
                            </button>
                            <button class="block-delete p-1 text-gray-600 hover:text-red-600" title="Delete Block">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                </svg>
                            </button>
                        </div>
                        <?php endif; ?>
                        
                        <?php echo $block['content']; ?>
                    </div>

                    <?php if (!$previewMode): ?>
                    <!-- Drop zone after each block -->
                    <div class="drop-zone" data-position="<?php echo ($block['position'] ?? ($index + 1)) + 0.5; ?>">
                        <div class="drop-zone-text">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M12 5v14m-7-7h14"/>
                            </svg>
                            Drop block here
                        </div>
                    </div>
                    <?php endif; ?>
                <?php endforeach; ?>
            <?php endif; ?>

            <?php if (!$previewMode): ?>
            <!-- Main Drop Area (when no blocks or at the end) -->
            <div id="mainDropArea" class="main-drop-area">
                <div class="text-center text-gray-500">
                    <div class="flex items-center justify-center flex-col py-10">
                        <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="0.75" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-layout-template-icon lucide-layout-template">
                            <rect width="18" height="7" x="3" y="3" rx="1"/>
                            <rect width="9" height="7" x="3" y="14" rx="1"/>
                            <rect width="5" height="7" x="16" y="14" rx="1"/>
                        </svg>
                        <p>Drag and drop blocks here</p>
                    </div>
                </div>
            </div>
            <?php endif; ?>
        </div>
    <?php endif; ?>

    <?php if (!$previewMode): ?>
    <!-- Flowbite Drawer (Right Side) -->
        <div id="edit-drawer" class="fixed top-0 right-0 h-screen overflow-hidden flex flex-col transition-transform translate-x-full bg-white w-98 dark:bg-gray-800" tabindex="-1" aria-labelledby="drawer-right-label">
            <!-- Drawer Header -->
            <h5 id="drawer-right-label" class="flex-shrink-0 p-4 border-b inline-flex items-center mb-4 text-base font-semibold text-gray-500 dark:text-gray-400">
                <svg class="w-4 h-4 me-2.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5ZM9.5 4a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3ZM12 15H8a1 1 0 0 1 0-2h1v-3H8a1 1 0 0 1 0-2h2a1 1 0 0 1 1 1v4h1a1 1 0 0 1 0 2Z"/>
                </svg>
                Edit Block
            </h5>
            
            <!-- Close Button -->
            <button type="button" data-drawer-hide="edit-drawer" aria-controls="edit-drawer" class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 absolute top-2.5 end-2.5 inline-flex items-center justify-center dark:hover:bg-gray-600 dark:hover:text-white">
                <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"/>
                </svg>
                <span class="sr-only">Close menu</span>
            </button>
            
            <!-- Drawer Content -->
            <div class="flex-1 overflow-y-auto p-4">
                <form id="blockEditForm">
                    <div id="formFields" class="space-y-4">
                        <!-- Dynamic form fields will be generated here -->
                        <p class="text-gray-500 text-center py-4">Select a block to edit its content</p>
                    </div>
                </form>
            </div>
            
            <!-- Drawer Footer -->
            <div class="flex-shrink-0 p-4 border-t pt-4 space-y-4 border-t border-gray-200 dark:border-gray-700">
                <div class="flex space-x-2">
                    <button id="saveChanges" type="button" class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800 flex-1">
                        Save Changes
                    </button>
                    <button id="cancelChanges" type="button" data-drawer-hide="edit-drawer" class="py-2.5 px-5 text-sm font-medium text-gray-900 focus:outline-none bg-white rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-100 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700">
                        Cancel
                    </button>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <!-- Block Dependencies JS -->
    <?php foreach ($dependenciesJS as $jsUrl): ?>
    <script src="<?php echo htmlspecialchars($jsUrl); ?>"></script>
    <?php endforeach; ?>

    <!-- Flowbite JS -->
    <?php if (!$previewMode): ?>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/flowbite/2.2.1/flowbite.min.js"></script>
    <?php endif; ?>

<?php if (!$previewMode): ?>
<script>
// Initialize global tracking variables
window.processingBlocks = {};
window.dropZoneInitialized = false;
window.dragActive = false;
window.currentEditingBlock = null;

// Function to normalize block data format
function normalizeBlockData(data) {
    console.log('Normalizing block data:', data);
    
    if (data && data.id) {
        return data;
    }
    
    let normalized = {};
    
    if (data && data.blockId) {
        console.log('Converting blockId to id:', data.blockId);
        normalized.id = data.blockId;
        if (data.blockType) normalized.type = data.blockType;
        if (data.html) normalized.html = data.html;
        return normalized;
    }
    
    console.error('Unable to normalize block data:', data);
    return null;
}

// Initialize blocks functionality
function initializeBlocks() {
    console.log('Initializing blocks...');
    if (!window.dropZoneInitialized) {
        initializeDropZones();
        window.dropZoneInitialized = true;
    }
}

function initializeDropZones() {
    console.log('Setting up drop zones');
    
    // Get all drop zones
    const dropZones = document.querySelectorAll('.drop-zone');
    const mainDropArea = document.getElementById('mainDropArea');
    
    // Listen for messages from parent window
    window.addEventListener('message', function(event) {
        console.log('Received message from parent:', event.data);
        
        if (event.data.type === 'dragStart') {
            showDropZones();
        }
        if (event.data.type === 'dragEnd') {
            hideDropZones();
        }
        
        if (event.data.type === 'addBlock') {
            const normalizedData = normalizeBlockData(event.data.blockData);
            if (normalizedData) {
                // Add to the end by default
                addNewBlock(normalizedData);
            }
        }
    });

    // Setup drop zones
    dropZones.forEach(dropZone => {
        setupDropZone(dropZone);
    });
    
    // Setup main drop area
    if (mainDropArea) {
        setupDropZone(mainDropArea, true);
    }
    
    // Global drag events
    document.addEventListener('dragover', function(e) {
        e.preventDefault();
        if (!window.dragActive) {
            showDropZones();
            window.dragActive = true;
        }
    });
    
    document.addEventListener('dragleave', function(e) {
        // Only hide if we're leaving the document
        if (!e.relatedTarget || e.relatedTarget.nodeName === 'HTML') {
            hideDropZones();
            window.dragActive = false;
        }
    });
    
    document.addEventListener('drop', function(e) {
        hideDropZones();
        window.dragActive = false;
    });
}

function setupDropZone(dropZone, isMainArea = false) {
    // Handle drag over
    dropZone.addEventListener('dragover', (e) => {
        e.preventDefault();
        dropZone.classList.add('drag-over');
    });

    // Handle drag enter
    dropZone.addEventListener('dragenter', (e) => {
        e.preventDefault();
        dropZone.classList.add('drag-over');
    });

    // Handle drag leave
    dropZone.addEventListener('dragleave', (e) => {
        e.preventDefault();
        dropZone.classList.remove('drag-over');
    });

    // Handle drop
    dropZone.addEventListener('drop', (e) => {
        e.preventDefault();
        e.stopPropagation();
        dropZone.classList.remove('drag-over');
        
        const position = isMainArea ? getNextPosition() : parseFloat(dropZone.dataset.position);
        processDropEvent(e, position);
    });
}

function showDropZones() {
    const dropZones = document.querySelectorAll('.drop-zone');
    dropZones.forEach(zone => {
        zone.classList.add('active');
    });
}

function hideDropZones() {
    const dropZones = document.querySelectorAll('.drop-zone');
    dropZones.forEach(zone => {
        zone.classList.remove('active', 'drag-over');
    });
    
    const mainDropArea = document.getElementById('mainDropArea');
    if (mainDropArea) {
        mainDropArea.classList.remove('drag-over');
    }
}

function getNextPosition() {
    const blocks = Array.from(document.querySelectorAll('.block-wrapper'));
    if (blocks.length === 0) return 1;
    
    const maxPosition = Math.max(...blocks.map(block => 
        parseFloat(block.getAttribute('data-position') || '0')
    ));
    return Math.floor(maxPosition) + 1;
}

function processDropEvent(e, targetPosition = null) {
    try {
        console.log('Processing drop event at position:', targetPosition);
        
        const jsonData = e.dataTransfer.getData('application/json');
        const textData = e.dataTransfer.getData('text/plain');
        
        let blockData;
        
        if (jsonData && jsonData.trim() !== '') {
            const parsed = JSON.parse(jsonData);
            blockData = normalizeBlockData(parsed);
        } else if (textData && textData.trim() !== '') {
            try {
                const parsed = JSON.parse(textData);
                blockData = normalizeBlockData(parsed);
            } catch (innerError) {
                if (textData.includes('id')) {
                    const idMatch = textData.match(/id["|']?\s*:\s*["|']?([^,"']+)/i);
                    if (idMatch && idMatch[1]) {
                        blockData = { id: idMatch[1] };
                    }
                }
            }
        }
        
        if (blockData && blockData.id) {
            console.log('Processing valid block data:', blockData);
            
            // Notify parent window about drop
            window.parent.postMessage({
                type: 'blockDropped',
                blockId: blockData.id,
                position: {
                    x: e.clientX,
                    y: e.clientY
                }
            }, '*');
            
            // Add block at specific position
            addNewBlock(blockData, targetPosition);
        } else {
            console.error('No valid block data found in drop event');
        }
    } catch (err) {
        console.error('Error handling block drop:', err);
    }
}

function addNewBlock(blockData, targetPosition = null) {
    console.log('Adding new block with data:', blockData, 'at position:', targetPosition);
    
    if (!blockData || !blockData.id) {
        console.error('Invalid block data, missing ID:', blockData);
        return;
    }
    
    const blockId = blockData.id;
    const requestId = Date.now() + '_' + Math.random();
    
    if (!window.processingBlocks) {
        window.processingBlocks = {};
    }
    
    if (window.processingBlocks[requestId]) {
        console.warn('Identical request already being processed');
        return;
    }
    
    window.processingBlocks[requestId] = true;
    
    // Calculate position
    let position;
    if (targetPosition !== null) {
        position = targetPosition;
    } else {
        position = getNextPosition();
    }
    
    const pageId = document.getElementById('page-id')?.value;
    if (!pageId) {
        console.error('Missing page ID');
        delete window.processingBlocks[requestId];
        return;
    }

    const requestData = {
        page_id: pageId,
        block_id: blockId,
        position: position
    };
    
    console.log('Sending request with data:', requestData);

    fetch(window.location.origin + '<?php echo BASE_URL; ?>/modules/pages_blocks/create_block.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData)
    })
    .then(response => response.json())
    .then(data => {
        console.log('Response data:', data);
        delete window.processingBlocks[requestId];
        
        if (data.status === 'success') {
            console.log('Block added successfully, reloading page...');
            location.reload();
        } else {
            console.error('Failed to add block:', data);
            alert('Failed to add new block: ' + (data.message || 'Unknown error'));
        }
    })
    .catch((error) => {
        console.error('Error adding block:', error);
        alert('Failed to add new block. Please try again.');
        delete window.processingBlocks[requestId];
    });
}

function collectNestedFormData() {
    const result = {};
    const form = document.getElementById('blockEditForm');
    const inputs = form.querySelectorAll('input, textarea, select');

    console.log('Collecting form data from', inputs.length, 'inputs');

    try {
        inputs.forEach(input => {
            const fieldPath = input.getAttribute('data-field-path') || input.name;
            if (!fieldPath) return;

            let value;
            if (input.type === 'checkbox') {
                value = input.checked;
            } else if (input.type === 'number') {
                value = input.value === '' ? null : parseFloat(input.value);
            } else {
                value = input.value;
            }

            console.log('Processing field:', fieldPath, 'with value:', value);
            setNestedValue(result, fieldPath, value);
        });

        // Clean up empty values and arrays
        const cleanedResult = cleanEmptyValues(result);
        console.log('Final collected data:', cleanedResult);
        
        return cleanedResult;
    } catch (error) {
        console.error('Error collecting form data:', error);
        alert('Error collecting form data. Please check your inputs.');
        return null;
    }
}

function cleanEmptyValues(obj) {
    if (Array.isArray(obj)) {
        return obj.map(item => cleanEmptyValues(item)).filter(item => 
            item !== null && item !== undefined && item !== ''
        );
    } else if (typeof obj === 'object' && obj !== null) {
        const cleaned = {};
        Object.keys(obj).forEach(key => {
            const cleanedValue = cleanEmptyValues(obj[key]);
            if (cleanedValue !== null && cleanedValue !== undefined && cleanedValue !== '') {
                if (Array.isArray(cleanedValue) && cleanedValue.length === 0) {
                    // Keep empty arrays as they might be intentional
                    cleaned[key] = cleanedValue;
                } else if (typeof cleanedValue === 'object' && Object.keys(cleanedValue).length === 0) {
                    // Skip empty objects
                    return;
                } else {
                    cleaned[key] = cleanedValue;
                }
            }
        });
        return cleaned;
    }
    return obj;
}

function setNestedValue(obj, path, value) {
    console.log('Setting nested value:', path, '=', value);
    
    const keys = path.split(/[\.\[\]]/).filter(key => key !== '');
    let current = obj;

    for (let i = 0; i < keys.length - 1; i++) {
        const key = keys[i];
        const nextKey = keys[i + 1];

        if (!current[key]) {
            // Determine if next level should be array or object
            current[key] = /^\d+$/.test(nextKey) ? [] : {};
        }
        current = current[key];
    }

    const lastKey = keys[keys.length - 1];
    if (/^\d+$/.test(lastKey)) {
        // Array index
        const index = parseInt(lastKey);
        if (!Array.isArray(current)) {
            console.warn('Expected array but found:', typeof current, 'for path:', path);
            return;
        }
        current[index] = value;
    } else {
        // Object property
        current[lastKey] = value;
    }
}
// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM Content Loaded');
    initializeBlocks();
    
    // Setup block controls
    setupBlockControls();
});

function setupBlockControls() {
    const blockWrappers = document.querySelectorAll('.block-wrapper');

    blockWrappers.forEach(wrapper => {
        const controls = wrapper.querySelector('.block-controls');
        
        wrapper.addEventListener('mouseenter', () => {
            controls.style.display = 'flex';
        });
        
        wrapper.addEventListener('mouseleave', () => {
            controls.style.display = 'none';
        });
    });

    // Edit functionality - Updated for Flowbite Drawer
    document.querySelectorAll('.block-edit').forEach(button => {
        button.addEventListener('click', function() {
            const blockWrapper = this.closest('.block-wrapper');
            prepareEditDrawer(blockWrapper);
        });
    });

    // Move up functionality
    document.querySelectorAll('.block-move-up').forEach(button => {
        button.addEventListener('click', function() {
            const blockWrapper = this.closest('.block-wrapper');
            const prevBlock = blockWrapper.previousElementSibling;
            
            // Skip over drop zones to find the previous block
            let prevSibling = prevBlock;
            while (prevSibling && prevSibling.classList.contains('drop-zone')) {
                prevSibling = prevSibling.previousElementSibling;
            }
            
            if (prevSibling && prevSibling.classList.contains('block-wrapper')) {
                // Find the drop zone before the previous block
                let insertBefore = prevSibling;
                if (prevSibling.previousElementSibling && prevSibling.previousElementSibling.classList.contains('drop-zone')) {
                    insertBefore = prevSibling.previousElementSibling;
                }
                
                blockWrapper.parentNode.insertBefore(blockWrapper, insertBefore);
                // Move the associated drop zone too
                const associatedDropZone = blockWrapper.nextElementSibling;
                if (associatedDropZone && associatedDropZone.classList.contains('drop-zone')) {
                    blockWrapper.parentNode.insertBefore(associatedDropZone, blockWrapper.nextSibling);
                }
            }
        });
    });
    
    // Move down functionality
    document.querySelectorAll('.block-move-down').forEach(button => {
        button.addEventListener('click', function() {
            const blockWrapper = this.closest('.block-wrapper');
            const associatedDropZone = blockWrapper.nextElementSibling;
            
            // Find next block (skip drop zones)
            let nextElement = associatedDropZone ? associatedDropZone.nextElementSibling : blockWrapper.nextElementSibling;
            while (nextElement && nextElement.classList.contains('drop-zone')) {
                nextElement = nextElement.nextElementSibling;
            }
            
            if (nextElement && nextElement.classList.contains('block-wrapper')) {
                const nextDropZone = nextElement.nextElementSibling;
                const insertAfter = nextDropZone && nextDropZone.classList.contains('drop-zone') ? nextDropZone : nextElement;
                
                // Move block and its drop zone after the next block
                blockWrapper.parentNode.insertBefore(blockWrapper, insertAfter.nextSibling);
                if (associatedDropZone && associatedDropZone.classList.contains('drop-zone')) {
                    blockWrapper.parentNode.insertBefore(associatedDropZone, blockWrapper.nextSibling);
                }
            }
        });
    });
    
    // Delete functionality
    document.querySelectorAll('.block-delete').forEach(button => {
        button.addEventListener('click', function() {
            const blockWrapper = this.closest('.block-wrapper');
            const blockId = blockWrapper.getAttribute('data-block-id');
            
            if (confirm('Do you really want to delete this block?')) {
                fetch('<?php echo BASE_URL; ?>/modules/pages_blocks/delete_block.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        block_id: blockId
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Remove the block and its associated drop zone
                        const associatedDropZone = blockWrapper.nextElementSibling;
                        blockWrapper.remove();
                        if (associatedDropZone && associatedDropZone.classList.contains('drop-zone')) {
                            associatedDropZone.remove();
                        }
                    } else {
                        alert('Failed to delete the block: ' + (data.message || 'Unknown error'));
                    }
                })
                .catch((error) => {
                    console.error('Error:', error);
                    alert('Failed to delete the block. Please try again.');
                });
            }
        });
    });
}

// Flowbite Drawer Functions
function prepareEditDrawer(blockWrapper) {
    const blockId = blockWrapper.getAttribute('data-block-id');
    const originalBlockId = blockWrapper.getAttribute('data-original-block-id');
    const jsonCode = blockWrapper.getAttribute('data-json-code');
    const blockClasses = blockWrapper.getAttribute('data-block-classes');
    const blockName = blockWrapper.getAttribute('data-block-name');

    window.currentEditingBlock = {
        id: blockId,
        originalBlockId: originalBlockId,
        classes: blockClasses,
        element: blockWrapper
    };

    // Set drawer title
    document.getElementById('drawer-right-label').innerHTML = `
        <svg class="w-4 h-4 me-2.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
            <path d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5ZM9.5 4a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3ZM12 15H8a1 1 0 0 1 0-2h1v-3H8a1 1 0 0 1 0-2h2a1 1 0 0 1 1 1v4h1a1 1 0 0 1 0 2Z"/>
        </svg>
        Edit ${blockName}
    `;

    // Generate form based on JSON
    generateFormFromJSON(jsonCode, blockClasses);
}

function generateFormFromJSON(jsonCode, blockClasses = '') {
    const formFields = document.getElementById('formFields');
    formFields.innerHTML = '';

    try {
        const data = JSON.parse(jsonCode || '{}');

        // Add classes field at the top
        addClassesField(formFields, blockClasses);

        if (Object.keys(data).length === 0) {
            // Only show the classes field if no other data
            const noDataMessage = document.createElement('p');
            noDataMessage.className = 'text-gray-500 text-center py-4';
            noDataMessage.textContent = 'No other editable fields found for this block.';
            formFields.appendChild(noDataMessage);
            return;
        }

        // Add separator
        const separator = document.createElement('div');
        separator.className = 'border-t border-gray-200 my-4';
        formFields.appendChild(separator);

        // Create a cleaner, section-based UI
        generateSectionBasedForm(data, formFields);

    } catch (error) {
        console.error('Error parsing JSON:', error);
        // Still add classes field even if JSON is invalid
        addClassesField(formFields, blockClasses);
        
        const errorDiv = document.createElement('div');
        errorDiv.className = 'p-4 mb-4 text-sm text-red-800 rounded-lg bg-red-50';
        errorDiv.setAttribute('role', 'alert');
        errorDiv.textContent = 'Error parsing block data. Please check the JSON format.';
        formFields.appendChild(errorDiv);
    }
}

function addClassesField(container, blockClasses = '') {
    const classesContainer = document.createElement('div');
    classesContainer.className = 'mb-6 bg-white border border-gray-200 rounded-lg overflow-hidden';
    
    // Classes Header
    const classesHeader = document.createElement('div');
    classesHeader.className = 'px-4 py-3 bg-gradient-to-r from-purple-50 to-pink-50 border-b border-gray-200';
    classesHeader.innerHTML = `
        <div class="flex items-center space-x-2">
            <div class="w-2 h-2 bg-purple-500 rounded-full"></div>
            <h3 class="text-sm font-semibold text-gray-800">Block Classes</h3>
            <span class="text-xs text-gray-500 bg-white px-2 py-1 rounded-full">CSS Classes</span>
        </div>
    `;
    
    // Classes Content
    const classesContent = document.createElement('div');
    classesContent.className = 'p-4';
    
    const classesFieldContainer = document.createElement('div');
    classesFieldContainer.className = 'field-container';
    
    const classesLabel = document.createElement('label');
    classesLabel.className = 'block text-sm font-medium text-gray-700 mb-2';
    classesLabel.textContent = 'CSS Classes';
    classesLabel.setAttribute('for', 'block_classes');
    
    const classesInput = document.createElement('input');
    classesInput.type = 'text';
    classesInput.className = 'w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-colors';
    classesInput.name = 'block_classes';
    classesInput.id = 'block_classes';
    classesInput.value = blockClasses || '';
    classesInput.placeholder = 'e.g., container mx-auto py-8';
    classesInput.setAttribute('data-field-path', 'block_classes');
    
    const helpText = document.createElement('p');
    helpText.className = 'mt-1 text-xs text-gray-500';
    helpText.textContent = 'Add CSS classes to customize the block styling. Separate multiple classes with spaces.';
    
    classesFieldContainer.appendChild(classesLabel);
    classesFieldContainer.appendChild(classesInput);
    classesFieldContainer.appendChild(helpText);
    classesContent.appendChild(classesFieldContainer);
    
    classesContainer.appendChild(classesHeader);
    classesContainer.appendChild(classesContent);
    container.appendChild(classesContainer);
}

function generateSectionBasedForm(data, container) {
    Object.entries(data).forEach(([sectionKey, sectionValue]) => {
        const sectionContainer = document.createElement('div');
        sectionContainer.className = 'mb-6 bg-white border border-gray-200 rounded-lg overflow-hidden';
        
        // Section Header
        const sectionHeader = document.createElement('div');
        sectionHeader.className = 'px-4 py-3 bg-gradient-to-r from-blue-50 to-indigo-50 border-b border-gray-200 cursor-pointer';
        sectionHeader.innerHTML = `
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-2">
                    <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
                    <h3 class="text-sm font-semibold text-gray-800">${formatSectionLabel(sectionKey)}</h3>
                    <span class="text-xs text-gray-500 bg-white px-2 py-1 rounded-full">${getFieldTypeLabel(sectionValue)}</span>
                </div>
                <svg class="w-4 h-4 text-gray-500 transform transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                </svg>
            </div>
        `;
        
        // Section Content
        const sectionContent = document.createElement('div');
        sectionContent.className = 'section-content p-4 space-y-4';
        
        if (Array.isArray(sectionValue)) {
            createArraySection(sectionContent, sectionKey, sectionValue, sectionKey);
        } else if (typeof sectionValue === 'object' && sectionValue !== null) {
            createObjectSection(sectionContent, sectionKey, sectionValue, sectionKey);
        } else {
            createSimpleField(sectionContent, sectionKey, sectionValue, sectionKey);
        }
        
        // Toggle functionality
        sectionHeader.addEventListener('click', function() {
            const isHidden = sectionContent.style.display === 'none';
            sectionContent.style.display = isHidden ? 'block' : 'none';
            const arrow = sectionHeader.querySelector('svg');
            arrow.style.transform = isHidden ? 'rotate(0deg)' : 'rotate(-180deg)';
        });
        
        sectionContainer.appendChild(sectionHeader);
        sectionContainer.appendChild(sectionContent);
        container.appendChild(sectionContainer);
    });
}

function createArraySection(container, key, value, fieldPath) {
    // Array Header with Add Button
    const arrayHeader = document.createElement('div');
    arrayHeader.className = 'flex items-center justify-between mb-4 p-3 bg-gray-50 rounded-lg';
    arrayHeader.innerHTML = `
        <div>
            <h4 class="text-sm font-medium text-gray-800">${formatSectionLabel(key)} Items</h4>
            <p class="text-xs text-gray-500">${value.length} item(s) currently</p>
        </div>
        <button type="button" class="add-item-btn bg-blue-500 hover:bg-blue-600 text-white px-3 py-1.5 rounded-md text-xs font-medium flex items-center space-x-1 transition-colors">
            <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
            </svg>
            <span>Add New Item</span>
        </button>
    `;
    
    container.appendChild(arrayHeader);
    
    // Items Container
    const itemsContainer = document.createElement('div');
    itemsContainer.className = 'items-container space-y-3';
    itemsContainer.setAttribute('data-array-container', fieldPath);
    
    // Add existing items
    value.forEach((item, index) => {
        createArrayItem(itemsContainer, item, `${fieldPath}[${index}]`, index, fieldPath);
    });
    
    container.appendChild(itemsContainer);
    
    // Add Item Event
    arrayHeader.querySelector('.add-item-btn').addEventListener('click', function() {
        const newIndex = itemsContainer.children.length;
        const template = value.length > 0 ? createTemplateObject(value[0]) : {};
        createArrayItem(itemsContainer, template, `${fieldPath}[${newIndex}]`, newIndex, fieldPath);
        
        // Update counter
        const counter = arrayHeader.querySelector('.text-xs.text-gray-500');
        counter.textContent = `${itemsContainer.children.length} item(s) currently`;
    });
}

function createArrayItem(container, value, fieldPath, index, arrayPath) {
    const itemCard = document.createElement('div');
    itemCard.className = 'item-card bg-white border border-gray-200 rounded-lg overflow-hidden shadow-sm';
    itemCard.setAttribute('data-array-item', fieldPath);
    
    // Item Header
    const itemHeader = document.createElement('div');
    itemHeader.className = 'flex items-center justify-between px-4 py-2 bg-gray-50 border-b border-gray-200';
    itemHeader.innerHTML = `
        <div class="flex items-center space-x-2">
            <div class="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-xs font-medium">
                ${index + 1}
            </div>
            <span class="text-sm font-medium text-gray-700">Item ${index + 1}</span>
        </div>
        <div class="flex items-center space-x-2">
            <button type="button" class="move-up-btn p-1 text-gray-400 hover:text-blue-500 transition-colors" title="Move Up">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"></path>
                </svg>
            </button>
            <button type="button" class="move-down-btn p-1 text-gray-400 hover:text-blue-500 transition-colors" title="Move Down">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                </svg>
            </button>
            <button type="button" class="remove-item-btn p-1 text-gray-400 hover:text-red-500 transition-colors" title="Remove Item">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                </svg>
            </button>
        </div>
    `;
    
    // Item Content
    const itemContent = document.createElement('div');
    itemContent.className = 'p-4 space-y-3';
    
    if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
        // Handle nested object - create proper form fields for each property
        Object.entries(value).forEach(([propKey, propValue]) => {
            if (Array.isArray(propValue)) {
                // Handle nested arrays within objects
                createNestedArrayField(itemContent, propKey, propValue, `${fieldPath}.${propKey}`);
            } else if (typeof propValue === 'object' && propValue !== null) {
                // Handle nested objects within objects
                createNestedObjectField(itemContent, propKey, propValue, `${fieldPath}.${propKey}`);
            } else {
                // Handle primitive values
                createFieldInGrid(itemContent, propKey, propValue, `${fieldPath}.${propKey}`);
            }
        });
    } else if (Array.isArray(value)) {
        // Handle primitive arrays
        createNestedArrayField(itemContent, 'items', value, fieldPath);
    } else {
        // Handle primitive values
        createSimpleField(itemContent, 'value', value, fieldPath);
    }
    
    itemCard.appendChild(itemHeader);
    itemCard.appendChild(itemContent);
    container.appendChild(itemCard);
    
    // Event Listeners
    setupItemControls(itemCard, container, arrayPath);
    
    // Scroll to new item
    setTimeout(() => {
        itemCard.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
    }, 100);
}

function createFieldInGrid(container, key, value, fieldPath) {
    const fieldContainer = document.createElement('div');
    fieldContainer.className = 'field-container mb-3';
    
    const label = document.createElement('label');
    label.className = 'block text-xs font-medium text-gray-700 mb-1';
    label.textContent = formatFieldLabel(key);
    label.setAttribute('for', `field_${fieldPath.replace(/\./g, '_').replace(/\[|\]/g, '_')}`);
    
    const input = createInputElement(value, fieldPath);
    
    fieldContainer.appendChild(label);
    fieldContainer.appendChild(input);
    container.appendChild(fieldContainer);
}

function createNestedArrayField(container, key, value, fieldPath) {
    const nestedSection = document.createElement('div');
    nestedSection.className = 'nested-array-section p-3 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-200 mb-3';
    
    // Nested Array Header
    const nestedHeader = document.createElement('div');
    nestedHeader.className = 'flex items-center justify-between mb-3';
    nestedHeader.innerHTML = `
        <div>
            <h5 class="text-sm font-medium text-gray-800">${formatFieldLabel(key)}</h5>
            <p class="text-xs text-gray-500">${value.length} nested item(s)</p>
        </div>
        <button type="button" class="add-nested-item-btn bg-blue-500 hover:bg-blue-600 text-white px-2 py-1 rounded text-xs font-medium flex items-center space-x-1">
            <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
            </svg>
            <span>Add</span>
        </button>
    `;
    
    // Nested Items Container
    const nestedContainer = document.createElement('div');
    nestedContainer.className = 'nested-items-container space-y-2';
    nestedContainer.setAttribute('data-nested-array', fieldPath);
    
    // Add existing nested items
    value.forEach((item, index) => {
        createNestedArrayItem(nestedContainer, item, `${fieldPath}[${index}]`, index, fieldPath);
    });
    
    nestedSection.appendChild(nestedHeader);
    nestedSection.appendChild(nestedContainer);
    container.appendChild(nestedSection);
    
    // Add nested item event
    nestedHeader.querySelector('.add-nested-item-btn').addEventListener('click', function() {
        const newIndex = nestedContainer.children.length;
        const template = value.length > 0 ? createTemplateObject(value[0]) : {};
        createNestedArrayItem(nestedContainer, template, `${fieldPath}[${newIndex}]`, newIndex, fieldPath);
        
        // Update counter
        const counter = nestedHeader.querySelector('.text-xs.text-gray-500');
        counter.textContent = `${nestedContainer.children.length} nested item(s)`;
    });
}

function createNestedArrayItem(container, value, fieldPath, index, arrayPath) {
    const nestedItem = document.createElement('div');
    nestedItem.className = 'nested-item bg-white border border-gray-200 rounded p-3';
    nestedItem.setAttribute('data-nested-item', fieldPath);
    
    // Nested Item Header
    const nestedItemHeader = document.createElement('div');
    nestedItemHeader.className = 'flex items-center justify-between mb-2 pb-2 border-b border-gray-100';
    nestedItemHeader.innerHTML = `
        <div class="flex items-center space-x-2">
            <div class="w-5 h-5 bg-green-100 text-green-600 rounded flex items-center justify-center text-xs font-medium">
                ${index + 1}
            </div>
            <span class="text-xs font-medium text-gray-600">Nested Item ${index + 1}</span>
        </div>
        <button type="button" class="remove-nested-item-btn p-1 text-gray-400 hover:text-red-500 transition-colors">
            <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
        </button>
    `;
    
    // Nested Item Content
    const nestedItemContent = document.createElement('div');
    nestedItemContent.className = 'space-y-2';
    
    if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
        // Create form fields for each property in the nested object
        Object.entries(value).forEach(([propKey, propValue]) => {
            createFieldInGrid(nestedItemContent, propKey, propValue, `${fieldPath}.${propKey}`);
        });
    } else {
        // Handle primitive values in nested array
        createFieldInGrid(nestedItemContent, 'value', value, fieldPath);
    }
    
    nestedItem.appendChild(nestedItemHeader);
    nestedItem.appendChild(nestedItemContent);
    container.appendChild(nestedItem);
    
    // Remove nested item event
    nestedItemHeader.querySelector('.remove-nested-item-btn').addEventListener('click', function() {
        if (confirm('Remove this nested item?')) {
            nestedItem.remove();
            reindexNestedItems(container, arrayPath);
            updateNestedCounter(container);
        }
    });
}

function createNestedObjectField(container, key, value, fieldPath) {
    const objectSection = document.createElement('div');
    objectSection.className = 'nested-object-section p-3 bg-gray-50 rounded-lg border border-gray-200 mb-3';
    
    const objectHeader = document.createElement('h5');
    objectHeader.className = 'text-sm font-medium text-gray-700 mb-3 pb-2 border-b border-gray-200';
    objectHeader.textContent = formatFieldLabel(key);
    
    const objectContent = document.createElement('div');
    objectContent.className = 'space-y-2';
    
    // Generate fields for object properties
    Object.entries(value).forEach(([propKey, propValue]) => {
        if (Array.isArray(propValue)) {
            createNestedArrayField(objectContent, propKey, propValue, `${fieldPath}.${propKey}`);
        } else if (typeof propValue === 'object' && propValue !== null) {
            createNestedObjectField(objectContent, propKey, propValue, `${fieldPath}.${propKey}`);
        } else {
            createFieldInGrid(objectContent, propKey, propValue, `${fieldPath}.${propKey}`);
        }
    });
    
    objectSection.appendChild(objectHeader);
    objectSection.appendChild(objectContent);
    container.appendChild(objectSection);
}

function reindexNestedItems(container, arrayFieldPath) {
    const items = Array.from(container.children);
    items.forEach((item, index) => {
        // Update nested item number display
        const numberBadge = item.querySelector('.w-5.h-5.bg-green-100');
        const titleSpan = item.querySelector('.text-xs.font-medium.text-gray-600');
        if (numberBadge) numberBadge.textContent = index + 1;
        if (titleSpan) titleSpan.textContent = `Nested Item ${index + 1}`;
        
        // Update all field paths
        const inputs = item.querySelectorAll('input, textarea, select');
        inputs.forEach(input => {
            const oldPath = input.getAttribute('data-field-path');
            if (oldPath) {
                const newPath = oldPath.replace(/\[\d+\]/, `[${index}]`);
                input.setAttribute('data-field-path', newPath);
                input.name = newPath;
                input.id = `field_${newPath.replace(/\./g, '_').replace(/\[|\]/g, '_')}`;
            }
        });
        
        item.setAttribute('data-nested-item', `${arrayFieldPath}[${index}]`);
    });
}

function updateNestedCounter(container) {
    const section = container.closest('.nested-array-section');
    const counter = section.querySelector('.text-xs.text-gray-500');
    if (counter) {
        counter.textContent = `${container.children.length} nested item(s)`;
    }
}

function createInputElement(value, fieldPath) {
    let input;
    
    if (typeof value === 'boolean') {
        const container = document.createElement('div');
        container.className = 'flex items-center space-x-2';
        
        input = document.createElement('input');
        input.type = 'checkbox';
        input.className = 'w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500';
        input.checked = value;
        
        const label = document.createElement('span');
        label.className = 'text-xs text-gray-600';
        label.textContent = 'Enable';
        
        container.appendChild(input);
        container.appendChild(label);
        
        // Set attributes on the checkbox input
        input.name = fieldPath;
        input.setAttribute('data-field-path', fieldPath);
        input.id = `field_${fieldPath.replace(/\./g, '_').replace(/\[|\]/g, '_')}`;
        
        return container;
    } else if (typeof value === 'number') {
        input = document.createElement('input');
        input.type = 'number';
        input.value = value;
    } else if (typeof value === 'string' && value.length > 100) {
        input = document.createElement('textarea');
        input.rows = 3;
        input.value = value;
    } else {
        input = document.createElement('input');
        input.type = 'text';
        input.value = value || '';
    }
    
    input.className = 'w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors';
    input.name = fieldPath;
    input.setAttribute('data-field-path', fieldPath);
    input.id = `field_${fieldPath.replace(/\./g, '_').replace(/\[|\]/g, '_')}`;
    
    return input;
}

function createObjectSection(container, key, value, fieldPath) {
    const fieldsContainer = document.createElement('div');
    fieldsContainer.className = 'grid grid-cols-1 gap-4';
    
    Object.entries(value).forEach(([propKey, propValue]) => {
        if (Array.isArray(propValue)) {
            createArraySection(fieldsContainer, propKey, propValue, `${fieldPath}.${propKey}`);
        } else if (typeof propValue === 'object' && propValue !== null) {
            const nestedSection = document.createElement('div');
            nestedSection.className = 'p-3 bg-gray-50 rounded-lg border border-gray-200';
            
            const nestedTitle = document.createElement('h5');
            nestedTitle.className = 'text-sm font-medium text-gray-700 mb-3';
            nestedTitle.textContent = formatFieldLabel(propKey);
            
            nestedSection.appendChild(nestedTitle);
            createObjectSection(nestedSection, propKey, propValue, `${fieldPath}.${propKey}`);
            fieldsContainer.appendChild(nestedSection);
        } else {
            createFieldInGrid(fieldsContainer, propKey, propValue, `${fieldPath}.${propKey}`);
        }
    });
    
    container.appendChild(fieldsContainer);
}

function createSimpleField(container, key, value, fieldPath) {
    const fieldContainer = document.createElement('div');
    fieldContainer.className = 'field-container';
    
    const label = document.createElement('label');
    label.className = 'block text-sm font-medium text-gray-700 mb-2';
    label.textContent = formatFieldLabel(key);
    
    const input = createInputElement(value, fieldPath);
    
    fieldContainer.appendChild(label);
    fieldContainer.appendChild(input);
    container.appendChild(fieldContainer);
}

function setupItemControls(itemCard, container, arrayPath) {
    const removeBtn = itemCard.querySelector('.remove-item-btn');
    const moveUpBtn = itemCard.querySelector('.move-up-btn');
    const moveDownBtn = itemCard.querySelector('.move-down-btn');
    
    // Remove Item
    removeBtn.addEventListener('click', function() {
        if (confirm('Are you sure you want to remove this item?')) {
            itemCard.remove();
            reindexArrayItems(container, arrayPath);
            updateItemCounter(container);
        }
    });
    
    // Move Up
    moveUpBtn.addEventListener('click', function() {
        const prevItem = itemCard.previousElementSibling;
        if (prevItem) {
            container.insertBefore(itemCard, prevItem);
            reindexArrayItems(container, arrayPath);
        }
    });
    
    // Move Down
    moveDownBtn.addEventListener('click', function() {
        const nextItem = itemCard.nextElementSibling;
        if (nextItem) {
            container.insertBefore(nextItem, itemCard);
            reindexArrayItems(container, arrayPath);
        }
    });
}

function updateItemCounter(container) {
    const section = container.closest('.mb-6');
    const counter = section.querySelector('.text-xs.text-gray-500');
    if (counter) {
        counter.textContent = `${container.children.length} item(s) currently`;
    }
}

function reindexArrayItems(container, arrayFieldPath) {
    const items = Array.from(container.children);
    items.forEach((item, index) => {
        // Update item number display
        const numberBadge = item.querySelector('.w-6.h-6.bg-blue-100');
        const titleSpan = item.querySelector('.text-sm.font-medium.text-gray-700');
        if (numberBadge) numberBadge.textContent = index + 1;
        if (titleSpan) titleSpan.textContent = `Item ${index + 1}`;
        
        // Update all field paths
        const inputs = item.querySelectorAll('input, textarea, select');
        inputs.forEach(input => {
            const oldPath = input.getAttribute('data-field-path');
            if (oldPath) {
                const newPath = oldPath.replace(/\[\d+\]/, `[${index}]`);
                input.setAttribute('data-field-path', newPath);
                input.name = newPath;
            }
        });
        
        item.setAttribute('data-array-item', `${arrayFieldPath}[${index}]`);
    });
}

// Helper functions
function formatSectionLabel(key) {
    return key.charAt(0).toUpperCase() + key.slice(1)
        .replace(/([A-Z])/g, ' $1')
        .replace(/_/g, ' ');
}

function formatFieldLabel(key) {
    return key.charAt(0).toUpperCase() + key.slice(1)
        .replace(/([A-Z])/g, ' $1')
        .replace(/_/g, ' ');
}

function getFieldTypeLabel(value) {
    if (Array.isArray(value)) {
        return `${value.length} items`;
    } else if (typeof value === 'object' && value !== null) {
        return `${Object.keys(value).length} fields`;
    } else {
        return typeof value;
    }
}

function createTemplateObject(obj) {
    if (Array.isArray(obj)) {
        return [];
    } else if (typeof obj === 'object' && obj !== null) {
        const template = {};
        Object.keys(obj).forEach(key => {
            const value = obj[key];
            if (Array.isArray(value)) {
                template[key] = [];
            } else if (typeof value === 'object' && value !== null) {
                template[key] = createTemplateObject(value);
            } else if (typeof value === 'number') {
                template[key] = 0;
            } else if (typeof value === 'boolean') {
                template[key] = false;
            } else {
                template[key] = '';
            }
        });
        return template;
    } else if (typeof obj === 'number') {
        return 0;
    } else if (typeof obj === 'boolean') {
        return false;
    } else {
        return '';
    }
}

// Save Changes Function
document.getElementById('saveChanges').addEventListener('click', function() {
    if (!window.currentEditingBlock) {
        alert('No block is currently being edited.');
        return;
    }

    const jsonData = collectNestedFormData();

    if (jsonData === null) {
        return; // Error occurred during collection
    }

    // Get classes value
    const classesInput = document.getElementById('block_classes');
    const blockClasses = classesInput ? classesInput.value : '';

    // Remove the classes from jsonData if it exists there
    delete jsonData.block_classes;

    // Save to server
    const requestData = {
        block_id: window.currentEditingBlock.id,
        json_code: JSON.stringify(jsonData),
        classes: blockClasses
    };

    // Show loading state
    const saveBtn = document.getElementById('saveChanges');
    const originalText = saveBtn.textContent;
    saveBtn.textContent = 'Saving...';
    saveBtn.disabled = true;

    fetch('<?php echo BASE_URL; ?>/modules/pages_blocks/update_block.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Show success notification
            showNotification('Block updated successfully!', 'success');
            
            // Close drawer using Flowbite
            const drawer = FlowbiteInstances.getInstance('Drawer', 'edit-drawer');
            if (drawer) {
                drawer.hide();
            }
            
            // Reload the page to show updated content
            setTimeout(() => {
                location.reload();
            }, 1000);
        } else {
            showNotification('Failed to update block: ' + (data.message || 'Unknown error'), 'error');
        }
    })
    .catch((error) => {
        console.error('Error updating block:', error);
        showNotification('Failed to update block. Please try again.', 'error');
    })
    .finally(() => {
        saveBtn.textContent = originalText;
        saveBtn.disabled = false;
    });
});

// Notification function
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 p-4 mb-4 text-sm rounded-lg ${
        type === 'success' ? 'text-green-800 bg-green-50 dark:bg-gray-800 dark:text-green-400' : 
        type === 'error' ? 'text-red-800 bg-red-50 dark:bg-gray-800 dark:text-red-400' : 
        'text-blue-800 bg-blue-50 dark:bg-gray-800 dark:text-blue-400'
    }`;
    notification.innerHTML = `
        <div class="flex items-center">
            <svg class="flex-shrink-0 w-4 h-4 me-2" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                <path d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5ZM9.5 4a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3ZM12 15H8a1 1 0 0 1 0-2h1v-3H8a1 1 0 0 1 0-2h2a1 1 0 0 1 1 1v4h1a1 1 0 0 1 0 2Z"/>
            </svg>
            <span>${message}</span>
        </div>
    `;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.remove();
    }, 4000);
}
</script>


<?php endif; ?>

<!-- Add hidden input for page ID -->
<?php if (!$previewMode): ?>
<input type="hidden" id="page-id" value="<?php echo htmlspecialchars($pageId); ?>">
<?php endif; ?>
 
<script>
<?php 
    if (!empty($pageBlocks)) {
        foreach ($pageBlocks as $block) {
            if (!empty($block['js'])) {
                echo "/* Block ID: {$block['block_id']} */\n";
                echo $block['js'] . "\n\n";
            }
        }
    }
?>
</script>
</body>
</html>