<?php
require_once __DIR__ . '/../config/config.php';

$pageId = $_GET['id'] ?? null;
$previewMode = ($_GET['preview'] ?? '') === 'true';

if (!$pageId) {
    header("HTTP/1.0 400 Bad Request");
    exit("Page ID is required");
}

$queryParams = [
    'type' => 'page',
    'id' => $pageId
];

if ($previewMode) {
    $queryParams['preview'] = 'true';
}

$redirectUrl = BASE_URL . '/views/preview.php?' . http_build_query($queryParams);
header("Location: $redirectUrl");
exit;
?>