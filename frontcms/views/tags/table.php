<?php
require_once __DIR__ . '/../../includes/TableHelper.php';
require_once __DIR__ . '/../../includes/TableComponent.php';

function renderTagsTable($data, $pagination) {
    // Handle empty data case
    if (empty($data)) {
        $data = [];
    }
    
    // Handle empty pagination case
    if (empty($pagination)) {
        $pagination = [
            'current_page' => 1,
            'per_page' => 10,
            'total' => 0,
            'last_page' => 1
        ];
    }

    $columns = [
        'id' => [
            'label' => 'ID',
            'sortable' => true
        ],
        'tag' => [
            'label' => 'Tag Name',
            'sortable' => true
        ],
        'created_at' => [
            'label' => 'Created At',
            'sortable' => true,
            'formatter' => function($value) {
                return formatDate($value);
            }
        ],
        'updated_at' => [
            'label' => 'Updated At',
            'sortable' => true,
            'formatter' => function($value) {
                return formatDate($value);
            }
        ]
    ];
    
    $actionButtons = [
        [
            'label' => 'Create New Tag',
            'icon' => 'plus',
            'attributes' => [
                'data-modal-target' => 'createTagModal',
                'data-modal-toggle' => 'createTagModal'
            ]
        ]
    ];
    
    $rowActions = [
        [
            'icon' => '<path d="M21.174 6.812a1 1 0 0 0-3.986-3.987L3.842 16.174a2 2 0 0 0-.5.83l-1.321 4.352a.5.5 0 0 0 .623.622l4.353-1.32a2 2 0 0 0 .83-.497z"/><path d="m15 5 4 4"/>',
            'tooltip' => 'Edit',
            'type' => 'button',
            'attributes' => [
                'data-modal-target' => 'editTagModal',
                'data-modal-toggle' => 'editTagModal',
                'data-id' => function($row) { return $row['id'] ?? ''; },
                'data-tag' => function($row) { return $row['tag'] ?? ''; }
            ]
        ],
        [
            'icon' => '<path d="M3 6h18"/><path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"/><path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"/>',
            'tooltip' => 'Delete',
            'type' => 'button',
            'attributes' => [
                'data-modal-target' => 'deleteTagModal',
                'data-modal-toggle' => 'deleteTagModal',
                'data-id' => function($row) { return $row['id'] ?? ''; }
            ]
        ]
    ];
    
    try {
        $table = new TableComponent($data, $columns, $pagination);
        $table->setTableId('tagsTable')
              ->setTitleBreadcrumb('Tags', [
                  ['label' => 'Home', 'url' => BASE_URL . '/dashboard'],
                  ['label' => 'Tags']
              ])
              ->setSearchConfig(true, 'searchTags')
              ->setActionButtons($actionButtons)
              ->setRowActions($rowActions)
              ->setGridConfig(['enabled' => false]);
        
        return $table->render();
    } catch (Exception $e) {
        return "<div class='alert alert-danger'>Error rendering table: {$e->getMessage()}</div>";
    }
}