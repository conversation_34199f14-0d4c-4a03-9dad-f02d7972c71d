<?php
require_once __DIR__ . '/../../config/config.php';
require_once __DIR__ . '/../../modules/tags/TagsController.php';

// Initialize controller
$controller = new TagsController();
$result = $controller->handleRequest();

// Start output buffering
ob_start();

// Display error message if there's an error

if (!$result['success']) {
    echo "<div class='alert alert-danger alert-dismissible fade show' role='alert' data-auto-dismiss='3000'>";
    echo $result['message'] ?? 'An error occurred';
    echo "<button type='button' class='btn-close' data-bs-dismiss='alert' aria-label='Close'></button>";
    echo "</div>";
} else {
    echo "<div class='alert alert-success alert-dismissible fade show' role='alert' data-auto-dismiss='3000'>";
    echo $result['message'] ?? '';
    echo "<button type='button' class='btn-close' data-bs-dismiss='alert' aria-label='Close'></button>";
    echo "</div>";
}

// Include and render table
require_once __DIR__ . '/table.php';

echo renderTagsTable(
    $result['data']['data'] ?? [], // Table data
    $result['data'] ?? []          // Pagination data
);

// Include modals and scripts
require_once __DIR__ . '/modal.php';
echo '<script src="' . BASE_URL . '/assets/js/tag/tag.js"></script>';

// Get the buffered content
$content = ob_get_clean();

// Include the main layout
require_once __DIR__ . '/../layouts/main.php';
