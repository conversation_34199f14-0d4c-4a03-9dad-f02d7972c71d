<?php
require_once __DIR__ . '/../../config/config.php';
require_once __DIR__ . '/../../modules/auth/Auth.php';

// Check if session is not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Initialize Auth class
$auth = new Auth();

// Check if user is logged in
if (!$auth->isLoggedIn()) {
    header("Location: " . BASE_URL . "/login");
    exit;
}

$error = '';
$success = '';

// Get user profile
$profileResult = $auth->getProfile();
$userProfile = $profileResult['success'] ? $profileResult['data'] : null;

// Handle form submission
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    if (isset($_POST['action'])) {
        // Profile update
        if ($_POST['action'] === 'update_profile') {
            $name = $_POST['name'];
            $email = $_POST['email'];
            
            $data = [
                'name' => $name,
                'email' => $email
            ];
            
            $result = $auth->updateProfile($data);
            
            if ($result['success']) {
                $success = 'Profile updated successfully.';
                // Refresh user profile after successful update
                $profileResult = $auth->getProfile();
                $userProfile = $profileResult['success'] ? $profileResult['data'] : null;
            } else {
                $error = $result['message'];
            }
        }
        
        // Change password
        if ($_POST['action'] === 'change_password') {
            $currentPassword = $_POST['current_password'];
            $newPassword = $_POST['new_password'];
            $passwordConfirmation = $_POST['password_confirmation'];
            
            // Validate passwords match
            if ($newPassword !== $passwordConfirmation) {
                $error = 'New passwords do not match.';
            } else {
                $data = [
                    'current_password' => $currentPassword,
                    'password' => $newPassword,
                    'password_confirmation' => $passwordConfirmation
                ];
                
                $result = $auth->changePassword($data);
                
                if ($result['success']) {
                    $success = 'Password changed successfully.';
                } else {
                    $error = $result['message'];
                }
            }
        }
    }
}

// Start output buffering
ob_start();
?>
    <!-- Main Content -->
    <div class="p-6">
        <h1 class="text-2xl font-bold mb-6">Profile Settings</h1>

        <?php if ($error): ?>
        <div class="mb-4 p-4 text-sm text-red-800 rounded-lg bg-red-50" role="alert">
            <?php echo $error; ?>
        </div>
        <?php endif; ?>

        <?php if ($success): ?>
        <div class="mb-4 p-4 text-sm text-green-800 rounded-lg bg-green-50" role="alert">
            <?php echo $success; ?>
        </div>
        <?php endif; ?>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-10">
            <!-- Profile Information -->
            <div>
                <h2 class="text-xl font-semibold">Profile Information</h2>
                <p class="text-xs text-gray-600 mb-6">Update your account profile information and email address.</p>

                <?php if ($userProfile): ?>
                <form id="profile-form" method="POST" action="" class="space-y-4">
                    <input type="hidden" name="action" value="update_profile">
                    
                    <!-- Name -->
                    <div>
                        <label for="name" class="block text-sm font-bold text-gray-700 mb-2">Name</label>
                        <input type="text" id="name" name="name" value="<?php echo htmlspecialchars($userProfile['name']); ?>" required
                            class="block w-full pr-4 py-2.5 text-sm text-gray-900 bg-white border border-gray-300 rounded-lg focus:border-blue-500 focus:ring-1 focus:ring-blue-500">
                    </div>

                    <!-- Email -->
                    <div>
                        <label for="email" class="block text-sm font-bold text-gray-700 mb-2">Email</label>
                        <input type="email" id="email" name="email" value="<?php echo htmlspecialchars($userProfile['email']); ?>" required
                            class="block w-full pr-4 py-2.5 text-sm text-gray-900 bg-white border border-gray-300 rounded-lg focus:border-blue-500 focus:ring-1 focus:ring-blue-500">
                    </div>

                    <div class="flex justify-end">
                        <button type="submit"
                            class="btn-primary py-2.5 px-4 border border-transparent rounded-lg text-sm font-medium text-white bg-[#0C5BE2] hover:bg-[#114bbc] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#1375fd]">
                            Save
                        </button>
                    </div>
                </form>
                <?php else: ?>
                <div class="p-4 text-sm text-yellow-800 rounded-lg bg-yellow-50">
                    Unable to load profile information. Please try again later.
                </div>
                <?php endif; ?>
            </div>

            <!-- Update Password -->
            <div>
                <h2 class="text-xl font-semibold">Update Password</h2>
                <p class="text-xs text-gray-600 mb-6">Ensure your account is using a long, random password to stay secure.</p>

                <form id="password-form" method="POST" action="" class="space-y-4">
                    <input type="hidden" name="action" value="change_password">
                    
                    <!-- Current Password -->
                    <div>
                        <label for="current_password" class="block text-sm font-bold text-gray-700 mb-2">Current Password</label>
                        <div class="relative">
                            <input type="password" id="current_password" name="current_password" required
                                class="block w-full pr-4 py-2.5 text-sm text-gray-900 bg-white border border-gray-300 rounded-lg focus:border-blue-500 focus:ring-1 focus:ring-blue-500">
                            <button type="button" class="absolute inset-y-0 right-0 pr-3 flex items-center" onclick="togglePasswordVisibility('current_password')">
                                <svg class="h-5 w-5 text-gray-400 hover:text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                </svg>
                            </button>
                        </div>
                    </div>

                    <!-- New Password -->
                    <div>
                        <label for="new_password" class="block text-sm font-bold text-gray-700 mb-2">New Password</label>
                        <div class="relative">
                            <input type="password" id="new_password" name="new_password" required
                                class="block w-full pr-4 py-2.5 text-sm text-gray-900 bg-white border border-gray-300 rounded-lg focus:border-blue-500 focus:ring-1 focus:ring-blue-500">
                            <button type="button" class="absolute inset-y-0 right-0 pr-3 flex items-center" onclick="togglePasswordVisibility('new_password')">
                                <svg class="h-5 w-5 text-gray-400 hover:text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                </svg>
                            </button>
                        </div>
                    </div>

                    <!-- Confirm Password -->
                    <div>
                        <label for="password_confirmation" class="block text-sm font-bold text-gray-700 mb-2">Confirm Password</label>
                        <div class="relative">
                            <input type="password" id="password_confirmation" name="password_confirmation" required
                                class="block w-full pr-4 py-2.5 text-sm text-gray-900 bg-white border border-gray-300 rounded-lg focus:border-blue-500 focus:ring-1 focus:ring-blue-500">
                            <button type="button" class="absolute inset-y-0 right-0 pr-3 flex items-center" onclick="togglePasswordVisibility('password_confirmation')">
                                <svg class="h-5 w-5 text-gray-400 hover:text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                </svg>
                            </button>
                        </div>
                    </div>

                    <div class="flex justify-end">
                        <button type="submit"
                            class="btn-primary py-2.5 px-4 border border-transparent rounded-lg text-sm font-medium text-white bg-[#0C5BE2] hover:bg-[#114bbc] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#1375fd]">
                            Update Password
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <script>
    // Password Visibility Toggle
    function togglePasswordVisibility(inputId) {
        const input = document.getElementById(inputId);
        const type = input.getAttribute('type') === 'password' ? 'text' : 'password';
        input.setAttribute('type', type);
    
        const button = input.nextElementSibling;
        const svg = button.querySelector('svg');
        if (type === 'password') {
            svg.innerHTML = '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" /><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />';
        } else {
            svg.innerHTML = '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.88 9.88l-3.29-3.29m7.532 7.532l3.29 3.29M3 3l3.59 3.59m0 0A9.953 9.953 0 0112 5c4.478 0 8.268 2.943 9.543 7a10.025 10.025 0 01-4.132 5.411m0 0L21 21" />';
        }
    }
    
    // Password form validation
    document.getElementById('password-form').addEventListener('submit', function(event) {
        const newPassword = document.getElementById('new_password').value;
        const passwordConfirmation = document.getElementById('password_confirmation').value;
        
        if (newPassword !== passwordConfirmation) {
            event.preventDefault();
            alert('New passwords do not match.');
        }
        
        if (newPassword.length < 8) {
            event.preventDefault();
            alert('Password must be at least 8 characters long.');
        }
    });
    </script>

<?php
$content = ob_get_clean();
require_once __DIR__ . '/../layouts/main.php';
?>