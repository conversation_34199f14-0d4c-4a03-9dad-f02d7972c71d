<?php
require_once __DIR__ . '/../../config/config.php';
require_once __DIR__ . '/../../modules/auth/Auth.php';

// Check if session is not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// If user is already logged in, redirect to dashboard
if (isset($_SESSION['token'])) {
    header("Location: " . BASE_URL . "/dashboard");
    exit;
}

$error = '';
$forgotSuccess = '';

// Initialize Auth class
$auth = new Auth();

// Handle login POST request
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // <PERSON><PERSON> forgot password request
    if (isset($_POST['action']) && $_POST['action'] === 'forgot_password') {
        $email = $_POST['email'];
        
        if (empty($email)) {
            $error = 'Email address is required.';
        } else {
            $result = $auth->forgotPassword($email);
            
            if ($result['success']) {
                $forgotSuccess = $result['message'];
            } else {
                $error = $result['message'];
            }
        }
    } 
    // Handle login request
    else {
        $email = $_POST['email'];
        $password = $_POST['password'];
        
        // Determine if this is an admin login or regular user login
        $isAdmin = isset($_POST['is_admin']) && $_POST['is_admin'] == '1';
        
        if ($isAdmin) {
            $result = $auth->adminLogin($email, $password);
        } else {
            $result = $auth->userLogin($email, $password);
        }
        
        if ($result['success']) {
            header("Location: " . BASE_URL . "/dashboard");
            exit;
        } else {
            $error = $result['message'];
        }
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - CMS Builder</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/flowbite/2.2.0/flowbite.min.css" rel="stylesheet" />
    <style>
        body {
            background-color: #f8fafc;
        }

        .card-shadow {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 
                        0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }

        .input-focus {
            transition: all 0.3s ease;
        }

        .input-focus:focus {
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .btn-primary {
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
        }

        .slide-in {
            animation: slideIn 0.3s ease-out forwards;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
</head>
<body class="min-h-screen flex items-center justify-center p-4">
    <!-- Toast Notification -->
    <div id="toast" class="fixed top-4 right-4 z-50 transform transition-all duration-300 translate-x-full">
        <div class="flex items-center p-4 mb-4 text-gray-500 bg-white rounded-lg shadow-lg">
            <div class="inline-flex items-center justify-center flex-shrink-0 w-8 h-8 text-red-500 bg-red-100 rounded-lg">
                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                </svg>
            </div>
            <div class="ml-3 text-sm font-normal" id="toastMessage"></div>
            <button type="button" class="ml-auto -mx-1.5 -my-1.5 text-gray-400 hover:text-gray-900 rounded-lg p-1.5 hover:bg-gray-100 inline-flex h-8 w-8" onclick="hideToast()">
                <span class="sr-only">Close</span>
                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                </svg>
            </button>
        </div>
    </div>

    <!-- Main Container -->
    <div class="w-full max-w-md">
        <!-- Auth Cards Container -->
        <div class="relative">
            <!-- Login Card -->
            <div id="loginCard" class="bg-white rounded-xl shadow-lg p-8 slide-in <?php if ($forgotSuccess): ?>hidden<?php endif; ?>">
                <!-- Logo -->
                <div class="flex justify-center mb-8">
                    <div class="relative">                             
                    <svg width="220px" viewBox="0 0 220 37.045">
                            <path id="a" d="m40.559 18.773c0-1.8537 0.36963-3.4977 1.1089-4.9321 0.73926-1.4344 1.7488-2.5377 3.0287-3.3101 1.2799-0.77236 2.7033-1.1585 4.27-1.1585 1.1916 0 2.3281 0.25929 3.4094 0.77788 1.0813 0.51858 1.9419 1.2082 2.5819 2.0688v-8.7056h4.7004v24.495h-4.7004v-2.7143c-0.57375 0.90476-1.3792 1.633-2.4164 2.1847-1.0372 0.55168-2.2398 0.82753-3.608 0.82753-1.5447 0-2.957-0.39721-4.2369-1.1916-1.2799-0.79443-2.2895-1.9143-3.0287-3.3598s-1.1089-3.106-1.1089-4.9817zm14.432 0.066202c0-1.1254-0.22067-2.0909-0.66202-2.8963-0.44135-0.80546-1.0372-1.4233-1.7875-1.8537-0.75029-0.43031-1.5558-0.64547-2.4164-0.64547s-1.6551 0.20964-2.3833 0.62892c-0.72822 0.41928-1.3185 1.0317-1.7709 1.8371-0.45238 0.80546-0.67857 1.7599-0.67857 2.8632s0.22619 2.0688 0.67857 2.8963c0.45238 0.82753 1.0482 1.462 1.7875 1.9033 0.73926 0.44135 1.5282 0.66202 2.3667 0.66202 0.86063 0 1.6661-0.21516 2.4164-0.64547 0.75029-0.43031 1.3461-1.0482 1.7875-1.8537 0.44135-0.80546 0.66202-1.7709 0.66202-2.8963zm11.552-11.354c-0.81649 0-1.4951-0.25929-2.0357-0.77788-0.54065-0.51858-0.81098-1.1641-0.81098-1.9364 0-0.77236 0.27033-1.4178 0.81098-1.9364 0.54065-0.51858 1.2192-0.77788 2.0357-0.77788 0.81649 0 1.4951 0.25929 2.0357 0.77788 0.54065 0.51858 0.81098 1.1641 0.81098 1.9364 0 0.77236-0.27033 1.4178-0.81098 1.9364-0.54065 0.51858-1.2192 0.77788-2.0357 0.77788zm2.284 2.1847v18.338h-4.6342v-18.338h4.6342zm11.751-0.29791c1.3682 0 2.5709 0.27033 3.608 0.81098 1.0372 0.54065 1.8537 1.2413 2.4495 2.1019v-2.615h4.6673v18.47c0 1.6992-0.34204 3.2163-1.0261 4.5514s-1.7102 2.3943-3.0784 3.1777c-1.3682 0.78339-3.0232 1.1751-4.9652 1.1751-2.604 0-4.739-0.60685-6.4051-1.8206-1.6661-1.2137-2.6095-2.8688-2.8301-4.9652h4.6011c0.24274 0.83856 0.76684 1.5061 1.5723 2.0026 0.80546 0.49652 1.7819 0.74477 2.9294 0.74477 1.3461 0 2.4384-0.40273 3.277-1.2082 0.83856-0.80546 1.2578-2.0247 1.2578-3.6577v-2.8467c-0.59582 0.86063-1.4178 1.5778-2.466 2.1516-1.0482 0.57375-2.2454 0.86063-3.5915 0.86063-1.5447 0-2.957-0.39721-4.2369-1.1916s-2.2895-1.9143-3.0287-3.3598c-0.73926-1.4454-1.1089-3.106-1.1089-4.9817 0-1.8537 0.36963-3.4977 1.1089-4.9321 0.73926-1.4344 1.7433-2.5377 3.0122-3.3101 1.2689-0.77236 2.6867-1.1585 4.2535-1.1585zm6.0575 9.4669c0-1.1254-0.22067-2.0909-0.66202-2.8963-0.44135-0.80546-1.0372-1.4233-1.7875-1.8537s-1.5558-0.64547-2.4164-0.64547c-0.86063 0-1.6551 0.20964-2.3833 0.62892-0.72822 0.41928-1.3185 1.0317-1.7709 1.8371-0.45238 0.80546-0.67857 1.7599-0.67857 2.8632s0.22619 2.0688 0.67857 2.8963c0.45238 0.82753 1.0482 1.462 1.7875 1.9033s1.5282 0.66202 2.3667 0.66202c0.86063 0 1.6661-0.21516 2.4164-0.64547s1.3461-1.0482 1.7875-1.8537c0.44135-0.80546 0.66202-1.7709 0.66202-2.8963zm11.552-11.354c-0.81649 0-1.4951-0.25929-2.0357-0.77788-0.54065-0.51858-0.81098-1.1641-0.81098-1.9364 0-0.77236 0.27033-1.4178 0.81098-1.9364 0.54065-0.51858 1.2192-0.77788 2.0357-0.77788 0.81649 0 1.4951 0.25929 2.0357 0.77788 0.54065 0.51858 0.81098 1.1641 0.81098 1.9364 0 0.77236-0.27032 1.4178-0.81098 1.9364-0.54065 0.51858-1.2192 0.77788-2.0357 0.77788zm2.284 2.1847v18.338h-4.6342v-18.338h4.6342zm3.3763 9.169c0-1.8978 0.38618-3.5584 1.1585-4.9817 0.77236-1.4233 1.8426-2.5267 3.2108-3.3101 1.3682-0.78339 2.935-1.1751 4.7004-1.1751 2.2729 0 4.1542 0.56824 5.6437 1.7047 1.4895 1.1365 2.4881 2.7308 2.9956 4.7831h-4.9983c-0.26481-0.79443-0.71167-1.4178-1.3406-1.8702-0.62892-0.45238-1.4068-0.67857-2.3336-0.67857-1.324 0-2.3722 0.47997-3.1446 1.4399-0.77236 0.95993-1.1585 2.3226-1.1585 4.088 0 1.7433 0.38618 3.095 1.1585 4.0549 0.77236 0.95993 1.8206 1.4399 3.1446 1.4399 1.8757 0 3.1005-0.83856 3.6742-2.5157h4.9983c-0.50755 1.9861-1.5116 3.5639-3.0122 4.7335-1.5006 1.1696-3.3763 1.7544-5.6272 1.7544-1.7654 0-3.3322-0.3917-4.7004-1.1751-1.3682-0.78339-2.4384-1.8868-3.2108-3.3101-0.77236-1.4233-1.1585-3.0839-1.1585-4.9817zm38.662-9.169-11.354 27.01h-4.9321l3.9721-9.1359-7.3484-17.875h5.1969l4.7335 12.81 4.7997-12.81h4.9321zm7.1167 2.6481c0.59582-0.83856 1.4178-1.5392 2.466-2.1019 1.0482-0.56272 2.2454-0.84408 3.5915-0.84408 1.5668 0 2.9846 0.38618 4.2535 1.1585 1.2689 0.77236 2.2729 1.8702 3.0122 3.2936s1.1089 3.0729 1.1089 4.9486-0.36963 3.5363-1.1089 4.9817-1.7433 2.5653-3.0122 3.3598-2.6867 1.1916-4.2535 1.1916c-1.3461 0-2.5322-0.27584-3.5584-0.82753-1.0261-0.55168-1.8592-1.2468-2.4991-2.0854v11.354h-4.6342v-27.077h4.6342v2.6481zm9.6986 6.4547c0-1.1034-0.22619-2.0578-0.67857-2.8632-0.45238-0.80546-1.0482-1.4178-1.7875-1.8371-0.73926-0.41928-1.5392-0.62892-2.3998-0.62892-0.83856 0-1.6275 0.21516-2.3667 0.64547-0.73926 0.43031-1.3351 1.0537-1.7875 1.8702-0.45238 0.81649-0.67857 1.7764-0.67857 2.8798s0.22619 2.0633 0.67857 2.8798c0.45238 0.81649 1.0482 1.4399 1.7875 1.8702 0.73926 0.43031 1.5282 0.64547 2.3667 0.64547 0.86063 0 1.6606-0.22067 2.3998-0.66202 0.73926-0.44135 1.3351-1.0703 1.7875-1.8868 0.45238-0.81649 0.67857-1.7875 0.67857-2.9129zm18.437-9.3676c1.3902 0 2.626 0.30343 3.7073 0.91028 1.0813 0.60685 1.9254 1.5006 2.5322 2.6812 0.60685 1.1806 0.91028 2.5984 0.91028 4.2535v10.758h-4.6342v-10.129c0-1.4564-0.36411-2.5764-1.0923-3.3598-0.72822-0.78339-1.7213-1.1751-2.9791-1.1751-1.2799 0-2.2895 0.3917-3.0288 1.1751-0.73926 0.78339-1.1089 1.9033-1.1089 3.3598v10.129h-4.6342v-24.495h4.6342v8.4408c0.59582-0.79443 1.3902-1.4178 2.3833-1.8702 0.99303-0.45238 2.0964-0.67857 3.3101-0.67857zm28.599 9.0366c0 0.66202-0.044135 1.2578-0.1324 1.7875h-13.406c0.11034 1.324 0.57375 2.3612 1.3902 3.1115 0.81649 0.75029 1.8206 1.1254 3.0122 1.1254 1.7213 0 2.946-0.73926 3.6742-2.2178h4.9983c-0.52962 1.7654-1.5447 3.2163-3.0453 4.3528-1.5006 1.1365-3.3432 1.7047-5.5279 1.7047-1.7654 0-3.3487-0.3917-4.75-1.1751-1.4013-0.78339-2.4936-1.8923-3.277-3.3267-0.78339-1.4344-1.1751-3.0894-1.1751-4.9652 0-1.8978 0.38618-3.5639 1.1585-4.9983 0.77236-1.4344 1.8537-2.5377 3.2439-3.3101 1.3902-0.77236 2.9901-1.1585 4.7997-1.1585 1.7433 0 3.3046 0.37515 4.6838 1.1254 1.3792 0.75029 2.4495 1.815 3.2108 3.1943s1.142 2.9625 1.142 4.75zm-4.7997-1.324c-0.022067-1.1916-0.45238-2.1461-1.2909-2.8632s-1.8647-1.0758-3.0784-1.0758c-1.1475 0-2.113 0.34756-2.8963 1.0427-0.78339 0.69512-1.2634 1.6606-1.4399 2.8963h8.7056zm12.81-4.6011c0.59582-0.97097 1.3737-1.7323 2.3336-2.284 0.95993-0.55168 2.0578-0.82753 3.2936-0.82753v4.8659h-1.2247c-1.4564 0-2.5543 0.34204-3.2936 1.0261-0.73926 0.68409-1.1089 1.8757-1.1089 3.5749v9.1359h-4.6342v-18.338h4.6342v2.8467z" fill="#0C5BE2"></path>
                            <path d="m28.009 7.6387h-8.9118c-1.4068 0-2.5462-1.1394-2.5462-2.5462v-2.5462c0-1.4068 1.1394-2.5462 2.5462-2.5462h6.3656c2.8123 0 5.0925 2.2802 5.0925 5.0925 0 1.4068-1.1394 2.5462-2.5462 2.5462z" fill="#E65100"></path>
                            <path d="m8.9118 10.185v-5.0925c0-1.4068-1.1394-2.5462-2.5462-2.5462h-1.2731c-2.8123 0-5.0925 2.2802-5.0925 5.0925v2.5462h8.9118zm1.2731 20.37h10.185v-7.6387h-10.185v7.6387zm12.731-7.6387v7.6387h2.5462c2.8123 0 5.0925-2.2802 5.0925-5.0925v-2.5462h-7.6387zm-15.277 0h-7.6387v2.5462c0 2.8123 2.2802 5.0925 5.0925 5.0925h2.5462v-7.6387zm6.3656-10.185h-14.004v7.6387h14.004v-7.6387zm2.5462 0v7.6387h14.004v-5.0925c0-1.4068-1.1394-2.5462-2.5462-2.5462h-11.458z" fill="#0C5BE2"></path>
                        </svg>
                    </div>
                </div>

                <!-- Welcome Text -->
                <div class="text-center mb-8">
                    <h2 class="text-2xl font-bold text-gray-900 mb-2">Welcome back!</h2>
                    <p class="text-gray-600">Please sign in to your account</p>
                </div>

                <?php if ($error): ?>
                <div class="mb-4 p-4 text-sm text-red-800 rounded-lg bg-red-50" role="alert">
                    <?php echo $error; ?>
                </div>
                <?php endif; ?>

                <!-- Login Form -->
                <form id="loginForm" class="space-y-5" method="POST" action="">
                    <!-- Email -->
                    <div>
                        <label for="login-email" class="block text-sm font-medium text-gray-700 mb-1">Email</label>
                        <div class="relative">
                            <input type="email" id="login-email" name="email" required
                                class="input-focus block w-full px-4 py-2.5 border border-gray-300 rounded-lg text-gray-900 placeholder-gray-400 focus:ring-2 focus:ring-[#1375fd] focus:border-[#1375fd]"
                                placeholder="Enter your email">
                        </div>
                        <p class="mt-1 text-sm text-red-600 hidden" id="email-error"></p>
                    </div>

                    <!-- Password -->
                    <div>
                        <label for="login-password" class="block text-sm font-medium text-gray-700 mb-1">Password</label>
                        <div class="relative">
                            <input type="password" id="login-password" name="password" required
                                class="input-focus block w-full px-4 py-2.5 border border-gray-300 rounded-lg text-gray-900 placeholder-gray-400 focus:ring-2 focus:ring-[#1375fd] focus:border-[#1375fd]"
                                placeholder="Enter your password">
                            <button type="button" class="absolute inset-y-0 right-0 pr-3 flex items-center" onclick="togglePasswordVisibility('login-password')">
                                <svg class="h-5 w-5 text-gray-400 hover:text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                </svg>
                            </button>
                        </div>
                        <p class="mt-1 text-sm text-red-600 hidden" id="password-error"></p>
                    </div>
                    
                    <!-- User Type (Admin or Regular User) -->
                    <div class="flex items-center space-x-4">
                        <div class="flex items-center">
                            <input type="radio" id="user-type-regular" name="is_admin" value="0"
                                class="h-4 w-4 text-[#0C5BE2] focus:ring-[#1375fd] border-gray-300">
                            <label for="user-type-regular" class="ml-2 block text-sm text-gray-700">Regular User</label>
                        </div>
                        <div class="flex items-center">
                            <input type="radio" id="user-type-admin" name="is_admin" value="1"
                                class="h-4 w-4 text-[#0C5BE2] focus:ring-[#1375fd] border-gray-300" checked>
                            <label for="user-type-admin" class="ml-2 block text-sm text-gray-700">Admin</label>
                        </div>
                    </div>

                    <!-- Remember Me & Forgot Password -->
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <input type="checkbox" id="remember-me" name="remember-me"
                                class="h-4 w-4 text-[#0C5BE2] focus:ring-[#1375fd] border-gray-300 rounded">
                            <label for="remember-me" class="ml-2 block text-sm text-gray-700">Remember me</label>
                        </div>
                        <button type="button" onclick="showForgotPassword()" 
                            class="text-sm font-medium text-[#0C5BE2] hover:text-[#1375fd]">
                            Forgot password?
                        </button>
                    </div>

                    <!-- Login Button -->
                    <button type="submit"
                        class="btn-primary w-full flex justify-center py-2.5 px-4 border border-transparent rounded-lg text-sm font-medium text-white bg-[#0C5BE2] hover:bg-[#114bbc] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#1375fd]">
                        Sign in
                    </button>
                    
                    <!-- Register Link -->
                    <div class="text-center mt-4">
                        <p class="text-sm text-gray-600">
                            Don't have an account? <a href="<?php echo BASE_URL; ?>/register" class="text-[#0C5BE2] hover:underline font-medium">Sign up</a>
                        </p>
                    </div>
                </form>
            </div>

            <!-- Forgot Password Card -->
            <div id="forgotCard" class="bg-white rounded-xl shadow-lg p-8 <?php if (!$forgotSuccess): ?>hidden<?php else: ?>slide-in<?php endif; ?>">
                <!-- Back Button -->
                <div class="mb-6">
                    <button onclick="showLoginCard()" 
                        class="inline-flex items-center text-sm font-medium text-[#0C5BE2] hover:text-[#1375fd]">
                        <svg class="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                        </svg>
                        Back to login
                    </button>
                </div>

                <!-- Title -->
                <div class="text-center mb-8">
                    <h2 class="text-2xl font-bold text-gray-900 mb-2">Forgot Password?</h2>
                    <p class="text-gray-600">Enter your email to reset your password</p>
                </div>

                <?php if ($forgotSuccess): ?>
                <div class="mb-4 p-4 text-sm text-green-800 rounded-lg bg-green-50" role="alert">
                    <?php echo $forgotSuccess; ?>
                </div>
                <div class="text-center mt-4">
                    <button onclick="showLoginCard()" 
                        class="btn-primary py-2.5 px-4 border border-transparent rounded-lg text-sm font-medium text-white bg-[#0C5BE2] hover:bg-[#114bbc] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#1375fd]">
                        Return to Login
                    </button>
                </div>
                <?php else: ?>
                
                <!-- Forgot Password Form -->
                <form id="forgotForm" class="space-y-5" method="POST" action="">
                    <input type="hidden" name="action" value="forgot_password">
                    <div>
                        <label for="forgot-email" class="block text-sm font-medium text-gray-700 mb-1">Email address</label>
                        <div class="relative">
                            <input type="email" id="forgot-email" name="email" required
                                class="input-focus block w-full px-4 py-2.5 border border-gray-300 rounded-lg text-gray-900 placeholder-gray-400 focus:ring-2 focus:ring-[#1375fd] focus:border-[#1375fd]"
                                placeholder="Enter your email">
                        </div>
                        <p class="mt-1 text-sm text-red-600 hidden" id="forgot-email-error"></p>
                    </div>

                    <button type="submit"
                        class="btn-primary w-full flex justify-center py-2.5 px-4 border border-transparent rounded-lg text-sm font-medium text-white bg-[#0C5BE2] hover:bg-[#114bbc] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#1375fd]">
                        Send Reset Link
                    </button>
                </form>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/flowbite/2.2.0/flowbite.min.js"></script>
    <script>
    // Card Management
    function showLoginCard() {
        const loginCard = document.getElementById('loginCard');
        const forgotCard = document.getElementById('forgotCard');
    
        forgotCard.classList.add('hidden');
        loginCard.classList.remove('hidden');
        loginCard.classList.add('slide-in');
    
        // Clear forgot password form
        document.getElementById('forgotForm').reset();
    }
    
    function showForgotPassword() {
        const loginCard = document.getElementById('loginCard');
        const forgotCard = document.getElementById('forgotCard');
    
        loginCard.classList.add('hidden');
        forgotCard.classList.remove('hidden');
        forgotCard.classList.add('slide-in');
    
        // Clear login form
        document.getElementById('loginForm').reset();
    }
    
    // Password Visibility Toggle
    function togglePasswordVisibility(inputId) {
        const input = document.getElementById(inputId);
        const type = input.getAttribute('type') === 'password' ? 'text' : 'password';
        input.setAttribute('type', type);
    
        const button = input.nextElementSibling;
        const svg = button.querySelector('svg');
        if (type === 'password') {
            svg.innerHTML = '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" /><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />';
        } else {
            svg.innerHTML = '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.88 9.88l-3.29-3.29m7.532 7.532l3.29 3.29M3 3l3.59 3.59m0 0A9.953 9.953 0 0112 5c4.478 0 8.268 2.943 9.543 7a10.025 10.025 0 01-4.132 5.411m0 0L21 21" />';
        }
    }
    
    // Form validation for login
    document.getElementById('loginForm').addEventListener('submit', function(event) {
        let isValid = true;
        
        // Reset errors
        document.querySelectorAll('.text-red-600').forEach(el => el.classList.add('hidden'));
        
        // Validate email
        const email = document.getElementById('login-email');
        if (email.value.trim() === '') {
            document.getElementById('email-error').textContent = 'Email is required';
            document.getElementById('email-error').classList.remove('hidden');
            isValid = false;
        } else if (!/^\S+@\S+\.\S+$/.test(email.value)) {
            document.getElementById('email-error').textContent = 'Please enter a valid email address';
            document.getElementById('email-error').classList.remove('hidden');
            isValid = false;
        }
        
        // Validate password
        const password = document.getElementById('login-password');
        if (password.value.trim() === '') {
            document.getElementById('password-error').textContent = 'Password is required';
            document.getElementById('password-error').classList.remove('hidden');
            isValid = false;
        }
        
        if (!isValid) {
            event.preventDefault();
        }
    });
    
    // Form validation for forgot password
    const forgotForm = document.getElementById('forgotForm');
    if (forgotForm) {
        forgotForm.addEventListener('submit', function(event) {
            let isValid = true;
            
            // Reset errors
            document.querySelectorAll('.text-red-600').forEach(el => el.classList.add('hidden'));
            
            // Validate email
            const email = document.getElementById('forgot-email');
            if (email.value.trim() === '') {
                document.getElementById('forgot-email-error').textContent = 'Email is required';
                document.getElementById('forgot-email-error').classList.remove('hidden');
                isValid = false;
            } else if (!/^\S+@\S+\.\S+$/.test(email.value)) {
                document.getElementById('forgot-email-error').textContent = 'Please enter a valid email address';
                document.getElementById('forgot-email-error').classList.remove('hidden');
                isValid = false;
            }
            
            if (!isValid) {
                event.preventDefault();
            }
        });
    }
    
    // Initialize
    document.addEventListener('DOMContentLoaded', function() {
        // Clear any existing form data
        <?php if (!$forgotSuccess): ?>
        document.getElementById('loginForm').reset();
        document.getElementById('forgotForm')?.reset();
        <?php endif; ?>
    });
    </script>
</body>
</html>