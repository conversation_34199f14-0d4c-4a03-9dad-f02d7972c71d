<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/PreviewHelper.php';

// Get parameters
$type = $_GET['type'] ?? 'page'; // default to page
$id = $_GET['id'] ?? null;
$previewMode = isset($_GET['preview']) && $_GET['preview'] === 'true';
$pageSlug = $_GET['page'] ?? null;

// Validate required parameters
if (!$id) {
    header("HTTP/1.0 400 Bad Request");
    exit("ID is required");
}

// Set options based on parameters
$options = [
    'preview_mode' => $previewMode,
    'hide_controls' => $previewMode,
    'page_slug' => $pageSlug,
    'is_template_page' => ($type === 'template') && !$pageSlug // Show as template page only if no specific page slug
];

// Render preview
$html = PreviewHelper::renderPreview($type, $id, $options);

// Handle errors
if (isset($html['error'])) {
    header("HTTP/1.0 404 Not Found");
    ?>
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Preview Error</title>
        <script src="https://cdn.tailwindcss.com"></script>
    </head>
    <body class="bg-gray-100">
        <div class="flex items-center justify-center min-h-screen">
            <div class="text-center">
                <div class="inline-flex items-center justify-center w-16 h-16 rounded-full bg-red-100 mb-4">
                    <svg class="w-8 h-8 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.864-.833-2.634 0L4.183 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                    </svg>
                </div>
                <h1 class="text-2xl font-bold text-gray-900 mb-2">Preview Error</h1>
                <p class="text-gray-600"><?php echo htmlspecialchars($html['error']); ?></p>
                <button onclick="history.back()" class="mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
                    Go Back
                </button>
            </div>
        </div>
    </body>
    </html>
    <?php
    exit;
}

// Output the generated HTML
echo $html;
?>