<?php
// Check if session is not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in
if (!isset($_SESSION['token'])) {
    header("Location: " . BASE_URL . "/login");
    exit;
}

// Define the absolute path to the root directory
define('ROOT_PATH', dirname(dirname(dirname(__FILE__))));

require_once ROOT_PATH . '/config/config.php';

// Set page title and breadcrumbs
$pageTitle = 'Dashboard';
$breadcrumbs = [
    [
        'label' => 'Dashboard',
        'url' => BASE_URL . '/dashboard'
    ]
];

// Start output buffering
ob_start();
?>

<!-- Dashboard Content -->
<div class="p-6">
    <!-- Welcome Section -->
    <div class="mb-8">
        <h1 class="text-2xl font-semibold text-gray-900">Welcome to CMS Dashboard</h1>
        <p class="mt-2 text-gray-600">Here's an overview of your content management system.</p>
    </div>

    <!-- Stats Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
        <!-- Templates Stats -->
        <div class="p-4 bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600">Total Templates</p>
                    <h3 class="text-2xl font-bold text-[#0C5BE2]">25</h3>
                </div>
                <div class="p-2 bg-[#0C5BE2]/10 rounded-lg">
                    <i data-lucide="layout-template" class="w-6 h-6 text-[#0C5BE2]"></i>
                </div>
            </div>
        </div>

        <!-- Blocks Stats -->
        <div class="p-4 bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600">Total Blocks</p>
                    <h3 class="text-2xl font-bold text-[#3CBF61]">42</h3>
                </div>
                <div class="p-2 bg-[#3CBF61]/10 rounded-lg">
                    <i data-lucide="box" class="w-6 h-6 text-[#3CBF61]"></i>
                </div>
            </div>
        </div>

        <!-- Pages Stats -->
        <div class="p-4 bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600">Total Pages</p>
                    <h3 class="text-2xl font-bold text-[#F69964]">18</h3>
                </div>
                <div class="p-2 bg-[#F69964]/10 rounded-lg">
                    <i data-lucide="file-text" class="w-6 h-6 text-[#F69964]"></i>
                </div>
            </div>
        </div>

        <!-- Users Stats -->
        <div class="p-4 bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600">Active Users</p>
                    <h3 class="text-2xl font-bold text-[#3C58BF]">8</h3>
                </div>
                <div class="p-2 bg-[#3C58BF]/10 rounded-lg">
                    <i data-lucide="users" class="w-6 h-6 text-[#3C58BF]"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activity Section -->
    <div class="mb-8">
        <h2 class="text-xl font-semibold text-gray-900 mb-4">Recent Activity</h2>
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="p-4">
                <div class="space-y-4">
                    <!-- Activity Item -->
                    <div class="flex items-center">
                        <div class="p-2 bg-[#0C5BE2]/10 rounded-lg mr-4">
                            <i data-lucide="layout-template" class="w-5 h-5 text-[#0C5BE2]"></i>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-900">New template created</p>
                            <p class="text-xs text-gray-500">2 hours ago</p>
                        </div>
                    </div>
                    <!-- Activity Item -->
                    <div class="flex items-center">
                        <div class="p-2 bg-[#3CBF61]/10 rounded-lg mr-4">
                            <i data-lucide="box" class="w-5 h-5 text-[#3CBF61]"></i>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-900">Block updated</p>
                            <p class="text-xs text-gray-500">5 hours ago</p>
                        </div>
                    </div>
                    <!-- Activity Item -->
                    <div class="flex items-center">
                        <div class="p-2 bg-[#F69964]/10 rounded-lg mr-4">
                            <i data-lucide="file-text" class="w-5 h-5 text-[#F69964]"></i>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-900">New page published</p>
                            <p class="text-xs text-gray-500">1 day ago</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>

<?php
$content = ob_get_clean();
require_once ROOT_PATH . '/views/layouts/main.php';
?>
