<?php
require_once __DIR__ . '/../../config/config.php';
require_once __DIR__ . '/../../modules/templates/Templates.php';
require_once __DIR__ . '/../../modules/pages/Pages.php';
require_once __DIR__ . '/../../modules/blocks/Blocks.php';

$templates = new Templates();
$pages = new Pages();
$blocks = new Blocks();
$error = null;
$success = isset($_GET['success']) ? $_GET['success'] : null;

// Set page title and breadcrumbs
$pageTitle = 'Template Builder';
$breadcrumbs = [
    [
        'label' => 'Templates',
        'url' => BASE_URL . '/templates'
    ],
    [
        'label' => 'Template Builder',
        'url' => '#'
    ]
];

// Get template ID from URL
$templateId = isset($_GET['id']) ? $_GET['id'] : null;
$templateData = null;
$allTemplates = [];
$templatePages = [];

// Get all templates for the dropdown
try {
    $response = $templates->getList();
    if (isset($response['data']['data']) && is_array($response['data']['data'])) {
        $allTemplates = $response['data']['data'];
    }
} catch (Exception $e) {
    $error = $e->getMessage();
}

// Get the current template data if ID is provided
if ($templateId) {
    try {
        // Use the new method to get template with pages
        $response = $templates->get($templateId);
        if (isset($response['data'])) {
            $templateData = $response['data'];
            
            // Extract pages from template data if available
            if (isset($templateData['pages']) && is_array($templateData['pages'])) {
                $templatePages = $templateData['pages'];
            }
        } else {
            $error = 'Template not found';
        }
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
    
    // Fallback: Get pages using Pages module if not in template data
    if (empty($templatePages)) {
        try {
            $pagesResponse = $pages->getList(['template_id' => $templateId]);
            if (isset($pagesResponse['data']['data']) && is_array($pagesResponse['data']['data'])) {
                $templatePages = $pagesResponse['data']['data'];
            }
        } catch (Exception $e) {
            error_log('Error loading template pages: ' . $e->getMessage());
        }
    }
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (isset($_POST['save_draft'])) {
            $data = [
                'template_status' => 0 // Draft
            ];
            $templates->update($templateId, $data);
            header("Location: " . BASE_URL . "/templates/builder?id=" . $templateId . "&success=Template saved as draft");
            exit;
        } 
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// Get global blocks for the sidebar
$globalBlockList = '';
try {
    $response = $blocks->getList();
    if (isset($response['success']) && $response['success'] === true) {
        $blocksData = [];
        if (isset($response['data']['data']) && is_array($response['data']['data'])) {
            $blocksData = $response['data']['data'];
        } elseif (isset($response['data']) && is_array($response['data'])) {
            $blocksData = $response['data'];
        }
        
        ob_start();
        
        if (empty($blocksData)) {
            echo '<div class="text-center py-4">';
            echo '<p class="text-gray-500">No blocks found</p>';
            echo '</div>';
        } else {
            foreach ($blocksData as $block) {
                if (!isset($block['id']) || !isset($block['name'])) {
                    continue;
                }
                
                $blockType = isset($block['slug']) ? $block['slug'] : 'default-block';
                $blockName = isset($block['name']) ? $block['name'] : 'Unnamed Block';
                $blockImage = isset($block['image']) ? BASE_URL . $block['image'] : 'https://placehold.co/600x300/e5e7eb/9ca3af?text=' . urlencode($blockName);
                $blockDesc = isset($block['description']) ? $block['description'] : '';
                ?>
                <div class="block-item cursor-move bg-white border border-gray-200 rounded-lg mb-2 hover:border-blue-500 hover:shadow-sm overflow-hidden" 
                draggable="true" 
                data-block-type="<?php echo htmlspecialchars($blockType); ?>" 
                data-block-id="<?php echo htmlspecialchars($block['id']); ?>">
                    <div class="flex items-start relative">
                        <img src="<?php echo htmlspecialchars($blockImage); ?>" alt="Block Image">
                        <p class="hidden"><?php echo htmlspecialchars($blockDesc); ?></p>
                    </div>
                    <h3 class="text-sm font-medium text-gray-900 w-full px-3 py-2 bg-gradient-to-t from-[#f7f7f7] to-white border-t border-gray-100"><?php echo htmlspecialchars($blockName); ?></h3>
                </div>
                <?php
            }
        }
        
        $globalBlockList = ob_get_clean();
        
    } else {
        ob_start();
        echo '<div class="text-center py-4 mb-4">';
        echo '<p class="text-red-500">Failed to load blocks from API.</p>';
        echo '</div>';
        $globalBlockList = ob_get_clean();
    }
} catch (Exception $e) {
    ob_start();
    echo '<div class="text-center py-4">';
    echo '<p class="text-red-500">Error: ' . htmlspecialchars($e->getMessage()) . '</p>';
    echo '</div>';
    $globalBlockList = ob_get_clean();
}

// Start output buffering
ob_start();
?>
<div class="container-fluid">
    <?php if ($error): ?>
    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
        <p><?php echo htmlspecialchars($error); ?></p>
    </div>
    <?php endif; ?>

    <?php if ($success): ?>
    <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
        <p><?php echo htmlspecialchars($success); ?></p>
    </div>
    <?php endif; ?>
    
    <!-- Template Builder Header -->
    <div class="border-b border-gray-200 p-3">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="flex items-center space-x-2">
                    <label for="templateSelector" class="text-xs font-medium text-gray-700">Template:</label>
                    <select id="templateSelector" class="text-xs bg-white border border-gray-300 text-gray-900 rounded focus:ring-blue-500 focus:border-blue-500 p-1.5">
                        <option value="">Select a template</option>
                        <?php foreach ($allTemplates as $template): ?>
                            <option value="<?php echo htmlspecialchars($template['id']); ?>" <?php echo ($templateId == $template['id']) ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($template['name']); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <div class="flex items-center space-x-2">
                    <label for="pageSelector" class="text-xs font-medium text-gray-700">Page:</label>
                    <select id="pageSelector" class="text-xs bg-white border border-gray-300 text-gray-900 rounded focus:ring-blue-500 focus:border-blue-500 p-1.5" disabled>
                        <option value="">Select a template first</option>
                    </select>
                </div>
            </div>
            
            <div class="flex items-center space-x-2">
                <button id="createPageBtnHeader" disabled class="text-xs text-white bg-green-500 px-2 py-1.5 rounded hover:bg-green-600 opacity-50 cursor-not-allowed flex items-center gap-1">
                    <svg class="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    Create Page
                </button>
                
                <button id="previewBtn" class="text-xs text-white bg-blue-600 px-2 py-1.5 rounded hover:bg-blue-700 flex items-center gap-1">
                    <svg class="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                    </svg>
                    Preview
                </button>
                
                <button id="publishBtn" class="text-xs text-white bg-emerald-500 px-2 py-1.5 rounded hover:bg-emerald-600 flex items-center gap-1">
                    <svg class="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    Publish
                </button>
            </div>
        </div>
    </div>

    <!-- Template Builder Body -->
    <div class="flex h-[calc(100vh-120px)]">
        <!-- Left Sidebar -->
        <div class="w-80 bg-gray-50 border-r border-gray-200 flex flex-col">
            <!-- Tabs -->
            <div class="border-b border-gray-200 bg-white">
                <nav class="-mb-px flex">
                    <button class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-xs tab-button active" data-tab="blocks">
                        Blocks
                    </button>
                    <button class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-xs tab-button ml-8" data-tab="pages">
                        Pages
                    </button>
                    <button class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-xs tab-button ml-8" data-tab="settings">
                        Settings
                    </button>
                </nav>
            </div>

            <!-- Tab Content -->
            <div class="flex-1 overflow-hidden">
                <!-- Blocks Tab -->
                <div class="h-full" id="blocks" role="tabpanel" aria-labelledby="blocks-tab">
                    <div class="p-3 border-b border-gray-200">
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                                <svg class="w-4 h-4 text-gray-500" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 20">
                                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z"/>
                                </svg>
                            </div>
                            <input type="text" id="blockSearch" class="block w-full p-2 pl-10 text-sm text-gray-900 border border-gray-300 rounded-lg bg-gray-50 focus:ring-blue-500 focus:border-blue-500" placeholder="Search blocks...">
                        </div>
                    </div>

                    <div class="flex-1 pl-3">
                        <div class="mb-4 h-[calc(100vh-183px)] overflow-auto pr-3">
                            <?php echo $globalBlockList; ?>
                        </div>
                    </div>
                </div>
                
                <!-- Pages Tab -->
                <div class="hidden" id="pages" role="tabpanel" aria-labelledby="pages-tab">
                    <div class="flex-1 pl-3">
                        <div class="mb-4 h-[calc(100vh-121px)] overflow-auto pr-3 pt-3">
                            <div id="pagesContent">
                                <div class="text-center py-8">
                                    <div class="inline-flex items-center justify-center w-12 h-12 rounded-full bg-gray-100 mb-3">
                                        <svg class="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                        </svg>
                                    </div>
                                    <p class="text-sm text-gray-500">Select a template to view pages</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Settings Tab -->
                <div class="hidden flex-1 flex flex-col custom-tabpanel" id="settings" role="tabpanel" aria-labelledby="settings-tab">
                    <div class="flex-1 pl-3">
                        <div class="mb-4 h-[calc(100vh-121px)] overflow-auto pr-3 pt-3">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Template Settings</h3>
                            <div class="space-y-4">
                                <div>
                                    <label for="templateStatus" class="block text-xs font-medium text-gray-700 mb-1">Template Status</label>
                                    <select id="templateStatus" class="bg-gray-50 border border-gray-300 text-gray-900 text-xs rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5">
                                        <option value="0">Draft</option>
                                        <option value="1">Published</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Canvas Area -->
        <div class="flex-1 bg-gray-100">
            <div id="canvasContainer" class="w-full h-full">
                <div class="flex items-center justify-center h-full">
                    <div class="text-center">
                        <div class="inline-flex items-center justify-center w-16 h-16 rounded-full bg-gray-200 mb-4">
                            <svg class="w-8 h-8 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                        </div>
                        <p class="text-lg font-medium text-gray-900">Select a Template & Page</p>
                        <p class="text-sm text-gray-500">Choose a template and page to start building</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- Enhanced Create Page Modal with Previously Used Blocks -->
<div id="createPageModal" tabindex="-1" aria-hidden="true" class="fixed top-0 left-0 right-0 z-50 hidden w-full p-4 overflow-x-hidden overflow-y-auto md:inset-0 h-[calc(100%-1rem)] max-h-full bg-gray-900 bg-opacity-50">
    <div class="relative w-full max-w-4xl max-h-full mx-auto">
        <div class="relative bg-white rounded-lg shadow-xl">
            <!-- Modal header -->
            <div class="flex items-start justify-between p-4 border-b rounded-t">
                <div class="flex items-center">
                    <div class="flex items-center justify-center h-10 w-10 rounded-full bg-blue-50 text-blue-500 mr-3">
                        <svg class="h-5 w-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
                            <path d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z"/>
                            <path d="M14 2v4a2 2 0 0 0 2 2h4"/>
                        </svg>
                    </div>
                    <div>
                        <h3 class="text-lg font-medium text-gray-900">Create New Page</h3>
                        <p class="text-sm text-gray-500">Create a new page and reuse your previously used blocks</p>
                    </div>
                </div>
                <button type="button" class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm p-1.5 ml-auto inline-flex items-center" data-modal-hide="createPageModal">
                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                    </svg>
                </button>
            </div>
            
            <!-- Modal body -->
            <form id="createPageForm" class="p-6">
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <!-- Left Column - Page Details -->
                    <div class="space-y-4">
                        <h4 class="text-md font-medium text-gray-900 border-b pb-2">Page Information</h4>
                        
                        <div>
                            <label for="pageName" class="block text-sm font-medium text-gray-700 mb-1">
                                Page Name <span class="text-red-500">*</span>
                            </label>
                            <input type="text" id="pageName" name="name" required 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                   placeholder="Enter page name">
                        </div>
                        
                        <div>
                            <label for="pageSlug" class="block text-sm font-medium text-gray-700 mb-1">Page Slug</label>
                            <input type="text" id="pageSlug" name="slug" 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                   placeholder="page-slug">
                            <p class="text-xs text-gray-500 mt-1">Auto-generated from page name if empty</p>
                        </div>
                        
                        <div>
                            <label for="pageStatus" class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                            <select id="pageStatus" name="status" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                                <option value="0">Draft</option>
                                <option value="1">Published</option>
                            </select>
                        </div>
                        
                        <div>
                            <label for="pageDescription" class="block text-sm font-medium text-gray-700 mb-1">Description</label>
                            <textarea id="pageDescription" name="description" rows="3" 
                                      class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                      placeholder="Optional page description"></textarea>
                        </div>
                    </div>
                    
                    <!-- Right Column - Previously Used Blocks -->
                    <div class="space-y-4">
                        <div class="flex items-center justify-between border-b pb-2">
                            <h4 class="text-md font-medium text-gray-900">Previously Used Blocks</h4>
                            <div class="flex items-center space-x-2">
                                <button type="button" id="selectAllBlocks" class="text-xs px-2 py-1 bg-blue-100 text-blue-700 rounded hover:bg-blue-200">
                                    Select All
                                </button>
                                <button type="button" id="selectCommonBlocks" class="text-xs px-2 py-1 bg-green-100 text-green-700 rounded hover:bg-green-200">
                                    Header/Footer
                                </button>
                                <button type="button" id="clearSelection" class="text-xs px-2 py-1 bg-gray-100 text-gray-700 rounded hover:bg-gray-200">
                                    Clear
                                </button>
                            </div>
                        </div>
                        
                        <div id="previouslyUsedBlocksContainer" class="border border-gray-200 rounded-lg max-h-80 overflow-y-auto">
                            <div class="text-center py-8">
                                <div class="animate-spin w-6 h-6 border-2 border-blue-500 border-t-transparent rounded-full mx-auto mb-2"></div>
                                <p class="text-sm text-gray-500">Loading previously used blocks...</p>
                            </div>
                        </div>
                        
                        <div id="selectionSummary" class="hidden">
                            <div class="bg-blue-50 border border-blue-200 rounded-lg p-3">
                                <p class="text-sm text-blue-800">
                                    <span id="selectedCount">0</span> blocks selected for the new page
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Form Actions -->
                <div class="flex items-center justify-end space-x-3 pt-6 border-t mt-6">
                    <button type="button" id="cancelCreate" class="text-gray-500 bg-white hover:bg-gray-100 focus:ring-4 focus:ring-gray-300 rounded-lg border border-gray-200 text-sm font-medium px-5 py-2.5">
                        Cancel
                    </button>
                    <button type="submit" id="createPageSubmit" class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5">
                        <span class="create-btn-text">Create Page</span>
                        <span class="create-btn-loading hidden">
                            <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            Creating...
                        </span>
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
<script>
// Enhanced Template Builder with Previously Used Blocks Feature
const BASE_URL = '<?php echo BASE_URL; ?>';

// API Endpoints
const API_ENDPOINTS = {
    GET_PAGES_BY_TEMPLATE: BASE_URL + '/modules/pages/get_pages_by_template.php?template_id=',
    GET_PAGE_BLOCKS: BASE_URL + '/modules/pages_blocks/get_page_blocks.php?page_id=',
    CREATE_PAGE: BASE_URL + '/modules/pages/create.php',
    SAVE_BLOCK_TO_PAGE: BASE_URL + '/modules/pages_blocks/save_block_to_page.php',
    GET_BLOCKS: BASE_URL + '/modules/blocks/get.php'
};

// Global variables
let processingBlocks = {};
let currentTemplatePages = [];
let previouslyUsedBlocks = [];
let selectedBlocks = new Set();

// Store template pages globally for modal use
window.currentTemplatePages = <?php echo json_encode($templatePages); ?>;

// Tab functionality
document.addEventListener('DOMContentLoaded', function() {
    const tabButtons = document.querySelectorAll('.tab-button');
    const tabPanels = document.querySelectorAll('[role="tabpanel"]');

    tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            const targetTab = this.getAttribute('data-tab');
            
            // Remove active class from all buttons
            tabButtons.forEach(btn => {
                btn.classList.remove('active', 'border-blue-500', 'text-blue-600');
                btn.classList.add('border-transparent', 'text-gray-500');
            });
            
            // Add active class to clicked button
            this.classList.add('active', 'border-blue-500', 'text-blue-600');
            this.classList.remove('border-transparent', 'text-gray-500');
            
            // Hide all tab panels
            tabPanels.forEach(panel => {
                panel.classList.add('hidden');
            });
            
            // Show target tab panel
            const targetPanel = document.getElementById(targetTab);
            if (targetPanel) {
                targetPanel.classList.remove('hidden');
            }
        });
    });

    // Initialize functions
    setupBlockSearch();
    setupTemplatePageHandlers();
    setupCreatePageModal();
});

// Block search functionality
function setupBlockSearch() {
    const searchInput = document.getElementById('blockSearch');
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            const blockItems = document.querySelectorAll('.block-item');
            
            blockItems.forEach(item => {
                const blockName = item.querySelector('h3')?.textContent?.toLowerCase() || '';
                const blockDesc = item.querySelector('p')?.textContent?.toLowerCase() || '';
                
                if (blockName.includes(searchTerm) || blockDesc.includes(searchTerm)) {
                    item.style.display = 'block';
                } else {
                    item.style.display = 'none';
                }
            });
        });
    }
}

// Template and page handlers
function setupTemplatePageHandlers() {
    const templateSelector = document.getElementById('templateSelector');
    const pageSelector = document.getElementById('pageSelector');
    const createPageBtnHeader = document.getElementById('createPageBtnHeader');
    
    // Template selector change handler
    templateSelector.addEventListener('change', function() {
        const templateId = this.value;
        const canvasContainer = document.getElementById('canvasContainer');
        
        if (templateId) {
            // Show loading state
            pageSelector.innerHTML = '<option value="">Loading pages...</option>';
            pageSelector.disabled = true;
            
            canvasContainer.innerHTML = `
                <div class="flex items-center justify-center h-full">
                    <div class="text-center">
                        <div class="inline-flex items-center justify-center w-16 h-16 rounded-full bg-gray-200 mb-4">
                            <svg class="w-8 h-8 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                        </div>
                        <p class="text-lg font-medium text-gray-900">Loading template...</p>
                    </div>
                </div>
            `;
            
            // Fetch template details
            fetch(`${BASE_URL}/modules/templates/get_template.php?id=${templateId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.data) {
                        const templateData = data.data;
                        let pages = [];
                        
                        if (templateData.pages && Array.isArray(templateData.pages)) {
                            pages = templateData.pages;
                        } else {
                            return fetch(`${API_ENDPOINTS.GET_PAGES_BY_TEMPLATE}${templateId}`)
                                .then(pagesResponse => pagesResponse.json())
                                .then(pagesData => {
                                    if (pagesData.success && pagesData.data) {
                                        pages = Array.isArray(pagesData.data.data) ? pagesData.data.data : pagesData.data;
                                    }
                                    return { templateData, pages };
                                });
                        }
                        
                        return { templateData, pages };
                    } else {
                        throw new Error(data.message || 'Failed to load template');
                    }
                })
                .then(({ templateData, pages }) => {
                    currentTemplatePages = pages;
                    window.currentTemplatePages = pages;
                    
                    // Populate page selector
                    pageSelector.innerHTML = '<option value="">Select a page</option>';
                    
                    if (pages && pages.length > 0) {
                        let defaultPageSelected = false;
                        
                        pages.forEach(page => {
                            const option = document.createElement('option');
                            option.value = page.slug || page.id;
                            option.textContent = page.name || `Page ${page.id}`;
                            option.setAttribute('data-page-id', page.id);
                            option.setAttribute('data-page-slug', page.slug || 'home');
                            
                            const isDefaultPage = page.slug === 'home' || page.is_default || !defaultPageSelected;
                            if (isDefaultPage) {
                                option.textContent += ' ★';
                                option.selected = true;
                                defaultPageSelected = true;
                                setTimeout(() => {
                                    loadTemplatePage(templateId, page.slug || 'home');
                                }, 100);
                            }
                            
                            pageSelector.appendChild(option);
                        });
                        
                        pageSelector.disabled = false;
                        updatePagesTabContent(pages);
                    } else {
                        pageSelector.innerHTML = '<option value="">No pages found</option>';
                    }
                })
                .catch(error => {
                    console.error('Error loading template:', error);
                    pageSelector.innerHTML = '<option value="">Error loading pages</option>';
                });
                
            // Update create page button
            if (createPageBtnHeader) {
                createPageBtnHeader.disabled = false;
                createPageBtnHeader.classList.remove('opacity-50', 'cursor-not-allowed');
            }
        } else {
            pageSelector.innerHTML = '<option value="">Select a template first</option>';
            pageSelector.disabled = true;
            currentTemplatePages = [];
            window.currentTemplatePages = [];
            
            if (createPageBtnHeader) {
                createPageBtnHeader.disabled = true;
                createPageBtnHeader.classList.add('opacity-50', 'cursor-not-allowed');
            }
        }
    });
    
    // Page selector change handler
    pageSelector.addEventListener('change', function() {
        const templateId = templateSelector.value;
        const pageSlug = this.value;
        
        if (templateId && pageSlug) {
            loadTemplatePage(templateId, pageSlug);
        }
    });
    
    // Preview button
    document.getElementById('previewBtn').addEventListener('click', function() {
        const templateId = templateSelector.value;
        const pageSlug = pageSelector.value;
        
        if (templateId) {
            let previewURL = `${BASE_URL}/views/preview.php?type=template&id=${templateId}&preview=true`;
            if (pageSlug) {
                previewURL += `&page=${pageSlug}`;
            }
            window.open(previewURL, '_blank');
        } else {
            alert('Please select a template first');
        }
    });
}

// Load template page
function loadTemplatePage(templateId, pageSlug) {
    const canvasContainer = document.getElementById('canvasContainer');
    
    if (templateId && pageSlug) {
        canvasContainer.innerHTML = `
            <iframe id="templateCanvas" 
                    src="${BASE_URL}/views/preview.php?type=template&id=${templateId}&page=${pageSlug}" 
                    class="w-full h-full border-0 transition-all duration-300">
            </iframe>
        `;
    }
}

// Update pages tab content
function updatePagesTabContent(pages) {
    const pagesContent = document.getElementById('pagesContent');
    
    if (!pages || pages.length === 0) {
        pagesContent.innerHTML = `
            <div class="text-center py-8">
                <div class="inline-flex items-center justify-center w-12 h-12 rounded-full bg-gray-100 mb-3">
                    <svg class="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                </div>
                <p class="text-sm text-gray-500">No pages available</p>
                <button class="mt-2 text-xs px-3 py-1.5 bg-blue-600 text-white rounded hover:bg-blue-700" onclick="showCreatePageModal()">
                    Create Page
                </button>
            </div>
        `;
        return;
    }
    
    let pagesHtml = '<div class="space-y-2">';
    pages.forEach(page => {
        const isDefault = page.slug === 'home' || page.is_default;
        pagesHtml += `
            <div class="p-3 bg-white border border-gray-200 rounded-lg hover:border-blue-300 cursor-pointer page-item" 
                 data-page-id="${page.id}" 
                 data-page-slug="${page.slug || 'home'}"
                 onclick="selectPageFromSidebar('${page.slug || 'home'}')">
                <div class="flex items-center justify-between">
                    <div>
                        <h4 class="text-sm font-medium text-gray-900">${page.name || 'Untitled Page'} ${isDefault ? '★' : ''}</h4>
                        <p class="text-xs text-gray-500">/${page.slug || 'home'}</p>
                    </div>
                    <div class="flex items-center space-x-1">
                        <span class="px-2 py-1 text-xs rounded-full ${page.status === '1' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}">
                            ${page.status === '1' ? 'Published' : 'Draft'}
                        </span>
                    </div>
                </div>
            </div>
        `;
    });
    pagesHtml += '</div>';
    pagesContent.innerHTML = pagesHtml;
}

// Select page from sidebar
function selectPageFromSidebar(pageSlug) {
    const templateId = document.getElementById('templateSelector').value;
    const pageSelector = document.getElementById('pageSelector');
    
    if (templateId && pageSlug) {
        pageSelector.value = pageSlug;
        loadTemplatePage(templateId, pageSlug);
    }
}
// Create Page Modal Setup
function setupCreatePageModal() {
    // Auto-generate slug from page name
    document.getElementById('pageName').addEventListener('input', function() {
        const slug = this.value.toLowerCase()
            .replace(/[^a-z0-9 -]/g, '')
            .replace(/\s+/g, '-')
            .replace(/-+/g, '-')
            .replace(/^-|-$/g, '');
        document.getElementById('pageSlug').value = slug;
    });
    
    // Block selection controls
    document.getElementById('selectAllBlocks').addEventListener('click', function() {
        const checkboxes = document.querySelectorAll('.block-checkbox');
        selectedBlocks.clear();
        checkboxes.forEach(checkbox => {
            checkbox.checked = true;
            selectedBlocks.add(checkbox.value);
        });
        updateSelectionSummary();
    });

    document.getElementById('selectCommonBlocks').addEventListener('click', function() {
        const checkboxes = document.querySelectorAll('.block-checkbox');
        selectedBlocks.clear();
        checkboxes.forEach(checkbox => checkbox.checked = false);
        
        const commonCheckboxes = document.querySelectorAll('.block-checkbox[data-common="true"]');
        commonCheckboxes.forEach(checkbox => {
            checkbox.checked = true;
            selectedBlocks.add(checkbox.value);
        });
        updateSelectionSummary();
    });

    document.getElementById('clearSelection').addEventListener('click', function() {
        const checkboxes = document.querySelectorAll('.block-checkbox');
        selectedBlocks.clear();
        checkboxes.forEach(checkbox => checkbox.checked = false);
        updateSelectionSummary();
    });
    
    // Form submission
    document.getElementById('createPageForm').addEventListener('submit', handleCreatePageSubmission);
    
    // Cancel button
    document.getElementById('cancelCreate').addEventListener('click', function() {
        document.getElementById('createPageModal').classList.add('hidden');
        resetCreatePageForm();
    });
    
    // Create page button handler
    const createPageBtnHeader = document.getElementById('createPageBtnHeader');
    if (createPageBtnHeader) {
        createPageBtnHeader.addEventListener('click', function(e) {
            e.preventDefault();
            showCreatePageModal();
        });
    }
}

// Show create page modal
function showCreatePageModal() {
    const templateId = document.getElementById('templateSelector').value;
    
    if (!templateId) {
        alert('Please select a template first');
        return;
    }
    
    resetCreatePageForm();
    loadPreviouslyUsedBlocksFromExistingAPI(templateId);
    document.getElementById('createPageModal').classList.remove('hidden');
}

// Reset create page form
function resetCreatePageForm() {
    document.getElementById('createPageForm').reset();
    selectedBlocks.clear();
    updateSelectionSummary();
}

// Load previously used blocks from existing API
async function loadPreviouslyUsedBlocksFromExistingAPI(templateId) {
    const container = document.getElementById('previouslyUsedBlocksContainer');
    
    container.innerHTML = `
        <div class="text-center py-8">
            <div class="animate-spin w-6 h-6 border-2 border-blue-500 border-t-transparent rounded-full mx-auto mb-2"></div>
            <p class="text-sm text-gray-500">Loading previously used blocks...</p>
        </div>
    `;
    
    try {
        if (window.currentTemplatePages && window.currentTemplatePages.length > 0) {
            console.log('🔍 Using template pages from global data');
            await processTemplatePages();
            return;
        }
        
        console.log('🔍 Fetching pages for template:', templateId);
        const pagesResponse = await fetch(`${API_ENDPOINTS.GET_PAGES_BY_TEMPLATE}${templateId}`);
        const pagesData = await pagesResponse.json();
        
        if (!pagesData.success || !pagesData.data || pagesData.data.length === 0) {
            showNoBlocksMessage(container, 'No pages found for this template');
            return;
        }
        
        window.currentTemplatePages = pagesData.data;
        await processTemplatePages();
        
    } catch (error) {
        console.error('❌ Error loading previously used blocks:', error);
        showErrorMessage(container, templateId);
    }
}

// Process template pages to extract blocks
async function processTemplatePages() {
    const container = document.getElementById('previouslyUsedBlocksContainer');
    
    try {
        const pages = window.currentTemplatePages;
        console.log('📄 Processing pages:', pages.length);
        
        const allBlockIds = new Set();
        const blockUsageMap = new Map();
        
        for (const page of pages) {
            try {
                console.log(`🔍 Processing page: ${page.name} (ID: ${page.id})`);
                
                if (page.page_blocks && Array.isArray(page.page_blocks)) {
                    console.log(`📦 Found ${page.page_blocks.length} blocks in page data`);
                    page.page_blocks.forEach(pageBlock => {
                        const blockId = pageBlock.block_id;
                        allBlockIds.add(blockId);
                        
                        if (!blockUsageMap.has(blockId)) {
                            blockUsageMap.set(blockId, { count: 0, pages: [] });
                        }
                        
                        const usage = blockUsageMap.get(blockId);
                        usage.count++;
                        usage.pages.push(page.name);
                    });
                } else {
                    const pageBlocksResponse = await fetch(`${API_ENDPOINTS.GET_PAGE_BLOCKS}${page.id}`);
                    const pageBlocksData = await pageBlocksResponse.json();
                    
                    if (pageBlocksData.success && pageBlocksData.blocks) {
                        pageBlocksData.blocks.forEach(pageBlock => {
                            const blockId = pageBlock.block_id;
                            allBlockIds.add(blockId);
                            
                            if (!blockUsageMap.has(blockId)) {
                                blockUsageMap.set(blockId, { count: 0, pages: [] });
                            }
                            
                            const usage = blockUsageMap.get(blockId);
                            usage.count++;
                            usage.pages.push(page.name);
                        });
                    }
                }
            } catch (error) {
                console.error(`❌ Error processing page ${page.id}:`, error);
            }
        }
        
        console.log('🧩 Total unique blocks found:', allBlockIds.size);
        
        if (allBlockIds.size === 0) {
            showNoBlocksMessage(container, 'No blocks found in any pages');
            return;
        }
        
        let blocksWithUsage = getBlockDetailsFromDOM(allBlockIds, blockUsageMap);
        
        // If DOM doesn't have block details, try to fetch from API
        if (blocksWithUsage.length === 0) {
            console.log('Fetching block details from API');
            const blockDetails = await loadBlockDetailsFromAPI(allBlockIds);
            
            if (blockDetails.length > 0) {
                blockDetails.forEach(block => {
                    const usage = blockUsageMap.get(block.id) || { count: 0, pages: [] };
                    
                    blocksWithUsage.push({
                        id: block.id,
                        name: block.name || 'Unnamed Block',
                        description: block.description || '',
                        image: block.image || '',
                        type: block.type || '',
                        usage_count: usage.count,
                        used_in_pages: usage.pages.join(', '),
                        is_common: isCommonBlock(block)
                    });
                });
            }
        }
        
        if (blocksWithUsage.length === 0) {
            showNoBlocksMessage(container, 'Block details not found on page');
            return;
        }
        
        blocksWithUsage.sort((a, b) => b.usage_count - a.usage_count);
        
        previouslyUsedBlocks = blocksWithUsage;
        displayPreviouslyUsedBlocks(blocksWithUsage);
        
    } catch (error) {
        console.error('❌ Error processing template pages:', error);
        showErrorMessage(container, 'Error processing pages');
    }
}

// Get block details from DOM
function getBlockDetailsFromDOM(allBlockIds, blockUsageMap) {
    const blocksWithUsage = [];
    const blockItems = document.querySelectorAll('.block-item');
    
    // If no block items found in DOM, use API data instead
    if (blockItems.length === 0) {
        console.log('No block items found in DOM, using API data instead');
        
        // Get block information from API data
        // You can use block IDs from the API here
        allBlockIds.forEach(blockId => {
            const usage = blockUsageMap.get(blockId) || { count: 0, pages: [] };
            
            // You can set default values or get more details from API
            blocksWithUsage.push({
                id: blockId,
                name: `Block ${blockId}`,
                description: 'Block from template',
                image: '',
                type: '',
                usage_count: usage.count,
                used_in_pages: usage.pages.join(', '),
                is_common: false
            });
        });
        
        // Try to load block details from API
        loadBlockDetailsFromAPI(allBlockIds).then(blockDetails => {
            if (blockDetails && blockDetails.length > 0) {
                // Update with accurate information
                const blocksWithAPI = blockDetails.map(block => {
                    const usage = blockUsageMap.get(block.id) || { count: 0, pages: [] };
                    return {
                        id: block.id,
                        name: block.name || `Block ${block.id}`,
                        description: block.description || 'No description',
                        image: block.image || '',
                        type: block.slug || '',
                        usage_count: usage.count,
                        used_in_pages: usage.pages.join(', '),
                        is_common: isCommonBlock({ name: block.name, type: block.slug })
                    };
                });
                displayPreviouslyUsedBlocks(blocksWithAPI);
            }
        });
        
        return blocksWithUsage;
    }
    
    // Existing code that gets block details from DOM
    blockItems.forEach(item => {
        const blockId = item.getAttribute('data-block-id');
        if (blockId && allBlockIds.has(blockId)) {
            const blockName = item.querySelector('h3')?.textContent || 'Unnamed Block';
            const blockImage = item.querySelector('img')?.src || '';
            const blockDesc = item.querySelector('p')?.textContent || '';
            const blockType = item.getAttribute('data-block-type') || '';
            
            const usage = blockUsageMap.get(blockId) || { count: 0, pages: [] };
            
            blocksWithUsage.push({
                id: blockId,
                name: blockName,
                description: blockDesc,
                image: blockImage,
                type: blockType,
                usage_count: usage.count,
                used_in_pages: usage.pages.join(', '),
                is_common: isCommonBlock({ name: blockName, type: blockType })
            });
        }
    });
    
    return blocksWithUsage;
}

// Check if block is common
function isCommonBlock(block) {
    const commonKeywords = ['header', 'footer', 'navigation', 'nav', 'menu'];
    const blockName = (block.name || '').toLowerCase();
    const blockType = (block.type || '').toLowerCase();
    
    return commonKeywords.some(keyword => 
        blockName.includes(keyword) || blockType.includes(keyword)
    );
}

// Show messages
function showNoBlocksMessage(container, message) {
    container.innerHTML = `
        <div class="text-center py-8">
            <div class="text-gray-400 mb-2">
                <svg class="w-8 h-8 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
            </div>
            <p class="text-sm text-gray-500">${message}</p>
        </div>
    `;
}

function showErrorMessage(container, templateId) {
    container.innerHTML = `
        <div class="text-center py-8">
            <div class="text-red-400 mb-2">
                <svg class="w-8 h-8 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
            <p class="text-sm text-red-600">Error loading blocks</p>
            <button onclick="loadPreviouslyUsedBlocksFromExistingAPI('${templateId}')" class="mt-2 text-xs px-3 py-1 bg-blue-600 text-white rounded hover:bg-blue-700">
                Retry
            </button>
        </div>
    `;
}

// Display previously used blocks
function displayPreviouslyUsedBlocks(blocks) {
    const container = document.getElementById('previouslyUsedBlocksContainer');
    
    if (!blocks || blocks.length === 0) {
        showNoBlocksMessage(container, 'No previously used blocks found');
        return;
    }
    
    let html = '<div class="p-3 space-y-2">';
    
    blocks.forEach(block => {
        const isCommon = block.is_common;
        const commonBadge = isCommon ? '<span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800 ml-2">Common</span>' : '';
        const blockImage = block.image || `https://placehold.co/200x100/e5e7eb/9ca3af?text=${encodeURIComponent(block.name || 'Block')}`;
        
        html += `
            <label class="flex items-start space-x-3 p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors ${isCommon ? 'border-green-200 bg-green-50' : ''}">
                <input type="checkbox" 
                       value="${block.id}" 
                       class="mt-1 rounded border-gray-300 text-blue-600 shadow-sm block-checkbox"
                       ${isCommon ? 'data-common="true"' : ''}>
                <div class="flex-1 min-w-0">
                    <div class="flex items-center">
                        <img src="${blockImage}" alt="${block.name || 'Block'}" class="w-12 h-8 object-cover rounded border mr-3">
                        <div class="flex-1">
                            <div class="flex items-center">
                                <span class="font-medium text-gray-900 text-sm">${block.name || 'Unnamed Block'}</span>
                                ${commonBadge}
                            </div>
                            <p class="text-xs text-gray-500 mt-1">${block.description || 'No description'}</p>
                            <div class="flex items-center text-xs text-gray-400 mt-1">
                                <span>Used ${block.usage_count} time${block.usage_count !== 1 ? 's' : ''}</span>
                                ${block.used_in_pages ? `<span class="mx-1">•</span><span>In: ${block.used_in_pages}</span>` : ''}
                            </div>
                        </div>
                    </div>
                </div>
            </label>
        `;
    });
    
    html += '</div>';
    container.innerHTML = html;
    
    // Add event listeners to checkboxes
    container.querySelectorAll('.block-checkbox').forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            if (this.checked) {
                selectedBlocks.add(this.value);
            } else {
                selectedBlocks.delete(this.value);
            }
            updateSelectionSummary();
        });
    });
}

// Load block details from API
async function loadBlockDetailsFromAPI(blockIds) {
    try {
        const response = await fetch(`${API_ENDPOINTS.GET_BLOCKS}?ids=${Array.from(blockIds).join(',')}`); 
        const data = await response.json();
        
        if (data.success && data.blocks) {
            return data.blocks;
        }
        return [];
    } catch (error) {
        console.error('Error loading block details:', error);
        return [];
    }
}

// Update selection summary
function updateSelectionSummary() {
    const summary = document.getElementById('selectionSummary');
    const count = document.getElementById('selectedCount');
    
    if (selectedBlocks.size > 0) {
        summary.classList.remove('hidden');
        count.textContent = selectedBlocks.size;
    } else {
        summary.classList.add('hidden');
    }
}

// Handle create page form submission
async function handleCreatePageSubmission(e) {
    e.preventDefault();
    
    const templateId = document.getElementById('templateSelector').value;
    const pageName = document.getElementById('pageName').value.trim();
    const pageSlug = document.getElementById('pageSlug').value.trim();
    const pageStatus = document.getElementById('pageStatus').value;
    const pageDescription = document.getElementById('pageDescription').value.trim();
    
    if (!templateId || !pageName) {
        alert('Please fill in required fields');
        return;
    }
    
    // Show loading state
    const submitBtn = document.getElementById('createPageSubmit');
    const btnText = submitBtn.querySelector('.create-btn-text');
    const btnLoading = submitBtn.querySelector('.create-btn-loading');
    
    submitBtn.disabled = true;
    btnText.classList.add('hidden');
    btnLoading.classList.remove('hidden');
    
    try {
        // Create the page
        const pageData = {
            name: pageName,
            slug: pageSlug || pageName.toLowerCase().replace(/[^a-z0-9 -]/g, '').replace(/\s+/g, '-').replace(/-+/g, '-').replace(/^-|-$/g, ''),
            template_id: templateId,
            status: pageStatus,
            description: pageDescription
        };
        
        console.log('📝 Creating page:', pageData);
        
        const createPageResponse = await fetch(API_ENDPOINTS.CREATE_PAGE, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(pageData)
        });
        
        const createPageResult = await createPageResponse.json();
        
        if (!createPageResult.success) {
            throw new Error(createPageResult.message || 'Failed to create page');
        }
        
        const newPageId = createPageResult.data.id;
        console.log('✅ Page created with ID:', newPageId);
        
        // Add selected blocks to the page
        let addedBlocksCount = 0;
        if (selectedBlocks.size > 0) {
            console.log('🧩 Adding selected blocks to page...');
            
            const selectedBlocksArray = Array.from(selectedBlocks);
            for (let i = 0; i < selectedBlocksArray.length; i++) {
                const blockId = selectedBlocksArray[i];
                
                try {
                    const blockToPageData = {
                        page_id: newPageId,
                        block: {
                            block_id: blockId,
                            position: i + 1,
                            data: {
                                json_code: '{}',
                                css_code: '',
                                js_code: '',
                                tmpl_code: ''
                            }
                        }
                    };
                    
                    const addBlockResponse = await fetch(API_ENDPOINTS.SAVE_BLOCK_TO_PAGE, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(blockToPageData)
                    });
                    
                    const addBlockResult = await addBlockResponse.json();
                    
                    if (addBlockResult.success) {
                        addedBlocksCount++;
                        console.log(`✅ Added block ${blockId} to page`);
                    } else {
                        console.error(`❌ Failed to add block ${blockId}:`, addBlockResult.message);
                    }
                } catch (error) {
                    console.error(`❌ Error adding block ${blockId}:`, error);
                }
            }
        }
        
        // Success notification
        const successMessage = `Page "${pageData.name}" created successfully!${addedBlocksCount > 0 ? ` ${addedBlocksCount} blocks added.` : ''}`;
        alert(successMessage);
        
        // Close modal and refresh
        document.getElementById('createPageModal').classList.add('hidden');
        
        // Refresh template to show new page
        document.getElementById('templateSelector').dispatchEvent(new Event('change'));
        
        resetCreatePageForm();
        
    } catch (error) {
        console.error('❌ Error creating page:', error);
        alert('Error creating page: ' + error.message);
    } finally {
        // Reset button state
        submitBtn.disabled = false;
        btnText.classList.remove('hidden');
        btnLoading.classList.add('hidden');
    }
}
</script>

<!-- CSS Styles -->
<style>
/* Enhanced styles for the modal */
.block-checkbox:checked + div {
    background-color: #eff6ff !important;
    border-color: #3b82f6 !important;
}

.create-page-modal .block-item-selected {
    border-color: #3b82f6;
    background-color: #eff6ff;
}

.create-page-modal .common-block {
    border-color: #10b981;
    background-color: #f0fdf4;
}

/* Modal animation */
#createPageModal {
    backdrop-filter: blur(4px);
}

#createPageModal .relative {
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: scale(0.95) translateY(-20px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

/* Loading states */
.animate-spin {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* Tab styling */
.tab-button.active {
    border-color: #3b82f6 !important;
    color: #2563eb !important;
}

/* Responsive adjustments */
@media (max-width: 1024px) {
    #createPageModal .max-w-4xl {
        max-width: 95vw;
    }
    
    #createPageModal .grid-cols-1.lg\\:grid-cols-2 {
        grid-template-columns: 1fr;
    }
}

/* Hover effects */
.block-item:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.page-item:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Search input focus */
#blockSearch:focus {
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Success message styling */
.bg-green-100 {
    animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
</style>

<?php
$content = ob_get_clean();
require_once __DIR__ . '/../layouts/main.php';
?>