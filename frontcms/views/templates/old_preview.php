<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once __DIR__ . '/../../config/config.php';
require_once __DIR__ . '/../../modules/templates/Templates.php';
require_once __DIR__ . '/../../modules/pages/Pages.php';
require_once __DIR__ . '/../../modules/pages_blocks/PagesBlocks.php';
require_once __DIR__ . '/../../modules/blocks/Blocks.php';
require_once __DIR__ . '/../../vendor/autoload.php';

$templates = new Templates();
$pages = new Pages();
$pagesBlocks = new PagesBlocks();
$blocks = new Blocks();
$error = null;

// Get template ID from URL
$templateId = isset($_GET['id']) ? $_GET['id'] : null;
$templateData = null;
$templatePages = [];
$allTemplateBlocks = [];

// Get the current template data if ID is provided
if ($templateId) {
    try {
        $response = $templates->get($templateId);
        if (isset($response['data'])) {
            $templateData = $response['data'];
        } else {
            $error = 'Template not found';
        }
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
    
    // Get all pages for this template
    try {
        $pagesResponse = $pages->getList(['template_id' => $templateId]);
        if (isset($pagesResponse['data']['data']) && is_array($pagesResponse['data']['data'])) {
            $templatePages = $pagesResponse['data']['data'];
            
            // Get blocks for each page
            foreach ($templatePages as $page) {
                $pageBlocksResponse = $pagesBlocks->getByPageId($page['id']);
                
                if (isset($pageBlocksResponse['success']) && $pageBlocksResponse['success'] === true) {
                    $pageBlocksData = [];
                    
                    if (isset($pageBlocksResponse['data']['data']) && is_array($pageBlocksResponse['data']['data'])) {
                        $pageBlocksData = $pageBlocksResponse['data']['data'];
                    } elseif (isset($pageBlocksResponse['data']) && is_array($pageBlocksResponse['data'])) {
                        $pageBlocksData = $pageBlocksResponse['data'];
                    }
                    
                    // Process each block
                    foreach ($pageBlocksData as $pageBlock) {
                        if (isset($pageBlock['block_id'])) {
                            // Get block details from Blocks API
                            $blockResponse = $blocks->get($pageBlock['block_id']);
                            
                            if (isset($blockResponse['success']) && $blockResponse['success'] === true && isset($blockResponse['data'])) {
                                $blockData = $blockResponse['data'];
                                
                                // Get template code and JSON data
                                $tmplCode = isset($blockData['tmpl_code']) ? $blockData['tmpl_code'] : '';
                                $jsonCode = isset($pageBlock['json_code']) ? $pageBlock['json_code'] : 
                                          (isset($blockData['json_code']) ? $blockData['json_code'] : '{}');
                                $cssCode = isset($pageBlock['css_code']) ? $pageBlock['css_code'] : 
                                         (isset($blockData['css_code']) ? $blockData['css_code'] : '');
                                $jsCode = isset($pageBlock['js_code']) ? $pageBlock['js_code'] : 
                                        (isset($blockData['js_code']) ? $blockData['js_code'] : '');
                                
                                // Process the template with JSON data if available using LightnCandy
                                $processedContent = $tmplCode;
                                
                                // If we have JSON data, compile and render the template using LightnCandy
                                if (!empty($jsonCode) && $jsonCode != '{}') {
                                    $jsonData = json_decode($jsonCode, true);
                                    if (is_array($jsonData)) {
                                        try {
                                            // Configure LightnCandy with Handlebars settings
                                            $options = array(
                                                'flags' => LightnCandy\LightnCandy::FLAG_HANDLEBARS | 
                                                         LightnCandy\LightnCandy::FLAG_ERROR_EXCEPTION |
                                                         LightnCandy\LightnCandy::FLAG_INSTANCE |
                                                         LightnCandy\LightnCandy::FLAG_RUNTIMEPARTIAL
                                            );
                                            
                                            // Compile the template
                                            $renderer = LightnCandy\LightnCandy::compile($tmplCode, $options);
                                            
                                            // Create the render function and execute it
                                            $render = eval($renderer);
                                            $processedContent = $render($jsonData);
                                        } catch (Exception $e) {
                                            error_log('Template processing error: ' . $e->getMessage());
                                            // Keep the original template if there's an error
                                            $processedContent = $tmplCode;
                                        }
                                    }
                                }
                                
                                // Add to all template blocks array with page information
                                $allTemplateBlocks[] = [
                                    'id' => $pageBlock['id'],
                                    'page_id' => $page['id'],
                                    'page_name' => $page['name'],
                                    'page_slug' => $page['slug'],
                                    'block_id' => $pageBlock['block_id'],
                                    'position' => $pageBlock['position'],
                                    'type' => isset($blockData['type']) ? $blockData['type'] : 'block',
                                    'content' => $processedContent,
                                    'css' => $cssCode,
                                    'js' => $jsCode
                                ];
                            }
                        }
                    }
                }
            }
        }
        
        // Sort all blocks by page and position
        usort($allTemplateBlocks, function($a, $b) {
            if ($a['page_id'] == $b['page_id']) {
                return ($a['position'] ?? 999) - ($b['position'] ?? 999);
            }
            return $a['page_id'] - $b['page_id'];
        });
        
    } catch (Exception $e) {
        error_log('Error loading template pages and blocks: ' . $e->getMessage());
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($templateData['site_title']) ? htmlspecialchars($templateData['site_title']) : 'Template Preview'; ?></title>
    <meta name="description" content="<?php echo isset($templateData['site_description']) ? htmlspecialchars($templateData['site_description']) : ''; ?>">
    
    <!-- Tailwind CSS -->
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    
    <!-- Custom CSS for template -->
    <style>
        /* Base styles */
        body {
            font-family: <?php echo isset($templateData['body_font_family']) ? "'" . $templateData['body_font_family'] . "', " : ""; ?>'Inter', sans-serif;
            font-size: <?php echo isset($templateData['body_font_size']) ? $templateData['body_font_size'] . 'px' : '16px'; ?>;
            line-height: <?php echo isset($templateData['line_height']) ? $templateData['line_height'] : '1.5'; ?>;
            color: #111827;
        }
        
        /* Page sections */
        .page-section {
            border: 2px dashed transparent;
            border-radius: 8px;
            margin: 16px 0;
            transition: all 0.2s ease;
        }
        
        .page-section:hover {
            border-color: #10b981;
            background-color: rgba(16, 185, 129, 0.02);
        }
        
        .page-section-header {
            background: rgba(16, 185, 129, 0.1);
            border: 1px solid rgba(16, 185, 129, 0.3);
            border-radius: 6px;
            padding: 8px 16px;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            justify-content: between;
        }
        
        .page-section-title {
            font-weight: 600;
            color: #047857;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .page-actions {
            display: flex;
            gap: 8px;
        }
        
        .page-action-btn {
            padding: 4px 8px;
            background: rgba(255, 255, 255, 0.8);
            border: 1px solid rgba(16, 185, 129, 0.3);
            border-radius: 4px;
            color: #047857;
            text-decoration: none;
            font-size: 12px;
            transition: all 0.2s ease;
        }
        
        .page-action-btn:hover {
            background: white;
            border-color: #047857;
        }
        
        /* Block wrapper with hover effect */
        .block-wrapper {
            position: relative;
            margin: 8px 0;
            transition: all 0.2s ease;
            border: 2px dashed transparent;
            border-radius: 6px;
            padding: 4px;
        }
        
        .block-wrapper:hover {
            border-color: #3b82f6;
            background-color: rgba(59, 130, 246, 0.02);
        }
        
        .block-wrapper:hover .block-controls {
            display: flex;
        }
        
        /* Block edit controls */
        .block-controls {
            display: none;
            position: absolute;
            top: 8px;
            right: 8px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border: 1px solid rgba(229, 231, 235, 0.8);
            border-radius: 6px;
            padding: 4px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            z-index: 10;
            transition: all 0.2s ease;
        }
        
        .block-controls button {
            margin: 0 1px;
            padding: 6px;
            background: none;
            border: none;
            cursor: pointer;
            transition: all 0.2s ease;
            border-radius: 4px;
            color: #6b7280;
        }
        
        .block-controls button:hover {
            background: rgba(59, 130, 246, 0.1);
            color: #3b82f6;
        }
        
        .block-controls button.block-delete:hover {
            background: rgba(239, 68, 68, 0.1);
            color: #ef4444;
        }
        
        /* Custom Tailwind Configuration */
        <?php if (isset($templateData['tailwind_configuration_code'])): ?>
        <?php echo $templateData['tailwind_configuration_code']; ?>
        <?php endif; ?>
        
        /* Custom CSS from template */
        <?php if (isset($templateData['custom_css'])): ?>
        <?php echo $templateData['custom_css']; ?>
        <?php endif; ?>
        
        /* Custom CSS from blocks */
        <?php 
        if (!empty($allTemplateBlocks)) {
            foreach ($allTemplateBlocks as $block) {
                if (!empty($block['css'])) {
                    echo "/* Block ID: {$block['block_id']} (Page: {$block['page_name']}) */\n";
                    echo $block['css'] . "\n\n";
                }
            }
        }
        ?>
    </style>
</head>
<body>
    <?php if ($error): ?>
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 m-4 rounded">
            <p><?php echo htmlspecialchars($error); ?></p>
        </div>
    <?php endif; ?>

    <?php if (empty($templateData)): ?>
        <div class="flex items-center justify-center h-screen bg-gray-100">
            <div class="text-center">
                <h1 class="text-3xl font-bold text-gray-800 mb-4">Template Not Found</h1>
                <p class="text-gray-600">The requested template could not be found or has not been created yet.</p>
            </div>
        </div>
    <?php else: ?>
        <div class="template-content">
            <?php if (!empty($allTemplateBlocks)): ?>
                <?php 
                $currentPageId = null;
                foreach ($allTemplateBlocks as $index => $block): 
                    // Check if we need to start a new page section
                    if ($currentPageId !== $block['page_id']):
                        // Close previous page section if exists
                        if ($currentPageId !== null): ?>
                        </div> <!-- Close previous page section -->
                        <?php endif; ?>
                        
                        <!-- Start new page section -->
                        <div class="page-section" data-page-id="<?php echo $block['page_id']; ?>">
                            <div class="page-section-header">
                                <div class="page-section-title">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <path d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z"/>
                                        <path d="M14 2v4a2 2 0 0 0 2 2h4"/>
                                    </svg>
                                    <?php echo htmlspecialchars($block['page_name']); ?>
                                    <span class="text-xs text-gray-500">(<?php echo htmlspecialchars($block['page_slug']); ?>)</span>
                                </div>
                                <div class="page-actions">
                                    <a href="<?php echo BASE_URL; ?>/pages/view?id=<?php echo $block['page_id']; ?>" target="_blank" class="page-action-btn">Edit Page</a>
                                    <a href="<?php echo BASE_URL; ?>/pages/preview?id=<?php echo $block['page_id']; ?>" target="_blank" class="page-action-btn">Preview</a>
                                </div>
                            </div>
                        
                        <?php $currentPageId = $block['page_id']; ?>
                    <?php endif; ?>
                    
                    <!-- Block content -->
                    <div class="block-wrapper relative" data-block-id="<?php echo $block['id']; ?>" data-page-id="<?php echo $block['page_id']; ?>" data-position="<?php echo $block['position']; ?>" data-block-type="<?php echo $block['type']; ?>">
                        <div class="block-controls absolute top-0 right-0 flex space-x-1 bg-white p-1 rounded shadow">
                            <button class="block-edit p-1 text-gray-600 hover:text-green-600" title="Edit Block" onclick="editBlock(<?php echo $block['page_id']; ?>, <?php echo $block['block_id']; ?>)">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                </svg>
                            </button>
                            <button class="block-move-up p-1 text-gray-600 hover:text-blue-600" title="Move Block Up">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18" />
                                </svg>
                            </button>
                            <button class="block-move-down p-1 text-gray-600 hover:text-blue-600" title="Move Block Down">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3" />
                                </svg>
                            </button>
                            <button class="block-delete p-1 text-gray-600 hover:text-red-600" title="Delete Block">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                </svg>
                            </button>
                        </div>
                        
                        <?php echo $block['content']; ?>
                    </div>
                    
                <?php endforeach; ?>
                
                <!-- Close last page section -->
                </div>
            <?php else: ?>
                <!-- No blocks found -->
                <div class="flex items-center justify-center h-screen bg-gray-100">
                    <div class="text-center">
                        <div class="inline-flex items-center justify-center w-16 h-16 rounded-full bg-gray-200 mb-4">
                            <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="0.75" stroke-linecap="round" stroke-linejoin="round" class="text-gray-500">
                                <rect width="18" height="7" x="3" y="3" rx="1"/>
                                <rect width="9" height="7" x="3" y="14" rx="1"/>
                                <rect width="5" height="7" x="16" y="14" rx="1"/>
                            </svg>
                        </div>
                        <h1 class="text-2xl font-bold text-gray-800 mb-2">Template Preview</h1>
                        <p class="text-gray-600">No pages or blocks found for this template.</p>
                        <p class="text-sm text-gray-500 mt-2">Create pages and add blocks to see the template content.</p>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    <?php endif; ?>

<script>
// Initialize global tracking variables
window.processingBlocks = {};

// Function to edit a block
function editBlock(pageId, blockId) {
    // Open page builder for specific page
    window.open(`<?php echo BASE_URL; ?>/pages/view?id=${pageId}`, '_blank');
}

// Setup block controls
function setupBlockControls() {
    const blockWrappers = document.querySelectorAll('.block-wrapper');

    blockWrappers.forEach(wrapper => {
        const controls = wrapper.querySelector('.block-controls');
        
        wrapper.addEventListener('mouseenter', () => {
            controls.style.display = 'flex';
        });
        
        wrapper.addEventListener('mouseleave', () => {
            controls.style.display = 'none';
        });
    });

    // Move up functionality
    document.querySelectorAll('.block-move-up').forEach(button => {
        button.addEventListener('click', function() {
            const blockWrapper = this.closest('.block-wrapper');
            const prevBlock = blockWrapper.previousElementSibling;
            
            if (prevBlock && prevBlock.classList.contains('block-wrapper')) {
                blockWrapper.parentNode.insertBefore(blockWrapper, prevBlock);
                // Here you would typically save the new order to the backend
                console.log('Move block up - implement save to backend');
            }
        });
    });
    
    // Move down functionality
    document.querySelectorAll('.block-move-down').forEach(button => {
        button.addEventListener('click', function() {
            const blockWrapper = this.closest('.block-wrapper');
            const nextBlock = blockWrapper.nextElementSibling;
            
            if (nextBlock && nextBlock.classList.contains('block-wrapper')) {
                blockWrapper.parentNode.insertBefore(nextBlock, blockWrapper);
                // Here you would typically save the new order to the backend
                console.log('Move block down - implement save to backend');
            }
        });
    });
    
    // Delete functionality
    document.querySelectorAll('.block-delete').forEach(button => {
        button.addEventListener('click', function() {
            const blockWrapper = this.closest('.block-wrapper');
            const blockId = blockWrapper.getAttribute('data-block-id');
            
            if (confirm('Do you really want to delete this block?')) {
                fetch('<?php echo BASE_URL; ?>/modules/pages_blocks/delete_block.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        block_id: blockId
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        blockWrapper.remove();
                    } else {
                        alert('Failed to delete the block: ' + (data.message || 'Unknown error'));
                    }
                })
                .catch((error) => {
                    console.error('Error:', error);
                    alert('Failed to delete the block. Please try again.');
                });
            }
        });
    });
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    console.log('Template Preview Loaded');
    setupBlockControls();
});

// Custom JS from template
<?php if (isset($templateData['custom_js'])): ?>
<?php echo $templateData['custom_js']; ?>
<?php endif; ?>

// Custom JS from blocks
<?php 
if (!empty($allTemplateBlocks)) {
    foreach ($allTemplateBlocks as $block) {
        if (!empty($block['js'])) {
            echo "/* Block ID: {$block['block_id']} (Page: {$block['page_name']}) */\n";
            echo $block['js'] . "\n\n";
        }
    }
}
?>
</script>

<!-- Add hidden input for template ID -->
<input type="hidden" id="template-id" value="<?php echo htmlspecialchars($templateId); ?>">
    
</body>
</html>