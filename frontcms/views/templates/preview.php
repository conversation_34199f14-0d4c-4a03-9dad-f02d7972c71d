<?php
require_once __DIR__ . '/../config/config.php';

$templateId = $_GET['id'] ?? null;
$previewMode = ($_GET['preview'] ?? '') === 'true';
$pageSlug = $_GET['page'] ?? null;

if (!$templateId) {
    header("HTTP/1.0 400 Bad Request");
    exit("Template ID is required");
}

$queryParams = [
    'type' => 'template',
    'id' => $templateId
];

if ($previewMode) {
    $queryParams['preview'] = 'true';
}

if ($pageSlug) {
    $queryParams['page'] = $pageSlug;
}

$redirectUrl = BASE_URL . '/views/preview.php?' . http_build_query($queryParams);
header("Location: $redirectUrl");
exit;
?>