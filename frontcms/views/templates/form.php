<?php
require_once __DIR__ . '/../../config/config.php';
require_once __DIR__ . '/../../modules/templates/Templates.php';
require_once __DIR__ . '/../../modules/template_categories/TemplateCategories.php';
require_once __DIR__ . '/../../modules/tags/Tags.php';
require_once __DIR__ . '/../../modules/common_tags/CommonTags.php';
require_once __DIR__ . '/../../modules/global_assets/GlobalAssets.php';
require_once __DIR__ . '/../../modules/template_categories_mapping/TemplateCategoriesMapping.php';
require_once __DIR__ . '/../../includes/FileUploadHelper.php';

// Add Select2 CSS and JS in the header
//ob_start();
?>


<?php
//$additionalHead = ob_get_clean();

// Initialize instances
$templates = new Templates();
$templateCategories = new TemplateCategories();
$tags = new Tags();
$commonTags = new CommonTags();
$globalAssets = new GlobalAssets();
$templateCategoriesMapping = new TemplateCategoriesMapping();

// Initialize file uploaders for different types
$logoUploader = new FileUploadHelper('templates', 'logos');
$screenshotUploader = new FileUploadHelper('templates', 'screenshots');

$error = null;
$success = null;

// Get template categories
$categories = [];
try {
    $response = $templateCategories->getList();
    if (isset($response['data']['data']) && is_array($response['data']['data'])) {
        $categories = $response['data']['data'];
    }
} catch (Exception $e) {
    $error = $e->getMessage();
}

$template = [
    'id' => '',
    'name' => '',
    'short_description' => '',
    'detailed_description' => '',
    'logo_image' => '',
    'favicon_image' => '',
    'site_title' => '',
    'site_description' => '',
    'meta_keywords' => '',
    'meta_description' => '',
    'body_font_family' => '',
    'body_font_size' => '',
    'heading_font_family' => '',
    'line_height' => '',
    'tailwind_configuration_code' => '',
    'custom_css' => '',
    'custom_js' => '',
    'multiple_template_screenshot' => [],
    'template_status' => 0,
    'categories' => [],
    'tags' => []  // This will be populated separately
];

// Check if we're editing an existing template
$isEdit = isset($_GET['id']);

if ($isEdit) {
    try {

        // Get template data using the method that includes tags
        $response = $templates->getTemplateWithTags($_GET['id']);
   
        if (isset($response['data'])) {
            $template = $response['data'];
        }
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    
    try {
        $currentDateTime = date('Y-m-d H:i:s');
        
        // Prepare template data (without tags)
        $templateData = [
            'name' => $_POST['name'] ?? '',
            'short_description' => $_POST['short_description'] ?? '',
            'detailed_description' => $_POST['detailed_description'] ?? '',
            'site_title' => $_POST['site_title'] ?? '',
            'site_description' => $_POST['site_description'] ?? '',
            'meta_keywords' => $_POST['meta_keywords'] ?? '',
            'meta_description' => $_POST['meta_description'] ?? '',
            'body_font_family' => $_POST['body_font_family'] ?? '',
            'body_font_size' => $_POST['body_font_size'] ?? '',
            'heading_font_family' => $_POST['heading_font_family'] ?? '',
            'line_height' => $_POST['line_height'] ?? '',
            'tailwind_configuration_code' => $_POST['tailwind_configuration_code'] ?? '',
            'custom_css' => $_POST['custom_css'] ?? '',
            'custom_js' => $_POST['custom_js'] ?? '',
            'template_status' => (int)($_POST['template_status'] ?? 0)
        ];

        // Add created_at only for new templates
        if (!$isEdit) {
            $templateData['created_at'] = $currentDateTime;
        }

        // Handle logo image upload
        if (!empty($_FILES['logo_image']['tmp_name'])) {
            $uploadResult = $logoUploader->handleUpload($_FILES['logo_image'], 'logo_');
            if ($uploadResult['success']) {
                $templateData['logo_image'] = $uploadResult['url'];
            } else {
                throw new Exception('Failed to upload logo: ' . $uploadResult['message']);
            }
        }

        // Handle favicon image upload
        if (!empty($_FILES['favicon_image']['tmp_name'])) {
            $uploadResult = $logoUploader->handleUpload($_FILES['favicon_image'], 'favicon_');
            if ($uploadResult['success']) {
                $templateData['favicon_image'] = $uploadResult['url'];
            } else {
                throw new Exception('Failed to upload favicon: ' . $uploadResult['message']);
            }
        }

        // Handle template screenshots
        if (!empty($_FILES['template_screenshots']['tmp_name'][0])) {
            // Get existing screenshots if any
            $existingScreenshots = [];
            if (!empty($template['multiple_template_screenshot'])) {
                $existingScreenshots = is_string($template['multiple_template_screenshot']) 
                    ? json_decode($template['multiple_template_screenshot'], true) 
                    : $template['multiple_template_screenshot'];
            }
            if (!is_array($existingScreenshots)) {
                $existingScreenshots = [];
            }
            
            // Handle new screenshots
            $newScreenshots = [];
            foreach ($_FILES['template_screenshots']['tmp_name'] as $key => $tmpName) {
                if (empty($tmpName)) continue;
                
                // Prepare file data for local upload
                $screenshot = [
                    'name' => $_FILES['template_screenshots']['name'][$key],
                    'type' => $_FILES['template_screenshots']['type'][$key],
                    'tmp_name' => $tmpName,
                    'error' => $_FILES['template_screenshots']['error'][$key],
                    'size' => $_FILES['template_screenshots']['size'][$key]
                ];
                
                $uploadResult = $screenshotUploader->handleUpload($screenshot, 'screenshot_');
                if ($uploadResult['success']) {
                    $newScreenshots[] = $uploadResult['url'];
                } else {
                    throw new Exception('Failed to upload screenshot: ' . $uploadResult['message']);
                }
            }
            
            // Merge existing and new screenshots
            $allScreenshots = array_merge($existingScreenshots, $newScreenshots);
            
            // Remove duplicates and empty values
            $allScreenshots = array_filter(array_unique($allScreenshots));
            
            if (!empty($allScreenshots)) {
                $templateData['multiple_template_screenshot'] = json_encode(array_values($allScreenshots));
            }
        }
        
        // Prepare categories and tags for separate handling
        $categories = $_POST['categories'] ?? [];
        $tags = $_POST['tags'] ?? [];

        // Add categories and tags to template data for processing
        $templateData['categories'] = $categories;
        $templateData['tags'] = $tags;
        
        // Create or update template
        if ($isEdit) {
            $response = $templates->update($_POST['id'], $templateData);
            $successMessage = "Template updated successfully";
        } else {
            $response = $templates->create($templateData);
            $successMessage = "Template created successfully";
        }

        if ($response['success']) {
            // Redirect to templates list with success message
            header("Location: " . BASE_URL . "/templates?success=" . urlencode($successMessage));
            exit;
        } else {
            throw new Exception($response['message'] ?? 'Failed to save template');
        }

    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// Set page title and breadcrumbs
$pageTitle = $isEdit ? 'Edit Template' : 'Create Template';
$breadcrumbs = [
    [
        'label' => 'Templates',
        'url' => BASE_URL . '/templates'
    ],
    [
        'label' => $pageTitle,
        'url' => '#'
    ]
];

function e($value) {
    return htmlspecialchars((string)($value ?? ''), ENT_QUOTES, 'UTF-8');
}
ob_start();
?>

    <!-- Form Container -->
    <?php if ($error): ?>
        <div class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6 rounded-r" role="alert">
            <p class="font-medium">Error</p>
            <p><?php echo e($error); ?></p>
        </div>
    <?php endif; ?>
    
    <form id="templateForm" method="POST" enctype="multipart/form-data"  class="h-full flex flex-col">
        <?php if ($isEdit): ?>
            <input type="hidden" name="id" value="<?php echo e($template['id'] ?? ''); ?>">
        <?php endif; ?>

        <!-- Progress Tracker -->
        <div class="container mx-auto p-6 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <div class="w-10 h-10 rounded-full bg-[#0C5BE2]/10 flex items-center justify-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 text-[#0C5BE2]" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M20 12V4H4v16h10M4 9h16M9 16h2M15 20l2 2 4-4"/>
                        </svg>
                    </div>
                    <div>
                        <h2 class="text-xl font-semibold text-gray-900" id="current-step-title">Basic Information</h2>
                        <p class="text-sm text-gray-500 mt-0.5" id="step-description">Enter the basic details of your template</p>
                    </div>
                </div>
                <div class="flex items-center space-x-3">
                    <span class="px-3 py-1 bg-[#0C5BE2]/10 text-[#0C5BE2] text-sm font-medium rounded-full">Step <span id="step-number">1</span> of 4</span>
                </div>
            </div>
        </div>

        <div class="bg-white flex flex-col flex-1">
            <!-- Form Content -->
            <div class="container mx-auto form-content p-6 flex-1">
                <div id="formSteps">
                    <!-- Step 1: Basic Information -->
                    <div class="step" id="step1">
                        <div class="grid grid-cols-10 gap-10">
                            <div class="col-span-5 space-y-5">
                                <!-- Template Name -->
                                <div class="form-group">
                                    <label class="block text-sm font-bold text-gray-700 mb-2">Template Name <span class="text-red-500">*</span></label>
                                    <input type="text" value="<?php echo e($template['name'] ?? ''); ?>" name="name" required class="w-full py-2 px-3 text-sm border border-gray-300 rounded-lg focus:border-[#0C5BE2] focus:ring-1 focus:ring-[#0C5BE2]/20" placeholder="Enter a descriptive name">
                                </div>
                                <!-- Tags -->
                                <div class="form-group">
                                    <label class="flex items-center text-sm font-medium text-gray-700 mb-2">
                                        Tags
                                        <button type="button" class="ml-1 group relative text-gray-400 hover:text-gray-500">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="w-3.5 h-3.5" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"/><path d="M12 16v-4"/><path d="M12 8h.01"/></svg>
                                            <span class="hidden group-hover:block absolute left-full ml-2 px-2 py-1 text-xs bg-gray-800 text-white rounded z-10 whitespace-nowrap">Press Enter or comma to add tags</span>
                                        </button>
                                    </label>
                                    <div class="relative">
                                        <div class="flex flex-wrap p-1 bg-white border border-gray-300 rounded-lg focus-within:border-[#0C5BE2] focus-within:ring-1">
                                            <div id="tags-container" class="flex flex-wrap gap-1 w-full"></div>
                                            <input type="text" id="tagInput" class="flex-1 min-w-[100px] py-1 px-2 text-sm border-0 focus:outline-none focus:ring-0" placeholder="Add tags (Enter or comma)">
                                        </div>
                                        <!-- Suggestions dropdown -->
                                        <div id="tagSuggestions" class="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg hidden">
                                            <!-- Suggestions will be populated here -->
                                        </div>
                                    </div>
                                </div>
                                <!-- Detailed Description -->
                                <div class="form-group">
                                    <label class="block text-sm font-bold text-gray-700 mb-2">Detailed Description</label>
                                    <style>
                                        .ck-editor__editable_inline { height: 270px; font-size: 14px !important; line-height: 1.6 !important;}
                                    </style>
                                    <textarea id="detailed_description" name="detailed_description" rows="5" class="w-full py-2 px-3 text-sm border border-gray-300 rounded-lg focus:border-[#0C5BE2] focus:ring-1" rows="2" placeholder="Comprehensive description of features and use cases"><?php echo e($template['detailed_description'] ?? ''); ?></textarea>
                                </div>
                                <script>
                                    document.addEventListener('DOMContentLoaded', function() {
                                        if (typeof ClassicEditor !== 'undefined') {
                                            ClassicEditor
                                                .create(document.querySelector('#detailed_description'), {
                                                    toolbar: {
                                                        items: ['heading', '|', 'bold', 'italic', 'link', 'bulletedList', 'numberedList', '|', 'undo', 'redo', '|', 'sourceEditing']
                                                    },
                                                    placeholder: 'Comprehensive description of features and use cases',
                                                    removePlugins: [],
                                                    htmlSupport: {
                                                        allow: [
                                                            {
                                                                name: /.*/,
                                                                attributes: true,
                                                                classes: true,
                                                                styles: true
                                                            }
                                                        ]
                                                    }
                                                })
                                                .then(editor => {
                                                    console.log('CKEditor initialized successfully');
                                                })
                                                .catch(error => {
                                                    console.error('CKEditor initialization error:', error);
                                                });
                                        } else {
                                            console.error('CKEditor is not loaded');
                                        }
                                    });
                                </script>
                                <!-- Template Status -->
                                <div class="form-group mt-4">
                                    <label class="block text-sm font-bold text-gray-700 mb-2">Template Status</label>
                                    <div class="radio-button-group mt-1">
                                        <input type="radio" name="template_status" value="0" id="status_draft" <?php echo (!isset($template['template_status']) || $template['template_status'] == 0) ? 'checked' : ''; ?>>
                                        <label for="status_draft">Draft</label>
                                        
                                        <input type="radio" name="template_status" value="1" id="status_published" <?php echo (isset($template['template_status']) && $template['template_status'] == 1) ? 'checked' : ''; ?>>
                                        <label for="status_published">Published</label>
                                        
                                        <input type="radio" name="template_status" value="2" id="status_archived" <?php echo (isset($template['template_status']) && $template['template_status'] == 2) ? 'checked' : ''; ?>>
                                        <label for="status_archived">Archived</label>
                                    </div>
                                </div>
                            </div>
                            <div class="col-span-5 space-y-5">
                                <!-- Category -->
                                <div class="form-group relative">
                                    <label class="block text-sm font-bold text-gray-700 mb-2">Category <span class="text-red-500">*</span></label>
                                    <div class="relative templateCategoryWrap">
                                        <select id="templateCategory" name="categories[]" class="select2 w-full text-sm" required multiple="multiple">
                                            <?php foreach ($categories as $category): ?>
                                                <option value="<?php echo e($category['id']); ?>" 
                                                    <?php 
                                                    if (isset($template['categories']) && is_array($template['categories'])) {
                                                        foreach ($template['categories'] as $templateCategory) {
                                                            if ($templateCategory['id'] == $category['id']) {
                                                                echo 'selected';
                                                                break;
                                                            }
                                                        }
                                                    }
                                                    ?>
                                                >
                                                    <?php echo e($category['name']); ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                    
                                    <label for="showNewCategory" class="text-xs flex items-center absolute top-0 right-0 cursor-pointer bg-[#0C5BE2]/10 text-[#0C5BE2] hover:bg-[#0C5BE2] hover:text-white font-medium rounded px-2 py-1">
                                        <input type="checkbox" id="showNewCategory" class="w-3 h-3 text-[#0C5BE2] border-gray-300 visible-hidden opacity-0 w-0 h-0 absolute">
                                        Add new category
                                    </label>
                                    
                                    <div id="newCategoryInput" class="w-full bg-white hidden">
                                        <div class="flex space-x-2">
                                            <input type="text" id="newCategoryName" class="w-full py-2 px-3 text-sm border border-gray-300 rounded-lg focus:border-[#0C5BE2] focus:ring-1" placeholder="New category name">
                                            <button type="button" onclick="addNewCategory()" class="px-3 py-2 bg-[#0C5BE2] text-white text-sm rounded-lg hover:bg-[#0C5BE2]/90">Add</button>
                                        </div>
                                    </div>
                                </div>
                                <!-- Short Description -->
                                <div class="form-group">
                                    <label class="block text-sm font-bold text-gray-700 mb-2">Short Description <span class="text-red-500">*</span></label>
                                    <input type="text" value="<?php echo e($template['short_description'] ?? ''); ?>" name="short_description" class="w-full py-2 px-3 text-sm border border-gray-300 rounded-lg focus:border-[#0C5BE2] focus:ring-1 focus:ring-[#0C5BE2]/20" placeholder="Sort description (max 160 characters)">
                                </div>
                                <!-- Template Screenshots -->
                                <div class="form-group">
                                    <label class="block text-sm font-bold text-gray-700 mb-2">Template Screenshots</label>
                                    <div class="mt-1">
                                        <div id="screenshotDropZone" class="w-full min-h-[200px] border-2 border-gray-300 border-dashed rounded-lg p-4">
                                            <div id="screenshotPreviews" class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                                                <?php
                                                if (!empty($template['multiple_template_screenshot'])) {
                                                    $screenshots = is_string($template['multiple_template_screenshot']) 
                                                        ? json_decode($template['multiple_template_screenshot'], true) 
                                                        : $template['multiple_template_screenshot'];
                                                    
                                                    if (is_array($screenshots)) {
                                                        foreach ($screenshots as $screenshot) {
                                                            // Remove any escaped slashes and quotes
                                                            $screenshot = str_replace(['\/', '\"'], ['/', ''], $screenshot);
                                                            echo '<div class="relative aspect-video group">';
                                                            echo '<img src="' . BASE_URL . $screenshot . '" class="w-full h-full object-contain rounded-lg" alt="Screenshot">';
                                                            echo '<div class="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity rounded-lg flex items-center justify-center">';
                                                            echo '<button type="button" class="text-white hover:text-red-500" onclick="removeScreenshot(this)">';
                                                            echo '<svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                                            </svg>';
                                                            echo '</button>';
                                                            echo '</div>';
                                                            echo '</div>';
                                                        }
                                                    }
                                                }
                                                ?>
                                            </div>
                                            <input type="file" name="template_screenshots[]" id="screenshotUpload" multiple accept="image/*" class="hidden">
                                            <div class="text-center mt-4">
                                                <button type="button" onclick="document.getElementById('screenshotUpload').click()" class="px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none">
                                                    Add Screenshots
                                                </button>
                                                <p class="text-xs text-gray-500 mt-2">Drag and drop images here or click to upload</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="grid grid-cols-2 gap-10">
                                    <!-- Logo Image -->
                                    <div class="form-group">
                                        <label class="block text-sm font-bold text-gray-700 mb-2">Logo Image</label>
                                        <div class="mt-1 flex items-center">
                                            <div class="relative w-full">
                                                <div id="logoPreview" class="w-full h-32 border-2 border-gray-300 border-dashed rounded-lg flex items-center justify-center overflow-hidden">
                                                    <?php if (!empty($template['logo_image'])): ?>
                                                        <img src="<?php echo BASE_URL . $template['logo_image']; ?>" alt="Logo Preview" class="max-w-full max-h-full object-contain">
                                                    <?php else: ?>
                                                        <svg class="w-8 h-8 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                                        </svg>
                                                    <?php endif; ?>
                                                </div>
                                                <input type="file" name="logo_image" id="logo_image" class="hidden" accept="image/*">
                                                <button type="button" onclick="document.getElementById('logo_image').click()" class="mt-2 w-full px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none">
                                                    <?php echo !empty($template['logo_image']) ? 'Change Logo' : 'Upload Logo'; ?>
                                                </button>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Favicon Image -->
                                    <div class="form-group">
                                        <label class="block text-sm font-bold text-gray-700 mb-2">Favicon Image</label>
                                        <div class="mt-1 flex items-center">
                                            <div class="relative w-full">
                                                <div id="faviconPreview" class="w-full h-32 border-2 border-gray-300 border-dashed rounded-lg flex items-center justify-center overflow-hidden">
                                                    <?php if (!empty($template['favicon_image'])): ?>
                                                        <img src="<?php echo BASE_URL . $template['favicon_image']; ?>" alt="Favicon Preview" class="max-w-full max-h-full object-contain">
                                                    <?php else: ?>
                                                        <svg class="w-8 h-8 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                                        </svg>
                                                    <?php endif; ?>
                                                </div>
                                                <input type="file" name="favicon_image" id="favicon_image" class="hidden" accept="image/*">
                                                <button type="button" onclick="document.getElementById('favicon_image').click()" class="mt-2 w-full px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none">
                                                    <?php echo !empty($template['favicon_image']) ? 'Change Favicon' : 'Upload Favicon'; ?>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Step 2: Site Settings -->
                    <div class="step hidden" id="step2">
                        <div class="grid gap-6">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-10">
                                <!-- Left Column -->
                                <div class="space-y-3">
                                    <!-- Site Title -->
                                    <div class="form-group">
                                        <label class="block text-sm font-bold text-gray-700 mb-2"> Site Title <span class="text-red-500">*</span> </label>
                                        <input type="text" name="site_title" value="<?php echo e($template['site_title'] ?? ''); ?>" required class="w-full py-2 px-3 text-sm border border-gray-300 rounded-lg focus:border-[#0C5BE2] focus:ring-1 focus:ring-[#0C5BE2]/20" placeholder="Enter site title">
                                        <p class="mt-0.5 text-xs text-gray-500">Enter your website's main title or brand name</p>
                                    </div>

                                    <!-- Site Description -->
                                    <div class="form-group">
                                        <label class="block text-sm font-bold text-gray-700 mb-2"> Site Description </label>
                                        <textarea name="site_description" rows="3" required class="w-full py-2 px-3 text-sm border border-gray-300 rounded-lg focus:border-[#0C5BE2] focus:ring-1" rows="2" placeholder="Enter site description for SEO"><?php echo e($template['site_description'] ?? ''); ?></textarea>
                                        <p class="mt-0.5 text-xs text-gray-500">Recommended: 150-160 characters</p>
                                    </div>
                                </div>
                                
                                <!-- Right Column -->
                                <div class="space-y-3">
                                    <!-- Meta Keywords -->
                                    <div class="form-group">
                                        <label class="block text-sm font-bold text-gray-700 mb-2"> Meta Keywords </label>
                                        <input type="text" name="meta_keywords" value="<?php echo e($template['meta_keywords'] ?? ''); ?>" class="w-full py-2 px-3 text-sm border border-gray-300 rounded-lg focus:border-[#0C5BE2] focus:ring-1" placeholder="keyword1, keyword2, keyword3">
                                        <p class="mt-0.5 text-xs text-gray-500">Separate keywords with commas</p>
                                    </div>
                                    
                                    <!-- Meta Description -->
                                    <div class="form-group">
                                        <label class="block text-sm font-bold text-gray-700 mb-2"> Meta Description </label>
                                        <textarea name="meta_description" rows="3" class="w-full py-2 px-3 text-sm border border-gray-300 rounded-lg focus:border-[#0C5BE2] focus:ring-1" rows="2" placeholder="Enter meta description"><?php echo e($template['meta_description'] ?? ''); ?></textarea>
                                        <p class="mt-0.5 text-xs text-gray-500">Recommended: 150-160 characters</p>
                                    </div>
                                </div>
                            </div>

                            <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-6">
                                <!-- Body Font Family -->
                                <div class="form-group">
                                    <label class="block text-sm font-bold text-gray-700 mb-2">
                                        Body Font
                                    </label>
                                    <div class="relative">
                                        <select name="body_font_family" class="w-full py-2 px-3 text-sm border border-gray-300 rounded-lg appearance-none focus:border-[#0C5BE2] focus:ring-1">
                                        <option value="">Select Font Family</option>
                                        <option value="inter" <?php echo ($template['body_font_family'] ?? '') === 'inter' ? 'selected' : ''; ?>>Inter</option>
                                        <option value="roboto" <?php echo ($template['body_font_family'] ?? '') === 'roboto' ? 'selected' : ''; ?>>Roboto</option>
                                        <option value="opensans" <?php echo ($template['body_font_family'] ?? '') === 'opensans' ? 'selected' : ''; ?>>Open Sans</option>
                                        <option value="lato" <?php echo ($template['body_font_family'] ?? '') === 'lato' ? 'selected' : ''; ?>>Lato</option>
                                        <option value="poppins" <?php echo ($template['body_font_family'] ?? '') === 'poppins' ? 'selected' : ''; ?>>Poppins</option>
                                        <option value="noto-sans" <?php echo ($template['body_font_family'] ?? '') === 'noto-sans' ? 'selected' : ''; ?>>Noto Sans</option>
                                        <option value="montserrat" <?php echo ($template['body_font_family'] ?? '') === 'montserrat' ? 'selected' : ''; ?>>Montserrat</option>
                                        <option value="source-sans-pro" <?php echo ($template['body_font_family'] ?? '') === 'source-sans-pro' ? 'selected' : ''; ?>>Source Sans Pro</option>
                                        </select>
                                        <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-gray-500" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M6 9l6 6 6-6"/></svg>
                                        </div>
                                    </div>
                                </div>

                                <!-- Body Font Size -->
                                <div class="form-group">
                                    <label class="block text-sm font-bold text-gray-700 mb-2">
                                        Base Font Size
                                    </label>
                                    <div class="flex items-center space-x-2">
                                        <input type="number" name="body_font_size" value="<?php echo e($template['body_font_size'] ?? '16'); ?>" 
                                        min="12" max="24" step="1"  class="w-full py-2 px-3 text-sm border border-gray-300 rounded-lg focus:border-[#0C5BE2] focus:ring-1" placeholder="16">
                                        <span class="text-sm text-gray-500">px</span>
                                    </div>
                                </div>
                                
                                <!-- Heading Font Family -->
                                <div class="form-group">
                                    <label class="block text-sm font-bold text-gray-700 mb-2">
                                        Heading Font
                                    </label>
                                    <div class="relative">
                                        <select name="heading_font_family" class="w-full py-2 px-3 text-sm border border-gray-300 rounded-lg appearance-none focus:border-[#0C5BE2] focus:ring-1">
                                            <option value="">Select Font Family</option>
                                            <option value="Playfair Display" <?php echo ($template['heading_font_family'] ?? '') === 'Playfair Display' ? 'selected' : ''; ?>>Playfair Display</option>
                                            <option value="Merriweather" <?php echo ($template['heading_font_family'] ?? '') === 'Merriweather' ? 'selected' : ''; ?>>Merriweather</option>
                                            <option value="Roboto Slab" <?php echo ($template['heading_font_family'] ?? '') === 'Roboto Slab' ? 'selected' : ''; ?>>Roboto Slab</option>
                                            <option value="Montserrat" <?php echo ($template['heading_font_family'] ?? '') === 'Montserrat' ? 'selected' : ''; ?>>Montserrat</option>
                                            <option value="Oswald" <?php echo ($template['heading_font_family'] ?? '') === 'Oswald' ? 'selected' : ''; ?>>Oswald</option>
                                            <option value="Lora" <?php echo ($template['heading_font_family'] ?? '') === 'Lora' ? 'selected' : ''; ?>>Lora</option>
                                            <option value="Source Serif Pro" <?php echo ($template['heading_font_family'] ?? '') === 'Source Serif Pro' ? 'selected' : ''; ?>>Source Serif Pro</option>
                                            <option value="PT Serif" <?php echo ($template['heading_font_family'] ?? '') === 'PT Serif' ? 'selected' : ''; ?>>PT Serif</option>
                                            <option value="Libre Baskerville" <?php echo ($template['heading_font_family'] ?? '') === 'Libre Baskerville' ? 'selected' : ''; ?>>Libre Baskerville</option>
                                            <option value="Crimson Text" <?php echo ($template['heading_font_family'] ?? '') === 'Crimson Text' ? 'selected' : ''; ?>>Crimson Text</option>
                                        </select>
                                        <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-gray-500" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M6 9l6 6 6-6"/></svg>
                                        </div>
                                    </div>
                                </div>

                                <!-- Line Heights -->
                                <div class="form-group">
                                    <label class="block text-sm font-bold text-gray-700 mb-2">
                                        Line Height
                                    </label>
                                    <input type="number" name="line_height" value="<?php echo e($template['line_height'] ?? '1.5'); ?>" 
                                    min="1" max="2" step="0.1" class="w-full py-2 px-3 text-sm border border-gray-300 rounded-lg focus:border-[#0C5BE2] focus:ring-1" placeholder="1.5">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Step 3: Appearance -->
                    <div class="step hidden" id="step3">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-10">
                            <div class="space-y-4">
                                <h3 class="text-base font-medium text-gray-900 mb-2">Color Theme</h3>
                                
                                <!-- Theme Selection Pills -->
                                <div class="flex flex-wrap gap-2 mb-3">
                                    <button type="button" class="bg-white px-3 py-1.5 rounded-full border border-[#0C5BE2] text-[#0C5BE2] text-xs font-medium shadow-sm" data-theme="corporate-light" data-color-code="#0C5BE2,#F3F4F6,#4F46E5">
                                        <span class="flex items-center">
                                        <span class="w-3 h-3 rounded-full bg-[#0C5BE2] mr-1.5"></span>
                                        Corporate Light
                                        </span>
                                    </button>
                                    <button type="button" class="bg-white px-3 py-1.5 rounded-full border border-gray-200 text-gray-700 text-xs font-medium hover:border-gray-300" data-theme="corporate-dark" data-color-code="#1F2937,#374151,#6366F1">
                                        <span class="flex items-center">
                                        <span class="w-3 h-3 rounded-full bg-gray-900 mr-1.5"></span>
                                        Corporate Dark
                                        </span>
                                    </button>
                                    <button type="button" class="bg-white px-3 py-1.5 rounded-full border border-gray-200 text-gray-700 text-xs font-medium hover:border-gray-300" data-theme="nature" data-color-code="#059669,#D1FAE5,#047857">
                                        <span class="flex items-center">
                                        <span class="w-3 h-3 rounded-full bg-[#059669] mr-1.5"></span>
                                        Nature
                                        </span>
                                    </button>
                                    <button type="button" class="bg-white px-3 py-1.5 rounded-full border border-gray-200 text-gray-700 text-xs font-medium hover:border-gray-300" data-theme="ocean" data-color-code="#0EA5E9,#E0F2FE,#0284C7">
                                        <span class="flex items-center">
                                        <span class="w-3 h-3 rounded-full bg-[#0EA5E9] mr-1.5"></span>
                                        Ocean
                                        </span>
                                    </button>
                                    <button type="button" class="bg-white px-3 py-1.5 rounded-full border border-gray-200 text-gray-700 text-xs font-medium hover:border-gray-300" data-theme="warm" data-color-code="#F97316,#FFF7ED,#EA580C">
                                        <span class="flex items-center">
                                        <span class="w-3 h-3 rounded-full bg-[#F97316] mr-1.5"></span>
                                        Warm
                                        </span>
                                    </button>
                                    <button type="button" class="bg-white px-3 py-1.5 rounded-full border border-gray-200 text-gray-700 text-xs font-medium hover:border-gray-300" data-theme="custom">
                                        <span class="flex items-center">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1.5 text-gray-500" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"/><line x1="12" y1="8" x2="12" y2="16"/><line x1="8" y1="12" x2="16" y2="12"/></svg>
                                        Custom
                                        </span>
                                    </button>
                                </div>
                                
                                <!-- Color Grid -->
                                <div class="grid grid-cols-1 md:grid-cols-3 gap-3 hidden" id="customColorGrid">
                                    <!-- Primary Color -->
                                    <div class="p-2 border border-gray-200 rounded-lg bg-white">
                                        <label class="block text-xs text-gray-700 mb-1">Primary Color</label>
                                        <div class="flex items-center space-x-1">
                                        <input type="color" id="primaryColor" value="#0C5BE2" class="w-8 h-8 rounded p-0 cursor-pointer">
                                        <input type="text" id="primaryColorHex" value="#0C5BE2" class="w-full h-8 px-2 text-xs border border-gray-300 rounded-md bg-white focus:outline-none focus:border-[#0C5BE2] focus:ring-1">
                                        </div>
                                        <div class="mt-1 text-xs text-gray-500">Buttons, links, accents</div>
                                    </div>
                                    
                                    <!-- Secondary Color -->
                                    <div class="p-2 border border-gray-200 rounded-lg bg-white">
                                        <label class="block text-xs text-gray-700 mb-1">Secondary Color</label>
                                        <div class="flex items-center space-x-1">
                                        <input type="color" id="secondaryColor" value="#F3F4F6" class="w-8 h-8 rounded p-0 cursor-pointer">
                                        <input type="text" id="secondaryColorHex" value="#F3F4F6" class="w-full h-8 px-2 text-xs border border-gray-300 rounded-md bg-white focus:outline-none focus:border-[#0C5BE2] focus:ring-1">
                                        </div>
                                        <div class="mt-1 text-xs text-gray-500">Backgrounds, inactive states</div>
                                    </div>
                                    
                                    <!-- Accent Color -->
                                    <div class="p-2 border border-gray-200 rounded-lg bg-white">
                                        <label class="block text-xs text-gray-700 mb-1">Accent Color</label>
                                        <div class="flex items-center space-x-1">
                                        <input type="color" id="accentColor" value="#4F46E5" class="w-8 h-8 rounded p-0 cursor-pointer">
                                        <input type="text" id="accentColorHex" value="#4F46E5" class="w-full h-8 px-2 text-xs border border-gray-300 rounded-md bg-white focus:outline-none focus:border-[#0C5BE2] focus:ring-1">
                                        </div>
                                        <div class="mt-1 text-xs text-gray-500">Highlights, call-to-actions</div>
                                    </div>
                                </div>

                                <!-- Tailwind Configuration -->
                                <div class="mt-4">
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Tailwind Configuration</label>
                                    <div class="relative">
                                        <textarea id="tailwindConfig" name="tailwind_configuration_code" rows="12" class="w-full font-mono text-sm bg-gray-50 p-3 border border-gray-200 rounded-lg" readonly><?php echo e($template['tailwind_configuration_code'] ?? ''); ?></textarea>
                                        <button type="button" onclick="copyConfig()" class="absolute top-2 right-2 p-1.5 text-gray-500 hover:text-gray-700 bg-white border border-gray-200 rounded-lg shadow-sm">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                <rect x="9" y="9" width="13" height="13" rx="2" ry="2"/>
                                                <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"/>
                                            </svg>
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <style>
                                :root {
                                    <?php
                                    if (!empty($template['tailwind_configuration_code'])) {
                                        $config = $template['tailwind_configuration_code'];
                                        // Extract the JSON object part
                                        if (preg_match('/tailwind\.config\s*=\s*({[\s\S]*})/', $config, $matches)) {
                                            $jsonStr = $matches[1];
                                            // Clean the string
                                            $jsonStr = preg_replace('/\\n/', '', $jsonStr);
                                            $jsonStr = preg_replace('/\\"/', '"', $jsonStr); // Fix escaped quotes
                                            $jsonStr = preg_replace('/\s+/', ' ', $jsonStr); // Normalize whitespace
                                            
                                            try {
                                                $colors = json_decode($jsonStr);
                                                if ($colors && isset($colors->theme->extend->colors)) {
                                                    $primary = $colors->theme->extend->colors->primary;
                                                    $secondary = $colors->theme->extend->colors->secondary;
                                                    $accent = $colors->theme->extend->colors->accent;
                                                } else {
                                                    throw new Exception('Invalid color structure');
                                                }
                                            } catch (Exception $e) {
                                                error_log('Error parsing colors: ' . $e->getMessage() . '\nJSON: ' . $jsonStr);
                                                $primary = '#4F46E5';
                                                $secondary = '#0EA5E9';
                                                $accent = '#F59E0B';
                                            }
                                        } else {
                                            $primary = '#4F46E5';
                                            $secondary = '#0EA5E9';
                                            $accent = '#F59E0B';
                                        }
                                    } else {
                                        $primary = '#4F46E5';
                                        $secondary = '#0EA5E9';
                                        $accent = '#F59E0B';
                                    }
                                    ?>
                                    --prev-primary-color: <?php echo e($primary); ?>;
                                    --prev-secondary-color: <?php echo e($secondary); ?>;
                                    --prev-accent-color: <?php echo e($accent); ?>;
                                }
                                .preview-bg-primary { background-color: var(--prev-primary-color); }
                                .preview-bg-secondary { background-color: var(--prev-secondary-color); }
                                .preview-bg-accent { background-color: var(--prev-accent-color); }
                                .preview-text-primary { color: var(--prev-primary-color); }
                                .preview-text-secondary { color: var(--prev-secondary-color); }
                                .preview-text-accent { color: var(--prev-accent-color); }
                            </style>
                            <!-- Color Theme Preview -->
                            <div class="border border-gray-200 rounded-lg p-4 bg-white">
                                <h4 class="text-sm font-medium text-gray-900 mb-4">Color Theme Preview</h4>
                                
                                <?php include 'colorthemePreview.php'; ?>
                            </div>
                        </div>
                    </div>

                    <!-- Step 4: Advanced -->
                    <div class="step hidden" id="step4">
                        <div class="grid grid-cols-2 gap-6">
                            <div class="form-group">
                                <label class="block text-sm font-medium text-gray-700 mb-1">Custom CSS</label>
                                <textarea name="custom_css" rows="10" class="w-full py-2 px-3 text-sm font-mono border border-gray-300 rounded-lg focus:border-[#0C5BE2] focus:ring-1 focus:ring-[#0C5BE2]/20"><?php echo e($template['custom_css'] ?? ''); ?></textarea>
                            </div>
                            <div class="form-group">
                                <label class="block text-sm font-medium text-gray-700 mb-1">Custom JavaScript</label>
                                <textarea name="custom_js" rows="10" class="w-full py-2 px-3 text-sm font-mono border border-gray-300 rounded-lg focus:border-[#0C5BE2] focus:ring-1 focus:ring-[#0C5BE2]/20"><?php echo e($template['custom_js'] ?? ''); ?></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Form Navigation -->
            <div class="form-navigation px-6 py-4 bg-white border-t border-gray-200 flex justify-between items-center sticky bottom-0 z-10 shadow-lg">
                <a class="inline-flex items-center text-sm font-medium px-4 py-2 border rounded-lg bg-black text-white border-black transition-all" href="<?php echo BASE_URL ?>/templates">Back to Templates</a>
                <div class="space-x-3">
                    <button type="button" id="prevBtn" class="inline-flex items-center text-sm font-medium px-4 py-2 border border-gray-300 bg-white text-gray-700 rounded-lg hover:bg-gray-50 transition-all disabled:opacity-50 disabled:cursor-not-allowed">
                        Previous Step
                    </button>
                    <button type="button" id="nextBtn" class="inline-flex items-center text-sm font-medium px-4 py-2 border border-[#0C5BE2] bg-[#0C5BE2] text-white rounded-lg hover:bg-[#0C5BE2]/90 hover:border-[#0C5BE2]/90">
                        Next Step
                    </button>
                    <button type="submit" id="submitBtn" class="hidden items-center text-sm font-medium px-4 py-2 border border-green-600 bg-green-600 text-white rounded-lg hover:bg-green-700 hover:border-green-600 transition-all">
                        Save Template
                    </button>
                </div>
            </div>
        </div>
    </form>
    </div>
<script>
// Enhanced tag management with separate handling
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Select2 for categories
    $('#templateCategory').select2({
        theme: 'default',
        placeholder: 'Select categories',
        allowClear: true,
        width: '100%',
        closeOnSelect: false,
        selectionCssClass: 'select2--large',
        templateResult: function(data) {
            if (!data.id) return data.text;
            return $('<span class="text-sm">' + data.text + '</span>');
        },
        templateSelection: function(data) {
            if (!data.id) return data.text;
            return $('<span class="text-sm">' + data.text + '</span>');
        }
    });

    // Tag management variables
    const tagInput = document.getElementById('tagInput');
    const tagContainer = document.getElementById('tags-container');
    const tagSuggestions = document.getElementById('tagSuggestions');
    let selectedTags = new Set();
    let debounceTimer;

    // Function to create a tag element
    function createTagElement(tagData) {
        const tag = document.createElement('span');
        tag.className = 'inline-flex items-center px-2 py-1 bg-[#0C5BE2]/10 text-[#0C5BE2] text-xs font-medium rounded';
        
        const tagText = tagData.tag || tagData.tag_name || 'Unknown Tag';
        
        tag.innerHTML = `
            ${tagText}
            <button type="button" class="ml-1" onclick="window.removeTag('${tagData.id}')">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3" viewBox="0 0 20 20" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                </svg>
            </button>
        `;
        tag.dataset.tagId = tagData.id;
        return tag;
    }

    // Function to create hidden input for form submission
    function createHiddenTagInput(tagId) {
        const hiddenInput = document.createElement('input');
        hiddenInput.type = 'hidden';
        hiddenInput.name = 'tags[]';
        hiddenInput.value = tagId;
        hiddenInput.dataset.tagId = tagId;
        return hiddenInput;
    }

    // Function to remove tag
    window.removeTag = function(tagId) {
        selectedTags.delete(tagId);
        
        const tagElement = document.querySelector(`span[data-tag-id="${tagId}"]`);
        if (tagElement) {
            tagElement.remove();
        }
        
        const hiddenInput = document.querySelector(`input[data-tag-id="${tagId}"]`);
        if (hiddenInput) {
            hiddenInput.remove();
        }
    };

    // Function to search tags via API
    async function searchTags(query) {
        try {
            const response = await fetch(`<?php echo BASE_URL ?>/modules/tags/tags_search.php?search=${encodeURIComponent(query)}`);
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const text = await response.text();
            console.log('Search response:', text); // Debug log
            
            let data;
            try {
                data = JSON.parse(text);
            } catch (e) {
                console.error('JSON parse error:', e, 'Response:', text);
                return [];
            }

            
            if (data.success && data.data && Array.isArray(data.data)) {
                return data.data.map(tag => ({
                    id: tag.id,
                    tag: tag.tag,
                    created_at: tag.created_at,
                    updated_at: tag.updated_at
                }));
            }
            return [];
        } catch (error) {
            console.error('Error searching tags:', error);
            return [];
        }
    }

    // Function to create a new tag via API
    async function createTag(name) {
        try {
            const response = await fetch('<?php echo BASE_URL ?>/modules/tags/tags_create.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ tag: name.trim() })
            });
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const text = await response.text();
            console.log('Create response:', text); // Debug log
            
            let data;
            try {
                data = JSON.parse(text);
            } catch (e) {
                console.error('JSON parse error:', e, 'Response:', text);
                throw new Error('Invalid response from server');
            }
            
            if (data.success && data.data) {
                return {
                    id: data.data.id || data.data.last_insert_id,
                    tag: name.trim()
                };
            }
            
            throw new Error(data.message || 'Failed to create tag');
        } catch (error) {
            console.error('Error creating tag:', error);
            throw error;
        }
    }

    // Function to add a tag
    async function addTag(tagInput) {
        let tagData;
        
        try {
            if (typeof tagInput === 'string') {
                // Create new tag
                tagData = await createTag(tagInput);
                if (!tagData) return;
            } else {
                // It's an existing tag object
                tagData = tagInput;
            }
            
            // Check if tag is already selected
            if (selectedTags.has(tagData.id.toString())) {
                return;
            }
            
            // Add to selected tags
            selectedTags.add(tagData.id.toString());
            
            // Create visual tag element
            tagContainer.appendChild(createTagElement(tagData));
            
            // Create hidden input for form submission
            const form = document.getElementById('templateForm');
            form.appendChild(createHiddenTagInput(tagData.id));
            
            // Clear input and hide suggestions
            tagInput.value = '';
            tagSuggestions.classList.add('hidden');
            
        } catch (error) {
            alert('Failed to add tag: ' + error.message);
        }
    }

    // Function to show suggestions
    function showSuggestions(suggestions) {
        tagSuggestions.innerHTML = '';
        
        const filteredSuggestions = suggestions.filter(tag => 
            !selectedTags.has(tag.id.toString())
        );
        
        if (filteredSuggestions.length === 0 && tagInput.value.trim()) {
            const div = document.createElement('div');
            div.className = 'px-3 py-2 text-sm text-gray-500 italic cursor-pointer hover:bg-gray-50';
            div.innerHTML = `Create new tag: "${tagInput.value.trim()}"`;
            div.onclick = () => addTag(tagInput.value.trim());
            tagSuggestions.appendChild(div);
        } else {
            filteredSuggestions.forEach(tag => {
                const div = document.createElement('div');
                div.className = 'px-3 py-2 hover:bg-gray-50 cursor-pointer text-sm';
                div.textContent = tag.tag;
                div.onclick = () => addTag(tag);
                tagSuggestions.appendChild(div);
            });
            
            if (tagInput.value.trim() && !filteredSuggestions.some(tag => 
                tag.tag.toLowerCase() === tagInput.value.trim().toLowerCase()
            )) {
                const div = document.createElement('div');
                div.className = 'px-3 py-2 hover:bg-gray-50 cursor-pointer text-sm text-[#0C5BE2] border-t border-gray-200';
                div.innerHTML = `Create: "${tagInput.value.trim()}"`;
                div.onclick = () => addTag(tagInput.value.trim());
                tagSuggestions.appendChild(div);
            }
        }

        tagSuggestions.classList.remove('hidden');
    }

    // Input event listeners
    tagInput.addEventListener('input', function() {
        clearTimeout(debounceTimer);
        const query = this.value.trim();
        
        if (query.length >= 1) {
            debounceTimer = setTimeout(async () => {
                const suggestions = await searchTags(query);
                showSuggestions(suggestions);
            }, 300);
        } else {
            tagSuggestions.classList.add('hidden');
        }
    });

    tagInput.addEventListener('keydown', async function(e) {
        if ((e.key === 'Enter' || e.key === ',') && this.value.trim()) {
            e.preventDefault();
            const tagName = this.value.trim().replace(/,/g, '');
            if (tagName) {
                await addTag(tagName);
            }
        } else if (e.key === 'Escape') {
            tagSuggestions.classList.add('hidden');
        }
    });

    // Close suggestions when clicking outside
    document.addEventListener('click', function(e) {
        if (!tagInput.contains(e.target) && !tagSuggestions.contains(e.target)) {
            tagSuggestions.classList.add('hidden');
        }
    });

    // Initialize with existing tags if editing
    <?php if ($isEdit && !empty($template['tags'])): ?>
        // Load existing tags
        const existingTags = <?php echo json_encode($template['tags']); ?>;
        existingTags.forEach(function(tagData) {
            const existingTag = {
                id: tagData.id,
                tag: tagData.tag || tagData.name
            };
            selectedTags.add(existingTag.id.toString());
            tagContainer.appendChild(createTagElement(existingTag));
            
            const form = document.getElementById('templateForm');
            form.appendChild(createHiddenTagInput(existingTag.id));
        });
    <?php endif; ?>

    // Form submission handler
    document.getElementById('templateForm').addEventListener('submit', function(e) {
        // Ensure all selected tags have hidden inputs
        selectedTags.forEach(tagId => {
            const existingInput = document.querySelector(`input[data-tag-id="${tagId}"]`);
            if (!existingInput) {
                const hiddenInput = createHiddenTagInput(tagId);
                this.appendChild(hiddenInput);
            }
        });
        
        console.log('Submitting template with tags:', Array.from(selectedTags));
    });
});

// Initialize step functionality
let currentStep = 1;
const totalSteps = 4;
const formSteps = document.querySelectorAll('.step');
const nextBtn = document.getElementById('nextBtn');
const prevBtn = document.getElementById('prevBtn');
const submitBtn = document.getElementById('submitBtn');
const stepCounter = document.querySelector('.rounded-full.font-medium > span');
const stepIcon = document.querySelector('.w-10.h-10 svg');

const steps = [
    {
        title: 'Basic Information',
        description: 'Enter the basic details of your template',
        icon: '<path d="M20 12V4H4v16h10M4 9h16M9 16h2M15 20l2 2 4-4"/>',
        requiredFields: ['name', 'short_description']
    },
    {
        title: 'Site Settings',
        description: 'Configure your site settings and metadata',
        icon: '<path d="M12 15a3 3 0 1 0 0-6 3 3 0 0 0 0 6Z"/><path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 1 1-2.83 2.83l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 1 1-4 0v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 1 1-2.83-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 1 1 0-4h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 1 1 2.83-2.83l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 1 1 4 0v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 1 1 2.83 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9c.26.604.852.997 1.51 1H21a2 2 0 1 1 0 4h-.09a1.65 1.65 0 0 0-1.51 1Z"/>',
        requiredFields: ['site_title', 'site_description']
    },
    {
        title: 'Appearance',
        description: 'Customize the look and feel of your template',
        icon: '<path d="M12 19l7-7 3 3-7 7-3-3z"/><path d="M18 13l-1.5-7.5L2 2l3.5 14.5L13 18l5-5z"/><path d="M2 2l7.586 7.586"/><circle cx="11" cy="11" r="2"/>',
        requiredFields: []
    },
    {
        title: 'Advanced',
        description: 'Configure advanced settings and custom code',
        icon: '<path d="M16 18 22 12 16 6M8 6 2 12 8 18"/>',
        requiredFields: []
    }
];

function validateStep(step) {
    const currentStepEl = document.getElementById('step' + step);
    const requiredFields = steps[step - 1].requiredFields;
    let isValid = true;
    let firstInvalidField = null;

    // Remove existing error messages
    currentStepEl.querySelectorAll('.error-message').forEach(el => el.remove());
    currentStepEl.querySelectorAll('.border-red-500').forEach(el => {
        el.classList.remove('border-red-500');
        el.classList.add('border-gray-300');
    });

    // Validate each required field
    requiredFields.forEach(fieldName => {
        const field = currentStepEl.querySelector(`[name="${fieldName}"]`);
        if (field) {
            let fieldValue = field.value.trim();
            
            // Special handling for select2 multiple
            if (field.classList.contains('select2-hidden-accessible')) {
                fieldValue = $(field).val(); // Using jQuery for select2
            }

            if (!fieldValue || (Array.isArray(fieldValue) && fieldValue.length === 0)) {
                isValid = false;
                
                // Add red border
                if (field.classList.contains('select2-hidden-accessible')) {
                    field.nextElementSibling.classList.add('border-red-500');
                    field.nextElementSibling.classList.remove('border-gray-300');
                } else {
                    field.classList.remove('border-gray-300');
                    field.classList.add('border-red-500');
                }
                
                // Add error message
                const errorDiv = document.createElement('div');
                errorDiv.className = 'error-message text-red-500 text-xs mt-1';
                errorDiv.textContent = 'This field is required';
                
                // For select2, add error after the select2 container
                if (field.classList.contains('select2-hidden-accessible')) {
                    field.nextElementSibling.insertAdjacentElement('afterend', errorDiv);
                } else {
                    field.parentNode.appendChild(errorDiv);
                }

                if (!firstInvalidField) {
                    firstInvalidField = field;
                }
            }
        }
    });

    if (!isValid && firstInvalidField) {
        // Scroll to the first invalid field
        firstInvalidField.scrollIntoView({ behavior: 'smooth', block: 'center' });
        // For select2 fields, focus on the search box
        if (firstInvalidField.classList.contains('select2-hidden-accessible')) {
            $(firstInvalidField).select2('open');
        } else {
            firstInvalidField.focus();
        }
    }

    return isValid;
}

function updateStep(step) {
    // Hide all steps
    formSteps.forEach(s => s.classList.add('hidden'));
    
    // Show current step
    document.getElementById('step' + step).classList.remove('hidden');
    
    // Update step counter
    stepCounter.textContent = step;
    
    // Update step icon
    stepIcon.innerHTML = steps[step - 1].icon;
    
    // Update buttons
    prevBtn.disabled = step === 1;
    if (step === totalSteps) {
        nextBtn.classList.add('hidden');
        submitBtn.classList.remove('hidden');
        submitBtn.classList.add('inline-flex');
    } else {
        nextBtn.classList.remove('hidden');
        submitBtn.classList.add('hidden');
        submitBtn.classList.remove('inline-flex');
    }
    
    // Update step title and description
    document.getElementById('current-step-title').textContent = steps[step - 1].title;
    document.getElementById('step-description').textContent = steps[step - 1].description;
}

nextBtn.addEventListener('click', () => {
    if (currentStep < totalSteps) {
        if (validateStep(currentStep)) {
            currentStep++;
            updateStep(currentStep);
        }
    }
});

prevBtn.addEventListener('click', () => {
    if (currentStep > 1) {
        currentStep--;
        updateStep(currentStep);
    }
});

// Color Theme Configuration
const colorThemes = {
    'corporate-light': {
        primary: '#0C5BE2',
        secondary: '#F3F4F6',
        accent: '#4F46E5'
    },
    'corporate-dark': {
        primary: '#1F2937',
        secondary: '#374151',
        accent: '#6366F1'
    },
    'nature': {
        primary: '#059669',
        secondary: '#D1FAE5',
        accent: '#047857'
    },
    'ocean': {
        primary: '#0EA5E9',
        secondary: '#E0F2FE',
        accent: '#0284C7'
    },
    'warm': {
        primary: '#F97316',
        secondary: '#FFF7ED',
        accent: '#EA580C'
    }
};

function updateTailwindConfig(colors) {
    const config = {
        theme: {
            extend: {
                colors: {
                    primary: colors.primary,
                    secondary: colors.secondary,
                    accent: colors.accent
                }
            }
        }
    };
    
    document.getElementById('tailwindConfig').value = 'tailwind.config = ' + JSON.stringify(config, null, 4);
}

// Initialize color pickers
function initColorPickers() {
    ['primary', 'secondary', 'accent'].forEach(type => {
        const colorInput = document.getElementById(type + 'Color');
        const hexInput = document.getElementById(type + 'ColorHex');
        
        if (colorInput && hexInput) {
            // Update hex input when color changes
            colorInput.addEventListener('input', (e) => {
                hexInput.value = e.target.value.toUpperCase();
                updateCustomColors();
            });

            // Update color input when hex changes
            hexInput.addEventListener('input', (e) => {
                const hex = e.target.value;
                if (/^#[0-9A-F]{6}$/i.test(hex)) {
                    colorInput.value = hex;
                    updateCustomColors();
                }
            });
        }
    });
}

// Update config when custom colors change
function updateCustomColors() {
    const colors = {
        primary: document.getElementById('primaryColorHex').value,
        secondary: document.getElementById('secondaryColorHex').value,
        accent: document.getElementById('accentColorHex').value
    };
    updateTailwindConfig(colors);
}

// Theme button click handlers
document.querySelectorAll('[data-theme]').forEach(button => {
    button.addEventListener('click', (e) => {
        // Remove active state from all buttons
        document.querySelectorAll('[data-theme]').forEach(btn => {
            btn.classList.remove('border-[#0C5BE2]', 'text-[#0C5BE2]');
            btn.classList.add('border-gray-200', 'text-gray-700');
        });

        const theme = e.currentTarget.dataset.theme;
        
        // Add active state to clicked button
        e.currentTarget.classList.remove('border-gray-200', 'text-gray-700');
        e.currentTarget.classList.add('border-[#0C5BE2]', 'text-[#0C5BE2]');

        // Handle custom theme
        const customColorGrid = document.getElementById('customColorGrid');
        if (theme === 'custom') {
            customColorGrid.classList.remove('hidden');
            updateCustomColors();
        } else {
            customColorGrid.classList.add('hidden');
            // Update config with predefined theme colors
            updateTailwindConfig(colorThemes[theme]);
        }

        const themeData = e.currentTarget.getAttribute('data-color-code');
        console.log(themeData);
        if (themeData) {
            const colors = themeData.split(','); // Assuming colors are comma-separated
            if (colors.length === 3) {
                document.documentElement.style.setProperty('--prev-primary-color', colors[0].trim());
                document.documentElement.style.setProperty('--prev-secondary-color', colors[1].trim());
                document.documentElement.style.setProperty('--prev-accent-color', colors[2].trim());

                // Optional: You might want to log or confirm the change
                console.log('Theme updated:', {
                    primary: colors[0].trim(),
                    secondary: colors[1].trim(),
                    accent: colors[2].trim()
                });
            } else {
                console.error('Invalid theme data format. Expected 3 colors separated by commas.');
            }
        }
    });
});

// Initialize color pickers
initColorPickers();

// Set initial theme based on database or default
const configTextarea = document.getElementById('tailwindConfig');
if (configTextarea && configTextarea.value.trim()) {
    // Use the database configuration
    const dbColors = extractColorsFromTailwindConfig(configTextarea.value);
    if (dbColors) {
        // Show custom color grid since we have custom colors
        document.getElementById('customColorGrid').classList.remove('hidden');
        // Set custom theme button as active
        document.querySelectorAll('button[data-theme]').forEach(btn => {
            if (btn.getAttribute('data-theme') === 'custom') {
                btn.classList.remove('border-gray-200', 'text-gray-700');
                btn.classList.add('border-[#0C5BE2]', 'text-[#0C5BE2]');
            } else {
                btn.classList.remove('border-[#0C5BE2]', 'text-[#0C5BE2]');
                btn.classList.add('border-gray-200', 'text-gray-700');
            }
        });
        // Update with database colors
        updateThemeColors(dbColors);
    } else {
        // Fallback to corporate-light if parsing fails
        updateTailwindConfig(colorThemes['corporate-light']);
    }
} else {
    // No database config, use default theme
    updateTailwindConfig(colorThemes['corporate-light']);
}

// Show/hide new category input with enhanced functionality
document.getElementById('showNewCategory').addEventListener('change', function() {
    const newCategoryInput = document.getElementById('newCategoryInput');
    const templateCategoryWrap = document.querySelector('.templateCategoryWrap');
    
    if (this.checked) {
        // Show new category input and hide template category wrap
        newCategoryInput.classList.remove('hidden');
        templateCategoryWrap.classList.add('hidden');
    } else {
        // Hide new category input and show template category wrap
        newCategoryInput.classList.add('hidden');
        templateCategoryWrap.classList.remove('hidden');
    }
});

// Function to hide new category input and show template category wrap
function hideNewCategoryInput() {
    const newCategoryInput = document.getElementById('newCategoryInput');
    const templateCategoryWrap = document.querySelector('.templateCategoryWrap');
    const showNewCategoryCheckbox = document.getElementById('showNewCategory');
    
    newCategoryInput.classList.add('hidden');
    templateCategoryWrap.classList.remove('hidden');
    showNewCategoryCheckbox.checked = false;
}

// Click outside handler - hide new category input when clicking outside
document.addEventListener('click', function(event) {
    const newCategoryInput = document.getElementById('newCategoryInput');
    const showNewCategoryLabel = document.querySelector('label[for="showNewCategory"]');
    const showNewCategoryCheckbox = document.getElementById('showNewCategory');
    
    // Check if click is outside the new category input area and not on the checkbox/label
    if (!newCategoryInput.contains(event.target) && 
        !showNewCategoryLabel.contains(event.target) && 
        event.target !== showNewCategoryCheckbox &&
        !newCategoryInput.classList.contains('hidden')) {
        
        hideNewCategoryInput();
    }
});

// Add a new category
window.addNewCategory = async function() {
    const newCategoryName = document.getElementById('newCategoryName').value.trim();
    if (!newCategoryName) {
        alert('Please enter a category name');
        return;
    }

    try {
        const response = await fetch('<?php echo BASE_URL ?>/modules/template_categories/template_categories.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ name: newCategoryName })
        });

        const data = await response.json();
        if (data.status === 'success') {
            const select = $('#templateCategory');
            
            // Get current selections
            const currentSelections = select.val() || [];
            
            // Create new option data
            const newOption = {
                id: data.data.last_insert_id,
                text: newCategoryName,
                selected: true
            };

            // Create and append the new option
            const optionElement = new Option(newOption.text, newOption.id, true, true);
            select.append(optionElement);

            // Add new selection to current selections if not already present
            if (!currentSelections.includes(newOption.id)) {
                currentSelections.push(newOption.id);
            }

            // Update Select2
            select.val(currentSelections).trigger('change');

            // Force refresh the Select2 instance
            select.select2('destroy');
            select.select2({
                theme: 'default',
                placeholder: 'Select categories',
                allowClear: true,
                width: '100%',
                closeOnSelect: false,
                selectionCssClass: 'select2--large',
                templateResult: function(data) {
                    if (!data.id) return data.text;
                    return $('<span class="text-sm">' + data.text + '</span>');
                },
                templateSelection: function(data) {
                    if (!data.id) return data.text;
                    return $('<span class="text-sm">' + data.text + '</span>');
                }
            });
            
            // Clear and hide input
            document.getElementById('newCategoryName').value = '';

            // Hide new category input and show template category wrap
            hideNewCategoryInput();
        } else {
            alert(data.message || 'Failed to create category');
        }
    } catch (error) {
        console.error('Error:', error);
        alert('Failed to create category. Please try again.');
    }
};

// File upload previews
document.getElementById('screenshotUpload').addEventListener('change', function(e) {
    const files = e.target.files;
    if (files.length > 0) {
        // Hide empty state, show preview grid
        document.getElementById('screenshotEmptyState').classList.add('hidden');
        document.getElementById('screenshotPreviewGrid').classList.remove('hidden');
        
        // Clear previous previews
        const previewGrid = document.getElementById('screenshotPreviewGrid');
        previewGrid.innerHTML = '';
        
        // Add new previews
        Array.from(files).forEach(file => {
            const reader = new FileReader();
            reader.onload = function(e) {
                const preview = document.createElement('div');
                preview.className = 'relative aspect-video group';
                preview.innerHTML = `
                    <img src="${e.target.result}" class="w-full h-24 object-cover rounded-lg" alt="Screenshot preview">
                    <div class="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity rounded-lg flex items-center justify-center">
                        <button type="button" class="text-white hover:text-red-500" onclick="removeScreenshot(this)">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                            </svg>
                        </button>
                    </div>
                `;
                previewGrid.appendChild(preview);
            };
            reader.readAsDataURL(file);
        });
    }
});

// Copy configuration function
window.copyConfig = function() {
    const configTextarea = document.getElementById('tailwindConfig');
    configTextarea.select();
    document.execCommand('copy');
    
    // Show copy feedback
    const button = document.querySelector('button[onclick="copyConfig()"]');
    const originalSvg = button.innerHTML;
    button.innerHTML = `
        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-green-500" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <polyline points="20 6 9 17 4 12"/>
        </svg>
    `;
    setTimeout(() => {
        button.innerHTML = originalSvg;
    }, 1500);
};

// Handle template status change
document.querySelectorAll('input[name="template_status"]').forEach(radio => {
    radio.addEventListener('change', function() {
        const status = parseInt(this.value);
        // You can add any additional logic here when status changes
        console.log('Template status changed to:', status);
    });
});

// Initialize first step
updateStep(1);

// Handle logo and favicon uploads
// Function to extract colors from Tailwind config
function extractColorsFromTailwindConfig(configCode) {
    if (!configCode) return null;
    
    try {
        // Remove any HTML entities that might have been encoded
        configCode = configCode.replace(/&quot;/g, '"').replace(/&#039;/g, "'");
        
        // Try to find the configuration object
        let configObject = configCode;
        
        // If it starts with tailwind.config, extract the object part
        if (configCode.includes('tailwind.config')) {
            const matches = configCode.match(/tailwind\.config\s*=\s*({[\s\S]*})/i);
            if (matches && matches[1]) {
                configObject = matches[1];
            }
        }
        
        // Clean up the config string
        configObject = configObject
            .replace(/\\n/g, '') // Remove escaped newlines
            .replace(/\\(["'])/g, '$1') // Remove escaped quotes
            .replace(/\s+/g, ' ') // Normalize whitespace
            .trim();
        
        // Parse the configuration
        let config;
        try {
            config = JSON.parse(configObject);
        } catch (e) {
            // If JSON.parse fails, try eval as a fallback
            config = eval('(' + configObject + ')');
        }
        
        // Extract colors from the parsed config
        const colors = config?.theme?.extend?.colors;
        if (colors) {
            // Clean the color values
            const cleanColor = (color) => {
                if (!color) return '';
                // Remove any remaining escape characters and normalize
                return color.replace(/\\|"/g, '').trim();
            };
            
            const extractedColors = {
                primary: cleanColor(colors.primary),
                secondary: cleanColor(colors.secondary),
                accent: cleanColor(colors.accent)
            };
            
            // Verify we have valid colors
            if (extractedColors.primary && extractedColors.secondary && extractedColors.accent) {
                console.log('Extracted colors:', extractedColors);
                return extractedColors;
            }
        }
    } catch (error) {
        console.error('Error parsing Tailwind config:', error);
        console.log('Original config:', configCode);
    }
    return null;
}

// Theme color definitions
const themes = {
    'corporate-light': {
        primary: '#0C5BE2',
        secondary: '#6366F1',
        accent: '#3B82F6'
    },
    'corporate-dark': {
        primary: '#1F2937',
        secondary: '#374151',
        accent: '#4B5563'
    },
    'nature': {
        primary: '#059669',
        secondary: '#10B981',
        accent: '#34D399'
    },
    'ocean': {
        primary: '#0EA5E9',
        secondary: '#38BDF8',
        accent: '#7DD3FC'
    },
    'warm': {
        primary: '#F97316',
        secondary: '#FB923C',
        accent: '#FDBA74'
    }
};

// Function to update theme colors
function updateThemeColors(colors) {
    // Update CSS variables for preview
    document.documentElement.style.setProperty('--primary-color', colors.primary);
    document.documentElement.style.setProperty('--secondary-color', colors.secondary);
    document.documentElement.style.setProperty('--accent-color', colors.accent);

    // Update preview classes
    document.querySelectorAll('.preview-bg-primary').forEach(el => {
        el.style.backgroundColor = colors.primary;
    });
    document.querySelectorAll('.preview-bg-secondary').forEach(el => {
        el.style.backgroundColor = colors.secondary;
    });
    document.querySelectorAll('.preview-bg-accent').forEach(el => {
        el.style.backgroundColor = colors.accent;
    });
    document.querySelectorAll('.preview-text-primary').forEach(el => {
        el.style.color = colors.primary;
    });
    document.querySelectorAll('.preview-text-secondary').forEach(el => {
        el.style.color = colors.secondary;
    });
    document.querySelectorAll('.preview-text-accent').forEach(el => {
        el.style.color = colors.accent;
    });
    
    // Update color inputs if they exist
    const primaryColorInput = document.getElementById('primaryColor');
    const primaryColorHex = document.getElementById('primaryColorHex');
    const secondaryColorInput = document.getElementById('secondaryColor');
    const secondaryColorHex = document.getElementById('secondaryColorHex');
    const accentColorInput = document.getElementById('accentColor');
    const accentColorHex = document.getElementById('accentColorHex');
    
    if (primaryColorInput && primaryColorHex) {
        primaryColorInput.value = colors.primary;
        primaryColorHex.value = colors.primary;
    }
    if (secondaryColorInput && secondaryColorHex) {
        secondaryColorInput.value = colors.secondary;
        secondaryColorHex.value = colors.secondary;
    }
    if (accentColorInput && accentColorHex) {
        accentColorInput.value = colors.accent;
        accentColorHex.value = colors.accent;
    }

    // Update Tailwind config
    const tailwindConfig = {
        theme: {
            extend: {
                colors: {
                    primary: colors.primary,
                    secondary: colors.secondary,
                    accent: colors.accent
                }
            }
        }
    };

    // Update the tailwind configuration textarea
    const configTextarea = document.getElementById('tailwind_configuration_code');
    if (configTextarea) {
        configTextarea.value = JSON.stringify(tailwindConfig, null, 2);
        // Trigger change event to ensure form picks up the new value
        configTextarea.dispatchEvent(new Event('change'));
    }
}

document.addEventListener('DOMContentLoaded', function() {
    // Theme selection handling
    const themeButtons = document.querySelectorAll('button[data-theme]');
    const customColorGrid = document.getElementById('customColorGrid');
    const configTextarea = document.getElementById('tailwind_configuration_code');
    const primaryColorInput = document.getElementById('primary_color');
    const secondaryColorInput = document.getElementById('secondary_color');
    const accentColorInput = document.getElementById('accent_color');

    // Add input event listeners for custom colors
    if (primaryColorInput) {
        primaryColorInput.addEventListener('input', function() {
            updateCustomColors();
        });
    }
    if (secondaryColorInput) {
        secondaryColorInput.addEventListener('input', function() {
            updateCustomColors();
        });
    }
    if (accentColorInput) {
        accentColorInput.addEventListener('input', function() {
            updateCustomColors();
        });
    }

    function updateCustomColors() {
        const colors = {
            primary: primaryColorInput.value,
            secondary: secondaryColorInput.value,
            accent: accentColorInput.value
        };
        updateThemeColors(colors);
    }
    
    // Check if we're editing and have existing config
    let initialColors = themes['corporate-light'];
    if (configTextarea && configTextarea.value.trim()) {
        const configColors = extractColorsFromTailwindConfig(configTextarea.value);
        if (configColors) {
            initialColors = configColors;
            // Show custom color grid since we have custom colors
            customColorGrid.classList.remove('hidden');
            // Set custom theme button as active
            themeButtons.forEach(btn => {
                if (btn.getAttribute('data-theme') === 'custom') {
                    btn.classList.remove('border-gray-200', 'text-gray-700');
                    btn.classList.add('border-[#0C5BE2]', 'text-[#0C5BE2]');
                } else {
                    btn.classList.remove('border-[#0C5BE2]', 'text-[#0C5BE2]');
                    btn.classList.add('border-gray-200', 'text-gray-700');
                }
            });
        }
    }
    
    // Apply initial colors
    updateThemeColors(initialColors);
    
    // Theme button click handlers
    themeButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Remove active state from all buttons
            themeButtons.forEach(btn => {
                btn.classList.remove('border-[#0C5BE2]', 'text-[#0C5BE2]');
                btn.classList.add('border-gray-200', 'text-gray-700');
            });
            
            // Add active state to clicked button
            this.classList.remove('border-gray-200', 'text-gray-700');
            this.classList.add('border-[#0C5BE2]', 'text-[#0C5BE2]');
            
            const themeKey = this.getAttribute('data-theme');
            
            // Toggle custom color grid
            if (themeKey === 'custom') {
                customColorGrid.classList.remove('hidden');
            } else {
                customColorGrid.classList.add('hidden');
                if (themes[themeKey]) {
                    updateThemeColors(themes[themeKey]);
                }
            }
        });
    });
    
    // Color picker input handlers
    const colorInputs = document.querySelectorAll('input[type="color"]');
    colorInputs.forEach(input => {
        input.addEventListener('input', function() {
            const colors = {
                primary: document.getElementById('primaryColor').value,
                secondary: document.getElementById('secondaryColor').value,
                accent: document.getElementById('accentColor').value
            };
            updateThemeColors(colors);
        });
    });

    // Hex input handlers
    const hexInputs = document.querySelectorAll('input[type="text"][id$="ColorHex"]');
    hexInputs.forEach(input => {
        input.addEventListener('change', function() {
            const colorInput = document.getElementById(this.id.replace('Hex', ''));
            colorInput.value = this.value;
            const colors = {
                primary: document.getElementById('primaryColor').value,
                secondary: document.getElementById('secondaryColor').value,
                accent: document.getElementById('accentColor').value
            };
            updateThemeColors(colors);
        });
    });
    
    // Logo upload preview
    const logoInput = document.getElementById('logo_image');
    const logoPreview = document.getElementById('logoPreview');
    const logoEmptyState = document.getElementById('logoEmptyState');
    const logoImg = logoPreview.querySelector('img');

    logoInput.addEventListener('change', function() {
        if (this.files && this.files[0]) {
            const reader = new FileReader();
            reader.onload = function(e) {
                logoImg.src = e.target.result;
                logoPreview.classList.remove('hidden');
                logoEmptyState.classList.add('hidden');
            };
            reader.readAsDataURL(this.files[0]);
        }
    });

    // Favicon upload preview
    const faviconInput = document.getElementById('favicon_image');
    const faviconPreview = document.getElementById('faviconPreview');
    const faviconEmptyState = document.getElementById('faviconEmptyState');
    const faviconImg = faviconPreview.querySelector('img');

    faviconInput.addEventListener('change', function() {
        if (this.files && this.files[0]) {
            const reader = new FileReader();
            reader.onload = function(e) {
                faviconImg.src = e.target.result;
                faviconPreview.classList.remove('hidden');
                faviconEmptyState.classList.add('hidden');
            };
            reader.readAsDataURL(this.files[0]);
        }
    });

    // Screenshot upload preview
    const screenshotInput = document.getElementById('screenshotUpload');
    const screenshotPreviewGrid = document.getElementById('screenshotPreviewGrid');
    const screenshotEmptyState = document.getElementById('screenshotEmptyState');

    screenshotInput.addEventListener('change', function() {
        if (this.files && this.files.length > 0) {
            screenshotPreviewGrid.innerHTML = ''; // Clear existing previews
            screenshotPreviewGrid.classList.remove('hidden');
            screenshotEmptyState.classList.add('hidden');

            Array.from(this.files).forEach((file, index) => {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const previewDiv = document.createElement('div');
                    previewDiv.className = 'relative aspect-video bg-gray-100 rounded-lg overflow-hidden';
                    
                    const img = document.createElement('img');
                    img.src = e.target.result;
                    img.className = 'w-full h-full object-cover';
                    img.alt = `Screenshot ${index + 1}`;
                    
                    previewDiv.appendChild(img);
                    screenshotPreviewGrid.appendChild(previewDiv);
                };
                reader.readAsDataURL(file);
            });
        }
    });

    // Enable drag and drop for screenshots
    const dropZone = document.querySelector('.border-dashed');
    
    dropZone.addEventListener('dragover', function(e) {
        e.preventDefault();
        this.classList.add('border-blue-500');
    });

    dropZone.addEventListener('dragleave', function(e) {
        e.preventDefault();
        this.classList.remove('border-blue-500');
    });

    dropZone.addEventListener('drop', function(e) {
        e.preventDefault();
        this.classList.remove('border-blue-500');
        
        if (e.dataTransfer.files.length > 0) {
            screenshotInput.files = e.dataTransfer.files;
            const event = new Event('change');
            screenshotInput.dispatchEvent(event);
        }
    });
});

// Logo preview
document.getElementById('logo_image').addEventListener('change', function(e) {
    const preview = document.getElementById('logoPreview');
    if (this.files && this.files[0]) {
        const reader = new FileReader();
        reader.onload = function(e) {
            preview.innerHTML = `<img src="${e.target.result}" alt="Logo Preview" class="max-w-full max-h-full object-contain">`;
            preview.classList.remove('border-2', 'border-dashed');
        };
        reader.readAsDataURL(this.files[0]);
    }
});

// Favicon preview
document.getElementById('favicon_image').addEventListener('change', function(e) {
    const preview = document.getElementById('faviconPreview');
    if (this.files && this.files[0]) {
        const reader = new FileReader();
        reader.onload = function(e) {
            preview.innerHTML = `<img src="${e.target.result}" alt="Favicon Preview" class="max-w-full max-h-full object-contain">`;
            preview.classList.remove('border-2', 'border-dashed');
        };
        reader.readAsDataURL(this.files[0]);
    }
});

// Screenshots preview
function createScreenshotPreview(file) {
    const reader = new FileReader();
    reader.onload = function(e) {
        const previewDiv = document.createElement('div');
        previewDiv.className = 'relative aspect-video group';
        previewDiv.innerHTML = `
            <img src="${e.target.result}" class="w-full h-full object-cover rounded-lg" alt="Screenshot">
            <div class="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity rounded-lg flex items-center justify-center">
                <button type="button" class="text-white hover:text-red-500" onclick="removeScreenshot(this)">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                </button>
            </div>
        `;
        document.getElementById('screenshotPreviews').appendChild(previewDiv);
    };
    reader.readAsDataURL(file);
}

document.getElementById('screenshotUpload').addEventListener('change', function(e) {
    if (this.files) {
        Array.from(this.files).forEach(createScreenshotPreview);
    }
});

function removeScreenshot(button) {
    const previewDiv = button.closest('.relative');
    if (previewDiv) {
        // If this is an existing screenshot, add it to a list of screenshots to remove
        const img = previewDiv.querySelector('img');
        if (img && img.src.startsWith('<?php echo BASE_URL; ?>')) {
            const removedScreenshotsInput = document.getElementById('removed_screenshots') || document.createElement('input');
            removedScreenshotsInput.type = 'hidden';
            removedScreenshotsInput.name = 'removed_screenshots[]';
            removedScreenshotsInput.id = 'removed_screenshots';
            removedScreenshotsInput.value = img.src.replace('<?php echo BASE_URL; ?>', '');
            document.querySelector('form').appendChild(removedScreenshotsInput);
        }
        previewDiv.remove();
    }
}

// Drag and drop for screenshots
const dropZone = document.getElementById('screenshotDropZone');

dropZone.addEventListener('dragover', function(e) {
    e.preventDefault();
    this.classList.add('border-blue-500');
});

dropZone.addEventListener('dragleave', function(e) {
    e.preventDefault();
    this.classList.remove('border-blue-500');
});

dropZone.addEventListener('drop', function(e) {
    e.preventDefault();
    this.classList.remove('border-blue-500');
    
    if (e.dataTransfer.files.length > 0) {
        const screenshotInput = document.getElementById('screenshotUpload');
        
        // Create a new DataTransfer object
        const dt = new DataTransfer();
        
        // Add existing files
        if (screenshotInput.files) {
            Array.from(screenshotInput.files).forEach(file => dt.items.add(file));
        }
        
        // Add new dropped files
        Array.from(e.dataTransfer.files).forEach(file => {
            if (file.type.startsWith('image/')) {
                dt.items.add(file);
                createScreenshotPreview(file);
            }
        });
        
        // Update the file input
        screenshotInput.files = dt.files;
    }
});
</script>

<?php
$content = ob_get_clean();
require_once __DIR__ . '/../layouts/main.php';
?>
