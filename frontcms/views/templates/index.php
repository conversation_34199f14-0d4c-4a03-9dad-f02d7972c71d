<?php
require_once __DIR__ . '/../../config/config.php';
require_once __DIR__ . '/../../modules/templates/Templates.php';
require_once __DIR__ . '/../../includes/TableComponent.php';
require_once __DIR__ . '/../../includes/TableHelper.php';

// Get sorting parameter from URL (in format 'sort=column' or 'sort=-column')
$sort = isset($_GET['sort']) ? $_GET['sort'] : 'id';
$direction = 'desc';

// Check if sort has a negative prefix (for descending)
if (substr($sort, 0, 1) === '-') {
    $realSort = substr($sort, 1); // Remove the minus sign
    $direction = 'desc';
} else {
    $realSort = $sort;
}

// Validate sort parameter 
$allowedSortColumns = ['id', 'name', 'page_count', 'created_at', 'updated_at'];
if (!in_array($realSort, $allowedSortColumns)) {
    $realSort = 'id';
    $sort = ($direction === 'desc') ? '-id' : 'id';
}

// Get limit from URL parameter
$items_per_page = isset($_GET['limit']) ? (int)$_GET['limit'] : 10;

// Set page title and breadcrumbs
$pageTitle = 'Templates';
$breadcrumbs = [
    [
        'label' => 'Templates',
        'url' => BASE_URL . '/templates'
    ]
];
    
$templates = new Templates();

// Handle Delete Action
if (isset($_POST['delete']) && isset($_POST['id'])) {
    try {
        $id = $_POST['id'];
        $result = $templates->delete($id);
        if ($result['success']) {
            header("Location: " . BASE_URL . "/templates?success=Template deleted successfully");
            exit;
        } else {
            $error = $result['message'];
        }
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// Get templates with sorting and pagination
try {
    $result = $templates->getList([
        'page' => isset($_GET['page']) ? (int)$_GET['page'] : 1,
        'sort' => $realSort,
        'direction' => $direction,
        'limit' => $items_per_page
    ]);
    
    if ($result['success']) {
        $templatesList = $result['data']['data'] ?? [];

        // Simple pagination configuration using the new structure
        $pagination = $result['data'];
    } else {
        $error = $result['message'];
        $templatesList = [];
        $pagination = [
            'current_page' => 1,
            'per_page' => 10,
            'from' => null,
            'to' => null,
            'total' => 0,
            'next_page_url' => null,
            'prev_page_url' => null,
            'first_page_url' => null,
            'last_page_url' => null
        ];
    }
} catch (Exception $e) {
    $error = $e->getMessage();
    $templatesList = [];
    $pagination = [
        'current_page' => 1,
        'per_page' => 10,
        'from' => null,
        'to' => null,
        'total' => 0,
        'next_page_url' => null,
        'prev_page_url' => null,
        'first_page_url' => null,
        'last_page_url' => null
    ];
}

// Start output buffering
ob_start(); 
?>

<?php
// Define table columns
$columns = [
    // 'template_image' => [
    //     'label' => 'Template Image',
    //     'cellClass' => 'cell_imagewrapper',
    //     'sortable' => true,
    //     'formatter' => function($value, $row) {
    //         $screenshots = $row['multiple_template_screenshot'] ?? '';
    
    //         if (empty($screenshots)) {
    //             return '<div class="image-placeholder">
    //             <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="0.75" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-layout-dashboard-icon lucide-layout-dashboard"><rect width="7" height="9" x="3" y="3" rx="1"/><rect width="7" height="5" x="14" y="3" rx="1"/><rect width="7" height="9" x="14" y="12" rx="1"/><rect width="7" height="5" x="3" y="16" rx="1"/></svg>
    //             </div>';
    //         }
    
    //         // Process the JSON string
    //         $decoded = html_entity_decode($screenshots, ENT_QUOTES, 'UTF-8');
    //         $images = json_decode($decoded, true);
            
    //         if (!is_array($images) || empty($images)) {
    //             return '<div class="image-placeholder">Invalid Data</div>';
    //         }
    
    //         // Clean and prepare images
    //         $cleanImages = array_map(function($img) {
    //             return BASE_URL . '/' . str_replace(['\\/', '\\"'], ['/', '"'], ltrim($img, '/'));
    //         }, $images);
            
    //         $firstImage = $cleanImages[0];
    //         $imageCount = count($cleanImages);
    
    //         // Return HTML with data attributes for gallery
    //         return '<div class="image-container" 
    //                     data-images="' . htmlspecialchars(json_encode($cleanImages)) . '"
    //                     data-count="' . $imageCount . '">
    //                     <img src="' . htmlspecialchars($firstImage) . '" 
    //                         alt="Template Screenshot" 
    //                         class="template-thumbnail"
    //                         onclick="openImageGallery(this)">
    //                     ' . ($imageCount > 1 ? '<span class="image-count-badge">' . $imageCount . '</span>' : '') . '
    //                 </div>';
    //     }
    // ],
    // 'template_name' => [
    //     'label' => 'Template Name',
    //     'cellClass' => 'cell_id__name',
    //     'sortable' => true,
    //     'formatter' => function($value, $row) {
    //         $id = $row['id'] ?? '';
    //         $name = $row['name'] ?? '';
    //         return '<div class="font-medium text-gray-900">' . htmlspecialchars($name) . '</div><div class="text-sm text-gray-500">' . htmlspecialchars('TEMP ID: ' . $id) . '</div>';
    //     }
    // ],
    'name' => [
        'label' => 'Templates',
        'sortable' => true,
        'formatter' => function($value, $row) {
            return formatImageWithText($row, [
                'imageKey' => 'preview_image',
                'titleKey' => 'name',
                'subtitleKey' => 'id',
                'subtitlePrefix' => 'ID: TEMP-',
                'placeholderText' => $row['name'] ?? 'Template'
            ]);
        }
    ],
    'categories' => [
        'label' => 'Category',
        'sortable' => false,
        'cellClass' => 'categories',
        'formatter' => function($value, $row) {
            return formatCategoryBadges($row['categories'] ?? []);
        }
    ],
    'page_count' => [
        'label' => 'Pages',
        'sortable' => true,
        'cellClass' => 'page_count',
    ],
    'updated_at' => [
        'label' => 'Last Updated',
        'sortable' => true,
        'cellClass' => 'updated_at',
        'formatter' => function($value) {
            return formatDate($value);
        }
    ],
    'created_at' => [
        'label' => 'Created At',
        'cellClass' => 'created_at',
        'sortable' => true,
        'formatter' => function($value) {
            return formatDate($value);
        }
    ]
];

// Define row actions
$rowActions = [
    [
        'icon' => '<path d="M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0"></path><circle cx="12" cy="12" r="3"></circle>',
        'tooltip' => 'Preview',
        'hoverTextColor' => 'hover:text-green-700',
        'type' => 'button',
        'attributes' => [
            'onclick' => function($row) {
                return "showTemplatePreview(" . $row['id'] . ")";
            },
            'data-modal-target' => 'templatePreviewModal',
            'data-modal-toggle' => 'templatePreviewModal'
        ]
    ],
    [
        'icon' => '<path d="M21.174 6.812a1 1 0 0 0-3.986-3.987L3.842 16.174a2 2 0 0 0-.5.83l-1.321 4.352a.5.5 0 0 0 .623.622l4.353-1.32a2 2 0 0 0 .83-.497z"/><path d="m15 5 4 4"/>',
        'tooltip' => 'Edit',
        'hoverTextColor' => 'hover:text-blue-700',
        'urlFormatter' => function($row) {
            return BASE_URL . "/templates/form/" . $row['id'];
        }
    ],
    [
        'icon' => '<path d="m18 16 4-4-4-4"/><path d="m6 8-4 4 4 4"/><path d="m14.5 4-5 16"/>',
        'tooltip' => 'Template Builder',
        'hoverTextColor' => 'hover:text-purple-700',
        'type' => 'link',
        'urlFormatter' => function($row) {
            return BASE_URL . "/templates/builder?id=" . $row['id'];
        }
    ],
    [
        'icon' => '<path d="M3 6h18"/><path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"/><path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"/>',
        'tooltip' => 'Delete',
        'hoverTextColor' => 'hover:text-red-700',
        'type' => 'button',
        'attributes' => [
            'data-id' => function($row) { return $row['id'] ?? ''; },
            'data-modal-target' => 'deleteModal',
            'data-modal-toggle' => 'deleteModal'
        ]
    ]
];

// Create action buttons
$actionButtons = [
    [
        'label' => 'Create New Template',
        'icon' => 'plus',
        'attributes' => [
            'onclick' => "window.location.href='" . BASE_URL . "/templates/form'"
        ]
    ]
];

$defaultView = 'grid';
$cookieKey = 'preferredView_/frontcms/templates';
$preferredView = $_COOKIE[$cookieKey] ?? $defaultView;

// Create table component with grid view enabled
$table = new TableComponent($templatesList, $columns, $pagination);
$table->setTableId('templateTable')
    ->setTitleBreadcrumb('Templates', [
        ['label' => 'Home', 'url' => BASE_URL . '/dashboard'],
        ['label' => 'Templates']
    ])
      ->setSearchConfig(true, 'templateSearch')
      ->setActionButtons($actionButtons)
      ->setRowActions($rowActions)
      ->setGridConfig([
        'enabled' => true,
        'columns' => 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4',
        'imageKey' => 'preview_image',
        'titleKey' => 'name',
        'subtitleKey' => 'id',
        'descriptionKey' => 'detailed_description',
        'layout' => 'default',
        'cardTemplate' => function($template, $config) {
            // Get template data
            $id = $template['id'] ?? '';
            $name = htmlspecialchars($template['name'] ?? 'Untitled Template');
            $previewImage = $template['preview_image'] ?? '';
            $pageCount = $template['page_count'] ?? 0;
            $updatedAt = $template['updated_at'] ?? '';
            $categories = $template['categories'] ?? [];
            $description = htmlspecialchars($template['detailed_description'] ?? '');
            
            // Get category names
            $categoryNames = [];
            if (is_array($categories)) {
                foreach ($categories as $category) {
                    if (isset($category['name'])) {
                        $categoryNames[] = $category['name'];
                    }
                }
            }
            $categoryText = !empty($categoryNames) ? implode(', ', $categoryNames) : 'Uncategorized';
            $firstCategory = !empty($categoryNames) ? $categoryNames[0] : 'Template';
            
            // Format time ago
            $timeAgo = '';
            if ($updatedAt) {
                $time = strtotime($updatedAt);
                $now = time();
                $diff = $now - $time;
                
                if ($diff < 3600) {
                    $timeAgo = floor($diff / 60) . ' min ago';
                } elseif ($diff < 86400) {
                    $timeAgo = floor($diff / 3600) . ' hours ago';
                } else {
                    $timeAgo = floor($diff / 86400) . ' days ago';
                }
            }
            
            // Default image if no preview
            $imageUrl = !empty($previewImage) ? BASE_URL . $previewImage : 'https://placehold.co/600x300/e5e7eb/9ca3af?text=' . urlencode($name);
            
            return '
            <div class="template-card group relative bg-white rounded-xl border border-gray-200 overflow-hidden transition-all duration-300 hover:scale-[1.02] hover:-translate-y-1" data-category="' . htmlspecialchars(strtolower($firstCategory)) . '" data-id="' . $id . '">
                <div class="relative aspect-[4/2] overflow-hidden">
                    <img src="' . $imageUrl . '" alt="' . $name . '" class="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110">
                    
                    <!-- Hover Overlay with Description -->
                    <div class="absolute inset-0 bg-gradient-to-t from-black/80 via-black/50 to-transparent opacity-0 group-hover:opacity-100 transition-all duration-300">
                        <div class="absolute bottom-0 left-0 right-0 p-4">
                            <div class="flex items-start justify-between gap-4">
                                <div class="flex-1 space-y-2">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                        ' . htmlspecialchars($firstCategory) . '
                                    </span>
                                    <p class="text-sm text-gray-100 line-clamp-2">' . (strlen($description) > 80 ? substr($description, 0, 80) . '...' : $description) . '</p>
                                </div>
                                <div class="flex items-center space-x-2 shrink-0 self-end">
                                    <button onclick="showTemplatePreview(' . $id . ')" class="previewBtn inline-flex items-center justify-center w-9 h-9 rounded-full bg-white/20 backdrop-blur-sm hover:bg-white/30 transition-colors" data-modal-target="templatePreviewModal" data-modal-toggle="templatePreviewModal">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-5 h-5 text-white">
                                            <path d="M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0"></path>
                                            <circle cx="12" cy="12" r="3"></circle>
                                        </svg>
                                    </button>
                                    <button onclick="window.location.href=\'' . BASE_URL . '/templates/form/' . $id . '\'" class="useBtn inline-flex items-center justify-center w-9 h-9 rounded-full bg-white text-gray-700 hover:bg-gray-50 transition-colors">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <path d="M21.174 6.812a1 1 0 0 0-3.986-3.987L3.842 16.174a2 2 0 0 0-.5.83l-1.321 4.352a.5.5 0 0 0 .623.622l4.353-1.32a2 2 0 0 0 .83-.497z"></path>
                                            <path d="m15 5 4 4"></path>
                                        </svg>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Card Content - Always Visible -->
                <div class="p-4">
                    <!-- Title -->
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">' . $name . '</h3>
                    <!-- Metadata -->
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-3">
                            <span class="flex items-center text-sm text-gray-500">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-4 h-4 mr-1">
                                    <rect width="18" height="7" x="3" y="3" rx="1"></rect>
                                    <rect width="9" height="7" x="3" y="14" rx="1"></rect>
                                    <rect width="5" height="7" x="16" y="14" rx="1"></rect>
                                </svg>
                                ' . $pageCount . ' Pages
                            </span>
                            <span class="flex items-center text-sm text-gray-500">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-4 h-4 mr-1">
                                    <circle cx="12" cy="12" r="10"></circle>
                                    <polyline points="12 6 12 12 16 14"></polyline>
                                </svg>
                                ' . $timeAgo . '
                            </span>
                        </div>
                        <div class="flex -space-x-2">
                            <img class="w-6 h-6 rounded-full border-2 border-white" src="https://ui-avatars.com/api/?name=Admin&background=0C5BE2&color=ffffff" alt="User">
                        </div>
                    </div>
                </div>
            </div>';
        }
    ])
      ->setDefaultView($preferredView);

// Render the table
echo $table->render();
?>

<!-- Delete Form -->
<form id="deleteForm" method="POST" style="display: none;">
    <input type="hidden" name="delete" value="1">
    <input type="hidden" name="id" id="deleteId">
</form>

<!-- Template Preview Modal -->
<div id="templatePreviewModal" tabindex="-1" class="fixed top-0 left-0 right-0 z-50 hidden w-full p-4 overflow-x-hidden overflow-y-auto md:inset-0 h-[calc(100%-1rem)] max-h-full">
    <div class="relative w-full max-w-7xl max-h-full">
        <!-- Modal content -->
        <div class="relative bg-white rounded-lg shadow-sm">
            <!-- Modal header -->
            <div class="flex items-center justify-between p-4 md:p-5 border-b rounded-t border-gray-200">
                <h3 class="text-xl font-medium text-gray-900">Template Title</h3>
                <button type="button" class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center" onclick="closeTemplatePreview()">
                    <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"/>
                    </svg>
                    <span class="sr-only">Close modal</span>
                </button>
            </div>
            <!-- Modal body -->
            <div class="space-y-4">
                <!-- Two Column Layout -->
                <div class="flex flex-col md:flex-row">
                    <!-- Left Side - Image Gallery -->
                    <div class="w-full md:w-3/5 bg-gray-50">
                        <div class="h-[700px] overflow-y-auto scroll-smooth">
                            Template screenshots
                        </div>
                    </div>

                    <!-- Right Side - Template Info -->
                    <div class="w-full md:w-2/5">
                        <div class="h-[700px] overflow-y-auto">
                            <div class="p-8">
                                <!-- Template Header -->
                                <div class="space-y-4">
                                    <div class="flex flex-wrap gap-6 mb-4">
                                        <div class="inline-flex items-center px-3 py-1.5 bg-gray-50 rounded-md border border-gray-200">
                                            <svg class="w-4 h-4 mr-2 text-blue-600" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                <rect width="7" height="7" x="3" y="3" rx="1"/>
                                                <rect width="7" height="7" x="14" y="3" rx="1"/>
                                                <rect width="7" height="7" x="14" y="14" rx="1"/>
                                                <rect width="7" height="7" x="3" y="14" rx="1"/>
                                            </svg>
                                            <span class="text-sm text-gray-600">Category: <span class="preview-category font-semibold text-gray-900 ml-1"></span></span>
                                        </div>
                                        <div class="inline-flex items-center px-3 py-1.5 bg-gray-50 rounded-md border border-gray-200">
                                            <svg class="w-4 h-4 mr-2 text-blue-600" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                <path d="M20 7h-3a2 2 0 0 1-2-2V2"/>
                                                <path d="M9 18a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h7l4 4v10a2 2 0 0 1-2 2Z"/>
                                                <path d="M3 7.6v12.8A1.6 1.6 0 0 0 4.6 22h9.8"/>
                                            </svg>
                                            <span class="text-sm text-gray-600">Pages: <span class="preview-sections font-semibold text-gray-900 ml-1"></span></span>
                                        </div>
                                    </div>
                                    <p class="text-gray-600 leading-relaxed detailedDescription">
                                        Detailed Description
                                    </p>
                                </div>                          
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- Modal footer -->
            <div class="flex items-center p-4 md:p-5 border-t border-gray-200 rounded-b">
                <button data-modal-hide="templatePreviewModal" type="button" class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center">Use Template</button>
                <button type="button" onclick="closeTemplatePreview()" class="py-2.5 px-5 ms-3 text-sm font-medium text-gray-900 focus:outline-none bg-white rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-100">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Delete Modal -->
<div id="deleteModal" tabindex="-1" aria-hidden="true" class="fixed top-0 left-0 right-0 z-50 hidden w-full p-4 overflow-x-hidden overflow-y-auto md:inset-0 h-[calc(100%-1rem)] max-h-full">
    <div class="relative w-full max-w-md max-h-full">
        <!-- Modal content -->
        <div class="relative bg-white rounded-lg shadow">
            <!-- Modal body -->
            <form id="deleteForm" method="POST" action="<?php echo BASE_URL; ?>">
                <div class="p-6">
                    <input type="hidden" name="delete" value="1">
                    <input type="hidden" name="id" id="deleteId">
                    <div class="text-center mb-4">
                        <svg class="mx-auto h-12 w-12 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                        </svg>
                    </div>
                    <p class="text-center text-base leading-relaxed text-gray-700 font-medium">
                        Are you sure you want to delete this template?
                    </p>
                    <p class="text-center text-sm text-gray-500 mt-1">
                        This action cannot be undone.
                    </p>
                </div>
                <!-- Modal footer -->
                <div class="flex items-center pt-2 pb-7 justify-center space-x-2 rounded-b">
                    <button type="submit" class="text-white bg-red-700 hover:bg-red-800 focus:ring-4 focus:outline-none focus:ring-red-300 font-medium rounded-lg text-sm px-5 py-2 text-center disabled:opacity-50 disabled:cursor-not-allowed">Delete</button>
                    <button type="button" class="text-gray-500 bg-white hover:bg-gray-100 focus:ring-4 focus:outline-none focus:ring-gray-200 rounded-lg border border-gray-200 text-sm font-medium px-5 py-2 hover:text-gray-900 focus:z-10" data-modal-hide="deleteModal">Close</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
let templatePreviewModal;

function closeTemplatePreview() {
    if (templatePreviewModal) {
        templatePreviewModal.hide();
    }
}

// Initialize Flowbite modals
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Flowbite
    if (typeof initFlowbite === 'function') {
        initFlowbite();
    } else if (typeof Flowbite !== 'undefined') {
        new Flowbite();
    }
    
    // Initialize template preview modal
    const templatePreviewModalElement = document.getElementById('templatePreviewModal');
    if (templatePreviewModalElement) {
        templatePreviewModal = new Modal(templatePreviewModalElement, {
            placement: 'center',
            backdrop: 'dynamic',
            closable: true
        });
    }

    // Initialize delete modal
    const deleteModal = document.getElementById('deleteModal');
    if (deleteModal) {
        const modal = new Modal(deleteModal, {
            placement: 'center',
            backdrop: 'dynamic',
            closable: true
        });
    }
    
    // Initialize Lucide icons
    if (typeof lucide !== "undefined" && typeof lucide.createIcons === "function") {
        lucide.createIcons();
    }
});

async function showTemplatePreview(templateId) {
    try {
        console.log('Fetching template:', templateId);
        // Fetch template data
        const response = await fetch(`<?php echo BASE_URL; ?>/modules/templates/get_template.php?id=${templateId}`);
        const result = await response.json();
        console.log('Template data:', result);
        
        if (result.success && result.data) {
            const data = result.data;
            
            // Show modal
            if (templatePreviewModal) {
                templatePreviewModal.show();
            }
            
            // Update modal title
            const titleEl = document.querySelector('#templatePreviewModal h3.text-xl');
            if (titleEl) {
                titleEl.textContent = data.name || 'Template Preview';
            }
            
            // Update screenshots
            const screenshotsContainer = document.querySelector('#templatePreviewModal .h-\\[700px\\].overflow-y-auto.scroll-smooth');
            if (screenshotsContainer) {
                if (data.multiple_template_screenshot && data.multiple_template_screenshot !== 'null') {
                    try {
                        const screenshots = JSON.parse(data.multiple_template_screenshot);
                        let screenshotsHtml = '';
                        screenshots.forEach(screenshot => {
                            screenshotsHtml += `
                                <div class="mb-4 p-4">
                                    <img src="<?php echo BASE_URL; ?>${screenshot}" class="w-full h-auto rounded-lg shadow-sm" alt="Template screenshot">
                                </div>
                            `;
                        });
                        screenshotsContainer.innerHTML = screenshotsHtml;
                    } catch (e) {
                        console.error('Error parsing screenshots:', e);
                        screenshotsContainer.innerHTML = '<div class="p-4 text-gray-500">Error loading screenshots</div>';
                    }
                } else if (data.preview_image) {
                    screenshotsContainer.innerHTML = `
                        <div class="mb-4 p-4">
                            <img src="<?php echo BASE_URL; ?>${data.preview_image}" class="w-full h-auto rounded-lg shadow-sm" alt="Template preview">
                        </div>
                    `;
                } else {
                    screenshotsContainer.innerHTML = '<div class="p-4 text-gray-500">No screenshots available</div>';
                }
            }
            
            // Update categories
            const categorySpan = document.querySelector('#templatePreviewModal .preview-category');
            if (categorySpan && data.categories) {
                const categoryNames = data.categories.map(cat => cat.name).join(', ');
                categorySpan.textContent = categoryNames || 'Uncategorized';
            }
            
            // Update pages count
            const pagesSpan = document.querySelector('#templatePreviewModal .preview-sections');
            if (pagesSpan && data.pages) {
                pagesSpan.innerHTML = data.pages.length.toString();
            }
            
            // Update description
            const descEl = document.querySelector('#templatePreviewModal .text-gray-600.leading-relaxed.detailedDescription');
            if (descEl) {
                descEl.innerHTML = data.detailed_description || 'No description available';
            }
        } else {
            console.error('Failed to fetch template data:', result.error || 'Unknown error');
        }
    } catch (error) {
        console.error('Error fetching template data:', error);
    }
}

// Store the ID temporarily when delete button is clicked
let currentDeleteId = null;

// Event listeners for the delete buttons
document.addEventListener('click', function(e) {
    if (e.target.closest('[data-modal-target="deleteModal"]')) {
        const button = e.target.closest('[data-modal-target="deleteModal"]');
        currentDeleteId = button.getAttribute('data-id');
        document.getElementById('deleteId').value = currentDeleteId;
    }
});

// Set up the event listener for the confirm button
document.addEventListener('DOMContentLoaded', function() {
    const confirmDeleteButton = document.querySelector('#deleteModal button[type="submit"]');
    if (confirmDeleteButton) {
        confirmDeleteButton.addEventListener('click', function(e) {
            e.preventDefault(); // Prevent default form submission
            if (currentDeleteId) {
                deleteTemplate(currentDeleteId);
            }
        });
    }
});

// Function to handle the actual deletion
function deleteTemplate(id) {
    document.getElementById('deleteId').value = id;
    document.getElementById('deleteForm').submit();
}
</script>

<?php
$content = ob_get_clean();
require_once __DIR__ . '/../layouts/main.php';
?>