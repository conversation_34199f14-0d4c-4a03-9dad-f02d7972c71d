<?php
require_once __DIR__ . '/../../config/config.php';
require_once __DIR__ . '/../../modules/global_assets/GlobalAssets.php';
require_once __DIR__ . '/../../modules/templates/Templates.php';
require_once __DIR__ . '/../../includes/TableComponent.php';
require_once __DIR__ . '/../../includes/TableHelper.php';

$globalAssets = new GlobalAssets();
$templates = new Templates();

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // Check if it's a delete operation
        if (isset($_POST['delete']) && isset($_POST['id'])) {
            $id = $_POST['id'];
            $result = $globalAssets->delete($id);
            if ($result['success']) {
                header("Location: " . BASE_URL . "/views/global_assets/index.php?success=Asset deleted successfully");
                exit;
            } else {
                $error = $result['message'] ?? 'Failed to delete asset';
            }
        }
        
        // Handle create/edit operations
        if (isset($_POST['save_asset'])) {
            $data = [
                'template_id' => $_POST['template_id'],
                'asset_type' => $_POST['asset_type'],
                'asset_value' => $_POST['asset_value']
            ];
            
            if (!empty($_POST['id'])) {
                // Edit operation
                $id = $_POST['id'];
                $result = $globalAssets->update($id, $data);
                if ($result['success']) {
                    header("Location: " . BASE_URL . "/views/global_assets/index.php?success=Asset updated successfully");
                    exit;
                } else {
                    $error = $result['message'] ?? 'Failed to update asset';
                }
            } else {
                // Create operation
                $result = $globalAssets->create($data);
                if ($result['success']) {
                    header("Location: " . BASE_URL . "/views/global_assets/index.php?success=Asset created successfully");
                    exit;
                } else {
                    $error = $result['message'] ?? 'Failed to create asset';
                }
            }
        }
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// Get sorting parameter from URL (in format 'sort=column' or 'sort=-column')
$sort = isset($_GET['sort']) ? $_GET['sort'] : 'id';
$direction = 'asc';

// Check if sort has a negative prefix (for descending)
if (substr($sort, 0, 1) === '-') {
    $realSort = substr($sort, 1); // Remove the minus sign
    $direction = 'desc';
} else {
    $realSort = $sort;
}

// Get limit from URL parameter
$items_per_page = isset($_GET['limit']) ? (int)$_GET['limit'] : 10;

// Validate sort parameter 
$allowedSortColumns = ['id', 'asset_type', 'template_id', 'asset_value', 'created_at', 'updated_at'];
if (!in_array($realSort, $allowedSortColumns)) {
    $realSort = 'id';
    $sort = ($direction === 'desc') ? '-id' : 'id';
}

// Get assets with sorting and pagination
$result = $globalAssets->getAllAssets([
    'page' => isset($_GET['page']) ? (int)$_GET['page'] : 1,
    'sort' => $realSort,
    'direction' => $direction,
    'limit' => $items_per_page
]);

$assets = $result['success'] ? ($result['data']['data'] ?? []) : [];

// Simple pagination configuration using the new structure
$pagination = $result['success'] ? $result['data'] : [
    'current_page' => 1,
    'per_page' => 10,
    'from' => null,
    'to' => null,
    'total' => 0,
    'next_page_url' => null,
    'prev_page_url' => null,
    'first_page_url' => null,
    'last_page_url' => null
];

// Set page title and breadcrumbs
$pageTitle = 'Global Assets';
$breadcrumbs = [
    [
        'label' => 'Global Assets',
        'url' => BASE_URL . '/views/global_assets/index.php'
    ]
];

// Get list of templates for dropdown
$templatesList = [];
try {
    $templatesResult = $templates->getList(['limit' => 100]);
    if ($templatesResult['success']) {
        $templatesList = $templatesResult['data']['data'] ?? [];
    }
} catch (Exception $e) {
    // Handle error silently
}

// Start output buffering
ob_start();

// Define table columns
$columns = [
    'id' => [
        'label' => 'ID',
        'sortable' => true
    ],
    'asset_type' => [
        'label' => 'Type',
        'sortable' => true
    ],
    'template_id' => [
        'label' => 'Template Name',
        'sortable' => true,
        'formatter' => function($value, $row) use ($templatesList) {
            foreach ($templatesList as $template) {
                if ($template['id'] == $value) {
                    return htmlspecialchars($template['name']);
                }
            }
            return '-';
        }
    ],
    'asset_value' => [
        'label' => 'Assets URL',
        'sortable' => true,
        'formatter' => function($value) {
            return '<a href="' . htmlspecialchars($value) . '" target="_blank" class="text-primary-600 hover:text-primary-900">' . htmlspecialchars($value) . '</a>';
        }
    ]
];

// Define action buttons
$actionButtons = [
    [
        'label' => 'Add New Asset',
        'icon' => 'plus',
        'attributes' => [
            'data-modal-target' => 'createAssetModal',
            'data-modal-toggle' => 'createAssetModal'
        ]
    ]
];

// Row actions
$rowActions = [
    [
        'icon' => '<path d="M21.174 6.812a1 1 0 0 0-3.986-3.987L3.842 16.174a2 2 0 0 0-.5.83l-1.321 4.352a.5.5 0 0 0 .623.622l4.353-1.32a2 2 0 0 0 .83-.497z"/><path d="m15 5 4 4"/>',
        'tooltip' => 'Edit',
        'type' => 'button',
        'hoverTextColor' => 'hover:text-blue-700',
        'attributes' => [
            'data-modal-target' => 'editAssetModal',
            'data-modal-toggle' => 'editAssetModal',
            'data-id' => function($row) { return $row['id'] ?? ''; },
            'data-type' => function($row) { return $row['asset_type'] ?? ''; },
            'data-value' => function($row) { return $row['asset_value'] ?? ''; },
            'data-template-id' => function($row) { return $row['template_id'] ?? ''; }
        ]
    ],
    [
        'icon' => '<path d="M3 6h18"/><path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"/><path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"/>',
        'tooltip' => 'Delete',
        'type' => 'button',
        'hoverTextColor' => 'hover:text-red-700',
        'attributes' => [
            'data-modal-target' => 'deleteAssetModal',
            'data-modal-toggle' => 'deleteAssetModal',
            'data-id' => function($row) { return $row['id'] ?? ''; }
        ]
    ]
];

// Initialize the table component with pagination
$table = new TableComponent($assets, $columns, $pagination);
$table->setTableId('assetsTable')
      ->setTitleBreadcrumb('Global Assets', [
        ['label' => 'Home', 'url' => BASE_URL . '/dashboard'],
        ['label' => 'Global Assets']
    ])
      ->setSearchConfig(true, 'searchAssets')
      ->setActionButtons($actionButtons)
      ->setRowActions($rowActions)
      ->setGridConfig(['enabled' => false]);

// Display the table
echo '<div>';
// Render the table
echo $table->render();
echo '</div>';

?>

<?php
// Include modals and scripts
require_once __DIR__ . '/modal.php';
echo '<script src="' . BASE_URL . '/assets/js/global_assets/global_assets.js"></script>';


$content = ob_get_clean();
require_once __DIR__ . '/../layouts/main.php';
?>