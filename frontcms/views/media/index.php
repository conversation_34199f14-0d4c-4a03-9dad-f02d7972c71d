<?php
require_once __DIR__ . '/../../config/config.php';
// Start output buffering
ob_start();
?>
    <header class="bg-white py-3 border-b border-gray-200 mx-4" >
    <div class="flex items-center justify-between">
        <!-- Left Side - Search Bar -->
        <div class="flex-1 max-w-xl">
            <div class="flex items-center">
                <div class="flex flex-col">
                    <!-- Breadcrumb -->
                    <div class="flex items-center space-x-2 text-[12px] text-gray-500">
                        <a href="<?php echo BASE_URL; ?>" class="hover:text-[#0C5BE2]">Home</a>
                        <span>/</span>
                        <span>Media</span>
                    </div>
                    <!-- page Title -->
                    <h1 class="text-xl font-semibold text-gray-900">
                        Media Manager
                    </h1>
                </div>
            </div>
        </div>
        <!-- Right Side - Actions -->
        <div class="flex items-center space-x-2">
            <button id="createFolderBtn" type="button" class="text-xs text-white bg-[#0C5BE2] px-2 py-1.5 rounded hover:bg-blue-700 flex items-center gap-1">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" data-lucide="plus" class="lucide lucide-plus w-4 h-4">
                    <path d="M5 12h14"></path>
                    <path d="M12 5v14"></path>
                </svg>
                <span>Create Folder</span>
            </button>
            <button id="uploadBtn"  type="button" class="text-xs text-white bg-green-600 px-2 py-1.5 rounded hover:bg-green-700 flex items-center gap-1">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"/>
                    </svg>
                <span>Upload Files</span>
            </button>
        </div>
    </div>
</header>
    <div class="container-fluid px-4 py-4">
        
        <!-- Stats Bar -->
        <div id="statsBar" class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div class="bg-white p-4 rounded-lg shadow-sm border">
                <div class="flex items-center">
                    <div class="p-2 bg-blue-100 rounded-lg">
                        <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Total Folders</p>
                        <p id="totalFolders" class="text-2xl font-semibold text-gray-900">0</p>
                    </div>
                </div>
            </div>
            <div class="bg-white p-4 rounded-lg shadow-sm border">
                <div class="flex items-center">
                    <div class="p-2 bg-green-100 rounded-lg">
                        <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Total Files</p>
                        <p id="totalFiles" class="text-2xl font-semibold text-gray-900">0</p>
                    </div>
                </div>
            </div>
            <div class="bg-white p-4 rounded-lg shadow-sm border">
                <div class="flex items-center">
                    <div class="p-2 bg-purple-100 rounded-lg">
                        <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"/>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Current Location</p>
                        <p id="currentLocation" class="text-lg font-semibold text-gray-900">Home</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Breadcrumb -->
        <div class="bg-white rounded-lg shadow-sm border p-4 mb-6">
            <nav class="flex" aria-label="Breadcrumb">
                <ol id="breadcrumb" class="inline-flex items-center space-x-1 md:space-x-3">
                    <li class="inline-flex items-center">
                        <span class="text-gray-700 hover:text-blue-600 cursor-pointer font-medium" data-path="">
                            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"/>
                            </svg>
                            Home
                        </span>
                    </li>
                </ol>
            </nav>
        </div>

        <!-- Upload Area -->
        <div id="uploadArea" class="hidden bg-white rounded-lg border-2 border-dashed border-gray-300 p-8 mb-6 text-center hover:border-blue-400 transition-colors duration-200">
            <svg class="mx-auto h-12 w-12 text-gray-400 mb-4" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
            <p class="text-xl font-medium text-gray-900 mb-2">Drop files here to upload</p>
            <p class="text-gray-500 mb-4">or click to select files (Max 10MB per file)</p>
            <input type="file" id="fileInput" class="hidden" multiple accept="image/*">
            <button id="selectFileBtn" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200">
                Select Files
            </button>
        </div>

        <!-- Loading Spinner -->
        <div id="loadingSpinner" class="hidden text-center py-12">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <p class="mt-4 text-gray-600">Loading media files...</p>
        </div>

        <!-- Media Grid -->
        <div id="mediaGrid" class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 xl:grid-cols-8 gap-4 mb-8">
            <!-- Content will be loaded here -->
        </div>

        <!-- No Content Message -->
        <div id="noContent" class="hidden text-center py-16">
            <svg class="mx-auto h-16 w-16 text-gray-300 mb-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
            </svg>
            <h3 class="text-xl font-medium text-gray-900 mb-2">No media files found</h3>
            <p class="text-gray-500 mb-6">Start by uploading some images or creating folders to organize your media</p>
            <button onclick="document.getElementById('uploadBtn').click()" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200">
                Upload Your First File
            </button>
        </div>
    </div>
    <!-- Create Folder Modal -->
    <div id="folderModal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-lg bg-white">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-gray-900">Create New Folder</h3>
                    <button id="closeFolderModal" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                        </svg>
                    </button>
                </div>
                <div class="mb-4">
                    <label for="folderNameInput" class="block text-sm font-medium text-gray-700 mb-2">Folder Name</label>
                    <input type="text" id="folderNameInput" placeholder="Enter folder name" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>
                <div class="flex justify-end space-x-3">
                    <button id="cancelFolderBtn" class="px-4 py-2 text-gray-600 hover:text-gray-800 font-medium">Cancel</button>
                    <button id="confirmFolderBtn" class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors duration-200">Create Folder</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Image Preview Modal -->
    <div id="previewModal" class="hidden fixed inset-0 bg-black bg-opacity-75 overflow-y-auto h-full w-full z-50">
        <div class="relative top-4 mx-auto p-5 border w-11/12 max-w-4xl shadow-lg rounded-lg bg-white mb-4">
            <div class="flex justify-between items-center mb-6">
                <h3 id="previewTitle" class="text-xl font-semibold text-gray-900"></h3>
                <button id="closePreviewBtn" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                    </svg>
                </button>
            </div>
            <div class="text-center">
                <div class="mb-6">
                    <img id="previewImage" class="max-w-full max-h-96 object-contain mx-auto rounded-lg shadow-lg">
                </div>
                <div id="previewInfo" class="bg-gray-50 rounded-lg p-4 mb-6 text-left">
                    <!-- File info will be populated here -->
                </div>
                <div class="flex flex-wrap justify-center gap-3">
                    <button id="copyUrlBtn" class="px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors duration-200 flex items-center">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"/>
                        </svg>
                        Copy URL
                    </button>
                    <button id="copyFullUrlBtn" class="px-6 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg font-medium transition-colors duration-200 flex items-center">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"/>
                        </svg>
                        Copy Full URL
                    </button>
                    <button id="deleteFileBtn" class="px-6 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg font-medium transition-colors duration-200 flex items-center">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                        </svg>
                        Delete File
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Toast Notification -->
    <div id="toast" class="hidden fixed top-4 right-4 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg z-50">
        <div class="flex items-center">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
            </svg>
            <span id="toastMessage">Operation successful</span>
        </div>
    </div>

    <!-- Error Toast -->
    <div id="errorToast" class="hidden fixed top-4 right-4 bg-red-500 text-white px-6 py-3 rounded-lg shadow-lg z-50">
        <div class="flex items-center">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
            </svg>
            <span id="errorToastMessage">An error occurred</span>
        </div>
    </div>
    <script>
        class MediaManager {
            constructor() {
                this.currentPath = '';
                this.apiBase = '<?php echo BASE_URL; ?>/modules/media/api.php';
                this.currentPreviewPath = '';
                this.currentPreviewUrl = '';
                this.currentPreviewFullUrl = '';
                this.init();
            }

            init() {
                this.bindEvents();
                this.loadMedia();
            }

            bindEvents() {
                // Create folder events
                document.getElementById('createFolderBtn').addEventListener('click', () => {
                    document.getElementById('folderModal').classList.remove('hidden');
                    document.getElementById('folderNameInput').focus();
                });

                document.getElementById('closeFolderModal').addEventListener('click', () => {
                    this.closeFolderModal();
                });

                document.getElementById('cancelFolderBtn').addEventListener('click', () => {
                    this.closeFolderModal();
                });

                document.getElementById('confirmFolderBtn').addEventListener('click', () => {
                    this.createFolder();
                });

                // Upload events
                document.getElementById('uploadBtn').addEventListener('click', () => {
                    const uploadArea = document.getElementById('uploadArea');
                    uploadArea.classList.toggle('hidden');
                });

                document.getElementById('selectFileBtn').addEventListener('click', () => {
                    document.getElementById('fileInput').click();
                });

                document.getElementById('fileInput').addEventListener('change', (e) => {
                    this.handleFiles(e.target.files);
                });

                // Drag and drop events
                const uploadArea = document.getElementById('uploadArea');
                
                uploadArea.addEventListener('dragover', (e) => {
                    e.preventDefault();
                    uploadArea.classList.add('border-blue-400', 'bg-blue-50');
                });

                uploadArea.addEventListener('dragleave', (e) => {
                    e.preventDefault();
                    uploadArea.classList.remove('border-blue-400', 'bg-blue-50');
                });

                uploadArea.addEventListener('drop', (e) => {
                    e.preventDefault();
                    uploadArea.classList.remove('border-blue-400', 'bg-blue-50');
                    this.handleFiles(e.dataTransfer.files);
                });

                // Preview modal events
                document.getElementById('closePreviewBtn').addEventListener('click', () => {
                    document.getElementById('previewModal').classList.add('hidden');
                });

                document.getElementById('copyUrlBtn').addEventListener('click', () => {
                    this.copyToClipboard(this.currentPreviewUrl, 'URL copied to clipboard!');
                });

                document.getElementById('copyFullUrlBtn').addEventListener('click', () => {
                    this.copyToClipboard(this.currentPreviewFullUrl, 'Full URL copied to clipboard!');
                });

                document.getElementById('deleteFileBtn').addEventListener('click', () => {
                    this.deleteCurrentFile();
                });

                // Keyboard shortcuts
                document.addEventListener('keydown', (e) => {
                    if (e.key === 'Escape') {
                        document.getElementById('folderModal').classList.add('hidden');
                        document.getElementById('previewModal').classList.add('hidden');
                    }
                });

                // Enter key for folder creation
                document.getElementById('folderNameInput').addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') {
                        this.createFolder();
                    }
                });
            }

            async loadMedia(path = '', retryCount = 0) {
                this.currentPath = path;
                this.showLoading(true);
                
                try {
                    const response = await fetch(`${this.apiBase}?action=list&folder=${encodeURIComponent(path)}`);
                    
                    // First check if the response is OK
                    if (!response.ok) {
                        throw new Error(`Server returned ${response.status}: ${response.statusText}`);
                    }
                    
                    // Try to get the response as text first
                    const responseText = await response.text();
                    
                    // If empty response, retry a few times (might be a timing issue)
                    if (!responseText.trim() && retryCount < 3) {
                        console.log(`Empty response received, retrying (${retryCount + 1}/3)...`);
                        setTimeout(() => {
                            this.loadMedia(path, retryCount + 1);
                        }, 500);
                        return;
                    }
                    
                    // Try to parse the JSON
                    let result;
                    try {
                        result = JSON.parse(responseText);
                    } catch (e) {
                        console.error('Invalid JSON response:', responseText);
                        throw new Error('Server returned invalid JSON');
                    }
                    
                    if (result.success) {
                        this.renderMedia(result.data);
                        this.updateBreadcrumb(path);
                        this.updateStats(result.data);
                    } else {
                        this.showError(result.message);
                    }
                } catch (error) {
                    this.showError('Failed to load media: ' + error.message);
                    console.error('Media loading error:', error);
                } finally {
                    this.showLoading(false);
                }
            }

            renderMedia(data) {
                const grid = document.getElementById('mediaGrid');
                const noContent = document.getElementById('noContent');
                
                if (data.folders.length === 0 && data.files.length === 0) {
                    grid.innerHTML = '';
                    noContent.classList.remove('hidden');
                    return;
                }
                
                noContent.classList.add('hidden');
                
                let html = '';
                
                // Render folders
                data.folders.forEach(folder => {
                    html += `
                        <div class="bg-white rounded-lg shadow-sm border hover:shadow-md transition-shadow duration-200 cursor-pointer group" onclick="mediaManager.loadMedia('${folder.path}')">
                            <div class="p-4 text-center">
                                <div class="relative">
                                    <svg class="w-12 h-12 mx-auto mb-3 text-yellow-500 group-hover:text-yellow-600 transition-colors duration-200" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M10 4H4c-1.11 0-2 .89-2 2v12c0 1.11.89 2 2 2h16c1.11 0 2-.89 2-2V8c0-1.11-.89-2-2-2h-8l-2-2z"/>
                                    </svg>
                                    ${folder.file_count > 0 ? `<span class="absolute -top-1 -right-1 bg-blue-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">${folder.file_count}</span>` : ''}
                                </div>
                                <p class="text-sm font-medium text-gray-900 truncate mb-1">${folder.name}</p>
                                <p class="text-xs text-gray-500">${this.formatDate(folder.created)}</p>
                                <button onclick="event.stopPropagation(); mediaManager.deleteItem('${folder.path}', 'folder')" class="mt-2 text-red-500 hover:text-red-700 text-xs opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                                    Delete Folder
                                </button>
                            </div>
                        </div>
                    `;
                });
                
                // Render files
                data.files.forEach(file => {
                    html += `
                        <div class="bg-white rounded-lg shadow-sm border hover:shadow-md transition-shadow duration-200 cursor-pointer group" onclick="mediaManager.previewFile('${file.path}', '${file.name}', '${file.url}', ${file.size}, '${file.dimensions.width}x${file.dimensions.height}', '${file.type}', '${file.created}')">
                            <div class="p-2">
                                <div class="aspect-w-1 aspect-h-1 mb-2">
                                    <img src="${file.url}" alt="${file.name}" class="w-full h-24 object-cover rounded-md">
                                </div>
                                <p class="text-xs font-medium text-gray-900 truncate px-1">${file.name}</p>
                                <p class="text-xs text-gray-500 px-1">${this.formatFileSize(file.size)}</p>
                                <p class="text-xs text-gray-400 px-1">${file.dimensions.width}×${file.dimensions.height}</p>
                            </div>
                        </div>
                    `;
                });
                
                grid.innerHTML = html;
            }

            updateBreadcrumb(path) {
                const breadcrumb = document.getElementById('breadcrumb');
                const parts = path ? path.split('/') : [];
                
                let html = `
                    <li class="inline-flex items-center">
                        <span class="text-gray-700 hover:text-blue-600 cursor-pointer font-medium" data-path="" onclick="mediaManager.loadMedia('')">
                            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"/>
                            </svg>
                            Home
                        </span>
                    </li>
                `;
                
                let currentPath = '';
                parts.forEach((part, index) => {
                    currentPath = currentPath ? `${currentPath}/${part}` : part;
                    html += `
                        <li>
                            <div class="flex items-center">
                                <svg class="flex-shrink-0 h-5 w-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
                                </svg>
                                <span class="ml-1 text-gray-700 hover:text-blue-600 cursor-pointer font-medium" onclick="mediaManager.loadMedia('${currentPath}')">${part}</span>
                            </div>
                        </li>
                    `;
                });
                
                breadcrumb.innerHTML = html;
            }

            updateStats(data) {
                document.getElementById('totalFolders').textContent = data.total_folders;
                document.getElementById('totalFiles').textContent = data.total_files;
                document.getElementById('currentLocation').textContent = data.current_path || 'Home';
            }

            async createFolder() {
                const folderName = document.getElementById('folderNameInput').value.trim();
                if (!folderName) {
                    this.showError('Please enter a folder name');
                    return;
                }
                
                try {
                    const response = await fetch(`${this.apiBase}?action=create-folder`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            name: folderName,
                            parent: this.currentPath
                        })
                    });
                    
                    const responseText = await response.text();
                    let result;
                    try {
                        result = JSON.parse(responseText);
                    } catch (e) {
                        console.error('Invalid JSON response:', responseText);
                        throw new Error('Server returned invalid JSON');
                    }
                    
                    if (result.success) {
                        this.closeFolderModal();
                        this.loadMedia(this.currentPath);
                        this.showSuccess('Folder created successfully');
                    } else {
                        this.showError(result.message);
                    }
                } catch (error) {
                    this.showError('Failed to create folder: ' + error.message);
                }
            }

            closeFolderModal() {
                document.getElementById('folderModal').classList.add('hidden');
                document.getElementById('folderNameInput').value = '';
            }

            async handleFiles(files) {
                const fileArray = Array.from(files);
                let uploadCount = 0;
                
                for (const file of fileArray) {
                    if (!file.type.startsWith('image/')) {
                        this.showError(`${file.name} is not an image file`);
                        continue;
                    }
                    
                    if (file.size > 10485760) { // 10MB
                        this.showError(`${file.name} is too large (max 10MB)`);
                        continue;
                    }
                    
                    const success = await this.uploadFile(file);
                    if (success) uploadCount++;
                }
                
                if (uploadCount > 0) {
                    this.loadMedia(this.currentPath);
                    this.showSuccess(`Successfully uploaded ${uploadCount} file(s)`);
                }
            }

            async uploadFile(file) {
                const formData = new FormData();
                formData.append('file', file);
                formData.append('folder', this.currentPath);
                
                try {
                    const response = await fetch(`${this.apiBase}?action=upload`, {
                        method: 'POST',
                        body: formData
                    });
                    
                    const responseText = await response.text();
                    let result;
                    try {
                        result = JSON.parse(responseText);
                    } catch (e) {
                        console.error('Invalid JSON response:', responseText);
                        throw new Error('Server returned invalid JSON');
                    }
                    
                    return result.success;
                } catch (error) {
                    this.showError(`Failed to upload ${file.name}: ${error.message}`);
                    return false;
                }
            }

            previewFile(path, name, url, size, dimensions, type, created) {
                this.currentPreviewPath = path;
                this.currentPreviewUrl = url;
                
                // Get full URL
                const protocol = window.location.protocol;
                const host = window.location.host;
                this.currentPreviewFullUrl = `${protocol}//${host}${url}`;
                
                document.getElementById('previewTitle').textContent = name;
                document.getElementById('previewImage').src = url;
                document.getElementById('previewInfo').innerHTML = `
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <p class="text-sm text-gray-600 mb-1"><strong>File Name:</strong></p>
                            <p class="text-sm text-gray-900 mb-3">${name}</p>
                            
                            <p class="text-sm text-gray-600 mb-1"><strong>File Size:</strong></p>
                            <p class="text-sm text-gray-900 mb-3">${this.formatFileSize(size)}</p>
                            
                            <p class="text-sm text-gray-600 mb-1"><strong>Dimensions:</strong></p>
                            <p class="text-sm text-gray-900 mb-3">${dimensions}</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-600 mb-1"><strong>File Type:</strong></p>
                            <p class="text-sm text-gray-900 mb-3">${type}</p>
                            
                            <p class="text-sm text-gray-600 mb-1"><strong>Created:</strong></p>
                            <p class="text-sm text-gray-900 mb-3">${this.formatDate(created)}</p>
                            
                            <p class="text-sm text-gray-600 mb-1"><strong>URL:</strong></p>
                            <p class="text-xs text-gray-900 bg-gray-100 p-2 rounded break-all">${url}</p>
                        </div>
                    </div>
                `;
                
                document.getElementById('previewModal').classList.remove('hidden');
            }

            async copyToClipboard(text, message) {
                try {
                    await navigator.clipboard.writeText(text);
                    this.showSuccess(message);
                } catch (error) {
                    // Fallback for older browsers
                    const textArea = document.createElement('textarea');
                    textArea.value = text;
                    document.body.appendChild(textArea);
                    textArea.select();
                    document.execCommand('copy');
                    document.body.removeChild(textArea);
                    this.showSuccess(message);
                }
            }

            async deleteCurrentFile() {
                if (!confirm('Are you sure you want to delete this file? This action cannot be undone.')) {
                    return;
                }
                
                await this.deleteItem(this.currentPreviewPath, 'file');
                document.getElementById('previewModal').classList.add('hidden');
            }

            async deleteItem(path, type) {
                if (!confirm(`Are you sure you want to delete this ${type}? This action cannot be undone.`)) {
                    return;
                }
                
                try {
                    const response = await fetch(`${this.apiBase}`, {
                        method: 'DELETE',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            path: path,
                            type: type
                        })
                    });
                    
                    const responseText = await response.text();
                    let result;
                    try {
                        result = JSON.parse(responseText);
                    } catch (e) {
                        console.error('Invalid JSON response:', responseText);
                        throw new Error('Server returned invalid JSON');
                    }
                    
                    if (result.success) {
                        this.showSuccess(result.message);
                        this.loadMedia(this.currentPath);
                    } else {
                        this.showError(result.message);
                    }
                } catch (error) {
                    this.showError(`Failed to delete ${type}: ${error.message}`);
                }
            }

            formatFileSize(bytes) {
                if (bytes === 0) return '0 Bytes';
                const k = 1024;
                const sizes = ['Bytes', 'KB', 'MB', 'GB'];
                const i = Math.floor(Math.log(bytes) / Math.log(k));
                return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
            }

            formatDate(dateString) {
                const date = new Date(dateString);
                return date.toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'short',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                });
            }

            showLoading(show) {
                const spinner = document.getElementById('loadingSpinner');
                const grid = document.getElementById('mediaGrid');
                const noContent = document.getElementById('noContent');

                if (show) {
                    spinner.classList.remove('hidden');
                    grid.classList.add('hidden');
                    noContent.classList.add('hidden');
                } else {
                    spinner.classList.add('hidden');
                    grid.classList.remove('hidden');
                }
            }

            showSuccess(message) {
                this.showToast(message, 'success');
            }

            showError(message) {
                this.showToast(message, 'error');
            }

            showToast(message, type) {
                const toastId = type === 'success' ? 'toast' : 'errorToast';
                const messageId = type === 'success' ? 'toastMessage' : 'errorToastMessage';
                
                const toast = document.getElementById(toastId);
                const messageEl = document.getElementById(messageId);
                
                messageEl.textContent = message;
                toast.classList.remove('hidden');
                
                // Auto hide after 3 seconds
                setTimeout(() => {
                    toast.classList.add('hidden');
                }, 3000);
            }
        }

        // Initialize the media manager when DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            window.mediaManager = new MediaManager();
        });
    </script>
<?php
$content = ob_get_clean();
require_once __DIR__ . '/../layouts/main.php';
?>