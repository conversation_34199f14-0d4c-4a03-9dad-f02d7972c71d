<?php 
require_once __DIR__ . '/../../config/config.php';
require_once __DIR__ . '/../../includes/FileUploadHelper.php';
require_once __DIR__ . '/../../vendor/autoload.php'; // Include Composer autoloader
require_once __DIR__ . '/../../modules/block_categories/BlockCategories.php';
require_once __DIR__ . '/../../modules/block_categories_mapping/BlockCategoriesMapping.php';
require_once __DIR__ . '/../../modules/blocks/Blocks.php'; // Add this line
require_once __DIR__ . '/../../modules/tags/Tags.php'; // Add this line
require_once __DIR__ . '/../../modules/common_tags/CommonTags.php'; // Add this line

use LightnCandy\LightnCandy;

// Define Handlebars helper functions
$helpers = [
    'mod' => function($arg1, $arg2) {
        return intval($arg1) % intval($arg2);
    },
    'eq' => function($arg1, $arg2) {
        return $arg1 === $arg2;
    },
    'ne' => function($arg1, $arg2) {
        return $arg1 !== $arg2;
    },
    'neq' => function($arg1, $arg2) {
        return $arg1 !== $arg2;
    },
    'lt' => function($arg1, $arg2) {
        return $arg1 < $arg2;
    },
    'gt' => function($arg1, $arg2) {
        return $arg1 > $arg2;
    },
    'lte' => function($arg1, $arg2) {
        return $arg1 <= $arg2;
    },
    'gte' => function($arg1, $arg2) {
        return $arg1 >= $arg2;
    },
    'and' => function() {
        foreach (func_get_args() as $arg) {
            if (!$arg) return false;
        }
        return true;
    },
    'or' => function() {
        foreach (func_get_args() as $arg) {
            if ($arg) return true;
        }
        return false;
    },
    'not' => function($arg) {
        return !$arg;
    },
    'isEven' => function($arg1) {
        return intval($arg1) % 2 === 0;
    },
    'isOdd' => function($arg1) {
        return intval($arg1) % 2 !== 0;
    }
];

// Function to process Handlebars templates
function processTemplate($template, $jsonData) {
    global $helpers;
    try {
        // Parse JSON data first
        $data = json_decode($jsonData, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            return ['success' => false, 'message' => 'Invalid JSON data: ' . json_last_error_msg()];
        }

        // Compile the template with helper functions
        $phpStr = LightnCandy::compile($template, [
            'flags' => LightnCandy::FLAG_HANDLEBARS | 
                      LightnCandy::FLAG_ERROR_EXCEPTION | 
                      LightnCandy::FLAG_RUNTIMEPARTIAL |
                      LightnCandy::FLAG_NOESCAPE,
            'helpers' => $helpers
        ]);
        
        if (!$phpStr) {
            return ['success' => false, 'message' => 'Failed to compile template'];
        }

        // Prepare the renderer
        $renderer = LightnCandy::prepare($phpStr);
        if (!$renderer) {
            return ['success' => false, 'message' => 'Failed to prepare template renderer'];
        }
        
        // Render the template with data
        $result = $renderer($data);
        return ['success' => true, 'html' => $result];
    } catch (Exception $e) {
        return ['success' => false, 'message' => 'Template processing error: ' . $e->getMessage()];
    }
}

// Handle AJAX request for template processing
if (isset($_POST['action']) && $_POST['action'] === 'process_template') {
    header('Content-Type: application/json');
    $template = $_POST['template'] ?? '';
    $jsonData = $_POST['json_data'] ?? '{}';
    
    $result = processTemplate($template, $jsonData);
    echo json_encode($result);
    exit;
}

// Handle image upload
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'upload_block_image') {
    header('Content-Type: application/json');
    
    try {
        if (!isset($_FILES['image'])) {
            throw new Exception('No image file uploaded');
        }

        // Use FileUploadHelper
        $uploader = new FileUploadHelper('blocks');
        $result = $uploader->handleUpload($_FILES['image']);
        
        if (!$result['success']) {
            throw new Exception($result['message']);
        }

        echo json_encode([
            'success' => true,
            'path' => $result['url']
        ]);
        exit;

    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => $e->getMessage()
        ]);
        exit;
    }
}

// Set page title and breadcrumbs
$pageTitle = 'Block Detail';

// Initialize block categories and blocks classes
$blockCategories = new BlockCategories();
$blockCategoriesMapping = new BlockCategoriesMapping();
$blocks = new Blocks(); // Initialize blocks class
$tags = new Tags(); // Initialize tags class
$commonTags = new CommonTags(); // Initialize common tags class

// Initialize error/success variables
$error = null;
$success = null;

// Handle Block Create/Edit/Delete
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    $id = $_POST['id'] ?? null;
    $name = $_POST['name'] ?? '';
    $slug = $_POST['slug'] ?? '';
    $description = $_POST['description'] ?? '';
    $classes = $_POST['classes'] ?? '';
    $image = $_POST['image'] ?? '';
    $tmpl_code = $_POST['tmpl_code'] ?? '';
    $json_code = $_POST['json_code'] ?? '';
    $css_code = $_POST['css_code'] ?? '';
    $js_code = $_POST['js_code'] ?? '';
    $dependencies = $_POST['dependencies'] ?? '';
    $category_id = $_POST['category_id'] ?? null;
    $tags = $_POST['tags'] ?? []; // Handle tags array
    
    try {
        $dataArr = [
            'name' => $name,
            'slug' => $slug,
            'description' => $description,
            'classes' => $classes,
            'image' => $image,
            'tmpl_code' => $tmpl_code,
            'json_code' => $json_code,
            'css_code' => $css_code,
            'js_code' => $js_code,
            'dependencies' => $dependencies,
            'tags' => $tags // Include tags in the data array
        ];
        
        // For backward compatibility
        if ($category_id) {
            $dataArr['category_id'] = $category_id;
        }
        
        // Process based on action using the Blocks class
        if ($action === 'add') {
            $result = $blocks->create($dataArr);
            $blockId = $result['block_id'] ?? ($result['data']['last_insert_id'] ?? null);
            $successMessage = 'Block created successfully';
        } elseif ($action === 'edit' && $id) {
            $result = $blocks->update($id, $dataArr);
            $blockId = $id;
            $successMessage = 'Block updated successfully';
        } elseif ($action === 'delete' && $id) {
            $result = $blocks->delete($id);
            $successMessage = 'Block deleted successfully';
        }
        
        if (isset($result) && $result['success']) {
            // Handle category mappings if categories are provided
            if (($action === 'add' || $action === 'edit') && isset($_POST['categories']) && is_array($_POST['categories'])) {
                if ($blockId) {
                    try {
                        // Delete existing mappings first
                        $blockCategoriesMapping->deleteByBlockId($blockId);
                        
                        // Create new mappings
                        foreach ($_POST['categories'] as $categoryId) {
                            $mappingData = [
                                'block_id' => $blockId,
                                'category_id' => $categoryId
                            ];
                            $blockCategoriesMapping->create($mappingData);
                        }
                    } catch (Exception $e) {
                        // Log the error but continue with the redirect
                        error_log('Error managing block categories: ' . $e->getMessage());
                    }
                }
            }
            
            // Set success message in session
            if ($_SESSION['success_message']) {
                header("Location: " . BASE_URL . "/blocks?success=$successMessage");   
            }
            // Redirect to the blocks list
            header('Location: ' . BASE_URL . '/blocks/list' . ($category_id ? '?category_id=' . $category_id : ''));
            exit;
        } else {
            $error = $result['message'] ?? 'Failed to process block';
        }
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// Fetch single block if block_id is provided
$block = null;
$isNewBlock = true;
$selectedBlockId = isset($_GET['block_id']) ? $_GET['block_id'] : null;
// Get selected category ID from session or URL
$selectedCategoryId = isset($_GET['category_id']) ? $_GET['category_id'] : (isset($_SESSION['selected_category_id']) ? $_SESSION['selected_category_id'] : null);

// Get categories for dropdown
$categories = [];
try {
    $response = $blockCategories->getList();
    if (isset($response['data']['data']) && is_array($response['data']['data'])) {
        $categories = $response['data']['data'];
    }
} catch (Exception $e) {
    // Handle error
    if (DEBUG) {
        echo "<!-- Debug: Categories Error: " . $e->getMessage() . " -->";
    }
}

// Fetch block data if editing
if ($selectedBlockId) {
    $isNewBlock = false;
    
    try {
        // Use the new method to get block with tags
        $blockResult = $blocks->getBlockWithTags($selectedBlockId);
        
        if ($blockResult['success']) {
            $block = $blockResult['data'];
            // Set page title with block name
            $pageTitle = 'Block: ' . ($block['name'] ?? 'Detail');
            
            // Debug output
            if (DEBUG) {
                echo "<!-- Debug: Block Data with Tags -->";
                echo "<!-- " . htmlspecialchars(json_encode($block)) . " -->";
            }
            
            // Fetch block categories
            try {
                $categoriesResponse = $blockCategoriesMapping->getByBlockId($selectedBlockId);
                
                // Debug output
                if (DEBUG) {
                    echo "<!-- Debug: Block Categories Response -->";
                    echo "<!-- " . htmlspecialchars(json_encode($categoriesResponse)) . " -->";
                }
                
                if (isset($categoriesResponse['data']['data']) && is_array($categoriesResponse['data']['data'])) {
                    $block['categories'] = [];
                    foreach ($categoriesResponse['data']['data'] as $mapping) {
                        if (isset($mapping['category_id'])) {
                            // Find the category details
                            foreach ($categories as $category) {
                                if ($category['id'] == $mapping['category_id']) {
                                    $block['categories'][] = $category;
                                    break;
                                }
                            }
                        }
                    }
                    
                    // Debug output
                    if (DEBUG) {
                        echo "<!-- Debug: Block Categories After Processing -->";
                        echo "<!-- " . htmlspecialchars(json_encode($block['categories'])) . " -->";
                    }
                }
            } catch (Exception $e) {
                // Handle error
                if (DEBUG) {
                    echo "<!-- Debug: Block Categories Error: " . $e->getMessage() . " -->";
                }
            }
        }
    } catch (Exception $e) {
        $error = $e->getMessage();
        if (DEBUG) {
            echo "<!-- Debug: Block Fetch Error: " . $e->getMessage() . " -->";
        }
    }
}

// Set breadcrumbs
if ($isNewBlock) {
    $pageTitle = 'Add New Block';
    $breadcrumbs = [
        [
            'label' => 'Blocks',
            'url' => BASE_URL . '/blocks/list'
        ],
        [
            'label' => 'Add New Block',
            'url' => BASE_URL . '/blocks/detail'
        ]
    ];
} else {
    $breadcrumbs = [
        [
            'label' => 'Blocks',
            'url' => BASE_URL . '/blocks/list'
        ],
        [
            'label' => $block['name'] ?? 'Block Detail',
            'url' => BASE_URL . '/blocks/detail?block_id=' . $selectedBlockId
        ]
    ];
}

// Start output buffering
ob_start();
?>

<!-- Required Scripts -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/codemirror.min.css">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/theme/monokai.min.css">
<script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/codemirror.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/mode/xml/xml.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/mode/javascript/javascript.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/mode/css/css.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/mode/htmlmixed/htmlmixed.min.js"></script>


<header class="bg-white py-3 border-b border-gray-200 mx-4" >
    <div class="flex items-center justify-between">
        <!-- Left Side - Search Bar -->
        <div class="flex-1 max-w-xl">
            <div class="flex items-center">
                <div class="flex flex-col">
                    <!-- Breadcrumb -->
                    <div class="flex items-center space-x-2 text-[12px] text-gray-500">
                        <a href="<?php echo BASE_URL; ?>" class="hover:text-[#0C5BE2]">Home</a>
                        <?php if (isset($breadcrumbs) && is_array($breadcrumbs)): ?>
                            <?php foreach ($breadcrumbs as $index => $crumb): ?>
                                <span>/</span>
                                <?php if ($index === array_key_last($breadcrumbs)): ?>
                                    <span class="text-gray-700"><?php echo htmlspecialchars($crumb['label']); ?></span>
                                <?php else: ?>
                                    <a href="<?php echo htmlspecialchars($crumb['url']); ?>" class="hover:text-[#0C5BE2]"><?php echo htmlspecialchars($crumb['label']); ?></a>
                                <?php endif; ?>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                    <!-- page Title -->
                    <h1 class="text-xl font-semibold text-gray-900">
                        <?php echo isset($pageTitle) ? htmlspecialchars($pageTitle) : 'Dashboard'; ?>
                    </h1>
                </div>
            </div>
        </div>
        <!-- Right Side - Actions -->
        <div class="flex items-center space-x-6">
            <!-- Theme Toggle -->
            <div class="flex items-center hidden">
                <button type="button" 
                    class="text-gray-500 hover:bg-gray-100 rounded-lg text-sm p-2 inline-flex items-center">
                    <i data-lucide="sun" class="w-5 h-5 text-gray-500 hover:text-[#0C5BE2]"></i>
                </button>
                <button type="button" 
                    class="text-white bg-gray-900 hover:bg-gray-800 rounded-lg text-sm p-2 inline-flex items-center">
                    <i data-lucide="moon" class="w-5 h-5 text-gray-500 hover:text-[#0C5BE2]"></i>
                </button>
            </div>

            <!-- Notification -->
            <button type="button" 
                class="relative text-gray-500 hover:bg-gray-100 rounded-lg p-2 inline-flex items-center">
                <i data-lucide="bell" class="w-5 h-5 text-gray-500 hover:text-[#0C5BE2]"></i>
                <div class="absolute inline-flex items-center justify-center w-2 h-2 bg-[#0C5BE2] rounded-full top-2 right-2"></div>
            </button>

            <!-- User Profile Dropdown
            <button type="button" 
                class="flex items-center space-x-3"
                id="userMenuButton" 
                data-dropdown-toggle="userMenu" 
                aria-expanded="false">
                <img class="w-9 h-9 rounded-full" 
                    src="https://ui-avatars.com/api/?name=<?php echo htmlspecialchars($userProfile['name'] ?? ''); ?>&background=0C5BE2&color=ffffff" 
                    alt="User">
                <div class="flex flex-col items-start">
                    <span class="text-sm font-medium text-gray-900"><?php echo htmlspecialchars($userProfile['name'] ?? ''); ?></span>
                    <span class="text-xs text-gray-500"><?php echo htmlspecialchars($userProfile['email'] ?? ''); ?></span>
                </div>
            </button> -->

            <!-- User Dropdown Menu
            <div class="hidden z-50 my-4 w-56 text-base list-none bg-white rounded-lg shadow-lg" 
                id="userMenu">
                <div class="py-3 px-4">
                    <span class="block text-sm font-semibold text-gray-900"><?php echo htmlspecialchars($userProfile['name'] ?? ''); ?></span>
                    <span class="block text-sm text-gray-500 truncate"><?php echo htmlspecialchars($userProfile['email'] ?? ''); ?></span>
                </div>
                <ul class="py-1" aria-labelledby="userMenuButton">
                    <li>
                        <a href="<?php echo BASE_URL; ?>/profile" class="flex items-center py-2 px-4 text-sm text-gray-700 hover:bg-gray-50">
                            <i data-lucide="user" class="w-4 h-4 mr-2 text-gray-500"></i>
                            Profile
                        </a>
                    </li>
                    <li class="hidden">
                        <a href="#" class="flex items-center py-2 px-4 text-sm text-gray-700 hover:bg-gray-50">
                            <i data-lucide="settings" class="w-4 h-4 mr-2 text-gray-500"></i>
                            Settings
                        </a>
                    </li>
                </ul>
                <div class="py-1">
                    <a href="<?php echo BASE_URL; ?>/logout" class="flex items-center py-2 px-4 text-sm text-red-600 hover:bg-gray-50">
                        <i data-lucide="log-out" class="w-4 h-4 mr-2 text-red-600"></i>
                        Sign out
                    </a>
                </div>
            </div> -->
        </div>
    </div>
</header>

<?php if (!empty($_SESSION['success_message'])): ?>
    <div class="mb-4 p-4 rounded bg-green-100 text-green-800 border border-green-300 text-center text-base">
        <?php echo htmlspecialchars($_SESSION['success_message']); unset($_SESSION['success_message']); ?>
    </div>
<?php endif; ?>

<?php if (!empty($error)): ?>
    <div class="mb-4 p-4 rounded bg-red-100 text-red-800 border border-red-300 text-center text-base">
        <?php echo htmlspecialchars($error); ?>
    </div>
<?php endif; ?>

<div class="flex gap-8 px-6">
    <!-- Left Sidebar - Steps Navigation -->
    <div class="w-[275px] border-r border-gray-200 pt-6 pr-6 flex flex-col justify-between">
        <!-- Steps List -->
        <div class="relative">
            <!-- Step 1 -->
            <div class="step-item relative pb-8">
                <div class="flex items-center">
                    <div class="relative z-40">
                        <!-- Outer circle for border effect -->
                        <div class="absolute -inset-0.5 bg-blue-100 rounded-full"></div>
                        <!-- Inner circle for step number -->
                        <div class="relative flex items-center justify-center w-8 h-8 bg-blue-600 rounded-full">
                            <span class="text-white text-sm">1</span>
                        </div>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-base font-semibold text-gray-900">Block Configuration</h3>
                        <p class="text-sm text-gray-500">Configure basic block settings</p>
                    </div>
                </div>
                <div class="absolute left-4 top-10 h-full w-0.5 bg-blue-100 -ml-px"></div>
            </div>

            <!-- Step 2 -->
            <div class="step-item relative pb-8">
                <div class="flex items-center">
                    <div class="relative z-40">
                        <!-- Outer circle for border effect -->
                        <div class="absolute -inset-0.5 bg-gray-200 rounded-full"></div>
                        <!-- Inner circle for step number -->
                        <div class="relative flex items-center justify-center w-8 h-8 bg-gray-100 rounded-full">
                            <span class="text-gray-500 text-sm">2</span>
                        </div>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-base font-semibold text-gray-400">Block Content</h3>
                        <p class="text-sm text-gray-400">Add and customize content</p>
                    </div>
                </div>
            </div>
        </div>
        <div class="flex items-center space-x-3">
            <a href="<?php echo BASE_URL; ?>/blocks/list<?php echo $selectedCategoryId ? '?category_id=' . $selectedCategoryId : ''; ?>" class="inline-flex items-center text-[14px] px-2.5 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 transition-all">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                <span>Back to Blocks</span>
            </a>
            <?php if (!$isNewBlock): ?>
            <button data-modal-target="deleteBlockModal" data-modal-toggle="deleteBlockModal" class="inline-flex items-center text-[14px] px-2.5 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-all">
                <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M3 6h18"/><path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"/><path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"/><line x1="10" x2="10" y1="11" y2="17"/><line x1="14" x2="14" y1="11" y2="17"/></svg>
                <span>Delete</span>
            </button>
            <?php endif; ?>
        </div>
    </div>

    <!-- Right Content Area -->
    <div class="w-[calc(100%-275px)]">
        <form method="POST" class="block-form">
            <input type="hidden" name="action" value="<?php echo $isNewBlock ? 'add' : 'edit'; ?>">
            <?php if (!$isNewBlock): ?>
            <input type="hidden" name="id" value="<?php echo htmlspecialchars($selectedBlockId); ?>">
            <?php endif; ?>
            <?php if ($selectedCategoryId): ?>
                <input type="hidden" name="category_id" value="<?php echo htmlspecialchars($selectedCategoryId); ?>">
            <?php endif; ?>
            
            <!-- Step 1 Content: Block Configuration -->
            <div id="step1Content" class="step-content">
                <!-- Header -->
                <div class="py-6 border-b">
                    <div class="flex items-center justify-between">
                        <div>
                            <h2 class="text-xl font-semibold text-gray-800">Block Configuration</h2>
                            <p class="mt-1 text-sm text-gray-500">Configure the basic settings for your block</p>
                        </div>
                        <span class="px-3 py-1 text-xs font-medium text-blue-700 bg-blue-50 rounded-full">Step 1 of 3</span>
                    </div>
                </div>

                <div class="py-6">
                    <div class="grid grid-cols-12 gap-8">
                        <!-- Left Column - Form Fields -->
                        <div class="col-span-7 space-y-6">
                            <!-- Block Name -->
                            <div class="form-group">
                                <div class="flex justify-between mb-2">
                                    <label for="blockName" class="text-sm font-medium text-gray-800">
                                        Block Name <span class="text-red-500">*</span>
                                    </label>
                                    <span class="text-xs text-gray-500">50 characters max</span>
                                </div>
                                <div class="relative rounded-lg">
                                    <input type="text" id="blockName" name="name"
                                        class="block w-full pl-4 pr-4 py-2.5 text-sm text-gray-900 bg-white border border-gray-300 rounded-lg focus:border-blue-500 focus:ring-1 focus:ring-blue-500" 
                                        placeholder="Enter block name"
                                        value="<?php echo htmlspecialchars($block['name'] ?? ''); ?>" required>
                                </div>
                            </div>
                            
                            <!-- Block Description -->
                            <div class="form-group">
                                <div class="flex justify-between mb-2">
                                    <label for="blockDescription" class="text-sm font-medium text-gray-800">
                                        Block Description
                                    </label>
                                </div>
                                <div class="relative rounded-lg">
                                    <textarea rows="2" id="blockDescription" name="description"
                                        class="block w-full pl-4 pr-4 py-2.5 text-sm text-gray-900 bg-white border border-gray-300 rounded-lg focus:border-blue-500 focus:ring-1 focus:ring-blue-500" 
                                        placeholder="Enter block description"><?php echo htmlspecialchars($block['description'] ?? ''); ?></textarea>
                                </div>
                            </div>
                            
                            <!-- Block Slug -->
                            <div class="form-group">
                                <div class="flex justify-between mb-2">
                                    <label for="blockSlug" class="text-sm font-medium text-gray-800">
                                        Block Slug <span class="text-red-500">*</span>
                                    </label>
                                    <span class="text-xs text-gray-500">Auto-generated</span>
                                </div>
                                <div class="relative rounded-lg">
                                    <input type="text" id="blockSlug" name="slug"
                                        class="block w-full pl-4 pr-4 py-2.5 text-sm text-gray-500 bg-gray-50 border border-gray-300 rounded-lg cursor-not-allowed" 
                                        value="<?php echo htmlspecialchars($block['slug'] ?? ''); ?>"
                                        readonly>
                                </div>
                            </div>

                            <!-- Block Category -->
                            <div class="form-group relative">
                                <label class="text-sm font-medium text-gray-800 mb-2 block">
                                    Block Categories <span class="text-red-500">*</span>
                                </label>
                                <!-- Category List -->
                                <div class="relative flex-1 blockCategoriesWrap">
                                    <select id="blockCategories" name="categories[]" 
                                        class="select2 w-full text-sm" required multiple="multiple">
                                        <?php foreach ($categories as $category): ?>
                                            <option value="<?php echo htmlspecialchars($category['id']); ?>" 
                                                <?php 
                                                if (isset($block['categories']) && is_array($block['categories'])) {
                                                    foreach ($block['categories'] as $blockCategory) {
                                                        if ($blockCategory['id'] == $category['id']) {
                                                            echo 'selected';
                                                            break;
                                                        }
                                                    }
                                                } else if (isset($block['category_id']) && $block['category_id'] == $category['id']) {
                                                    // For backward compatibility
                                                    echo 'selected';
                                                } else if ($selectedCategoryId == $category['id']) {
                                                    echo 'selected';
                                                }
                                                ?>
                                            >
                                                <?php echo htmlspecialchars($category['name']); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                
                                <label for="showNewCategory" class="text-xs flex items-center absolute top-0 right-0 cursor-pointer bg-[#0C5BE2]/10 text-[#0C5BE2] hover:bg-[#0C5BE2] hover:text-white font-medium rounded px-2 py-1">
                                    <input type="checkbox" id="showNewCategory" class="w-3 h-3 text-[#0C5BE2] border-gray-300 visible-hidden opacity-0 w-0 h-0 absolute">
                                    Add new category
                                </label>
                                
                                <div id="newCategoryInput" class="w-full bg-white hidden">
                                    <div class="flex space-x-2">
                                        <input type="text" id="newCategoryName" class="w-full py-2 px-3 text-sm border border-gray-300 rounded-lg focus:border-blue-500 focus:ring-1" placeholder="New category name">
                                        <button type="button" onclick="addNewBlockCategory()" class="px-3 py-2 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700">Add</button>
                                    </div>
                                </div>
                            </div>

                            <!-- Tags Section - IMPORTANT: NEW ADDITION -->
                            <div class="form-group">
                                <label class="flex items-center text-sm font-medium text-gray-700 mb-1">
                                    Tags
                                    <button type="button" class="ml-1 group relative text-gray-400 hover:text-gray-500">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="w-3.5 h-3.5" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"/><path d="M12 16v-4"/><path d="M12 8h.01"/></svg>
                                        <span class="hidden group-hover:block absolute left-full ml-2 px-2 py-1 text-xs bg-gray-800 text-white rounded z-10 whitespace-nowrap">Press Enter or comma to add tags</span>
                                    </button>
                                </label>
                                <div class="relative">
                                    <div class="flex flex-wrap p-1 bg-white border border-gray-300 rounded-lg focus-within:border-[#0C5BE2] focus-within:ring-1">
                                        <div id="tags-container" class="flex flex-wrap gap-1 w-full"></div>
                                        <input type="text" id="tagInput" class="flex-1 min-w-[100px] py-1 px-2 text-sm border-0 focus:outline-none focus:ring-0" placeholder="Add tags (Enter or comma)">
                                    </div>
                                    <!-- Suggestions dropdown -->
                                    <div id="tagSuggestions" class="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg hidden">
                                        <!-- Suggestions will be populated here -->
                                    </div>
                                </div>
                            </div>

                            <!-- Block Classes -->
                            <div class="form-group">
                                <div class="flex justify-between mb-2">
                                    <label for="blockClasses" class="text-sm font-medium text-gray-800">
                                        Block Classes
                                    </label>
                                    <span class="text-xs text-gray-500">Optional</span>
                                </div>
                                <div class="relative rounded-lg">
                                    <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                                        <svg class="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
                                        </svg>
                                    </div>
                                    <input type="text" id="blockClasses" name="classes"  
                                        class="block w-full pl-11 pr-4 py-2.5 text-sm text-gray-900 bg-white border border-gray-300 rounded-lg focus:border-blue-500 focus:ring-1 focus:ring-blue-500" 
                                        placeholder="Enter CSS classes"
                                        value="<?php echo htmlspecialchars($block['classes'] ?? ''); ?>">
                                </div>
                                <p class="mt-1.5 text-xs text-gray-500">Add custom CSS classes (space-separated)</p>
                            </div>
                        </div>

                        <!-- Right Column - Image Upload -->
                        <div class="col-span-5">
                            <div class="form-group">
                                <div class="flex justify-between mb-2">
                                    <label class="text-sm font-medium text-gray-800">
                                        Block Poster Image
                                    </label>
                                    <span class="text-xs text-gray-500">800x400px recommended</span>
                                </div>
                                <div class="w-full">
                                    <div class="relative">
                                        <div class="flex flex-col items-center justify-center w-full h-[495px] border-2 border-dashed border-gray-300 rounded-lg cursor-pointer bg-gray-50 hover:bg-gray-100/50 transition-all group">
                                            <!-- Upload Placeholder -->
                                            <div class="flex flex-col items-center justify-center p-6" id="uploadPlaceholder" <?php echo isset($block['image']) && !empty($block['image']) ? 'style="display:none;"' : ''; ?>>
                                                <div class="mb-4 rounded-full bg-blue-50 p-3 group-hover:bg-blue-100/80 transition-colors">
                                                    <svg class="w-6 h-6 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                                    </svg>
                                                </div>
                                                <p class="mb-1 text-sm text-blue-600 font-medium">Drop your image here, or 
                                                    <span class="underline cursor-pointer" id="browseButton">browse</span>
                                                </p>
                                                <p class="text-xs text-gray-500">SVG, PNG, JPG or GIF (MAX. 800x400px)</p>
                                            </div>

                                            <!-- Image Preview -->
                                            <div id="imagePreview" class="absolute inset-0 flex items-center justify-center <?php echo isset($block['image']) && !empty($block['image']) ? '' : 'hidden'; ?>">
                                                <img id="preview" class="max-w-full max-h-full object-contain rounded-lg" src="<?php echo htmlspecialchars($block['image'] ?? ''); ?>">
                                                <div class="absolute inset-0 bg-black/40 opacity-0 hover:opacity-100 transition-opacity rounded-lg">
                                                    <button type="button" id="removeImage" class="absolute top-4 right-4 p-2 bg-white/90 backdrop-blur-sm rounded-lg text-gray-600 hover:text-red-500 shadow-sm transition-colors">
                                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                                        </svg>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                        <!-- File Input -->
                                        <input type="file" id="blockPoster" name="blockPoster" class="hidden" accept="image/*" />
                                        <!-- Hidden input to store image path -->
                                        <input type="hidden" id="blockImage" name="image" value="<?php echo htmlspecialchars($block['image'] ?? ''); ?>" />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Step 2 Content: Block Content -->
            <div id="step2Content" class="step-content hidden">
                <!-- Header -->
                <div class="py-6 border-b">
                    <div class="flex items-center justify-between">
                        <div>
                            <h2 class="text-lg font-semibold text-gray-800">Block Content</h2>
                            <p class="mt-1 text-sm text-gray-500">Edit your block content and structure</p>
                        </div>
                        <span class="px-3 py-1 text-xs font-medium text-blue-700 bg-blue-50 rounded-full">Step 2 of 3</span>
                    </div>
                </div>

                <!-- Main Content Area -->
                <div class="py-6">
                    <div class="grid grid-cols-12 gap-6">
                        <!-- Left Column - Code Editor -->
                        <div class="col-span-12 leftColumnfullscreen">
                            <!-- Editor content wrapper -->
                            <div class="editor-content">
                                <!-- Tab Navigation -->
                                <div class="flex items-center gap-2 mb-4" data-tabs-group="editor-tabs">
                                    <button class="inline-flex items-center gap-1.5 px-3 py-1.5 text-sm font-medium rounded-full bg-blue-50 text-blue-600" id="template-tab" data-editor-target="#template" type="button" role="tab">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
                                        </svg>
                                        Template
                                        <span class="text-xs text-gray-400">.tmpl</span>
                                    </button>
                                
                                    <button class="inline-flex items-center gap-1.5 px-3 py-1.5 text-sm font-medium rounded-full text-gray-500 hover:bg-gray-100" id="content-tab" data-editor-target="#content" type="button" role="tab">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16m-7 6h7"/>
                                        </svg>
                                        Content
                                        <span class="text-xs text-gray-400">.json</span>
                                    </button>
                                
                                    <button class="inline-flex items-center gap-1.5 px-3 py-1.5 text-sm font-medium rounded-full text-gray-500 hover:bg-gray-100" id="css-tab" data-editor-target="#css" type="button" role="tab">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                                        </svg>
                                        CSS
                                    </button>
                                
                                    <button class="inline-flex items-center gap-1.5 px-3 py-1.5 text-sm font-medium rounded-full text-gray-500 hover:bg-gray-100" id="javascript-tab" data-editor-target="#javascript" type="button" role="tab">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 9l3 3-3 3m5 0h3M5 20h14a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                                        </svg>
                                        JavaScript
                                    </button>

                                    <button class="inline-flex items-center gap-1.5 px-3 py-1.5 text-sm font-medium rounded-full text-gray-500 hover:bg-gray-100" id="dependencies-tab" data-editor-target="#dependencies" type="button" role="tab">
                                        <svg class="w-4 h-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-cable"><path d="M17 21v-2a1 1 0 0 1-1-1v-1a2 2 0 0 1 2-2h2a2 2 0 0 1 2 2v1a1 1 0 0 1-1 1"/><path d="M19 15V6.5a1 1 0 0 0-7 0v11a1 1 0 0 1-7 0V9"/><path d="M21 21v-2h-4"/><path d="M3 5h4V3"/><path d="M7 5a1 1 0 0 1 1 1v1a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V6a1 1 0 0 1 1-1V3"/></svg>
                                        Dependencies
                                    </button>
                                    <button class="inline-flex items-center gap-1.5 px-3 py-1.5 text-sm font-medium rounded-full text-gray-500 hover:bg-gray-100" id="preview-tab" data-editor-target="#preview" type="button" role="tab">
                                        <svg class="w-4 h-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-scan-eye-icon lucide-scan-eye"><path d="M3 7V5a2 2 0 0 1 2-2h2"/><path d="M17 3h2a2 2 0 0 1 2 2v2"/><path d="M21 17v2a2 2 0 0 1-2 2h-2"/><path d="M7 21H5a2 2 0 0 1-2-2v-2"/><circle cx="12" cy="12" r="1"/><path d="M18.944 12.33a1 1 0 0 0 0-.66 7.5 7.5 0 0 0-13.888 0 1 1 0 0 0 0 .66 7.5 7.5 0 0 0 13.888 0"/></svg>
                                        Preview
                                    </button>
                                </div>

                                <!-- Editor Container -->
                                <div class="tab-content border rounded-lg bg-gray-50 editor-tab-content flex flex-col">
                                    <!-- Editor Header/Toolbar -->
                                    <div class="flex items-center rounded-t-lg justify-between p-3 border-b bg-white sticky top-0 z-50">
                                        <div class="flex items-center gap-3 mr-auto">
                                            <span class="text-sm text-gray-600 font-medium pl-2">Editor</span>
                                        </div>
                                        <div id="themeToggle" class="flex gap-1.5 pr-1.5">
                                            <span onclick="setThemeInIframe('theme1')"
                                                    class="cursor-pointer w-6 h-6 rounded-lg hover:scale-110 transition-transform border border-gray-200 bg-[linear-gradient(45deg,#473d5c_0%,#473d5c_40%,#bebb9d_33%,#bebb9d_60%,#668d5e_50%,#668d5e_100%)]">
                                            </span>
                                            <span onclick="setThemeInIframe('theme2')"
                                                    class="cursor-pointer w-6 h-6 rounded-lg hover:scale-110 transition-transform border border-gray-200 bg-[linear-gradient(45deg,#3a5a40_0%,#3a5a40_40%,#acbfc3_33%,#acbfc3_60%,#97a7b4_50%,#97a7b4_100%)]">
                                            </span>
                                            <span onclick="setThemeInIframe('theme3')"
                                                    class="cursor-pointer w-6 h-6 rounded-lg hover:scale-110 transition-transform border border-gray-200 bg-[linear-gradient(45deg,#274c77_0%,#274c77_40%,#5f95b9_33%,#5f95b9_60%,#ba8a7d_50%,#ba8a7d_100%)]">
                                            </span>
                                            <span onclick="setThemeInIframe('theme4')"
                                                    class="cursor-pointer w-6 h-6 rounded-lg hover:scale-110 transition-transform border border-gray-200 bg-[linear-gradient(45deg,#d77a60_0%,#d77a60_40%,#d9b2a1_33%,#d9b2a1_60%,#8e8943_50%,#8e8943_100%)]">
                                            </span>
                                        </div>
                                        <span class="cursor-pointer p-1.5 text-gray-400 hover:text-gray-600 rounded-md hover:bg-gray-100 hidden" id="resetPreview">
                                            <svg class="w-4 h-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-refresh-ccw-icon lucide-refresh-ccw"><path d="M21 12a9 9 0 0 0-9-9 9.75 9.75 0 0 0-6.74 2.74L3 8"/><path d="M3 3v5h5"/><path d="M3 12a9 9 0 0 0 9 9 9.75 9.75 0 0 0 6.74-2.74L21 16"/><path d="M16 16h5v5"/></svg>
                                        </span>
                                        <!-- Theme Toggle -->
                                        <span id="darkModeToggle" class="cursor-pointer p-1.5 text-gray-400 hover:text-gray-600 rounded-md hover:bg-gray-100 hidden" onclick="toggleDarkModeInIframe()">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"/>
                                            </svg>
                                        </span>
                                        <span id="desktopBtn" class="responsive-btn cursor-pointer p-1.5 text-gray-400 hover:text-gray-600 rounded-md hover:bg-gray-100 hidden" data-device="desktop" title="Desktop">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
                                            </svg>
                                        </span>
                                        <span id="tabletBtn" class="responsive-btn cursor-pointer p-1.5 text-gray-400 hover:text-gray-600 rounded-md hover:bg-gray-100 hidden" data-device="tablet" title="Tablet">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"/>
                                            </svg>
                                        </span>
                                        <span id="mobileBtn" class="responsive-btn cursor-pointer p-1.5 text-gray-400 hover:text-gray-600 rounded-md hover:bg-gray-100 hidden" data-device="mobile" title="Mobile">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M12 18h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"/>
                                            </svg>
                                        </span>
                                        <span id="editorFullscreenBtn" class="cursor-pointer p-1.5 text-gray-500 hover:text-blue-600 rounded-md hover:bg-gray-100" title="Fullscreen">
                                            <svg class="w-4 h-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-expand-icon lucide-expand"><path d="m15 15 6 6"/><path d="m15 9 6-6"/><path d="M21 16.2V21h-4.8"/><path d="M21 7.8V3h-4.8"/><path d="M3 16.2V21h4.8"/><path d="m3 21 6-6"/><path d="M3 7.8V3h4.8"/><path d="M9 9 3 3"/></svg>
                                        </span>
                                    </div>
                                
                                    <!-- Editor Content -->
                                    <div class="relative flex-1">
                                        <!-- Template Panel -->
                                        <div class="hidden" id="template" role="tabpanel">
                                            <div class="absolute top-2 right-3 px-2 py-1 text-xs text-gray-500 bg-gray-200 rounded">HTML</div>
                                            <div id="templateEditor" class="min-h-[300px] p-4"></div>
                                            <textarea id="blockTmplCode" name="tmpl_code" rows="15" class="w-full p-2 border border-gray-300 rounded-md font-mono hidden"><?php echo htmlspecialchars($block['tmpl_code'] ?? ''); ?></textarea>
                                        </div>
                                
                                        <!-- Content Panel -->
                                        <div class="hidden" id="content" role="tabpanel">
                                            <div class="absolute top-2 right-3 px-2 py-1 text-xs text-gray-500 bg-gray-200 rounded">JSON</div>
                                            <div id="contentEditor" class="min-h-[300px] p-4"></div>
                                            <textarea id="blockJsonCode" name="json_code" rows="10" class="w-full p-2 border border-gray-300 rounded-md font-mono hidden"><?php echo htmlspecialchars($block['json_code'] ?? ''); ?></textarea>
                                        </div>
                                
                                        <!-- CSS Panel -->
                                        <div class="hidden" id="css" role="tabpanel">
                                            <div class="absolute top-2 right-3 px-2 py-1 text-xs text-gray-500 bg-gray-200 rounded">CSS</div>
                                            <div id="cssEditor" class="min-h-[300px] p-4"></div>
                                            <textarea id="blockCssCode" name="css_code" rows="15" class="w-full p-2 border border-gray-300 rounded-md font-mono hidden"><?php echo htmlspecialchars($block['css_code'] ?? ''); ?></textarea>
                                        </div>
                                
                                        <!-- JavaScript Panel -->
                                        <div class="hidden" id="javascript" role="tabpanel">
                                            <div class="absolute top-2 right-3 px-2 py-1 text-xs text-gray-500 bg-gray-200 rounded">JS</div>
                                            <div id="javascriptEditor" class="min-h-[300px] p-4"></div>
                                            <textarea id="blockJsCode" name="js_code" rows="15" class="w-full p-2 border border-gray-300 rounded-md font-mono hidden"><?php echo htmlspecialchars($block['js_code'] ?? ''); ?></textarea>
                                        </div>

                                        <!-- Dependencies Panel -->
                                        <div class="hidden" id="dependencies" role="tabpanel">
                                            <div class="absolute top-2 right-3 px-2 py-1 text-xs text-gray-500 bg-gray-200 rounded">CDN</div>
                                            <div class="p-4 space-y-6">
                                                <!-- Introduction Section -->
                                                <div class="border-b pb-4">
                                                    <div class="flex items-start gap-3">
                                                        <div class="p-2 rounded-lg bg-gradient-to-br from-blue-50 to-indigo-50">
                                                            <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
                                                            </svg>
                                                        </div>
                                                        <div>
                                                            <h2 class="text-base font-medium text-gray-900 mb-1">External Dependencies</h2>
                                                            <p class="text-sm text-gray-500 leading-relaxed">
                                                                Add external JavaScript and CSS resources to enhance your block's functionality. 
                                                                <span class="inline-flex items-center gap-1 text-blue-600">
                                                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                                                    </svg>
                                                                    Use CDN URLs for better performance
                                                                </span>
                                                            </p>
                                                        </div>
                                                    </div>
                                                </div>
                                                
                                                <!-- JavaScript Dependencies -->
                                                <div class="py-4">
                                                    <div class="flex items-center justify-between">
                                                        <h3 class="text-sm font-medium text-gray-700">JavaScript Dependencies</h3>
                                                        <span id="addJsDepBtn" class="p-1.5 text-gray-500 hover:text-blue-600 rounded-md hover:bg-gray-100 cursor-pointer" title="Add JavaScript Dependency">
                                                        <svg class="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-square-plus-icon lucide-square-plus"><rect width="18" height="18" x="3" y="3" rx="2"/><path d="M8 12h8"/><path d="M12 8v8"/></svg>
                                                        </span>
                                                    </div>
                                                    <div id="jsDependencies" class="space-y-2">
                                                    <?php
                                                        $dependencies = json_decode($block['dependencies'] ?? '{}', true);
                                                        $jsDependencies = $dependencies['js'] ?? [];
                                                        foreach ($jsDependencies as $jsUrl): 
                                                    ?>
                                                        <div class="flex items-center gap-2">
                                                            <input type="text" class="flex-1 px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="Enter JavaScript CDN URL" value="<?php echo htmlspecialchars($jsUrl); ?>">
                                                            <span class="p-1.5 text-red-500 hover:text-red-600 rounded-md hover:bg-red-50 cursor-pointer" title="Remove">
                                                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                                                                </svg>
                                                            </span>
                                                        </div>
                                                    <?php endforeach; ?>
                                                    </div>
                                                </div>

                                                <!-- CSS Dependencies -->
                                                <div class="border-t pt-4">
                                                    <div class="flex items-center justify-between">
                                                        <h3 class="text-sm font-medium text-gray-700">CSS Dependencies</h3>
                                                        <span id="addCssDepBtn" class="p-1.5 text-gray-500 hover:text-blue-600 rounded-md hover:bg-gray-100 cursor-pointer" title="Add CSS Dependency">
                                                            <svg class="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-square-plus-icon lucide-square-plus"><rect width="18" height="18" x="3" y="3" rx="2"/><path d="M8 12h8"/><path d="M12 8v8"/></svg>
                                                        </span>
                                                    </div>
                                                    <div id="cssDependencies" class="space-y-2">
                                                    <?php
                                                        $cssDependencies = $dependencies['css'] ?? [];
                                                        foreach ($cssDependencies as $cssUrl): 
                                                    ?>
                                                        <div class="flex items-center gap-2">
                                                            <input type="text" class="flex-1 px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="Enter CSS CDN URL" value="<?php echo htmlspecialchars($cssUrl); ?>">
                                                            <span class="p-1.5 text-red-500 hover:text-red-600 rounded-md hover:bg-red-50 cursor-pointer" title="Remove">
                                                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                                                                </svg>
                                                            </span>
                                                        </div>
                                                    <?php endforeach; ?>
                                                    </div>
                                                </div>
                                            </div>
                                            <textarea id="blockDependencies" name="dependencies" rows="5" class="w-full p-2 border border-gray-300 rounded-md font-mono hidden"><?php echo htmlspecialchars($block['dependencies'] ?? ''); ?></textarea>
                                        </div>

                                        <div class="hidden" id="preview" role="tabpanel">
                                            <!-- Preview Content -->
                                            <div id="previewContent" class="relative">
                                                <iframe id="previewFrame" class="w-full h-full border-2 border-dashed border-gray-300 rounded-lg" frameborder="0"></iframe>
                                            </div>
                                        </div>
                                    </div>
                                
                                    <!-- Editor Footer -->
                                    <div class="hidden rounded-b-lg flex items-center justify-between px-4 py-2 border-t bg-white text-xs text-gray-500 sticky bottom-0 z-50 w-full self-end">
                                        <div class="flex items-center gap-4">
                                            <span class="flex items-center gap-1">
                                                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                                </svg>
                                                No errors
                                            </span>
                                            <span>Ln 1, Col 1</span>
                                            <span>Spaces: 2</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>                            
            </div>
            
            <!-- Navigation Buttons -->
            <div class="flex justify-end gap-4 mt-6 pt-6 border-t">
                <a href="javascript:void(0);" id="prevBtn" class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 cursor-pointer">
                    Back
                </a>
                <a href="javascript:void(0);" id="nextBtn" class="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700 cursor-pointer">
                    Continue
                </a>
                <button type="submit" id="submitBtn" class="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700 hidden cursor-pointer">
                    <?php echo $isNewBlock ? 'Create Block' : 'Update Block'; ?>
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div id="deleteBlockModal" tabindex="-1" aria-hidden="true"  class="fixed top-0 left-0 right-0 z-50 hidden w-full p-4 overflow-x-hidden overflow-y-auto md:inset-0 h-[calc(100%-1rem)] max-h-full">
    <div class="relative w-full max-w-md max-h-full">
        <div class="relative bg-white rounded-lg shadow">
            <form method="POST" class="p-6 text-center">
                <div class="p-6">
                    <input type="hidden" name="id" value="<?php echo htmlspecialchars($selectedBlockId); ?>">
                    <input type="hidden" name="action" value="delete">
                    <?php if ($selectedCategoryId): ?>
                        <input type="hidden" name="category_id" value="<?php echo htmlspecialchars($selectedCategoryId); ?>">
                    <?php endif; ?>
                    
                    <div class="text-center mb-4">
                        <svg class="mx-auto h-12 w-12 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                        </svg>
                    </div>
                    <p class="text-center text-base leading-relaxed text-gray-700 font-medium">
                        Are you sure you want to delete this block?
                    </p>
                    <p class="text-center text-sm text-gray-500 mt-1">
                        This action cannot be undone.
                    </p>
                </div>
                <!-- Modal footer -->
                <div class="flex items-center pt-2 pb-7 justify-center space-x-2 rounded-b">
                    <button type="submit" class="text-white bg-red-700 hover:bg-red-800 focus:ring-4 focus:outline-none focus:ring-red-300 font-medium rounded-lg text-sm px-5 py-2 text-center disabled:opacity-50 disabled:cursor-not-allowed">Delete</button>
                    <button type="button" class="text-gray-500 bg-white hover:bg-gray-100 focus:ring-4 focus:outline-none focus:ring-gray-200 rounded-lg border border-gray-200 text-sm font-medium px-5 py-2 hover:text-gray-900 focus:z-10" data-modal-hide="deleteBlockModal">Close</button>
                </div>
            </form>
        </div>
  </div>
</div>

<style>
    .block-detail pre {
        font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
    }
    
    /* Tag Styles */
    .tag-input-container {
        position: relative;
    }

    .tag-suggestions {
        max-height: 200px;
        overflow-y: auto;
        z-index: 1000;
    }

    .tag-item {
        transition: all 0.2s ease;
    }

    .tag-item:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .tag-remove-btn {
        transition: color 0.2s ease;
    }

    .tag-remove-btn:hover {
        color: #dc2626;
    }
</style>

<script src="<?php echo BASE_URL; ?>/assets/js/blocks-detail-new.js"></script>

<script>
// Block Tags Management Class
class BlockTagsManager {
    constructor() {
        this.selectedTags = new Set();
        this.debounceTimer = null;
        this.tagInput = document.getElementById('tagInput');
        this.tagContainer = document.getElementById('tags-container');
        this.tagSuggestions = document.getElementById('tagSuggestions');
        
        this.init();
    }
    
    init() {
        if (!this.tagInput) return;
        
        this.setupEventListeners();
        this.loadExistingTags();
    }
    
    setupEventListeners() {
        // Input event listeners
        this.tagInput.addEventListener('input', (e) => {
            clearTimeout(this.debounceTimer);
            const query = e.target.value.trim();
            
            if (query.length >= 1) {
                this.debounceTimer = setTimeout(() => {
                    this.searchTags(query);
                }, 300);
            } else {
                this.hideSuggestions();
            }
        });

        this.tagInput.addEventListener('keydown', async (e) => {
            if ((e.key === 'Enter' || e.key === ',') && e.target.value.trim()) {
                e.preventDefault();
                const tagName = e.target.value.trim().replace(/,/g, '');
                if (tagName) {
                    await this.addTag(tagName);
                }
            } else if (e.key === 'Escape') {
                this.hideSuggestions();
            }
        });

        // Close suggestions when clicking outside
        document.addEventListener('click', (e) => {
            if (!this.tagInput.contains(e.target) && !this.tagSuggestions.contains(e.target)) {
                this.hideSuggestions();
            }
        });
        
        // Form submission handler
        const blockForm = document.querySelector('.block-form');
        if (blockForm) {
            blockForm.addEventListener('submit', (e) => {
                this.ensureTagInputs();
            });
        }
    }
    
    async loadExistingTags() {
        const blockId = new URLSearchParams(window.location.search).get('block_id');
        
        if (!blockId) return;
        
        try {
            const response = await fetch(`<?php echo BASE_URL; ?>/modules/tags/block_tags.php?block_id=${blockId}`);
            const data = await response.json();
            
            if (data.success && data.data) {
                data.data.forEach(tagData => {
                    const existingTag = {
                        id: tagData.tag_id,
                        tag: tagData.tag || tagData.tag_name
                    };
                    this.selectedTags.add(existingTag.id.toString());
                    this.tagContainer.appendChild(this.createTagElement(existingTag));
                    
                    const form = document.querySelector('.block-form');
                    form.appendChild(this.createHiddenTagInput(existingTag.id));
                });
            }
        } catch (error) {
            console.error('Error loading block tags:', error);
        }
    }
    
    async searchTags(query) {
        try {
            const response = await fetch(`<?php echo BASE_URL; ?>/modules/tags/tags_search.php?search=${encodeURIComponent(query)}`);
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const data = await response.json();
            
            if (data.success && data.data && Array.isArray(data.data)) {
                const suggestions = data.data.map(tag => ({
                    id: tag.id,
                    tag: tag.tag,
                    created_at: tag.created_at,
                    updated_at: tag.updated_at
                }));
                
                this.showSuggestions(suggestions);
            }
        } catch (error) {
            console.error('Error searching tags:', error);
        }
    }
    
    async createTag(name) {
        try {
            const response = await fetch(`<?php echo BASE_URL; ?>/modules/tags/tags_create.php`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ tag: name.trim() })
            });
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const data = await response.json();
            
            if (data.success && data.data) {
                return {
                    id: data.data.id || data.data.last_insert_id,
                    tag: name.trim()
                };
            }
            
            throw new Error(data.message || 'Failed to create tag');
        } catch (error) {
            console.error('Error creating tag:', error);
            throw error;
        }
    }
    
    async addTag(tagInput) {
        let tagData;
        
        try {
            if (typeof tagInput === 'string') {
                // Create new tag
                tagData = await this.createTag(tagInput);
                if (!tagData) return;
            } else {
                // It's an existing tag object
                tagData = tagInput;
            }
            
            // Check if tag is already selected
            if (this.selectedTags.has(tagData.id.toString())) {
                return;
            }
            
            // Add to selected tags
            this.selectedTags.add(tagData.id.toString());
            
            // Create visual tag element
            this.tagContainer.appendChild(this.createTagElement(tagData));
            
            // Create hidden input for form submission
            const form = document.querySelector('.block-form');
            form.appendChild(this.createHiddenTagInput(tagData.id));
            
            // Clear input and hide suggestions
            this.tagInput.value = '';
            this.hideSuggestions();
            
        } catch (error) {
            alert('Failed to add tag: ' + error.message);
        }
    }
    
    removeTag(tagId) {
        this.selectedTags.delete(tagId);
        
        const tagElement = document.querySelector(`span[data-tag-id="${tagId}"]`);
        if (tagElement) {
            tagElement.remove();
        }
        
        const hiddenInput = document.querySelector(`input[data-tag-id="${tagId}"]`);
        if (hiddenInput) {
            hiddenInput.remove();
        }
    }
    
    createTagElement(tagData) {
        const tag = document.createElement('span');
        tag.className = 'inline-flex items-center px-2 py-1 bg-blue-100 text-blue-800 text-xs font-medium rounded';
        
        const tagText = tagData.tag || tagData.tag_name || 'Unknown Tag';
        
        tag.innerHTML = `
            ${tagText}
            <button type="button" class="ml-1 tag-remove-btn" onclick="blockTagsManager.removeTag('${tagData.id}')">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                </svg>
            </button>
        `;
        tag.dataset.tagId = tagData.id;
        return tag;
    }
    
    createHiddenTagInput(tagId) {
        const hiddenInput = document.createElement('input');
        hiddenInput.type = 'hidden';
        hiddenInput.name = 'tags[]';
        hiddenInput.value = tagId;
        hiddenInput.dataset.tagId = tagId;
        return hiddenInput;
    }
    
    showSuggestions(suggestions) {
        this.tagSuggestions.innerHTML = '';
        
        const filteredSuggestions = suggestions.filter(tag => 
            !this.selectedTags.has(tag.id.toString())
        );
        
        if (filteredSuggestions.length === 0 && this.tagInput.value.trim()) {
            const div = document.createElement('div');
            div.className = 'px-3 py-2 text-sm text-gray-500 italic cursor-pointer hover:bg-gray-50';
            div.innerHTML = `Create new tag: "${this.tagInput.value.trim()}"`;
            div.onclick = () => this.addTag(this.tagInput.value.trim());
            this.tagSuggestions.appendChild(div);
        } else {
            filteredSuggestions.forEach(tag => {
                const div = document.createElement('div');
                div.className = 'px-3 py-2 hover:bg-gray-50 cursor-pointer text-sm';
                div.textContent = tag.tag;
                div.onclick = () => this.addTag(tag);
                this.tagSuggestions.appendChild(div);
            });
            
            if (this.tagInput.value.trim() && !filteredSuggestions.some(tag => 
                tag.tag.toLowerCase() === this.tagInput.value.trim().toLowerCase()
            )) {
                const div = document.createElement('div');
                div.className = 'px-3 py-2 hover:bg-gray-50 cursor-pointer text-sm text-blue-600 border-t border-gray-200';
                div.innerHTML = `Create: "${this.tagInput.value.trim()}"`;
                div.onclick = () => this.addTag(this.tagInput.value.trim());
                this.tagSuggestions.appendChild(div);
            }
        }

        this.tagSuggestions.classList.remove('hidden');
    }
    
    hideSuggestions() {
        this.tagSuggestions.classList.add('hidden');
    }
    
    ensureTagInputs() {
        // Ensure all selected tags have hidden inputs
        this.selectedTags.forEach(tagId => {
            const existingInput = document.querySelector(`input[data-tag-id="${tagId}"]`);
            if (!existingInput) {
                const hiddenInput = this.createHiddenTagInput(tagId);
                const form = document.querySelector('.block-form');
                form.appendChild(hiddenInput);
            }
        });
        
        console.log('Submitting block with tags:', Array.from(this.selectedTags));
    }
}

// Initialize Block Tags Manager
let blockTagsManager;
document.addEventListener('DOMContentLoaded', function() {
    blockTagsManager = new BlockTagsManager();
});

// ============================================================================
// OTHER EXISTING JAVASCRIPT FUNCTIONS
// ============================================================================

// Initialize Select2 for block categories
document.addEventListener('DOMContentLoaded', function() {
    if (typeof $ !== 'undefined' && $.fn.select2) {
        $('#blockCategories').select2({
            theme: 'default',
            placeholder: 'Select categories',
            allowClear: true
        });
    } else {
        console.error('Select2 is not loaded');
    }
});

// Show/hide new block category input with enhanced functionality
document.getElementById('showNewCategory').addEventListener('change', function() {
    const newCategoryInput = document.getElementById('newCategoryInput');
    const blockCategoriesWrap = document.querySelector('.blockCategoriesWrap');
    
    if (this.checked) {
        // Show new category input and hide block categories wrap
        newCategoryInput.classList.remove('hidden');
        blockCategoriesWrap.classList.add('hidden');
    } else {
        // Hide new category input and show block categories wrap
        newCategoryInput.classList.add('hidden');
        blockCategoriesWrap.classList.remove('hidden');
    }
});

// Function to hide new category input and show block categories wrap
function hideNewBlockCategoryInput() {
    const newCategoryInput = document.getElementById('newCategoryInput');
    const blockCategoriesWrap = document.querySelector('.blockCategoriesWrap');
    const showNewCategoryCheckbox = document.getElementById('showNewCategory');
    
    newCategoryInput.classList.add('hidden');
    blockCategoriesWrap.classList.remove('hidden');
    showNewCategoryCheckbox.checked = false;
}

// Click outside handler - hide new category input when clicking outside
document.addEventListener('click', function(event) {
    const newCategoryInput = document.getElementById('newCategoryInput');
    const showNewCategoryLabel = document.querySelector('label[for="showNewCategory"]');
    const showNewCategoryCheckbox = document.getElementById('showNewCategory');
    
    // Check if click is outside the new category input area and not on the checkbox/label
    if (!newCategoryInput.contains(event.target) && 
        !showNewCategoryLabel.contains(event.target) && 
        event.target !== showNewCategoryCheckbox &&
        !newCategoryInput.classList.contains('hidden')) {
        
        hideNewBlockCategoryInput();
    }
});

// Add new block category
window.addNewBlockCategory = async function() {
    const newCategoryName = document.getElementById('newCategoryName').value.trim();
    if (!newCategoryName) {
        alert('Please enter a category name');
        return;
    }
    
    try {
        // Use direct URL to the API endpoint
        const response = await fetch('<?php echo BASE_URL; ?>/modules/block_categories/block_categories.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                name: newCategoryName
            })
        });
        
        const data = await response.json();
        console.log('API Response:', data); // Debug log
        
        // Check for the correct response structure based on your API
        if (data.status === 'success' && data.data) {
            // Get the ID from last_insert_id
            const categoryId = data.data.last_insert_id || data.data.id;
            
            if (categoryId) {
                const newCategory = {
                    id: categoryId,
                    name: newCategoryName
                };
                
                // Add to select2
                const select = $('#blockCategories');
                const newOption = new Option(newCategory.name, newCategory.id, true, true);
                select.append(newOption).trigger('change');
                
                // Clear and hide the input
                document.getElementById('newCategoryName').value = '';
                
                // Hide new category input and show block categories wrap
                hideNewBlockCategoryInput();
            } else {
                alert('Failed to create category: Category ID not found in response');
            }
        } else {
            alert('Failed to create category: ' + (data.message || 'Unknown error'));
        }
    } catch (error) {
        console.error('Error creating category:', error);
        alert('Failed to create category. Please try again.');
    }
};

document.addEventListener('DOMContentLoaded', function() {
    const previewFrame = document.getElementById('previewFrame');
    const desktopBtn = document.getElementById('desktopBtn');
    const tabletBtn = document.getElementById('tabletBtn');
    const mobileBtn = document.getElementById('mobileBtn');
    
    // Wait for iframe to load before attaching event listeners
    previewFrame.addEventListener('load', function() {
        setupResponsiveControls();
    });
    
    function setupResponsiveControls() {
        // Set active button function
        function setActiveButton(activeBtn) {
            [desktopBtn, tabletBtn, mobileBtn].forEach(btn => {
                btn.classList.remove('active');
            });
            activeBtn.classList.add('active');
        }
        
        // Desktop view
        desktopBtn.addEventListener('click', function() {
            const frameBody = previewFrame;
            frameBody.style.width = '100%';
            setActiveButton(desktopBtn);
        });
        
        // Tablet view
        tabletBtn.addEventListener('click', function() {
            const frameBody = previewFrame;
            frameBody.style.width = '768px';
            frameBody.style.margin = '0 auto';
            setActiveButton(tabletBtn);
        });
        
        // Mobile view
        mobileBtn.addEventListener('click', function() {
            const frameBody = previewFrame;
            frameBody.style.width = '375px';
            frameBody.style.margin = '0 auto';
            setActiveButton(mobileBtn);
        });
    }
});
// Function to set theme in the iframe
function setThemeInIframe(themeName) {
    const iframe = document.getElementById('previewFrame');
    if (iframe && iframe.contentDocument) {
        const iframeDocument = iframe.contentDocument;
        iframeDocument.documentElement.setAttribute('data-theme', themeName);
        
        // If the iframe has local storage, also save the theme there
        try {
            iframe.contentWindow.localStorage.setItem('theme', themeName);
        } catch (e) {
            console.log('Could not save theme to iframe localStorage:', e);
        }
    }
}

function toggleDarkModeInIframe() {
    const iframe = document.getElementById('previewFrame');
    
    if (iframe && iframe.contentDocument) {
        const iframeDocument = iframe.contentDocument;
        const isDark = iframeDocument.documentElement.classList.toggle('dark');
        
        // If the iframe has local storage, also save the preference there
        try {
            iframe.contentWindow.localStorage.setItem('darkMode', isDark);
        } catch (e) {
            console.log('Could not save dark mode to iframe localStorage:', e);
        }
    }
}

// ============================================================================
// PREVIEW UPDATE FUNCTION (EXISTING)
// ============================================================================

// Function to update preview with processed template
function updatePreview() {
    const templateCode = editors.template.getValue();
    const jsonCode = editors.content.getValue();
    const cssCode = editors.css.getValue();
    const jsCode = editors.javascript.getValue();
    const dependencies = JSON.parse(document.getElementById('blockDependencies').value || '{"js":[],"css":[]}');
    

    // Create CSS links from dependencies
    const cssLinks = dependencies.css.map(url => 
        `<link rel="stylesheet" href="${url}">`
    ).join('\n');

    // Create JS script tags from dependencies
    const jsScripts = dependencies.js.map(url => 
        `<script src="${url}"><\/script>`
    ).join('\n');
    
    // Show loading state
    const previewContent = document.getElementById('previewContent');
    const previewFrame = document.getElementById('previewFrame');
    
    // Create loading indicator
    previewContent.insertAdjacentHTML('beforeend', 
        '<div id="previewLoader" class="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center">' +
        '<div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>' +
        '</div>'
    );
    
    // Process template with LightCandy on the server
    const formData = new FormData();
    formData.append('action', 'process_template');
    formData.append('template', templateCode);
    formData.append('json_data', jsonCode);
    
    fetch(window.location.href, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        const iframeDoc = previewFrame.contentDocument || previewFrame.contentWindow.document;
        
        if (data.success) {
            const processedHtml = data.html;
            const htmlContent = `
                <!DOCTYPE html>
                <html data-theme="theme4">
                <head>
                    <meta charset="UTF-8">
                    <meta name="viewport" content="width=device-width, initial-scale=1.0">
                    <script src="https://cdn.jsdelivr.net/npm/@phosphor-icons/web"><\/script>
                    <script src="https://cdn.tailwindcss.com"><\/script>
                    <script>
                        // tailwind.config.js
                        tailwind.config = {
                            darkMode: 'class',
                            theme: {
                                extend: {
                                    colors: {
                                        primary: {
                                            50: 'var(--primary-50)',
                                            100: 'var(--primary-100)',
                                            200: 'var(--primary-200)',
                                            300: 'var(--primary-300)',
                                            400: 'var(--primary-400)',
                                            500: 'var(--primary-500)',
                                            600: 'var(--primary-600)',
                                            700: 'var(--primary-700)',
                                            800: 'var(--primary-800)',
                                            900: 'var(--primary-900)',
                                            950: 'var(--primary-950)',
                                            DEFAULT: 'var(--primary-500)',
                                            dark: 'var(--primary-500)'
                                        },
                                        secondary: {
                                            50: 'var(--secondary-50)',
                                            100: 'var(--secondary-100)',
                                            200: 'var(--secondary-200)',
                                            300: 'var(--secondary-300)',
                                            400: 'var(--secondary-400)',
                                            500: 'var(--secondary-500)',
                                            600: 'var(--secondary-600)',
                                            700: 'var(--secondary-700)',
                                            800: 'var(--secondary-800)',
                                            900: 'var(--secondary-900)',
                                            950: 'var(--secondary-950)',
                                            DEFAULT: 'var(--secondary-500)',
                                            dark: 'var(--secondary-500)'
                                        },
                                        tertiary: {
                                            50: 'var(--tertiary-50)',
                                            100: 'var(--tertiary-100)',
                                            200: 'var(--tertiary-200)',
                                            300: 'var(--tertiary-300)',
                                            400: 'var(--tertiary-400)',
                                            500: 'var(--tertiary-500)',
                                            600: 'var(--tertiary-600)',
                                            700: 'var(--tertiary-700)',
                                            800: 'var(--tertiary-800)',
                                            900: 'var(--tertiary-900)',
                                            950: 'var(--tertiary-950)',
                                            DEFAULT: 'var(--tertiary-500)',
                                            dark: 'var(--tertiary-500)'
                                        },
                                        quaternary: {
                                            50: 'var(--quaternary-50)',
                                            100: 'var(--quaternary-100)',
                                            200: 'var(--quaternary-200)',
                                            300: 'var(--quaternary-300)',
                                            400: 'var(--quaternary-400)',
                                            500: 'var(--quaternary-500)',
                                            600: 'var(--quaternary-600)',
                                            700: 'var(--quaternary-700)',
                                            800: 'var(--quaternary-800)',
                                            900: 'var(--quaternary-900)',
                                            950: 'var(--quaternary-950)',
                                            DEFAULT: 'var(--quaternary-500)',
                                            dark: 'var(--quaternary-500)'
                                        },
                                        quinary: {
                                            50: 'var(--quinary-50)',
                                            100: 'var(--quinary-100)',
                                            200: 'var(--quinary-200)',
                                            300: 'var(--quinary-300)',
                                            400: 'var(--quinary-400)',
                                            500: 'var(--quinary-500)',
                                            600: 'var(--quinary-600)',
                                            700: 'var(--quinary-700)',
                                            800: 'var(--quinary-800)',
                                            900: 'var(--quinary-900)',
                                            950: 'var(--quinary-950)',
                                            DEFAULT: 'var(--quinary-500)',
                                            dark: 'var(--quinary-500)'
                                        }
                                    }
                                }
                            }
                        }
                    <\/script>
                    ${cssLinks}
                    <style>
                        /* Reset iframe specific styles */
                        body { margin: 0; padding: 0; }
                        :root[data-theme="theme1"]{
                            --primary-50: #ECEAF1;
                            --primary-100: #D9D4E3;
                            --primary-200: #B2A9C6;
                            --primary-300: #8C7EAA;
                            --primary-400: #685987;
                            --primary-500: #473D5C;
                            --primary-600: #39314A;
                            --primary-700: #2A2537;
                            --primary-800: #1C1825;
                            --primary-900: #0E0C12;
                            --primary-950: #070609;

                            --secondary-50: #F9F9F6;
                            --secondary-100: #F3F2ED;
                            --secondary-200: #E5E3D7;
                            --secondary-300: #D8D7C5;
                            --secondary-400: #CAC8AF;
                            --secondary-500: #BEBB9D;
                            --secondary-600: #A19D72;
                            --secondary-700: #7E7A53;
                            --secondary-800: #535037;
                            --secondary-900: #2B2A1C;
                            --secondary-950: #15150E;

                            --tertiary-50: #F1F5F0;
                            --tertiary-100: #DFE9DD;
                            --tertiary-200: #BFD2BC;
                            --tertiary-300: #A3BE9D;
                            --tertiary-400: #83A77B;
                            --tertiary-500: #668D5E;
                            --tertiary-600: #52714B;
                            --tertiary-700: #3E5639;
                            --tertiary-800: #283725;
                            --tertiary-900: #141C12;
                            --tertiary-950: #0B0F0A;

                            --quaternary-50: #FFFFFF;
                            --quaternary-100: #FCFCFD;
                            --quaternary-200: #F9F9FB;
                            --quaternary-300: #F6F6F9;
                            --quaternary-400: #F4F3F7;
                            --quaternary-500: #F1F0F5;
                            --quaternary-600: #BAB6CE;
                            --quaternary-700: #847BA7;
                            --quaternary-800: #554E74;
                            --quaternary-900: #2B273A;
                            --quaternary-950: #16141F;

                            --quinary-50: #E2E0EB;
                            --quinary-100: #C9C5D8;
                            --quinary-200: #928BB1;
                            --quinary-300: #605884;
                            --quinary-400: #363149;
                            --quinary-500: #0B0A0F;
                            --quinary-600: #09080C;
                            --quinary-700: #070609;
                            --quinary-800: #040406;
                            --quinary-900: #020203;
                            --quinary-950: #000000;
                        }
                        .dark[data-theme="theme1"]  {
                            --primary-50: #F7F6F9;
                            --primary-100: #EFEDF3;
                            --primary-200: #DEDAE7;
                            --primary-300: #CEC8DA;
                            --primary-400: #BDB5CE;
                            --primary-500: #ADA3C2;
                            --primary-600: #8678A6;
                            --primary-700: #635581;
                            --primary-800: #423956;
                            --primary-900: #211C2B;
                            --primary-950: #110E15;

                            --secondary-50: #F1F0EA;
                            --secondary-100: #E3E1D4;
                            --secondary-200: #C8C6AC;
                            --secondary-300: #ACA881;
                            --secondary-400: #8D895E;
                            --secondary-500: #625F41;
                            --secondary-600: #504D35;
                            --secondary-700: #3A3927;
                            --secondary-800: #28271A;
                            --secondary-900: #12120C;
                            --secondary-950: #090906;

                            --tertiary-50: #F1F5F0;
                            --tertiary-100: #E5EDE3;
                            --tertiary-200: #CBDAC8;
                            --tertiary-300: #AEC6A9;
                            --tertiary-400: #94B48E;
                            --tertiary-500: #7AA172;
                            --tertiary-600: #5F8458;
                            --tertiary-700: #476241;
                            --tertiary-800: #31432D;
                            --tertiary-900: #182216;
                            --tertiary-950: #0B0F0A;

                            --quaternary-50: #E2E0EB;
                            --quaternary-100: #C9C5D8;
                            --quaternary-200: #928BB1;
                            --quaternary-300: #605884;
                            --quaternary-400: #363149;
                            --quaternary-500: #0B0A0F;
                            --quaternary-600: #09080C;
                            --quaternary-700: #070609;
                            --quaternary-800: #040406;
                            --quaternary-900: #020203;
                            --quaternary-950: #000000;

                            --quinary-50: #FFFFFF;
                            --quinary-100: #FCFCFD;
                            --quinary-200: #F9F9FB;
                            --quinary-300: #F6F6F9;
                            --quinary-400: #F4F3F7;
                            --quinary-500: #F1F0F5;
                            --quinary-600: #BAB6CE;
                            --quinary-700: #847BA7;
                            --quinary-800: #554E74;
                            --quinary-900: #2B273A;
                            --quinary-950: #16141F;
                        }

                        :root[data-theme="theme2"]  {
                            --primary-50: #E9F1EB;
                            --primary-100: #D4E3D6;
                            --primary-200: #A8C7AE;
                            --primary-300: #7AA982;
                            --primary-400: #56855F;
                            --primary-500: #3A5A40;
                            --primary-600: #2E4733;
                            --primary-700: #223525;
                            --primary-800: #18251A;
                            --primary-900: #0C130D;
                            --primary-950: #060907;

                            --secondary-50: #F6F8F9;
                            --secondary-100: #EDF1F2;
                            --secondary-200: #DEE6E7;
                            --secondary-300: #CDD8DB;
                            --secondary-400: #BECDD0;
                            --secondary-500: #ACBFC3;
                            --secondary-600: #839FA5;
                            --secondary-700: #5C797F;
                            --secondary-800: #3E5256;
                            --secondary-900: #1E2829;
                            --secondary-950: #0F1415;

                            --tertiary-50: #F3F5F6;
                            --tertiary-100: #EAEDF0;
                            --tertiary-200: #D6DCE1;
                            --tertiary-300: #C1CAD2;
                            --tertiary-400: #ACB9C3;
                            --tertiary-500: #97A7B4;
                            --tertiary-600: #718798;
                            --tertiary-700: #536574;
                            --tertiary-800: #38434D;
                            --tertiary-900: #1C2227;
                            --tertiary-950: #0D1012;

                            --quaternary-50: #FFFFFF;
                            --quaternary-100: #FFFFFF;
                            --quaternary-200: #FCFDFC;
                            --quaternary-300: #FCFDFC;
                            --quaternary-400: #F9FBF9;
                            --quaternary-500: #F9FBF9;
                            --quaternary-600: #BCD2BC;
                            --quaternary-700: #82AB82;
                            --quaternary-800: #507750;
                            --quaternary-900: #293D29;
                            --quaternary-950: #141F14;

                            --quinary-50: #E2E9E2;
                            --quinary-100: #C8D5C8;
                            --quinary-200: #8FA88F;
                            --quinary-300: #5E785E;
                            --quinary-400: #313F31;
                            --quinary-500: #070907;
                            --quinary-600: #040604;
                            --quinary-700: #040604;
                            --quinary-800: #020302;
                            --quinary-900: #020302;
                            --quinary-950: #000000;
                        }
                        .dark[data-theme="theme2"]  {
                            --primary-50: #F6F9F6;
                            --primary-100: #ECF3EE;
                            --primary-200: #DAE7DC;
                            --primary-300: #CADDCE;
                            --primary-400: #B8D1BC;
                            --primary-500: #A5C5AB;
                            --primary-600: #7AA982;
                            --primary-700: #56855F;
                            --primary-800: #38573E;
                            --primary-900: #1C2B1F;
                            --primary-950: #0E160F;

                            --secondary-50: #EAEFF0;
                            --secondary-100: #D6DFE1;
                            --secondary-200: #A9BDC1;
                            --secondary-300: #809DA3;
                            --secondary-400: #5A777C;
                            --secondary-500: #3C4F53;
                            --secondary-600: #2F3E41;
                            --secondary-700: #243032;
                            --secondary-800: #181F21;
                            --secondary-900: #0D1112;
                            --secondary-950: #060809;

                            --tertiary-50: #EDF0F2;
                            --tertiary-100: #D8DEE3;
                            --tertiary-200: #B2BEC7;
                            --tertiary-300: #8B9DAC;
                            --tertiary-400: #677C8E;
                            --tertiary-500: #4B5B68;
                            --tertiary-600: #3C4953;
                            --tertiary-700: #2D363E;
                            --tertiary-800: #1E2429;
                            --tertiary-900: #0F1215;
                            --tertiary-950: #090A0C;

                            --quaternary-50: #E0EBE0;
                            --quaternary-100: #C2D6C2;
                            --quaternary-200: #88AF88;
                            --quaternary-300: #547D54;
                            --quaternary-400: #2D432D;
                            --quaternary-500: #040604;
                            --quaternary-600: #040604;
                            --quaternary-700: #020302;
                            --quaternary-800: #020302;
                            --quaternary-900: #000000;
                            --quaternary-950: #000000;

                            --quinary-50: #FFFFFF;
                            --quinary-100: #FCFDFC;
                            --quinary-200: #FCFDFC;
                            --quinary-300: #F9FBF9;
                            --quinary-400: #F9FBF9;
                            --quinary-500: #F6F8F6;
                            --quinary-600: #C0CEC0;
                            --quinary-700: #87A187;
                            --quinary-800: #577057;
                            --quinary-900: #2A372A;
                            --quinary-950: #161D16;
                        }

                        :root[data-theme="theme3"]  {
                            --primary-50: #E4EDF6;
                            --primary-100: #C9DAED;
                            --primary-200: #93B5DC;
                            --primary-300: #6293CB;
                            --primary-400: #396FAD;
                            --primary-500: #274C77;
                            --primary-600: #1F3E60;
                            --primary-700: #182F49;
                            --primary-800: #0F1E2E;
                            --primary-900: #080F17;
                            --primary-950: #04070C;

                            --secondary-50: #EDF3F7;
                            --secondary-100: #DFEAF1;
                            --secondary-200: #BFD5E3;
                            --secondary-300: #9FC0D5;
                            --secondary-400: #7FAAC7;
                            --secondary-500: #5F95B9;
                            --secondary-600: #44799C;
                            --secondary-700: #335B75;
                            --secondary-800: #223C4E;
                            --secondary-900: #111E27;
                            --secondary-950: #080E12;

                            --tertiary-50: #F8F3F2;
                            --tertiary-100: #F1E7E4;
                            --tertiary-200: #E3CFCA;
                            --tertiary-300: #D6BAB2;
                            --tertiary-400: #C8A298;
                            --tertiary-500: #BA8A7D;
                            --tertiary-600: #A36757;
                            --tertiary-700: #7B4E41;
                            --tertiary-800: #50332A;
                            --tertiary-900: #281915;
                            --tertiary-950: #140D0B;

                            --quaternary-50: #FFFFFF;
                            --quaternary-100: #FFFFFF;
                            --quaternary-200: #FCFCFD;
                            --quaternary-300: #FCFCFD;
                            --quaternary-400: #F9FAFB;
                            --quaternary-500: #F9FAFB;
                            --quaternary-600: #BCC7D2;
                            --quaternary-700: #8296AB;
                            --quaternary-800: #506377;
                            --quaternary-900: #29333D;
                            --quaternary-950: #141A1F;

                            --quinary-50: #E1E8EF;
                            --quinary-100: #C0CFDD;
                            --quinary-200: #809EBC;
                            --quinary-300: #4C6E8F;
                            --quinary-400: #2A3D50;
                            --quinary-500: #090D11;
                            --quinary-600: #070A0D;
                            --quinary-700: #05080A;
                            --quinary-800: #040507;
                            --quinary-900: #020303;
                            --quinary-950: #020303;

                        }
                        .dark[data-theme="theme3"]  {
                            --primary-50: #F3F7FB;
                            --primary-100: #E8EFF7;
                            --primary-200: #D1DFF0;
                            --primary-300: #B6CDE7;
                            --primary-400: #9FBDE0;
                            --primary-500: #88ADD8;
                            --primary-600: #5288C6;
                            --primary-700: #34659D;
                            --primary-800: #23456C;
                            --primary-900: #122236;
                            --primary-950: #09111B;

                            --secondary-50: #EDF3F7;
                            --secondary-100: #D8E5EE;
                            --secondary-200: #B1CBDD;
                            --secondary-300: #8AB1CC;
                            --secondary-400: #6398BB;
                            --secondary-500: #467CA0;
                            --secondary-600: #386380;
                            --secondary-700: #2A4A60;
                            --secondary-800: #1C3140;
                            --secondary-900: #0E1920;
                            --secondary-950: #080E12;

                            --tertiary-50: #F4EDEB;
                            --tertiary-100: #EADBD7;
                            --tertiary-200: #D5B7AF;
                            --tertiary-300: #BE9084;
                            --tertiary-400: #A86C5C;
                            --tertiary-500: #825245;
                            --tertiary-600: #674137;
                            --tertiary-700: #4D3029;
                            --tertiary-800: #35221C;
                            --tertiary-900: #1B110E;
                            --tertiary-950: #0D0807;

                            --quaternary-50: #E0E6EB;
                            --quaternary-100: #C2CCD6;
                            --quaternary-200: #889CAF;
                            --quaternary-300: #54697D;
                            --quaternary-400: #2D3843;
                            --quaternary-500: #040506;
                            --quaternary-600: #040506;
                            --quaternary-700: #020303;
                            --quaternary-800: #020303;
                            --quaternary-900: #000000;
                            --quaternary-950: #000000;

                            --quinary-50: #FCFCFD;
                            --quinary-100: #FCFCFD;
                            --quinary-200: #F8FAFB;
                            --quinary-300: #F5F7FA;
                            --quinary-400: #F2F5F8;
                            --quinary-500: #EEF2F6;
                            --quinary-600: #AFC2D5;
                            --quinary-700: #7091B3;
                            --quinary-800: #43617F;
                            --quinary-900: #22303F;
                            --quinary-950: #10171E;
                        }

                        :root[data-theme="theme4"]  {
                            --primary-50: #FBF1EF;
                            --primary-100: #F7E4DE;
                            --primary-200: #EFC8BE;
                            --primary-300: #E7B0A1;
                            --primary-400: #DF9581;
                            --primary-500: #D77A60;
                            --primary-600: #C85332;
                            --primary-700: #973E26;
                            --primary-800: #622819;
                            --primary-900: #31140C;
                            --primary-950: #180A06;

                            --secondary-50: #FBF6F4;
                            --secondary-100: #F8F0ED;
                            --secondary-200: #F0E1DB;
                            --secondary-300: #E7CFC5;
                            --secondary-400: #E0C0B3;
                            --secondary-500: #D9B2A1;
                            --secondary-600: #C3856A;
                            --secondary-700: #A05D41;
                            --secondary-800: #6D402C;
                            --secondary-900: #362016;
                            --secondary-950: #190F0A;

                            --tertiary-50: #F5F4EA;
                            --tertiary-100: #EBEAD5;
                            --tertiary-200: #D8D5AC;
                            --tertiary-300: #C6C286;
                            --tertiary-400: #B2AC5C;
                            --tertiary-500: #8E8943;
                            --tertiary-600: #726E36;
                            --tertiary-700: #575429;
                            --tertiary-800: #37351A;
                            --tertiary-900: #1C1B0D;
                            --tertiary-950: #0E0D07;

                            --quaternary-50: #FFFFFF;
                            --quaternary-100: #FCFCFC;
                            --quaternary-200: #FAFAFA;
                            --quaternary-300: #FAFAFA;
                            --quaternary-400: #F7F7F7;
                            --quaternary-500: #F5F5F5;
                            --quaternary-600: #C4C4C4;
                            --quaternary-700: #949494;
                            --quaternary-800: #616161;
                            --quaternary-900: #303030;
                            --quaternary-950: #1A1A1A;

                            --quinary-50: #EFDEDC;
                            --quinary-100: #E0C1BD;
                            --quinary-200: #C1837B;
                            --quinary-300: #954F46;
                            --quinary-400: #532C27;
                            --quinary-500: #110908;
                            --quinary-600: #0E0707;
                            --quinary-700: #0A0605;
                            --quinary-800: #070403;
                            --quinary-900: #030202;
                            --quinary-950: #000000;

                        }
                        .dark[data-theme="theme4"]  {
                            --primary-50: #F9EBE7;
                            --primary-100: #F3D6CE;
                            --primary-200: #E6AD9D;
                            --primary-300: #D98168;
                            --primary-400: #CD5837;
                            --primary-500: #9F4228;
                            --primary-600: #7E3420;
                            --primary-700: #5E2718;
                            --primary-800: #411B10;
                            --primary-900: #210D08;
                            --primary-950: #100704;

                            --secondary-50: #FBF6F4;
                            --secondary-100: #F8F0ED;
                            --secondary-200: #F0E1DB;
                            --secondary-300: #E7CFC5;
                            --secondary-400: #E0C0B3;
                            --secondary-500: #D9B2A1;
                            --secondary-600: #C3856A;
                            --secondary-700: #A05D41;
                            --secondary-800: #6D402C;
                            --secondary-900: #362016;
                            --secondary-950: #190F0A;

                            --tertiary-50: #F8F8F1;
                            --tertiary-100: #F2F1E3;
                            --tertiary-200: #E5E3C8;
                            --tertiary-300: #D6D3A8;
                            --tertiary-400: #C9C58D;
                            --tertiary-500: #BCB771;
                            --tertiary-600: #A39D4D;
                            --tertiary-700: #797539;
                            --tertiary-800: #535027;
                            --tertiary-900: #2A2814;
                            --tertiary-950: #15140A;

                            --quaternary-50: #E6E6E6;
                            --quaternary-100: #CFCFCF;
                            --quaternary-200: #9E9E9E;
                            --quaternary-300: #6B6B6B;
                            --quaternary-400: #3B3B3B;
                            --quaternary-500: #0A0A0A;
                            --quaternary-600: #080808;
                            --quaternary-700: #050505;
                            --quaternary-800: #050505;
                            --quaternary-900: #030303;
                            --quaternary-950: #000000;

                            --quinary-50: #FFFFFF;
                            --quinary-100: #FDFCFC;
                            --quinary-200: #FCF8F8;
                            --quinary-300: #FAF5F5;
                            --quinary-400: #F8F2F1;
                            --quinary-500: #F7EFEE;
                            --quinary-600: #D8B1AC;
                            --quinary-700: #B9736A;
                            --quinary-800: #84463E;
                            --quinary-900: #42231F;
                            --quinary-950: #231210;

                        }
                        ${cssCode}
                    <\/style>
                </head>
                <body class="antialiased font-sans">
                    <div class="preview-content">
                        ${processedHtml}
                    </div>
                    ${jsScripts}
                    <script>${jsCode}<\/script>
                </body>
                </html>`;
            
            iframeDoc.open();
            iframeDoc.write(htmlContent);
            iframeDoc.close();
        } else {
            const errorContent = `
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset="UTF-8">
                    <style>
                        body { 
                            font-family: system-ui, -apple-system, sans-serif; 
                            padding: 1rem; 
                            margin: 0;
                        }
                        .error-box { 
                            padding: 1rem;
                            background-color: #FEF2F2;
                            color: #DC2626;
                            border: 1px solid #FEE2E2;
                            border-radius: 0.5rem;
                        }
                        .error-title { font-weight: 500; margin: 0; }
                        .error-message { font-size: 0.875rem; margin-top: 0.25rem; }
                    </style>
                </head>
                <body class="bg-white dark:bg-gray-950 font-sans">
                    <div class="error-box">
                        <p class="error-title">Error Processing Template</p>
                        <p class="error-message">${data.message}</p>
                    </div>
                </body>
                </html>`;
            
            iframeDoc.open();
            iframeDoc.write(errorContent);
            iframeDoc.close();
        }
    })
    .catch(error => {
        console.error('Error processing template:', error);
        const iframeDoc = previewFrame.contentDocument || previewFrame.contentWindow.document;
        
        const errorContent = `
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <style>
                    body { 
                        font-family: system-ui, -apple-system, sans-serif; 
                        padding: 1rem; 
                        margin: 0;
                    }
                    .error-box { 
                        padding: 1rem;
                        background-color: #FEF2F2;
                        color: #DC2626;
                        border: 1px solid #FEE2E2;
                        border-radius: 0.5rem;
                    }
                    .error-title { font-weight: 500; margin: 0; }
                    .error-message { font-size: 0.875rem; margin-top: 0.25rem; }
                </style>
            </head>
            <body>
                <div class="error-box">
                    <p class="error-title">Error</p>
                    <p class="error-message">Failed to process template. Please try again.</p>
                </div>
            </body>
            </html>`;
        
        iframeDoc.open();
        iframeDoc.write(errorContent);
        iframeDoc.close();
    })
    .finally(() => {
        // Remove loading indicator
        document.getElementById('previewLoader')?.remove();
    });
}

// Add event listeners to update preview when editors change
document.addEventListener('DOMContentLoaded', function() {
    // Add event listener to next button to update preview when moving to step 3
    const nextBtn = document.getElementById('nextBtn');
    if (nextBtn) {
        nextBtn.addEventListener('click', function() {
            if (currentStep === 2) {
                // Moving to step 3, update preview
                setTimeout(updatePreview, 100);
            }
        });
    }
    
    // Add event listener to reset preview button
    const resetPreviewBtn = document.getElementById('resetPreview');
    if (resetPreviewBtn) {
        resetPreviewBtn.addEventListener('click', updatePreview);
    }
});

</script>

<?php
$content = ob_get_clean();
require_once __DIR__ . '/../layouts/main.php';
?>
