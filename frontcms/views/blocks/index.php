<?php
require_once __DIR__ . '/../../config/config.php';
require_once __DIR__ . '/../../modules/blocks/Blocks.php';
require_once __DIR__ . '/../../includes/TableComponent.php';
require_once __DIR__ . '/../../includes/TableHelper.php';
require_once __DIR__ . '/../../includes/FileUploadHelper.php';

// Get sorting parameter from URL (in format 'sort=column' or 'sort=-column')
$sort = isset($_GET['sort']) ? $_GET['sort'] : 'id';
$direction = 'asc';

// Check if sort has a negative prefix (for descending)
if (substr($sort, 0, 1) === '-') {
    $realSort = substr($sort, 1); // Remove the minus sign
    $direction = 'desc';
} else {
    $realSort = $sort;
}

// Get limit from URL parameter
$items_per_page = isset($_GET['limit']) ? (int)$_GET['limit'] : 10;

// Validate sort parameter 
$allowedSortColumns = ['id', 'name', 'created_at', 'updated_at'];
if (!in_array($realSort, $allowedSortColumns)) {
    $realSort = 'id';
    $sort = ($direction === 'desc') ? '-id' : 'id';
}

$blocks = new Blocks();
$categoryUploader = new FileUploadHelper('blocks', 'categories');

// Handle form submission for create, update, and delete
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // Check if it's a delete operation
        if (isset($_POST['delete']) && isset($_POST['id'])) {
            $id = $_POST['id'];
            $result = $blocks->deleteBlockCategory($id);
            if ($result['success']) {
                header("Location: " . BASE_URL . "/blocks?success=Category deleted successfully");
                exit;
            } else {
                $error = $result['message'] ?? 'Failed to delete category';
            }
        }

        // Handle create/edit operations
        if (isset($_POST['save_category'])) {
            $data = [
                'name' => $_POST['name'],
                'parent_category_id' => !empty($_POST['parent_category_id']) ? $_POST['parent_category_id'] : null
            ];

            // Handle Category image upload
            if (!empty($_FILES['image']['tmp_name'])) {
                $uploadResult = $categoryUploader->handleUpload($_FILES['image'], 'block_');
                if ($uploadResult['success']) {
                    $data['image'] = $uploadResult['url'];
                    error_log('Category data being sent to API: ' . print_r($data, true));
                } else {
                    throw new Exception('Failed to upload image: ' . $uploadResult['message']);
                }
            }
            if (!empty($_POST['id'])) {
                // Edit operation
                $id = $_POST['id'];

                $result = $blocks->updateBlockCategory($id, $data);
                if ($result['success']) {
                    header("Location: " . BASE_URL . "/blocks?success=Category updated successfully");
                    exit;
                } else {
                    $error = $result['message'] ?? 'Failed to update category';
                }
            } else {
                // Create operation
                $result = $blocks->createBlockCategory($data);
                if ($result['success']) {
                    header("Location: " . BASE_URL . "/blocks?success=Category created successfully");
                    exit;
                } else {
                    $error = $result['message'] ?? 'Failed to create category';
                }
            }
        }
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// Get block categories with sorting and pagination
$result = $blocks->getBlockCategories([
    'page' => isset($_GET['page']) ? (int)$_GET['page'] : 1,
    'sort' => $realSort,
    'direction' => $direction,
    'limit' => $items_per_page,
    'per_page' => $items_per_page
]);

$categoriesData = $result['success'] ? ($result['data']['data'] ?? []) : [];

// Simple pagination configuration using the new structure
$pagination = $result['success'] ? $result['data'] : [];

// Get parent categories for dropdown
$allCategoriesResult = $blocks->getBlockCategories(['limit' => 100]);
$parentCategories = $allCategoriesResult['success'] ? ($allCategoriesResult['data']['data'] ?? []) : [];

// Set page title and breadcrumbs
$pageTitle = 'Block Categories';
$breadcrumbs = [
    [
        'label' => 'Block Categories',
        'url' => BASE_URL . '/block_categories'
    ]
];

// Start output buffering
ob_start();

// Define table columns
$columns = [
    // 'category_image' => [
    //     'label' => 'Category Image',
    //     'sortable' => true,
    //     'cellClass' => 'cell_imagewrapper',
    //     'formatter' => function($value, $row) {
    //         $id = $row['category_id'] ?? '';
    //         $image = $row['category_image'] ?? '';
    //         $imageHtml = empty($image) ? 
    //             '<div class="image-placeholder h-12 w-20"><svg viewBox="0 0 24 24" class="h-12" fill="none" stroke="currentColor" stroke-width="0.75" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-box-icon lucide-box"><path d="M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16Z"/><path d="m3.3 7 8.7 5 8.7-5"/><path d="M12 22V12"/></svg></div>' : 
    //             '<div class="image-container h-12 w-20"><img src="' . BASE_URL . '/' . htmlspecialchars($image) . '" alt="Block Image" class="w-full h-full object-cover"></div>';
    //         return '<a class="w-full" href="' . BASE_URL . '/blocks/list?category_id=' . $id . '">' . $imageHtml . '</a>';
    //     }
    // ],
    // 'category_name' => [
    //     'label' => 'Category Name',
    //     'sortable' => true,
    //     'cellClass' => 'cell_id__name',
    //     'formatter' => function($value, $row) {
    //         $id = $row['category_id'] ?? '';
    //         return '<div class="font-medium text-gray-900">' . htmlspecialchars($value) . '</div><div class="text-sm text-gray-500">Category ID: '.$id.'</div></div></div>';
    //     }
    // ],
    'category_name' => [
        'label' => 'Category Name',
        'sortable' => true,
        'formatter' => function($value, $row) {
            $id = $row['category_id'] ?? '';
            $image = $row['category_image'] ?? '';
            $imageHtml = empty($image) ? 
                '<div class="w-20 h-12 bg-gray-100 rounded flex items-center justify-center"><svg class="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path></svg></div>' : 
                '<div class="h-12 w-20 flex-shrink-0"><img src="' . BASE_URL . '/' . htmlspecialchars($image) . '" alt="Block Image" class="h-full w-full object-cover rounded"></div>';
            return '<a href="' . BASE_URL . '/blocks/list?category_id=' . $id . '"><div class="flex items-center space-x-3">' . $imageHtml . '<div><div class="font-semibold text-gray-900">' . htmlspecialchars($value) . '</div><div class="text-sm text-gray-500">Category ID: '.$id.'</div></div></div></a>';
        }
    ],
    'block_count' => [
        'label' => 'Block Count',
        'cellClass' => 'px-6 py-3 whitespace-nowrap block_count',
        'sortable' => true,
        'formatter' => function ($value) {
            return '<span class="inline-flex items-center justify-center px-2 py-1 rounded-full text-xs font-medium bg-[#0C5BE2] text-white">' . $value . '</span>';
        }
    ],
    'created_at' => [
        'label' => 'Created At',
        'cellClass' => 'px-6 py-3 whitespace-nowrap cell_created_at',
        'sortable' => true,
        'formatter' => function ($value) {
            return formatDate($value);
        }
    ],
    'updated_at' => [
        'label' => 'Updated At',
        'cellClass' => 'px-6 py-3 whitespace-nowrap cell_updated_at',
        'sortable' => true,
        'formatter' => function ($value) {
            return formatDate($value);
        }
    ]
];

// Define action buttons
$actionButtons = [
    [
        'label' => 'Create New Category',
        'icon' => 'plus',
        'attributes' => [
            'data-modal-target' => 'createCategoryModal',
            'data-modal-toggle' => 'createCategoryModal'
        ]
    ],
    [
        'label' => 'Create New Block',
        'icon' => 'plus',
        'class' => 'text-xs text-white bg-emerald-500 px-2 py-1.5 rounded hover:bg-emerald-600 flex items-center gap-1',
        'attributes' => [
            'onclick' => "window.location.href='" . BASE_URL . "/blocks/form'"
        ]
    ]
];

// Row actions
$rowActions = [
    [
        'icon' => '<path d="M21.174 6.812a1 1 0 0 0-3.986-3.987L3.842 16.174a2 2 0 0 0-.5.83l-1.321 4.352a.5.5 0 0 0 .623.622l4.353-1.32a2 2 0 0 0 .83-.497z"/><path d="m15 5 4 4"/>',
        'tooltip' => 'Edit',
        'type' => 'button',
        'hoverTextColor' => 'hover:text-blue-700',
        'attributes' => [
            'data-modal-target' => 'editCategoryModal',
            'data-modal-toggle' => 'editCategoryModal',
            'data-id' => function ($row) {
                return $row['category_id'] ?? '';
            },
            'data-name' => function ($row) {
                return $row['category_name'] ?? '';
            },
            'data-image' => function ($row) {
                return $row['image'] ?? '';
            },
            'data-parent' => function ($row) {
                return $row['parent_category_id'] ?? '';
            },
        ]
    ],
    [
        'icon' => '<path d="M3 6h18"/><path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"/><path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"/>',
        'tooltip' => 'Delete',
        'type' => 'button',
        'hoverTextColor' => 'delete-category-btn hover:text-red-700',
        'attributes' => [
            'data-modal-target' => 'deleteCategoryModal',
            'data-modal-toggle' => 'deleteCategoryModal',
            'data-id' => function ($row) {
                return $row['category_id'] ?? '';
            },
            'data-block-count' => function ($row) {
                return $row['block_count'] ?? 0;
            }
        ]
    ],
    [
        'icon' => '<path d="M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16Z"/><path d="m3.3 7 8.7 5 8.7-5"/><path d="M12 22V12"/>',
        'tooltip' => 'View Blocks',
        'urlFormatter' => function ($row) {
            $_SESSION['selected_category_id'] = $row['category_id'] ?? '';
            $_SESSION['selected_category_name'] = $row['category_name'] ?? '';
            return BASE_URL . '/blocks/list?category_id=' . ($row['category_id'] ?? '');
        }
    ]
];

$defaultView = 'grid';
$cookieKey = 'preferredView_/frontcms/blocks';
$preferredView = $_COOKIE[$cookieKey] ?? $defaultView;

// Initialize the table component with grid view enabled
$table = new TableComponent($categoriesData, $columns, $pagination);
$table->setTableId('blockCategoriesTable')
      ->setTitleBreadcrumb('Block Categories', [
        ['label' => 'Home', 'url' => BASE_URL . '/dashboard'],
        ['label' => 'Block Categories']
      ])
      ->setSearchConfig(true, 'searchCategories')
      ->setActionButtons($actionButtons)
      ->setRowActions($rowActions)
      ->setGridConfig([
        'enabled' => true,
        'columns' => 'grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-5',
        'imageKey' => 'category_image',
        'titleKey' => 'category_name', 
        'subtitleKey' => 'category_id',
        'descriptionKey' => 'description',
        'layout' => 'default',
        'cardTemplate' => function($category, $config) {
            return '
            <div class="grid-item group bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-200 overflow-hidden border border-gray-200 category-card" data-id="nav">
                <div class="flex flex-col h-full">
                    <!-- Top part with image and count -->
                    <div class="relative">
                        <!-- Category count badge -->
                        <div class="absolute top-4 left-4 z-10">
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-white text-black">
                                Blocks: ' . ($category['block_count'] ?? 0) . '
                            </span>
                        </div>
                        
                        <!-- Image container -->
                        <div class="h-[211px] rounded-t-lg overflow-hidden">
                            <img src="' . (!empty($category['category_image']) ? BASE_URL . $category['category_image'] : 'https://placehold.co/604x420/000000/f3f4f6?text=' . urlencode($category['category_name'] ?? '')) . '" 
                                 alt="' . htmlspecialchars($category['category_name'] ?? '') . '" 
                                 class="w-full h-full object-cover">
                        </div>
                    </div>
                    
                    <!-- Bottom part with title and actions -->
                    <div class="p-3 flex flex-col flex-grow">
                        <!-- Title -->
                        <h3 class="text-sm font-medium text-gray-800 mb-2 truncate">' . htmlspecialchars($category['category_name'] ?? '') . '</h3>
                        
                        <div class="text-xs text-gray-500 mb-3 flex items-center hidden">
                            <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            ' . formatDate($category['created_at'] ?? '') . '
                        </div>
                        
                        <!-- Actions -->
                        <div class="flex items-center justify-between mt-auto pt-2 border-t border-gray-100">
                            <a href="' . BASE_URL . '/blocks/list?category_id=' . ($category['category_id'] ?? '') . '" 
                               class="view-blocks-btn text-xs text-blue-600 font-medium hover:text-blue-800 hover:underline">
                                View Blocks
                            </a>
                            <div class="flex items-center space-x-1">
                                <button class="edit-category-btn p-1 text-gray-500 hover:text-blue-600 rounded hover:bg-gray-100"
                                        data-modal-target="editCategoryModal"
                                        data-modal-toggle="editCategoryModal"
                                        data-id="' . ($category['category_id'] ?? '') . '"
                                        data-name="' . htmlspecialchars($category['category_name'] ?? '') . '"
                                        data-image="' . (!empty($category['image']) ? BASE_URL . $category['image'] : '') . '">
                                    <svg class="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"></path>
                                    </svg>
                                </button>
                                <button class="delete-category-btn p-1 text-gray-500 hover:text-red-600 rounded hover:bg-gray-100"
                                        data-modal-target="deleteCategoryModal"
                                        data-modal-toggle="deleteCategoryModal"
                                        data-id="' . ($category['category_id'] ?? '') . '"
                                        data-block-count="' . ($category['block_count'] ?? 0) . '">
                                    <svg class="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>';
        }
    ])
      ->setDefaultView($preferredView);

// Render the table
echo $table->render();
?>

<!-- Create Category Modal -->
<div id="createCategoryModal" tabindex="-1" aria-hidden="true" class="fixed top-0 left-0 right-0 z-50 hidden w-full p-4 overflow-x-hidden overflow-y-auto md:inset-0 h-[calc(100%-1rem)] max-h-full">
    <div class="relative w-full max-w-2xl max-h-full">
        <!-- Modal content -->
        <div class="relative bg-white rounded-lg shadow">
            <!-- Modal header -->
            <div class="flex items-start justify-between p-4 border-b rounded-t">
                <div class="flex items-center">
                    <div class="flex items-center justify-center h-10 w-10 rounded-full bg-blue-50 text-blue-500 mr-3">
                        <svg class="h-5 w-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-boxes-icon lucide-boxes">
                            <path d="M2.97 12.92A2 2 0 0 0 2 14.63v3.24a2 2 0 0 0 .97 1.71l3 1.8a2 2 0 0 0 2.06 0L12 19v-5.5l-5-3-4.03 2.42Z" />
                            <path d="m7 16.5-4.74-2.85" />
                            <path d="m7 16.5 5-3" />
                            <path d="M7 16.5v5.17" />
                            <path d="M12 13.5V19l3.97 2.38a2 2 0 0 0 2.06 0l3-1.8a2 2 0 0 0 .97-1.71v-3.24a2 2 0 0 0-.97-1.71L17 10.5l-5 3Z" />
                            <path d="m17 16.5-5-3" />
                            <path d="m17 16.5 4.74-2.85" />
                            <path d="M17 16.5v5.17" />
                            <path d="M7.97 4.42A2 2 0 0 0 7 6.13v4.37l5 3 5-3V6.13a2 2 0 0 0-.97-1.71l-3-1.8a2 2 0 0 0-2.06 0l-3 1.8Z" />
                            <path d="M12 8 7.26 5.15" />
                            <path d="m12 8 4.74-2.85" />
                            <path d="M12 13.5V8" />
                        </svg>
                    </div>
                    <div>
                        <h3 class="text-lg font-medium text-gray-900">Create New Category</h3>
                        <p class="text-sm text-gray-500">Add a new category to organize your blocks</p>
                    </div>
                </div>
            </div>
            <!-- Modal body -->
            <form id="createCategoryForm" method="POST" action="<?php echo BASE_URL; ?>/blocks" enctype="multipart/form-data">
                <div class="p-6 pb-0">
                    <input type="hidden" name="save_category" value="1">
                    <div class="grid grid-cols-1 gap-4">
                        <div class="mb-4">
                            <label for="name" class="block text-sm font-bold text-gray-700 mb-2">Category Name</label>
                            <input type="text" name="name" required class="block w-full px-4 py-2.5 text-sm text-gray-900 bg-white border border-gray-300 rounded-lg focus:border-blue-500 focus:ring-1 focus:ring-blue-500">
                        </div>
                        <div class="mb-4 hidden">
                            <label for="parent_category_id" class="block text-sm font-bold text-gray-700 mb-2">Parent Category (Optional)</label>
                            <select name="parent_category_id" class="block w-full px-4 py-2.5 text-sm text-gray-900 bg-white border border-gray-300 rounded-lg focus:border-blue-500 focus:ring-1 focus:ring-blue-500">
                                <option value="">None</option>
                                <?php foreach ($parentCategories as $category): ?>
                                    <option value="<?php echo $category['category_id']; ?>"><?php echo htmlspecialchars($category['category_name']); ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                    <div class="mb-4">
                        <label class="block text-sm font-bold text-gray-700 mb-2">Category Image</label>
                        <div class="mt-1 flex items-center">
                            <div class="relative w-full">
                                <div id="createImagePreview" onclick="document.getElementById('createCategoryImage').click()" class="w-full h-32 border border-gray-300 border-dashed rounded-lg flex items-center justify-center overflow-hidden cursor-pointer hover:bg-gray-50 transition-colors duration-200">
                                    <div class="text-center px-6">
                                        <p class="text-sm font-medium text-gray-900 mb-1">Click to Upload Image</p>
                                        <p class="text-xs text-gray-500">PNG, JPG or GIF (max 2MB)</p>
                                        <div class="mt-4 flex justify-center">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-gray-400">
                                                <rect width="18" height="18" x="3" y="3" rx="2" ry="2" />
                                                <circle cx="9" cy="9" r="2" />
                                                <path d="m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21" />
                                            </svg>
                                        </div>
                                    </div>
                                </div>
                                <input type="file" name="image" id="createCategoryImage" class="hidden" accept="image/*">
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Modal footer -->
                <div class="flex items-center pb-7 px-6 justify-end space-x-2 rounded-b">
                    <button type="submit" class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2 text-center">Save</button>
                    <button type="button" class="text-gray-500 bg-white hover:bg-gray-100 focus:ring-4 focus:outline-none focus:ring-gray-200 rounded-lg border border-gray-200 text-sm font-medium px-5 py-2 hover:text-gray-900 focus:z-10" data-modal-hide="createCategoryModal">Cancel</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Category Modal -->
<div id="editCategoryModal" tabindex="-1" aria-hidden="true" class="fixed top-0 left-0 right-0 z-50 hidden w-full p-4 overflow-x-hidden overflow-y-auto md:inset-0 h-[calc(100%-1rem)] max-h-full">
    <div class="relative w-full max-w-2xl max-h-full">
        <!-- Modal content -->
        <div class="relative bg-white rounded-lg shadow">
            <!-- Modal header -->
            <div class="flex items-start justify-between p-4 border-b rounded-t">
                <div class="flex items-center">
                    <div class="flex items-center justify-center h-10 w-10 rounded-full bg-blue-50 text-blue-500 mr-3">
                        <svg class="h-5 w-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-boxes-icon lucide-boxes">
                            <path d="M2.97 12.92A2 2 0 0 0 2 14.63v3.24a2 2 0 0 0 .97 1.71l3 1.8a2 2 0 0 0 2.06 0L12 19v-5.5l-5-3-4.03 2.42Z" />
                            <path d="m7 16.5-4.74-2.85" />
                            <path d="m7 16.5 5-3" />
                            <path d="M7 16.5v5.17" />
                            <path d="M12 13.5V19l3.97 2.38a2 2 0 0 0 2.06 0l3-1.8a2 2 0 0 0 .97-1.71v-3.24a2 2 0 0 0-.97-1.71L17 10.5l-5 3Z" />
                            <path d="m17 16.5-5-3" />
                            <path d="m17 16.5 4.74-2.85" />
                            <path d="M17 16.5v5.17" />
                            <path d="M7.97 4.42A2 2 0 0 0 7 6.13v4.37l5 3 5-3V6.13a2 2 0 0 0-.97-1.71l-3-1.8a2 2 0 0 0-2.06 0l-3 1.8Z" />
                            <path d="M12 8 7.26 5.15" />
                            <path d="m12 8 4.74-2.85" />
                            <path d="M12 13.5V8" />
                        </svg>
                    </div>
                    <div>
                        <h3 class="text-lg font-medium text-gray-900">Edit Category</h3>
                        <p class="text-sm text-gray-500">Edit the details of the selected category.</p>
                    </div>
                </div>
            </div>
            <!-- Modal body -->
            <form id="editCategoryForm" method="POST" action="<?php echo BASE_URL; ?>/blocks" enctype="multipart/form-data">
                <div class="p-6 pb-0">
                    <input type="hidden" name="save_category" value="1">
                    <input type="hidden" name="id" id="editCategoryId">
                    <div class="grid grid-cols-1 gap-4">
                        <div class="mb-4">
                            <label for="name" class="block text-sm font-bold text-gray-700 mb-2">Category Name</label>
                            <input type="text" name="name" id="editCategoryName" required class="block w-full pr-4 py-2.5 text-sm text-gray-900 bg-white border border-gray-300 rounded-lg focus:border-blue-500 focus:ring-1 focus:ring-blue-500">
                        </div>
                        <div class="mb-4 hidden">
                            <label for="parent_category_id" class="block text-sm font-bold text-gray-700 mb-2">Parent Category (Optional)</label>
                            <select name="parent_category_id" id="editCategoryParent" class="block w-full pl-4 pr-4 py-2.5 text-sm text-gray-900 bg-white border border-gray-300 rounded-lg focus:border-blue-500 focus:ring-1 focus:ring-blue-500">
                                <option value="">None</option>
                                <?php foreach ($parentCategories as $category): ?>
                                    <option value="<?php echo $category['category_id']; ?>"><?php echo htmlspecialchars($category['category_name']); ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                    <div class="mb-4">
                        <div id="editImagePreview" onclick="document.getElementById('editCategoryImage').click()" class="w-full h-32 border border-gray-300 border-dashed rounded-lg flex items-center justify-center overflow-hidden cursor-pointer hover:bg-gray-50 transition-colors duration-200">
                            <div class="text-center px-6">
                                <p class="text-sm font-medium text-gray-900 mb-1">Click to Upload Image</p>
                                <p class="text-xs text-gray-500">PNG, JPG or GIF (max 2MB)</p>
                                <div class="mt-4 flex justify-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-gray-400">
                                        <rect width="18" height="18" x="3" y="3" rx="2" ry="2" />
                                        <circle cx="9" cy="9" r="2" />
                                        <path d="m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21" />
                                    </svg>
                                </div>
                            </div>
                        </div>
                        <input type="file" name="image" id="editCategoryImage" class="hidden" accept="image/*">
                    </div>
                </div>
                <!-- Modal footer -->
                <div class="flex items-center pb-7 px-6 justify-end space-x-2 rounded-b">
                    <button type="submit" class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2 text-center">Save</button>
                    <button type="button" class="text-gray-500 bg-white hover:bg-gray-100 focus:ring-4 focus:outline-none focus:ring-gray-200 rounded-lg border border-gray-200 text-sm font-medium px-5 py-2 hover:text-gray-900 focus:z-10" data-modal-hide="editCategoryModal">Cancel</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Category Confirmation Modal -->
<div id="deleteCategoryModal" tabindex="-1" aria-hidden="true" class="fixed top-0 left-0 right-0 z-50 hidden w-full p-4 overflow-x-hidden overflow-y-auto md:inset-0 h-[calc(100%-1rem)] max-h-full">
    <div class="relative w-full max-w-md max-h-full">
        <!-- Modal content -->
        <div class="relative bg-white rounded-lg shadow">
            <!-- Modal body -->
            <form id="deleteCategoryForm" method="POST" action="<?php echo BASE_URL; ?>/blocks">
                <div class="p-6">
                    <input type="hidden" name="delete" value="1">
                    <input type="hidden" name="id" id="deleteCategoryId">
                    <input type="hidden" name="block_count" id="deleteCategoryBlockCount">
                    <!-- Case: Category has blocks -->
                    <div id="deleteMessageWithBlocks" class="hidden">
                        <div class="text-center mb-4">
                            <svg class="mx-auto h-12 w-12 text-yellow-500" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-triangle-alert-icon lucide-triangle-alert">
                                <path d="m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3" />
                                <path d="M12 9v4" />
                                <path d="M12 17h.01" />
                            </svg>
                        </div>
                        <p class="text-center text-base leading-relaxed text-gray-700 font-medium">
                            This category has <span id="blockCountText"></span> assigned to it.
                        </p>
                        <p class="text-center text-sm text-gray-500 mt-1">
                            Please reassign or delete these blocks <br>before deleting this category.
                        </p>
                    </div>

                    <!-- Case: Category has no blocks -->
                    <div id="deleteMessageNoBlocks" class="hidden">
                        <div class="text-center mb-4">
                            <svg class="mx-auto h-12 w-12 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                            </svg>
                        </div>
                        <p class="text-center text-base leading-relaxed text-gray-700 font-medium">
                            Are you sure you want to delete this category?
                        </p>
                        <p class="text-center text-sm text-gray-500 mt-1">
                            This action cannot be undone.
                        </p>
                    </div>
                </div>
                <!-- Modal footer -->
                <div class="flex items-center pt-2 pb-7 justify-center space-x-2 rounded-b">
                    <button type="submit" id="deleteCategoryBtn" class="text-white bg-red-700 hover:bg-red-800 focus:ring-4 focus:outline-none focus:ring-red-300 font-medium rounded-lg text-sm px-5 py-2 text-center disabled:opacity-50 disabled:cursor-not-allowed" disabled>Delete</button>
                    <button type="button" class="text-gray-500 bg-white hover:bg-gray-100 focus:ring-4 focus:outline-none focus:ring-gray-200 rounded-lg border border-gray-200 text-sm font-medium px-5 py-2 hover:text-gray-900 focus:z-10" data-modal-hide="deleteCategoryModal">Close</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script src="<?php echo BASE_URL; ?>/assets/js/block-categories.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize Flowbite
        if (typeof initFlowbite === 'function') {
            initFlowbite();
        } else if (typeof Flowbite !== 'undefined') {
            new Flowbite();
        }

        // Handle edit button clicks
        document.addEventListener('click', function(e) {
            if (e.target.closest('[data-modal-target="editCategoryModal"]')) {
                const button = e.target.closest('[data-modal-target="editCategoryModal"]');
                const id = button.dataset.id;
                const name = button.dataset.name;
                const image = button.dataset.image;
                const parent = button.dataset.parent;

                // Set form values
                document.getElementById('editCategoryId').value = id;
                document.getElementById('editCategoryName').value = name;
                document.getElementById('editCategoryImage').value = image || '';

                // Set parent category dropdown value
                const parentSelect = document.getElementById('editCategoryParent');
                if (parent) {
                    const option = Array.from(parentSelect.options).find(opt => opt.value === parent);
                    if (option) {
                        option.selected = true;
                    }
                } else {
                    parentSelect.value = '';
                }
            }
        });

        // Handle delete button clicks
        document.addEventListener('click', function(e) {
            if (e.target.closest('[data-modal-target="deleteCategoryModal"]')) {
                const button = e.target.closest('[data-modal-target="deleteCategoryModal"]');
                const id = button.dataset.id;

                // Set the category ID in the delete confirmation modal
                document.getElementById('deleteCategoryId').value = id;
            }
        });

        // Initialize Lucide icons
        if (typeof lucide !== "undefined" && typeof lucide.createIcons === "function") {
            lucide.createIcons();
        }
    });
</script>

<?php
$content = ob_get_clean();
require_once __DIR__ . '/../layouts/main.php';
?>