<?php
require_once __DIR__ . '/../../config/config.php';
require_once __DIR__ . '/../../modules/blocks/Blocks.php';
require_once __DIR__ . '/../../includes/TableComponent.php';
require_once __DIR__ . '/../../includes/TableHelper.php';
require_once __DIR__ . '/../../modules/auth/Auth.php';

$auth = new Auth();
$blocks = new Blocks();

// Check if category_id is provided in URL
if (!isset($_GET['category_id']) || empty($_GET['category_id'])) {
    header('Location: ' . BASE_URL . '/blocks');
    exit;
}

$category_id = $_GET['category_id'];

// Get category details to display the name
$categoryDetails = [];
$categoryResult = $blocks->getBlockCategories();

if ($categoryResult['success']) {
    foreach ($categoryResult['data']['data'] as $cat) {
        if ($cat['category_id'] == $category_id) {
            $categoryDetails = $cat;
            // Set session variables for current category
            $_SESSION['selected_category_id'] = $cat['category_id'];
            $_SESSION['selected_category_name'] = $cat['category_name'];
            break;
        }
    }
}
$profileResult = $auth->getProfile();
$userProfile = $profileResult['success'] ? $profileResult['data'] : null;

// Get sorting parameter from URL (in format 'sort=column' or 'sort=-column')
$sort = isset($_GET['sort']) ? $_GET['sort'] : 'id';
$direction = 'asc';

// Check if sort has a negative prefix (for descending)
if (substr($sort, 0, 1) === '-') {
    $realSort = substr($sort, 1); // Remove the minus sign
    $direction = 'desc';
} else {
    $realSort = $sort;
}

// EASY PAGINATION SETTINGS - Change this number (same as index.php)
$items_per_page = isset($_GET['limit']) ? (int)$_GET['limit'] : 10; // Number of items to show per page

// Validate sort parameter 
$allowedSortColumns = ['id', 'name', 'created_at', 'updated_at'];
if (!in_array($realSort, $allowedSortColumns)) {
    $realSort = 'id';
    $sort = ($direction === 'desc') ? '-id' : 'id';
}

$blocks = new Blocks();

// Handle form submission for delete
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // Check if it's a delete operation
        if (isset($_POST['delete']) && isset($_POST['id'])) {
            $id = $_POST['id'];
            
            $result = $blocks->deleteBlockByCategory($id, $category_id);
            if ($result['success']) {
                header("Location: " . BASE_URL . "/blocks/list.php?category_id=" . $category_id . "&success=Block deleted successfully");
                exit;
            } else {
                $error = $result['message'] ?? 'Failed to delete block';
            }
        }
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// Get blocks for the selected category (same structure as index.php)
$result = $blocks->getBlocksByCategory($category_id, [
    'page' => isset($_GET['page']) ? (int)$_GET['page'] : 1,
    'sort' => $realSort,
    'direction' => $direction,
    'limit' => $items_per_page
]);

// Extract data and pagination info (FIXED - same as index.php)
$blocksData = $result['success'] ? ($result['data']['data'] ?? []) : [];

// Use the pagination data from API response (same as index.php)
$pagination = $result['success'] ? $result['data'] : [
    'current_page' => 1,
    'per_page' => $items_per_page,
    'total' => count($blocksData),
    'from' => null,
    'to' => null,
    'next_page_url' => null,
    'prev_page_url' => null,
    'first_page_url' => null,
    'last_page_url' => null,
    'has_more_pages' => false,
    'has_previous_pages' => false
];

if ($categoryResult['success']) {
    foreach ($categoryResult['data']['data'] as $cat) {
        if ($cat['category_id'] == $category_id) {
            $categoryDetails = $cat;
            break;
        }
    }
}

// Start output buffering
ob_start();

// Define table columns
$columns = [
    // 'block_image' => [
    //     'label' => 'Block Image',
    //     'cellClass' => 'cell_imagewrapper ',
    //     'sortable' => false,
    //     'formatter' => function($value, $row) {
    //         $id = $row['id'] ?? '';
    //         $image = $row['image'] ?? '';
    //         $imageHtml = empty($image) ? 
    //             '<div class="image-placeholder"><svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="0.75" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-box-icon lucide-box"><path d="M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16Z"/><path d="m3.3 7 8.7 5 8.7-5"/><path d="M12 22V12"/></svg></div>' : 
    //             '<div class="image-container"><img src="' . BASE_URL . htmlspecialchars($image) . '" alt="Block Image"></div>';
    //         return '<a class="w-full" href="' . BASE_URL . '/blocks/form?block_id=' . $id . '">' . $imageHtml . '</a>';
    //     }
    // ],
    // 'name' => [
    //     'label' => 'Block Name',
    //     'cellClass' => 'cell_id__name',
    //     'sortable' => true,
    //     'formatter' => function($value, $row) {
    //         $id = $row['id'] ?? '';
    //         return '<div class="font-medium text-gray-900">' . htmlspecialchars($value) . '</div><div class="text-sm text-gray-500">Block ID: '. $id .'</div></div></div>';
    //     }
    // ],
    'name' => [
        'label' => 'Block Name',
        'sortable' => true,
        'formatter' => function($value, $row) {
            $id = $row['id'] ?? '';
            $image = $row['image'] ?? '';
            $imageHtml = empty($image) ? 
                '<div class="w-20 h-12 bg-gray-100 rounded flex items-center justify-center"><svg class="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path></svg></div>' : 
                '<div class="h-12 w-20 flex-shrink-0"><img src="' . htmlspecialchars($image) . '" alt="Block Image" class="h-full w-full object-cover rounded"></div>';
            return '<a href="' . BASE_URL . '/blocks/form?block_id=' . $id . '"><div class="flex items-center space-x-3">' . $imageHtml . '<div><div class="font-semibold text-gray-900">' . htmlspecialchars($value) . '</div><div class="text-sm text-gray-500">Block ID: '.$id.'</div></div></div></a>';
        }
    ],
    'slug' => [
        'label' => 'Slug',
        'cellClass' => 'cell_slug',
        'sortable' => false
    ],
    'description' => [
        'label' => 'Description',
        'cellClass' => 'cell_description',
        'sortable' => false,
        'formatter' => function($value) {
            if (empty($value)) {
                return '-';
            }
            return strlen($value) > 50 ? substr(htmlspecialchars($value), 0, 50) . '...' : htmlspecialchars($value);
        }
    ],
    'created_at' => [
        'label' => 'Created At',
        'cellClass' => 'cell_created_at',
        'sortable' => true,
        'formatter' => function($value) {
            return formatDate($value);
        }
    ],
    'updated_at' => [
        'label' => 'Updated At',
        'cellClass' => 'cell_updated_at',
        'sortable' => true,
        'formatter' => function($value) {
            return formatDate($value);
        }
    ]
];

// Define action buttons
$actionButtons = [
    [
        'label' => 'Back to Categories',
        'icon' => 'arrow-left',
        'class' => 'text-xs text-white bg-[#000000] px-2 py-1.5 rounded hover:bg-black flex items-center gap-1',
        'attributes' => [
            'onclick' => "window.location.href='" . BASE_URL . "/blocks'"
        ]
    ],
    [
        'label' => 'Create New Block',
        'icon' => 'plus',
        'class' => 'text-xs text-white bg-emerald-500 px-2 py-1.5 rounded hover:bg-emerald-600 flex items-center gap-1',
        'attributes' => [
            'onclick' => "window.location.href='" . BASE_URL . "/blocks/form'"
        ]
    ]
];

// Row actions
$rowActions = [
    [
        'icon' => '<path d="M21.174 6.812a1 1 0 0 0-3.986-3.987L3.842 16.174a2 2 0 0 0-.5.83l-1.321 4.352a.5.5 0 0 0 .623.622l4.353-1.32a2 2 0 0 0 .83-.497z"/><path d="m15 5 4 4"/>',
        'tooltip' => 'Edit',
        'urlFormatter' => function($row) { 
            return BASE_URL . '/blocks/form?block_id=' . ($row['id'] ?? '');
        }
    ],
    [
        'icon' => '<path d="M3 6h18"/><path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"/><path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"/>',
        'tooltip' => 'Delete',
        'type' => 'button',
        'hoverTextColor' => 'hover:text-red-700',
        'attributes' => [
            'data-modal-target' => 'deleteBlockModal',
            'data-modal-toggle' => 'deleteBlockModal',
            'data-id' => function($row) { return $row['id'] ?? ''; }
        ]
    ]
];

$defaultView = 'grid';
$cookieKey = 'preferredView_/frontcms/blocks';
$preferredView = $_COOKIE[$cookieKey] ?? $defaultView;
// Initialize the table component with grid view enabled (PAGINATION FIXED)
$table = new TableComponent($blocksData, $columns, $pagination);
$table->setTableId('blocksTable')
      ->setTableCustomClass('gridTableWrapper')
      ->setTitleBreadcrumb(isset($_SESSION['selected_category_name']) ? $_SESSION['selected_category_name'] : ($categoryDetails['name'] ?? 'Uncategorized'), [
        ['label' => 'Home', 'url' => BASE_URL . '/dashboard'],
        [ 'label' => 'Blocks Categories', 'url' => BASE_URL . '/blocks' ],
        ['label' => 'Blocks']
      ])
      ->setSearchConfig(true, 'searchBlocks')
      ->setActionButtons($actionButtons)
      ->setRowActions($rowActions)
      ->setGridConfig([
        'enabled' => true,
        'columns' => 'grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-4 xl:grid-cols-5',
        'imageKey' => 'image',
        'titleKey' => 'name',
        'subtitleKey' => 'id',
        'descriptionKey' => 'description',
        'layout' => 'detailed',
        'cardTemplate' => function($block, $config) use ($userProfile) {
            return '
            <div class="block-card grid-item bg-white rounded-lg shadow-sm border border-gray-100 transition-all duration-200 hover:border-blue-200" data-id="' . ($block['id'] ?? '') . '" data-category="' . htmlspecialchars($block['name'] ?? '') . '">
                <div class="flex flex-col h-full">
                    <!-- Top part with image -->
                    <div class="relative p-3 pb-0">
                        <!-- Image container -->
                        <div class="h-36 rounded-lg overflow-hidden bg-gray-50">
                            <img src="' . (!empty($block['image']) ? BASE_URL . $block['image'] : 'https://placehold.co/400x200/000000/f3f4f6?text=' . urlencode($block['name'] ?? '')) . '" 
                                 alt="' . htmlspecialchars($block['name'] ?? '') . '" 
                                 class="w-full h-full object-cover">
                        </div>
                    </div>
                    
                    <!-- Bottom part with title and actions -->
                    <div class="p-3 flex flex-col flex-grow">
                        <!-- Title -->
                        <h3 class="text-sm font-medium text-gray-800 mb-1 truncate">' . htmlspecialchars($block['name'] ?? '') . '</h3>
                        
                        <!-- Description -->
                        <p class="text-xs text-gray-500 mb-3 line-clamp-2 flex-grow">' . htmlspecialchars($block['description'] ?? '') . '</p>
                        
                        <div class="flex items-center justify-between text-xs text-gray-500 mb-3">
                            <span class="flex items-center">
                                <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                ' . formatDate($block['created_at'] ?? '') . '
                            </span>
                            <div class="relative group/tooltip">
                                <img class="w-5 h-5 rounded-full ring-1 ring-gray-200" 
                                     src="https://ui-avatars.com/api/?name=' . urlencode($userProfile['name'] ?? '') . '&background=0C5BE2&color=ffffff" 
                                     alt="' . htmlspecialchars($userProfile['name'] ?? '') . '">
                                <div class="absolute bottom-full right-0 mb-1.5 opacity-0 invisible group-hover/tooltip:opacity-100 group-hover/tooltip:visible transition-all duration-200 z-10">
                                    <div class="bg-gray-900 text-white text-xs whitespace-nowrap rounded px-2 py-1">
                                        ' . htmlspecialchars($userProfile['name'] ?? '') . '
                                        <div class="absolute top-full right-1.5 -mt-0.5 border-4 border-transparent border-t-gray-900"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Actions -->
                        <div class="flex items-center justify-between mt-auto pt-2 border-t border-gray-100">
                            <a href="' . BASE_URL . '/blocks/form?block_id=' . ($block['id'] ?? '') . '" 
                               class="text-xs text-blue-600 font-medium hover:text-blue-800 hover:underline">
                                Edit Block
                            </a>
                            <div class="flex items-center space-x-1">
                                <button class="duplicate-block-btn p-1 text-gray-500 hover:text-blue-600 rounded hover:bg-gray-100 hidden" title="Duplicate">
                                    <svg class="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                    </svg>
                                </button>
                                <button class="delete-block-btn p-1 text-gray-500 hover:text-red-600 rounded hover:bg-gray-100" 
                                        title="Delete"
                                        data-modal-target="deleteBlockModal"
                                        data-modal-toggle="deleteBlockModal"
                                        data-id="' . ($block['id'] ?? '') . '">
                                    <svg class="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>';
        }
    ])
      ->setDefaultView($preferredView);

// FIXED: Add current category_id to all URLs with proper sorting
foreach ($columns as $key => &$column) {
    if (isset($column['sortable']) && $column['sortable']) {
        $column['sortUrl'] = function($direction) use ($key, $category_id) {
            $sort = $direction === 'desc' ? '-' . $key : $key;
            $queryParams = $_GET;
            $queryParams['category_id'] = $category_id;
            $queryParams['sort'] = $sort;
            $queryParams['page'] = 1; // Reset to page 1 when sorting
            return BASE_URL . '/views/blocks/list.php?' . http_build_query($queryParams);
        };
    }
}

// Render the table
echo $table->render();
?>

<!-- Delete Block Confirmation Modal -->
<div id="deleteBlockModal" tabindex="-1" aria-hidden="true" class="fixed top-0 left-0 right-0 z-50 hidden w-full p-4 overflow-x-hidden overflow-y-auto md:inset-0 h-[calc(100%-1rem)] max-h-full">
    <div class="relative w-full max-w-md max-h-full">
        <!-- Modal content -->
        <div class="relative bg-white rounded-lg shadow">
            <!-- Modal body -->
            <form id="deleteBlockForm" method="POST" action="<?php echo BASE_URL . '/views/blocks/list.php?category_id=' . $category_id; ?>">
                <div class="p-6 space-y-6">
                    <input type="hidden" name="delete" value="1">
                    <input type="hidden" name="id" id="deleteBlockId">
                    <div class="text-center mb-4">
                        <svg class="mx-auto h-12 w-12 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                        </svg>
                    </div>
                    <p class="text-center text-base leading-relaxed text-gray-700 font-medium">
                    Are you sure you want to delete this block? <br>This action cannot be undone.
                    </p>
                </div>
                <!-- Modal footer -->
                <div class="flex items-center pt-2 pb-7 justify-center space-x-2 rounded-b">
                    <button type="submit" class="text-white bg-red-700 hover:bg-red-800 focus:ring-4 focus:outline-none focus:ring-red-300 font-medium rounded-lg text-sm px-5 py-2 text-center">Delete</button>
                    <button type="button" class="text-gray-500 bg-white hover:bg-gray-100 focus:ring-4 focus:outline-none focus:ring-gray-200 rounded-lg border border-gray-200 text-sm font-medium px-5 py-2 hover:text-gray-900 focus:z-10" data-modal-hide="deleteBlockModal">Cancel</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log('Checking localStorage...');
    // Get category info from localStorage if not in URL
    if (!window.location.search.includes('category_id')) {
        const storedCategoryId = localStorage.getItem('selectedCategoryId');
        const storedCategoryName = localStorage.getItem('selectedCategoryName');
        console.log('Stored category:', { storedCategoryId, storedCategoryName });
        
        if (storedCategoryId) {
            console.log('Redirecting to stored category...');
            window.location.href = `${window.location.pathname}?category_id=${storedCategoryId}`;
        }
    } else {
        console.log('Category ID found in URL');
    }
    
    // Initialize Flowbite
    if (typeof initFlowbite === 'function') {
        initFlowbite();
    } else if (typeof Flowbite !== 'undefined') {
        new Flowbite();
    }
    
    // Handle delete button clicks
    document.addEventListener('click', function(e) {
        if (e.target.closest('[data-modal-target="deleteBlockModal"]')) {
            const button = e.target.closest('[data-modal-target="deleteBlockModal"]');
            const id = button.dataset.id;
            
            // Set the block ID in the delete confirmation modal
            document.getElementById('deleteBlockId').value = id;
        }
    });
    
    // Initialize Lucide icons
    if (typeof lucide !== "undefined" && typeof lucide.createIcons === "function") {
        lucide.createIcons();
    }
});
</script>

<?php
$content = ob_get_clean();
require_once __DIR__ . '/../layouts/main.php';
?>