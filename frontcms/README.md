# Front CMS Project

A PHP-based web CMS project that performs CRUD operations on multiple modules using an external API. The project uses modern web technologies for a robust content management experience.

## Project Structure

```
frontcms/
├── api/                    # API endpoints
│   ├── blocks/            # Block-related APIs
│   └── tags/              # Tag-related APIs
├── assets/                # Static assets
│   ├── css/              # Stylesheet files
│   └── js/               # JavaScript files
├── config/               # Configuration files
├── includes/            # Common include files
├── logs/                # Application logs
├── modules/             # Core modules
│   ├── block_categories/
│   ├── blocks/
│   ├── pages/
│   ├── pages_blocks/
│   ├── tags/
│   ├── template_categories/
│   ├── template_categories_mapping/
│   └── templates/
├── routes/              # Route definitions
├── uploads/            # File uploads
│   ├── blocks/
│   └── templates/
│       ├── logos/
│       └── screenshots/
└── views/              # View templates
    ├── auth/
    ├── blocks/
    ├── dashboard/
    ├── layouts/
    ├── pages/
    ├── tags/
    ├── template_categories/
    └── templates/
```

## Core Components

- **API Layer**: Handles all external API communications
- **Modules**: Contains business logic for different features
- **Views**: Template files for rendering UI
- **Routes**: URL routing and request handling
- **Config**: Application configuration
- **Assets**: Static resources (CSS, JS)
- **Uploads**: User-uploaded content

## Features

- Full CRUD operations for all modules
- Clean and modular code structure
- Modern UI/UX design
- File upload handling
- Template management
- Block-based content system
- Tag management
- Page management
- Authentication system

## Modules

The following modules are available:

- Block Categories
- Blocks
- Pages
- Page Blocks
- Tags
- Template Categories
- Templates

Each module supports standard CRUD operations:
- List/View
- Create
- Update
- Delete

## File Organization

- All module-specific logic is contained in `/modules`
- Views are organized by feature in `/views`
- Static assets are properly separated in `/assets`
- Uploads are managed in `/uploads` with proper categorization
- Configuration is centralized in `/config`

## Development

To maintain the project structure:

1. Keep module-specific code in respective module directories
2. Place all views in the appropriate `/views` subdirectory
3. Store static assets in `/assets`
4. Use proper routing in `/routes`
5. Keep configurations in `/config`
