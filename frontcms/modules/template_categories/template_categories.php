<?php
require_once __DIR__ . '/../../config/config.php';
require_once __DIR__ . '/TemplateCategories.php';

header('Content-Type: application/json');

$templateCategories = new TemplateCategories();

try {
    // Handle POST request to create new category
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $data = json_decode(file_get_contents('php://input'), true);
        
        if (!isset($data['name']) || empty(trim($data['name']))) {
            throw new Exception('Category name is required');
        }
        
        $categoryName = trim($data['name']);
        
        // Check if category with same name already exists (case-insensitive)
        $existingCategories = $templateCategories->getList();
        
        if ($existingCategories['success'] && isset($existingCategories['data']['data'])) {
            foreach ($existingCategories['data']['data'] as $category) {
                if (strtolower(trim($category['name'])) === strtolower($categoryName)) {
                    throw new Exception('A category with this name already exists: ' . $category['name']);
                }
            }
        }
        
        // Create the category if no duplicate found
        $response = $templateCategories->create(['name' => $categoryName]);
        
        if (isset($response['data'])) {
            echo json_encode([
                'status' => 'success',
                'message' => 'Category created successfully',
                'data' => $response['data']
            ]);
        } else {
            throw new Exception('Failed to create category');
        }
    } else {
        throw new Exception('Invalid request method');
    }
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'status' => 'error',
        'message' => $e->getMessage()
    ]);
}