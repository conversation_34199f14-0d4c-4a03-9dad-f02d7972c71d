<?php
require_once __DIR__ . '/../../api/ApiClient.php';
require_once __DIR__ . '/../../config/config.php';

class TemplateCategories {
    private $apiClient;
    private $endpoint = 'template_categories';

    public function __construct() {
        $this->apiClient = new ApiClient(API_BASE_URL_V1);
    }

    /**
     * Get list of template categories
     */
    public function getList($params = []) {
        try {
            $response = $this->apiClient->get($this->endpoint, $params);
            return [
                'success' => true,
                'data' => $response['data'] ?? []
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage(),
                'data' => []
            ];
        }
    }

    /**
     * Get a single template category by ID
     */
    public function get($id) {
        return $this->apiClient->get($this->endpoint . '/' . $id);
    }

    /**
     * Create a new template category
     */
    public function create($data) {
        return $this->apiClient->post($this->endpoint, $data);
    }

    /**
     * Update an existing template category
     */
    public function update($id, $data) {
        return $this->apiClient->put($this->endpoint . '/' . $id, $data);
    }

    /**
     * Delete a template category
     */
    public function delete($id) {
        return $this->apiClient->delete($this->endpoint . '/' . $id);
    }
}
