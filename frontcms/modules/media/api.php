<?php
// modules/media/api.php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

require_once __DIR__ . '/Media.php';

$media = new Media();
$method = $_SERVER['REQUEST_METHOD'];
$action = $_GET['action'] ?? '';

try {
    switch ($method) {
        case 'GET':
            if ($action === 'list') {
                $folder = $_GET['folder'] ?? '';
                $result = $media->getMediaList($folder);
            } elseif ($action === 'url') {
                $path = $_GET['path'] ?? '';
                if (empty($path)) {
                    throw new Exception('Path is required');
                }
                $result = $media->getFileUrl($path);
            } else {
                $folder = $_GET['folder'] ?? '';
                $result = $media->getMediaList($folder);
            }
            break;
            
        case 'POST':
            if ($action === 'create-folder') {
                $input = json_decode(file_get_contents('php://input'), true);
                $folderName = $input['name'] ?? '';
                $parentFolder = $input['parent'] ?? '';
                
                if (empty($folderName)) {
                    throw new Exception('Folder name is required');
                }
                
                $result = $media->createFolder($folderName, $parentFolder);
            } elseif ($action === 'upload') {
                if (!isset($_FILES['file'])) {
                    throw new Exception('No file uploaded');
                }
                
                $folder = $_POST['folder'] ?? '';
                $result = $media->uploadFile($_FILES['file'], $folder);
            } else {
                throw new Exception('Invalid action');
            }
            break;
            
        case 'DELETE':
            $input = json_decode(file_get_contents('php://input'), true);
            $path = $input['path'] ?? '';
            $type = $input['type'] ?? 'file';
            
            if (empty($path)) {
                throw new Exception('Path is required');
            }
            
            $result = $media->deleteItem($path, $type);
            break;
            
        default:
            throw new Exception('Method not allowed');
    }
    
    echo json_encode($result);
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>