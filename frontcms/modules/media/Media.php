<?php
// modules/media/Media.php
require_once __DIR__ . '/../../config/config.php';

class Media {
    private $uploadBaseDir;
    private $allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/svg+xml', 'image/webp'];
    private $maxFileSize = 10485760; // 10MB
    
    public function __construct() {
        // Set upload directory to root/uploads/media
        $this->uploadBaseDir = __DIR__ . '/../../uploads/media';
        if (!file_exists($this->uploadBaseDir)) {
            mkdir($this->uploadBaseDir, 0777, true);
            // Try to set permissions, but don't fail if it doesn't work
            @chmod($this->uploadBaseDir, 0777);
        }
    }
    
    /**
     * Get media files and folders list
     */
    public function getMediaList($folder = '') {
        $basePath = $this->uploadBaseDir;
        if (!empty($folder)) {
            $basePath .= '/' . $folder;
        }
        
        if (!file_exists($basePath)) {
            return [
                'success' => true,
                'data' => [
                    'folders' => [],
                    'files' => [],
                    'current_path' => $folder
                ]
            ];
        }
        
        $folders = [];
        $files = [];
        
        $items = scandir($basePath);
        foreach ($items as $item) {
            if ($item === '.' || $item === '..') continue;
            
            $fullPath = $basePath . '/' . $item;
            $relativePath = $folder ? $folder . '/' . $item : $item;
            
            if (is_dir($fullPath)) {
                $folders[] = [
                    'name' => $item,
                    'path' => $relativePath,
                    'created' => date('Y-m-d H:i:s', filemtime($fullPath)),
                    'file_count' => $this->countFiles($fullPath)
                ];
            } else {
                $fileInfo = $this->getFileInfo($fullPath, $relativePath);
                if ($fileInfo) {
                    $files[] = $fileInfo;
                }
            }
        }
        
        // Sort folders and files alphabetically
        usort($folders, function($a, $b) {
            return strcasecmp($a['name'], $b['name']);
        });
        
        usort($files, function($a, $b) {
            return strcasecmp($a['name'], $b['name']);
        });
        
        return [
            'success' => true,
            'data' => [
                'folders' => $folders,
                'files' => $files,
                'current_path' => $folder,
                'total_folders' => count($folders),
                'total_files' => count($files)
            ]
        ];
    }
    
    /**
     * Create a new folder
     */
    public function createFolder($folderName, $parentFolder = '') {
        // Sanitize folder name
        $folderName = $this->sanitizeFolderName($folderName);
        
        if (empty($folderName)) {
            throw new Exception('Invalid folder name');
        }
        
        $folderPath = $this->uploadBaseDir;
        if (!empty($parentFolder)) {
            $folderPath .= '/' . $parentFolder;
        }
        $folderPath .= '/' . $folderName;
        
        if (file_exists($folderPath)) {
            throw new Exception('Folder already exists');
        }
        
        if (!mkdir($folderPath, 0777, true)) {
            throw new Exception('Failed to create folder');
        }
        
        // Try to set permissions, but don't fail if it doesn't work
        @chmod($folderPath, 0777);
        
        return [
            'success' => true,
            'message' => 'Folder created successfully',
            'data' => [
                'name' => $folderName,
                'path' => $parentFolder ? $parentFolder . '/' . $folderName : $folderName
            ]
        ];
    }
    
    /**
     * Upload file to specific folder
     */
    public function uploadFile($file, $folder = '') {
        $this->validateUpload($file);
        
        $uploadDir = $this->uploadBaseDir;
        if (!empty($folder)) {
            $uploadDir .= '/' . $folder;
            if (!file_exists($uploadDir)) {
                mkdir($uploadDir, 0777, true);
                chmod($uploadDir, 0777);
            }
        }
        
        $uniqueName = $this->generateUniqueFilename($file['name']);
        $targetPath = $uploadDir . '/' . $uniqueName;
        
        if (!move_uploaded_file($file['tmp_name'], $targetPath)) {
            throw new Exception('Failed to upload file');
        }
        
        // Try to set permissions, but don't fail if it doesn't work
        @chmod($targetPath, 0777);
        
        // Generate URL
        $url = BASE_URL . '/uploads/media';
        if (!empty($folder)) {
            $url .= '/' . $folder;
        }
        $url .= '/' . $uniqueName;
        
        return [
            'success' => true,
            'message' => 'File uploaded successfully',
            'data' => [
                'name' => $uniqueName,
                'original_name' => $file['name'],
                'url' => $url,
                'folder' => $folder,
                'size' => $file['size'],
                'type' => $file['type'],
                'path' => $folder ? $folder . '/' . $uniqueName : $uniqueName
            ]
        ];
    }
    
    /**
     * Delete folder or file
     */
    public function deleteItem($path, $type = 'file') {
        $fullPath = $this->uploadBaseDir . '/' . $path;
        
        if (!file_exists($fullPath)) {
            throw new Exception('Item not found');
        }
        
        if ($type === 'folder') {
            if (!$this->deleteDirectory($fullPath)) {
                throw new Exception('Failed to delete folder');
            }
        } else {
            if (!unlink($fullPath)) {
                throw new Exception('Failed to delete file');
            }
        }
        
        return [
            'success' => true,
            'message' => ucfirst($type) . ' deleted successfully'
        ];
    }
    
    /**
     * Get file URL for copying
     */
    public function getFileUrl($path) {
        $url = BASE_URL . '/uploads/media/' . $path;
        $fullUrl = 'http' . (isset($_SERVER['HTTPS']) ? 's' : '') . '://' . $_SERVER['HTTP_HOST'] . $url;
        
        return [
            'success' => true,
            'data' => [
                'url' => $url,
                'full_url' => $fullUrl
            ]
        ];
    }
    
    // Helper methods
    private function sanitizeFolderName($name) {
        $name = preg_replace('/[^a-zA-Z0-9\-_\s]/', '', $name);
        $name = preg_replace('/\s+/', '-', $name);
        return trim($name, '-');
    }
    
    private function validateUpload($file) {
        if ($file['error'] !== UPLOAD_ERR_OK) {
            throw new Exception('Upload error: ' . $this->getUploadErrorMessage($file['error']));
        }
        
        if ($file['size'] > $this->maxFileSize) {
            throw new Exception('File size exceeds limit of ' . ($this->maxFileSize / 1024 / 1024) . 'MB');
        }
        
        if (!in_array($file['type'], $this->allowedTypes)) {
            throw new Exception('Invalid file type. Allowed: jpg, png, gif, svg, webp');
        }
    }
    
    private function generateUniqueFilename($originalName) {
        $extension = pathinfo($originalName, PATHINFO_EXTENSION);
        $filename = pathinfo($originalName, PATHINFO_FILENAME);
        $filename = preg_replace('/[^a-zA-Z0-9\-_]/', '', $filename);
        $filename = substr($filename, 0, 50); // Limit filename length
        return $filename . '_' . uniqid() . '.' . strtolower($extension);
    }
    
    private function getFileInfo($fullPath, $relativePath) {
        $pathInfo = pathinfo($fullPath);
        $extension = strtolower($pathInfo['extension'] ?? '');
        
        // Check if it's a valid image
        $validExtensions = ['jpg', 'jpeg', 'png', 'gif', 'svg', 'webp'];
        if (!in_array($extension, $validExtensions)) {
            return null;
        }
        
        $url = BASE_URL . '/uploads/media/' . $relativePath;
        
        return [
            'name' => basename($fullPath),
            'path' => $relativePath,
            'url' => $url,
            'size' => filesize($fullPath),
            'type' => mime_content_type($fullPath),
            'extension' => $extension,
            'created' => date('Y-m-d H:i:s', filemtime($fullPath)),
            'dimensions' => $this->getImageDimensions($fullPath)
        ];
    }
    
    private function getImageDimensions($path) {
        try {
            $imageSize = getimagesize($path);
            return [
                'width' => $imageSize[0] ?? 0,
                'height' => $imageSize[1] ?? 0
            ];
        } catch (Exception $e) {
            return ['width' => 0, 'height' => 0];
        }
    }
    
    private function countFiles($dir) {
        if (!is_dir($dir)) return 0;
        
        $count = 0;
        $items = scandir($dir);
        foreach ($items as $item) {
            if ($item === '.' || $item === '..') continue;
            
            $fullPath = $dir . '/' . $item;
            if (is_file($fullPath)) {
                $count++;
            } elseif (is_dir($fullPath)) {
                $count += $this->countFiles($fullPath);
            }
        }
        
        return $count;
    }
    
    private function deleteDirectory($dir) {
        if (!is_dir($dir)) {
            return unlink($dir);
        }
        
        $files = array_diff(scandir($dir), ['.', '..']);
        foreach ($files as $file) {
            $this->deleteDirectory($dir . '/' . $file);
        }
        
        return rmdir($dir);
    }
    
    private function getUploadErrorMessage($code) {
        switch ($code) {
            case UPLOAD_ERR_INI_SIZE:
                return 'File exceeds upload_max_filesize';
            case UPLOAD_ERR_FORM_SIZE:
                return 'File exceeds MAX_FILE_SIZE';
            case UPLOAD_ERR_PARTIAL:
                return 'File was only partially uploaded';
            case UPLOAD_ERR_NO_FILE:
                return 'No file was uploaded';
            case UPLOAD_ERR_NO_TMP_DIR:
                return 'Missing temporary folder';
            case UPLOAD_ERR_CANT_WRITE:
                return 'Failed to write file to disk';
            case UPLOAD_ERR_EXTENSION:
                return 'PHP extension stopped upload';
            default:
                return 'Unknown upload error';
        }
    }
}
?>