<?php
require_once __DIR__ . '/../../api/ApiClient.php';

class BlockCategoriesMapping {
    private $apiClient;
    private $endpoint = 'block_categories_mapping';
    
    public function __construct() {
        // Use v1 API for mapping
        $this->apiClient = new ApiClient(API_BASE_URL_V1);
    }

    /**
     * Create a new block category mapping
     */
    public function create($data) {
        return $this->apiClient->post($this->endpoint, $data);
    }

    /**
     * Delete all mappings for a block
     * 
     * Note: This requires custom implementation since there's no direct endpoint
     */
    public function deleteByBlockId($blockId) {
        // First get all mappings for this block
        $mappings = $this->getByBlockId($blockId);
        
        if (!isset($mappings['data']['data']) || !is_array($mappings['data']['data'])) {
            return ['success' => true, 'message' => 'No mappings found to delete'];
        }
        
        // Delete each mapping individually
        $results = [];
        foreach ($mappings['data']['data'] as $mapping) {
            if (isset($mapping['id'])) {
                $result = $this->apiClient->delete($this->endpoint . '/' . $mapping['id']);
                $results[] = $result;
            }
        }
        
        return ['success' => true, 'message' => 'Mappings deleted', 'results' => $results];
    }
    
    /**
     * Get all category mappings for a block
     */
    public function getByBlockId($blockId) {
        // Use filter to get mappings for a specific block
        return $this->apiClient->get($this->endpoint, ['filter[block_id]' => $blockId]);
    }
}
