<?php
require_once __DIR__ . '/../../api/ApiClient.php';

class Tags
{
    private $apiClient;
    private $endpoint = 'tags';

    public function __construct()
    {
        $this->apiClient = new ApiClient(API_BASE_URL_V1);
    }

    public function getList($params = [])
    {
        try {
            $defaultParams = [
                'page' => 1,
                'sort' => 'id',
                'direction' => 'desc',
                'limit' => 10
            ];

            $params = array_merge($defaultParams, $params);
            $queryParams = $this->buildQueryParams($params);

            $response = $this->apiClient->get($this->endpoint, $queryParams);

            // Ensure we have a consistent data structure
            return [
                'success' => true,
                'data' => [
                    'data' => $response['data']['data'] ?? [],
                    'current_page' => $response['data']['current_page'] ?? 1,
                    'per_page' => $response['data']['per_page'] ?? $params['limit'],
                    'total' => $response['data']['total'] ?? 0,
                    'last_page' => $response['data']['last_page'] ?? 1,
                    'from' => $response['data']['from'] ?? null,
                    'to' => $response['data']['to'] ?? null,
                    'next_page_url' => $response['data']['next_page_url'] ?? null,
                    'prev_page_url' => $response['data']['prev_page_url'] ?? null
                ]
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage(),
                'data' => [
                    'data' => [],
                    'current_page' => 1,
                    'per_page' => $params['limit'],
                    'total' => 0,
                    'last_page' => 1
                ]
            ];
        }
    }

    public function create($data)
    {
        try {
            $response = $this->apiClient->post($this->endpoint, $data);
            if ($response['status'] == 'success') {
                return [
                    'success' => true,
                    'data' => $this->getList()['data'] ?? null,
                    'message' => 'Tag created successfully'
                ];
            }
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    public function update($id, $data)
    {
        try {
            $response = $this->apiClient->put($this->endpoint . '/' . $id, $data);
            if ($response['status'] == 'success') {
                return [
                    'success' => true,
                    'data' => $this->getList()['data'] ?? null,
                    'message' => 'Tag updated successfully'
                ];
            }
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    public function delete($id)
    {
        try {
            $response = $this->apiClient->delete($this->endpoint . '/' . $id);
            if ($response['status'] == 'success') {
                return [
                    'success' => true,
                    'data' => $this->getList()['data'] ?? null,
                    'message' => 'Tag deleted successfully'
                ];
            }
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    private function buildQueryParams($params)
    {
        $queryParams = [];

        if (!empty($params['page'])) {
            $queryParams['page'] = (int)$params['page'];
        }

        if (!empty($params['limit'])) {
            $queryParams['limit'] = (int)$params['limit'];
        }

        if (!empty($params['sort'])) {
            $sortColumn = $params['sort'];
            if ($params['direction'] === 'desc') {
                $sortColumn = '-' . $sortColumn;
            }
            $queryParams['sort'] = $sortColumn;
        }

        return $queryParams;
    }
}
