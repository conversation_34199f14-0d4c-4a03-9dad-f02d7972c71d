<?php
require_once __DIR__ . '/../common_tags/CommonTags.php';
require_once __DIR__ . '/Tags.php';
require_once __DIR__ . '/../../config/config.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

try {
    $blockId = $_GET['block_id'] ?? '';
    
    if (empty($blockId)) {
        echo json_encode([
            'success' => false,
            'message' => 'Block ID is required'
        ]);
        exit;
    }
    
    $commonTags = new CommonTags();
    $tags = new Tags();
    
    // Get all tag associations for this block
    $result = $commonTags->getByModel('App\\Models\\Block', $blockId);
    
    if ($result['success']) {
        $tagAssociations = $result['data']['data'] ?? [];
        $enrichedTags = [];
        $processedTagIds = []; // To avoid duplicates
        
        // Get all tags first
        $allTagsResult = $tags->getList(['limit' => 1000]);
        $allTags = [];
        
        if ($allTagsResult['success']) {
            $allTags = $allTagsResult['data']['data'] ?? [];
        }
        
        // Enrich with tag details
        foreach ($tagAssociations as $association) {
            $tagId = $association['tag_id'];
            
            // Skip if already processed (to avoid duplicates)
            if (in_array($tagId, $processedTagIds)) {
                continue;
            }
            
            // Find tag in all tags
            foreach ($allTags as $tagData) {
                if ($tagData['id'] == $tagId) {
                    $enrichedTags[] = [
                        'id' => $association['id'],
                        'tag_id' => $tagId,
                        'tag_name' => $tagData['tag'],
                        'tag' => $tagData['tag'], // For compatibility
                        'model_id' => $association['model_id'],
                        'model_type' => $association['model_type']
                    ];
                    $processedTagIds[] = $tagId;
                    break;
                }
            }
        }
        
        echo json_encode([
            'success' => true,
            'data' => $enrichedTags
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => $result['message'] ?? 'Failed to fetch block tags',
            'data' => []
        ]);
    }
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage(),
        'data' => []
    ]);
}
?>