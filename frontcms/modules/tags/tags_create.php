<?php
require_once __DIR__ . '/Tags.php';
require_once __DIR__ . '/../../config/config.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode([
        'success' => false,
        'message' => 'Method not allowed'
    ]);
    exit;
}

try {
    // Handle both JSON and form data
    $input = null;
    $contentType = $_SERVER['CONTENT_TYPE'] ?? '';
    
    if (strpos($contentType, 'application/json') !== false) {
        $rawInput = file_get_contents('php://input');
        $input = json_decode($rawInput, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            echo json_encode([
                'success' => false,
                'message' => 'Invalid JSON: ' . json_last_error_msg()
            ]);
            exit;
        }
    } else {
        $input = $_POST;
    }
    
    if (!$input || !isset($input['tag']) || empty(trim($input['tag']))) {
        echo json_encode([
            'success' => false,
            'message' => 'Tag name is required'
        ]);
        exit;
    }
    
    $tagName = trim($input['tag']);
    
    // Check if tag already exists
    $tags = new Tags();
    $existingResult = $tags->getList(['limit' => 1000]);
    
    if ($existingResult['success'] && !empty($existingResult['data']['data'])) {
        foreach ($existingResult['data']['data'] as $existingTag) {
            if (strtolower($existingTag['tag']) === strtolower($tagName)) {
                echo json_encode([
                    'success' => true,
                    'data' => [
                        'id' => $existingTag['id'],
                        'tag' => $existingTag['tag']
                    ],
                    'message' => 'Tag already exists'
                ]);
                exit;
            }
        }
    }
    
    // Create new tag
    $result = $tags->create([
        'tag' => $tagName
    ]);
    
    if ($result['success']) {
        $newId = $result['data']['last_insert_id'] ?? $result['data']['id'] ?? null;
        
        echo json_encode([
            'success' => true,
            'data' => [
                'id' => $newId,
                'tag' => $tagName
            ]
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => $result['message'] ?? 'Failed to create tag'
        ]);
    }
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>