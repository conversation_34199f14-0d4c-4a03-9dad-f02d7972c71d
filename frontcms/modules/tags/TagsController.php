<?php
require_once __DIR__ . '/Tags.php';

class TagsController
{
    private $tags;
    private $allowedSortColumns = ['id', 'tag', 'created_at', 'updated_at'];

    public function __construct()
    {
        $this->tags = new Tags();
    }

    public function handleRequest()
    {
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $result = $this->handlePostRequest();

            if ($result['success']) {
                return $this->getList();
            }
        }
        return $this->getList();
    }

    private function handlePostRequest()
    {
        if (isset($_POST['delete'])) {
            return $this->delete($_POST['id']);
        }

        if (isset($_POST['save_tag'])) {

            $data = [
                'tag' => $_POST['tag'],
                'updated_at' => date('Y-m-d H:i:s')
            ];

            if (empty($_POST['id'])) {
                $data['created_at'] = date('Y-m-d H:i:s');
            }


            return !empty($_POST['id'])
                ? $this->update($_POST['id'], $data)
                : $this->create($data);
        }

        return ['success' => false, 'message' => 'Invalid request'];
    }

    public function getList()
    {
        $sort = $this->validateSort($_GET['sort'] ?? '-updated_at');

        $result = $this->tags->getList([
            'page' => $_GET['page'] ?? 1,
            'limit' => $_GET['limit'] ?? 10,
            'sort' => $sort['column'],
            'direction' => $sort['direction']
        ]);

        // Debug the response if needed
        if (isset($_GET['debug'])) {
            echo '<pre>';
            print_r($result);
            echo '</pre>';
        }

        return $result;
    }

    private function create($data)
    {
        return $this->tags->create($data);
    }

    private function update($id, $data)
    {
        return $this->tags->update($id, $data);
    }

    private function delete($id)
    {
        return $this->tags->delete($id);
    }

    private function validateSort($sort)
    {
        $direction = 'asc';

        if (strpos($sort, '-') === 0) {
            $direction = 'desc';
            $sort = substr($sort, 1);
        }

        if (!in_array($sort, $this->allowedSortColumns)) {
            $sort = 'id';
        }

        return [
            'column' => $sort,
            'direction' => $direction
        ];
    }
}
