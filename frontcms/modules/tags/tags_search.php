<?php
require_once __DIR__ . '/Tags.php';
require_once __DIR__ . '/../../config/config.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

try {
    $tags = new Tags();
    $search = $_GET['search'] ?? '';
    
    $params = [
        'limit' => 100,
        'page' => 1
    ];
    
    $result = $tags->getList($params);
    
    if ($result['success']) {
        $allTags = $result['data']['data'] ?? [];
        
        // If search query provided, filter results
        if (!empty($search)) {
            $filteredTags = array_filter($allTags, function($tag) use ($search) {
                return stripos($tag['tag'], $search) !== false;
            });
            $allTags = array_values($filteredTags);
        }
        
        echo json_encode([
            'success' => true,
            'data' => $allTags
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => $result['message'] ?? 'Failed to fetch tags',
            'data' => []
        ]);
    }
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage(),
        'data' => []
    ]);
}
?>