<?php
require_once __DIR__ . '/../../api/ApiClient.php';
require_once __DIR__ . '/../../config/config.php';

class GlobalAssets {
    private $apiClientCms;
    private $apiClientV1;
    private $endpoint = 'global_assets';
    
    public function __construct() {
        // Initialize both API clients with correct base URLs
        $this->apiClientCms = new ApiClient(API_BASE_URL_CMS);
        $this->apiClientV1 = new ApiClient(API_BASE_URL_V1);
    }
    
    /**
     * Get all global assets with proper pagination handling
     */
    public function getAllAssets($params = []) {
        try {
            // Set default parameters
            $defaultParams = [
                'page' => 1,
                'sort' => 'id',
                'direction' => 'desc',
                'limit' => 10
            ];
            
            // Merge provided parameters with defaults
            $params = array_merge($defaultParams, $params);
            
            // Build query parameters
            $queryParams = [];
            
            // Add pagination parameter
            if (!empty($params['page'])) {
                $queryParams['page'] = $params['page'];
            }
            
            // Add limit parameter explicitly
            if (!empty($params['limit'])) {
                $queryParams['limit'] = $params['limit'];
            }

            // Add sorting parameter based on API format - use hyphen prefix for descending
            if (!empty($params['sort'])) {
                $sortColumn = $params['sort'];
                $sortDirection = $params['direction'] ?? 'asc';
                
                // Prefix with minus sign for descending order
                if ($sortDirection === 'desc') {
                    $sortColumn = '-' . $sortColumn;
                }
                
                $queryParams['sort'] = $sortColumn;
            }
            
            // Add any additional filters that might be in the params
            foreach ($params as $key => $value) {
                if (!in_array($key, ['page', 'sort', 'direction', 'limit']) && !empty($value)) {
                    $queryParams[$key] = $value;
                }
            }
            
            // Make the paginated request using V1 API
            $response = $this->apiClientV1->get($this->endpoint, $queryParams);
            
            // Check if response is successful and has data
            if (!isset($response['data'])) {
                return [
                    'success' => false,
                    'message' => 'No data received from API'
                ];
            }
            
            // Extract pagination information directly from the V1 API response
            $paginationData = $response['data'];
            
            // Prepare the return data structure compatible with TableComponent
            $returnData = [
                'success' => true,
                'data' => [
                    'data' => $paginationData['data'] ?? [],
                    // V1 API pagination structure
                    'current_page' => $paginationData['current_page'] ?? 1,
                    'per_page' => $paginationData['per_page'] ?? $params['limit'],
                    'from' => $paginationData['from'] ?? null,
                    'to' => $paginationData['to'] ?? null,
                    'total' => $paginationData['total'] ?? null,
                    'next_page_url' => $paginationData['next_page_url'] ?? null,
                    'prev_page_url' => $paginationData['prev_page_url'] ?? null,
                    'first_page_url' => $paginationData['first_page_url'] ?? null,
                    'last_page_url' => $paginationData['last_page_url'] ?? null
                ]
            ];
            
            return $returnData;
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage(),
                'data' => [
                    'data' => [],
                    'current_page' => 1,
                    'per_page' => 10,
                    'from' => null,
                    'to' => null,
                    'total' => 0,
                    'next_page_url' => null,
                    'prev_page_url' => null,
                    'first_page_url' => null,
                    'last_page_url' => null
                ]
            ];
        }
    }

    /**
     * Create a new global asset
     */
    public function create($data) {
        try {
            $response = $this->apiClientV1->post($this->endpoint, $data);
            return [
                'success' => true,
                'data' => $response['data'] ?? null
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Update a global asset
     */
    public function update($id, $data) {
        try {
            $response = $this->apiClientV1->put($this->endpoint . '/' . $id, $data);
            return [
                'success' => true,
                'data' => $response['data'] ?? null
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Delete a global asset
     */
    public function delete($id) {
        try {
            $response = $this->apiClientV1->delete($this->endpoint . '/' . $id);
            return [
                'success' => true,
                'data' => $response['data'] ?? null
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }
}