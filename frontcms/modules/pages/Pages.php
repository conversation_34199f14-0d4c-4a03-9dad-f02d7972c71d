<?php
require_once __DIR__ . '/../../api/ApiClient.php';
require_once __DIR__ . '/../../config/config.php';

class Pages {
    private $apiClientCms;
    private $apiClientV1;
    private $endpoint = 'pages';
    
    public function __construct() {
        // Initialize both API clients with correct base URLs
        $this->apiClientCms = new ApiClient(API_BASE_URL_CMS);
        $this->apiClientV1 = new ApiClient(API_BASE_URL_V1);
    }
    
    /**
     * Get a single page by ID
     */
    public function getById($id) {
        if (empty($id)) {
            throw new Exception('ID is required');
        }
        
        try {
            $response = $this->apiClientV1->get($this->endpoint . '/' . $id);
            return [
                'success' => true,
                'data' => $response['data'] ?? null
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Get list of pages with filtering, sorting, and pagination
     */
    public function getList($params = []) {
        try {
            // Set default parameters
            $defaultParams = [
                'page' => 1,
                'sort' => 'id',
                'direction' => 'desc',
                'limit' => 10
            ];
            
            // Merge provided parameters with defaults
            $params = array_merge($defaultParams, $params);
            
            // Build query parameters
            $queryParams = [];
            
            // Add pagination parameter
            if (!empty($params['page'])) {
                $queryParams['page'] = $params['page'];
            }
            
            // Add limit parameter explicitly
            if (!empty($params['limit'])) {
                $queryParams['limit'] = $params['limit'];
            }

            // Add sorting parameter based on API format - use hyphen prefix for descending
            if (!empty($params['sort'])) {
                $sortColumn = $params['sort'];
                $sortDirection = $params['direction'] ?? 'asc';
                
                // Prefix with minus sign for descending order
                if ($sortDirection === 'desc') {
                    $sortColumn = '-' . $sortColumn;
                }
                
                $queryParams['sort'] = $sortColumn;
            }
            
            // Template ID filter
            if (!empty($params['template_id'])) {
                $queryParams['template_id'] = $params['template_id'];
            }
            
            // Add any additional filters that might be in the params
            foreach ($params as $key => $value) {
                if (!in_array($key, ['page', 'sort', 'direction', 'limit', 'template_id']) && !empty($value)) {
                    $queryParams[$key] = $value;
                }
            }
            
            // Make the paginated request using V1 API
            $response = $this->apiClientV1->get($this->endpoint, $queryParams);
            
            // Check if response is successful and has data
            if (!isset($response['data'])) {
                return [
                    'success' => false,
                    'message' => 'No data received from API'
                ];
            }
            
            // Extract pagination information directly from the V1 API response
            $paginationData = $response['data'];
            
            // Process the data to fix status (from 0/1 to inactive/active)
            if (isset($paginationData['data']) && is_array($paginationData['data'])) {
                foreach ($paginationData['data'] as $key => $page) {
                    // Convert status according to API documentation: 0 = Draft, 1 = Published
                    if (isset($page['status'])) {
                        if ($page['status'] === "1") {
                            $paginationData['data'][$key]['status'] = 'active';
                        } else if ($page['status'] === "0") {
                            $paginationData['data'][$key]['status'] = 'inactive';
                        } else if ($page['status'] === "") {
                            $paginationData['data'][$key]['status'] = 'inactive';
                        }
                    } else {
                        $paginationData['data'][$key]['status'] = 'inactive';
                    }
                }
            }
            
            // Prepare the return data structure compatible with TableComponent
            // V1 API pagination structure
            $returnData = [
                'success' => true,
                'data' => [
                    'data' => $paginationData['data'] ?? [],
                    // V1 API pagination structure
                    'current_page' => $paginationData['current_page'] ?? 1,
                    'per_page' => $paginationData['per_page'] ?? $params['limit'],
                    'from' => $paginationData['from'] ?? null,
                    'to' => $paginationData['to'] ?? null,
                    'total' => $paginationData['total'] ?? null,
                    'next_page_url' => $paginationData['next_page_url'] ?? null,
                    'prev_page_url' => $paginationData['prev_page_url'] ?? null,
                    'first_page_url' => $paginationData['first_page_url'] ?? null,
                    'last_page_url' => $paginationData['last_page_url'] ?? null
                ]
            ];
            
            return $returnData;
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage(),
                'data' => [
                    'data' => [],
                    'current_page' => 1,
                    'per_page' => 10,
                    'from' => null,
                    'to' => null,
                    'total' => 0,
                    'next_page_url' => null,
                    'prev_page_url' => null,
                    'first_page_url' => null,
                    'last_page_url' => null
                ]
            ];
        }
    }

    /**
     * Create a new page
     */
    public function create($data) {
        try {
            // Set default values for required fields if not provided
            $data = $this->prepareData($data);
            
            // Debug information if needed
            if (isset($_GET['debug']) && $_GET['debug'] == 1) {
                echo "<div class='p-4 mb-4 text-sm text-gray-800 rounded-lg bg-gray-100'>";
                echo "<h3>Data being sent to API in create():</h3>";
                echo "<pre>";
                print_r($data);
                echo "</pre>";
                echo "</div>";
            }
            
            $response = $this->apiClientV1->post($this->endpoint, $data);
            return [
                'success' => $response['status'] === 'success',
                'data' => $response['data'] ?? null,
                'message' => $response['message'] ?? ''
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Update a page
     */
    public function update($id, $data) {
        try {
            // Set default values for required fields if not provided
            $data = $this->prepareData($data);
            
            // Debug information if needed
            if (isset($_GET['debug']) && $_GET['debug'] == 1) {
                echo "<div class='p-4 mb-4 text-sm text-gray-800 rounded-lg bg-gray-100'>";
                echo "<h3>Data being sent to API in update():</h3>";
                echo "<pre>";
                print_r($data);
                echo "</pre>";
                echo "</div>";
            }
            
            $response = $this->apiClientV1->put($this->endpoint . '/' . $id, $data);
            return [
                'success' => $response['status'] === 'success',
                'data' => $response['data'] ?? null,
                'message' => $response['message'] ?? ''
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Delete a page
     */
    public function delete($id) {
        try {
            $response = $this->apiClientV1->delete($this->endpoint . '/' . $id);
            return [
                'success' => $response['status'] === 'success',
                'data' => $response['data'] ?? null,
                'message' => $response['message'] ?? ''
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Prepare data for API submission by setting default values
     */
    private function prepareData($data) {
        // Convert status to match API documentation: 0 = Draft, 1 = Published
        if (isset($data['status'])) {
            if ($data['status'] === 'active') {
                $data['status'] = "1"; // Published
            } else {
                $data['status'] = "0"; // Draft
            }
        } else {
            $data['status'] = "0"; // Default to Draft
        }
        
        // Set current time for timestamps if not provided
        $currentTime = date('Y-m-d H:i:s');
        
        // For created_at, only set if not already provided
        if (!isset($data['created_at']) || empty($data['created_at'])) {
            $data['created_at'] = $currentTime;
        }
        
        // Always update the updated_at timestamp
        $data['updated_at'] = $currentTime;
        
        return $data;
    }
}