<?php
/**
 * API endpoint to publish a page
 */
require_once __DIR__ . '/Pages.php';

// Set headers
header('Content-Type: application/json');

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode([
        'success' => false,
        'message' => 'Only POST requests are allowed'
    ]);
    exit;
}

// Get JSON data from request body
$jsonData = file_get_contents('php://input');
$data = json_decode($jsonData, true);

// Check if required data is provided
if (!isset($data['page_id']) || empty($data['page_id'])) {
    echo json_encode([
        'success' => false,
        'message' => 'Page ID is required'
    ]);
    exit;
}

$pageId = $data['page_id'];

try {
    // Initialize Pages module
    $pages = new Pages();
    
    // Get current page data
    $pageResponse = $pages->getById($pageId);
    
    if (!isset($pageResponse['success']) || $pageResponse['success'] !== true) {
        echo json_encode([
            'success' => false,
            'message' => 'Failed to get page data'
        ]);
        exit;
    }
    
    $pageData = $pageResponse['data'];
    
    // Update page status to published
    $updateData = [
        'status' => 'published'
    ];
    
    // Update page
    $response = $pages->update($pageId, $updateData);
    
    if (isset($response['success']) && $response['success'] === true) {
        echo json_encode([
            'success' => true,
            'message' => 'Page published successfully',
            'data' => isset($response['data']) ? $response['data'] : null
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => isset($response['message']) ? $response['message'] : 'Failed to publish page'
        ]);
    }
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
