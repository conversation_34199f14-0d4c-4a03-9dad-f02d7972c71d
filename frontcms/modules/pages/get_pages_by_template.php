<?php
/**
 * API endpoint to get pages by template ID
 */
require_once __DIR__ . '/Pages.php';

// Set headers
header('Content-Type: application/json');

// Check if template_id is provided
if (!isset($_GET['template_id']) || empty($_GET['template_id'])) {
    echo json_encode([
        'success' => false,
        'message' => 'Template ID is required'
    ]);
    exit;
}

try {
    // Get template ID from query parameters
    $templateId = $_GET['template_id'];
    
    // Initialize Pages module
    $pages = new Pages();
    
    // Get pages by template ID
    $result = $pages->getList(['template_id' => $templateId]);
    
    // Return pages data
    echo json_encode([
        'success' => true,
        'data' => isset($result['data']) ? $result['data'] : []
    ]);
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}