<?php
// File: modules/pages/create.php

/**
 * API endpoint to create a new page (used by template builder)
 */
require_once __DIR__ . '/Pages.php';

// Set headers
header('Content-Type: application/json');

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode([
        'success' => false,
        'message' => 'Only POST requests are allowed'
    ]);
    exit;
}

// Get JSON data from request body
$jsonData = file_get_contents('php://input');
$data = json_decode($jsonData, true);

// Check if required data is provided
if (!isset($data['name']) || empty($data['name'])) {
    echo json_encode([
        'success' => false,
        'message' => 'Page name is required'
    ]);
    exit;
}

if (!isset($data['slug']) || empty($data['slug'])) {
    echo json_encode([
        'success' => false,
        'message' => 'Page slug is required'
    ]);
    exit;
}

try {
    // Initialize Pages module
    $pages = new Pages();
    
    // Prepare page data
    $pageData = [
        'name' => $data['name'],
        'slug' => $data['slug'],
        'status' => isset($data['status']) ? $data['status'] : 'inactive',
        'description' => isset($data['description']) ? $data['description'] : ''
    ];
    
    // Add template_id if provided
    if (isset($data['template_id']) && !empty($data['template_id'])) {
        $pageData['template_id'] = $data['template_id'];
    }
    
    // Create page
    $response = $pages->create($pageData);
    
    if (isset($response['success']) && $response['success'] === true) {
        echo json_encode([
            'success' => true,
            'message' => 'Page created successfully',
            'data' => isset($response['data']) ? $response['data'] : null
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => isset($response['message']) ? $response['message'] : 'Failed to create page'
        ]);
    }
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
