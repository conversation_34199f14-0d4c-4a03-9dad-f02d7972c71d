<?php
// Add these headers to fix CORS issues
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight OPTIONS request
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// Error reporting for debugging
if (defined('DEBUG') && DEBUG) {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
    
    // Log the raw input
    error_log('Raw input: ' . file_get_contents('php://input'));
}

require_once __DIR__ . '/PagesBlocks.php';

try {
    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);
    
    // Debug logging
    if (defined('DEBUG') && DEBUG) {
        error_log('Decoded input: ' . print_r($input, true));
    }
    
    // Check for form-encoded data if JSON is not available
    if (!$input && !empty($_POST)) {
        $input = $_POST;
        error_log('Using POST data: ' . print_r($_POST, true));
    }
    
    if (!$input || !isset($input['block_id'])) {
        echo json_encode([
            'success' => false,
            'message' => 'Block ID is required'
        ]);
        exit;
    }
    
    $blockId = $input['block_id'];
    
    // Initialize PagesBlocks class
    $pagesBlocks = new PagesBlocks();
    
    // Prepare the data for update
    $data = [];
    
    // Handle JSON code update
    if (isset($input['json_code'])) {
        // Validate JSON code if provided
        $jsonData = json_decode($input['json_code'], true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            echo json_encode([
                'success' => false,
                'message' => 'Invalid JSON code: ' . json_last_error_msg()
            ]);
            exit;
        }
        $data['json_code'] = $input['json_code'];
    }
    
    // Handle classes update
    if (isset($input['classes'])) {
        $data['classes'] = trim($input['classes']);
    }
    
    // Handle other possible fields
    if (isset($input['css_code'])) {
        $data['css_code'] = $input['css_code'];
    }
    
    if (isset($input['js_code'])) {
        $data['js_code'] = $input['js_code'];
    }
    
    if (isset($input['position'])) {
        $data['position'] = floatval($input['position']);
    }
    
    // Check if we have any data to update
    if (empty($data)) {
        echo json_encode([
            'success' => false,
            'message' => 'No data provided for update'
        ]);
        exit;
    }
    
    // Log the update request for debugging
    if (defined('DEBUG') && DEBUG) {
        error_log('Updating block ' . $blockId . ' with data: ' . print_r($data, true));
    }
    
    // Update the block
    $result = $pagesBlocks->update($blockId, $data);
    
    // Debug logging
    if (defined('DEBUG') && DEBUG) {
        error_log('Update result: ' . print_r($result, true));
    }
    
    if (isset($result['success']) && $result['success']) {
        // Return success response
        echo json_encode([
            'success' => true,
            'message' => 'Block updated successfully',
            'data' => [
                'block_id' => $blockId,
                'updated_fields' => array_keys($data)
            ]
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => isset($result['message']) ? $result['message'] : 'Failed to update block'
        ]);
    }
    
} catch (Exception $e) {
    // Log the error for debugging
    error_log('Error updating block: ' . $e->getMessage());
    
    // Return error response
    echo json_encode([
        'success' => false,
        'message' => 'Server error: ' . $e->getMessage()
    ]);
}
?>