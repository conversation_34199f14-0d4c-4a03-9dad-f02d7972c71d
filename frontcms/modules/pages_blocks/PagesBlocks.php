<?php
require_once __DIR__ . '/../../api/ApiClient.php';
require_once __DIR__ . '/../../config/config.php';

class PagesBlocks {
    private $apiClientCms;
    private $apiClientV1;
    private $endpoint = 'page_blocks';
    
    public function __construct() {
        // Initialize both API clients with correct base URLs
        $this->apiClientCms = new ApiClient(API_BASE_URL_CMS);
        $this->apiClientV1 = new ApiClient(API_BASE_URL_V1);
    }
    
    /**
     * Get all page blocks
     */
    public function getList($params = []) {
        try {
            // Set default parameters
            $defaultParams = [
                'page' => 1,
                'sort' => 'id',
                'direction' => 'desc',
                'limit' => 1000
            ];
            
            // Merge provided parameters with defaults
            $params = array_merge($defaultParams, $params);
            
            // Build query parameters
            $queryParams = [];
            
            // Add pagination parameter
            if (!empty($params['page'])) {
                $queryParams['page'] = $params['page'];
            }
            
            // Add limit parameter explicitly
            if (!empty($params['limit'])) {
                $queryParams['limit'] = $params['limit'];
            }

            // Add sorting parameter based on API format - use hyphen prefix for descending
            if (!empty($params['sort'])) {
                $sortColumn = $params['sort'];
                $sortDirection = $params['direction'] ?? 'asc';
                
                // Prefix with minus sign for descending order
                if ($sortDirection === 'desc') {
                    $sortColumn = '-' . $sortColumn;
                }
                
                $queryParams['sort'] = $sortColumn;
            }
            
            // Add any additional filters that might be in the params
            foreach ($params as $key => $value) {
                if (!in_array($key, ['page', 'sort', 'direction', 'limit']) && !empty($value)) {
                    $queryParams[$key] = $value;
                }
            }
            
            // Make the paginated request using V1 API
            $response = $this->apiClientV1->get($this->endpoint, $queryParams);
            
            // Check if response is successful and has data
            if (!isset($response['data'])) {
                return [
                    'success' => false,
                    'message' => 'No data received from API'
                ];
            }
            
            // Extract pagination information directly from the API response
            $paginationData = $response['data'];
            
            // Prepare the return data structure
            $returnData = [
                'success' => true,
                'data' => [
                    'data' => $paginationData['data'] ?? [],
                    'total' => $paginationData['total'] ?? count($paginationData['data'] ?? []),
                    'per_page' => $paginationData['per_page'],
                    'current_page' => $paginationData['current_page'],
                    'last_page' => $paginationData['last_page'] ?? max(1, ceil((($paginationData['total'] ?? count($paginationData['data'] ?? [])) / $paginationData['per_page'])))
                ]
            ];
            
            return $returnData;
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Get all page blocks for a specific page
     */
    public function getByPageId($pageId, $params = []) {
        try {
            // Set filter for page_id
            $params['filter'] = isset($params['filter']) ? $params['filter'] : [];
            $params['filter']['page_id'] = $pageId;
            
            return $this->getList($params);
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Create a new page block
     */
    public function create($data) {
        try {
            // Validate required fields
            if (empty($data['page_id'])) {
                throw new Exception('Page ID is required');
            }
            
            if (empty($data['block_id'])) {
                throw new Exception('Block ID is required');
            }
            
            $response = $this->apiClientV1->post($this->endpoint, $data);
            return [
                'success' => true,
                'data' => $response['data'] ?? null
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Update a page block
     */
    public function update($id, $data) {
        try {
            $response = $this->apiClientV1->put($this->endpoint . '/' . $id, $data);
            return [
                'success' => true,
                'data' => $response['data'] ?? null
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Delete a page block
     */
    public function delete($id) {
        try {
            $response = $this->apiClientV1->delete($this->endpoint . '/' . $id);
            return [
                'success' => true,
                'data' => $response['data'] ?? null
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Save all page blocks for a page
     */
    public function savePageBlocks($pageId, $blocks) {
        try {
            // First, get all existing blocks for this page
            $existingBlocks = $this->getByPageId($pageId);
            
            // Delete all existing blocks if they exist
            if (isset($existingBlocks['success']) && $existingBlocks['success'] === true) {
                if (isset($existingBlocks['data']['data']) && is_array($existingBlocks['data']['data'])) {
                    foreach ($existingBlocks['data']['data'] as $block) {
                        $this->delete($block['id']);
                    }
                }
            }
            
            // Then, create new blocks
            $results = [];
            $position = 0;
            
            foreach ($blocks as $block) {
                $blockData = [
                    'page_id' => $pageId,
                    'block_id' => $block['block_id'],
                    'position' => $position++,
                    'data' => isset($block['data']) ? $block['data'] : null
                ];
                
                $results[] = $this->create($blockData);
            }
            
            return [
                'success' => true,
                'message' => 'Page blocks saved successfully',
                'results' => $results
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Get page blocks by block ID
     */
    public function getByBlockId($blockId, $params = []) {
        try {
            // Set filter for block_id
            $params['filter'] = isset($params['filter']) ? $params['filter'] : [];
            $params['filter']['block_id'] = $blockId;
            
            return $this->getList($params);
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Bulk update page blocks positions
     */
    public function updatePositions($blocks) {
        try {
            $results = [];
            
            foreach ($blocks as $block) {
                if (empty($block['id']) || !isset($block['position'])) {
                    continue;
                }
                
                $updateData = [
                    'position' => $block['position']
                ];
                
                $results[] = $this->update($block['id'], $updateData);
            }
            
            return [
                'success' => true,
                'message' => 'Block positions updated successfully',
                'results' => $results
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Clone page blocks from one page to another
     */
    public function clonePageBlocks($sourcePageId, $targetPageId) {
        try {
            // Get blocks from source page
            $sourceBlocks = $this->getByPageId($sourcePageId);
            
            if (!isset($sourceBlocks['success']) || $sourceBlocks['success'] !== true) {
                throw new Exception('Failed to retrieve source page blocks');
            }
            
            if (!isset($sourceBlocks['data']['data']) || !is_array($sourceBlocks['data']['data'])) {
                throw new Exception('No blocks found in source page');
            }
            
            // Prepare blocks for target page
            $newBlocks = [];
            foreach ($sourceBlocks['data']['data'] as $block) {
                $newBlocks[] = [
                    'block_id' => $block['block_id'],
                    'data' => $block['data'] ?? null
                ];
            }
            
            // Save blocks to target page
            return $this->savePageBlocks($targetPageId, $newBlocks);
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }
}