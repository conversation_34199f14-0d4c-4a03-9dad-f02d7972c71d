<?php
/**
 * API endpoint to save a single block to a page
 */

require_once __DIR__ . '/PagesBlocks.php';

// Set headers
header('Content-Type: application/json');

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode([
        'success' => false,
        'message' => 'Only POST requests are allowed'
    ]);
    exit;
}

// Get JSON data from request body
$jsonData = file_get_contents('php://input');
$data = json_decode($jsonData, true);

// Check if required data is provided
if (!isset($data['page_id']) || empty($data['page_id'])) {
    echo json_encode([
        'success' => false,
        'message' => 'Page ID is required'
    ]);
    exit;
}

if (!isset($data['block']) || !isset($data['block']['block_id'])) {
    echo json_encode([
        'success' => false,
        'message' => 'Block data is required'
    ]);
    exit;
}

$pageId = $data['page_id'];
$block = $data['block'];

try {
    // Initialize PagesBlocks module
    $pagesBlocks = new PagesBlocks();
    
    // Log received data for debugging
    if (DEBUG) {
        error_log('Received block data: ' . print_r($block, true));
    }
    
    // Create block data
    $blockData = [
        'page_id' => $pageId,
        'block_id' => $block['block_id'],
        'position' => isset($block['position']) ? $block['position'] : 999,
        'json_code' => isset($block['data']['json_code']) ? $block['data']['json_code'] : '{}',
        'css_code' => isset($block['data']['css_code']) ? $block['data']['css_code'] : '',
        'js_code' => isset($block['data']['js_code']) ? $block['data']['js_code'] : '',
        'tmpl_code' => isset($block['data']['tmpl_code']) ? $block['data']['tmpl_code'] : ''
    ];
    
    // Log processed data for debugging
    if (DEBUG) {
        error_log('Processed block data: ' . print_r($blockData, true));
    }
    
    // Save block to page
    $response = $pagesBlocks->create($blockData);
    
    
    // The API is returning a success message with "Resource created successfully"
    // but our code is interpreting it as an error because it's not in the expected format
    if (isset($response['success']) && $response['success'] === true) {
        echo json_encode([
            'success' => true,
            'message' => 'Block saved successfully',
            'data' => isset($response['data']) ? $response['data'] : null
        ]);
    } else if (isset($response['message']) && strpos($response['message'], 'created successfully') !== false) {
        // Handle the case where the API returns a success message but not in our expected format
        echo json_encode([
            'success' => true,
            'message' => $response['message'],
            'data' => isset($response['data']) ? $response['data'] : null
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => isset($response['message']) ? $response['message'] : 'Failed to save block'
        ]);
    }
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
