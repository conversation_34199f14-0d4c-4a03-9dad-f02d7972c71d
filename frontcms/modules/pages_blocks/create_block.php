<?php
require_once __DIR__ . '/PagesBlocks.php';

// Set headers for JSON response
header('Content-Type: application/json');

try {
    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!isset($input['page_id']) || !isset($input['block_id'])) {
        throw new Exception('Page ID and Block ID are required');
    }
    
    // Initialize PagesBlocks class
    $pagesBlocks = new PagesBlocks();
    
    // Prepare block data
    $blockData = [
        'page_id' => $input['page_id'],
        'block_id' => $input['block_id'],
        'position' => isset($input['position']) ? $input['position'] : 999 // Default to end if not specified
    ];
    
    // Create the block
    $result = $pagesBlocks->create($blockData);
    
    if (!$result['success']) {
        throw new Exception($result['message'] ?? 'Failed to create block');
    }
    
    // Return success response in the format your API expects
    echo json_encode([
        'status' => 'success',
        'data' => [
            'current_page' => 1,
            'data' => [$result['data']],
            'per_page' => 1,
            'total' => 1
        ],
        'message' => 'Block created successfully'
    ]);
    
} catch (Exception $e) {
    // Return error response
    http_response_code(400);
    echo json_encode([
        'status' => 'error',
        'data' => null,
        'message' => $e->getMessage()
    ]);
}