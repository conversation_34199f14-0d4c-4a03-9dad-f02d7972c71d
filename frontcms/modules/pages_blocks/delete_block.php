<?php
require_once __DIR__ . '/PagesBlocks.php';

// Set headers for JSON response
header('Content-Type: application/json');

try {
    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!isset($input['block_id'])) {
        throw new Exception('Block ID is required');
    }
    
    $blockId = $input['block_id'];
    
    // Initialize PagesBlocks class
    $pagesBlocks = new PagesBlocks();
    
    // Delete the block
    $result = $pagesBlocks->delete($blockId);
    
    if (!$result['success']) {
        throw new Exception($result['message'] ?? 'Failed to delete block');
    }
    
    // Return success response
    echo json_encode([
        'success' => true,
        'message' => 'Block deleted successfully'
    ]);
    
} catch (Exception $e) {
    // Return error response
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
