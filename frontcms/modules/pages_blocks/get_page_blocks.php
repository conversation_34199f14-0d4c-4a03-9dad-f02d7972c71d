<?php
/**
 * API endpoint to get blocks for a specific page
 */

 require_once __DIR__ . '/PagesBlocks.php';

// Set headers
header('Content-Type: application/json');

// Check if page_id is provided
if (!isset($_GET['page_id']) || empty($_GET['page_id'])) {
    echo json_encode([
        'success' => false,
        'message' => 'Page ID is required'
    ]);
    exit;
}

$pageId = $_GET['page_id'];

try {
    // Initialize PagesBlocks module
    $pagesBlocks = new PagesBlocks();
    
    // Get blocks for the page
    $response = $pagesBlocks->getByPageId($pageId);
    
    if (isset($response['success']) && $response['success'] === true) {
        $blocks = [];
        
        // Extract blocks from response
        if (isset($response['data']['data']) && is_array($response['data']['data'])) {
            $blocks = $response['data']['data'];
        } elseif (isset($response['data']) && is_array($response['data'])) {
            $blocks = $response['data'];
        }
        
        echo json_encode([
            'success' => true,
            'blocks' => $blocks
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => isset($response['message']) ? $response['message'] : 'Failed to get page blocks',
            'blocks' => []
        ]);
    }
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage(),
        'blocks' => []
    ]);
}
?>
