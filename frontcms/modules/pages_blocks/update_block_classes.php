<?php
// Save this as: modules/pages_blocks/update_block_classes.php

error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once __DIR__ . '/../../config/config.php';
require_once __DIR__ . '/PagesBlocks.php';

header('Content-Type: application/json');

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

try {
    // Get JSON input
    $input = file_get_contents('php://input');
    $data = json_decode($input, true);
    
    if (json_last_error() !== JSON_ERROR_NONE) {
        throw new Exception('Invalid JSON data');
    }
    
    // Validate required fields
    if (!isset($data['block_id'])) {
        throw new Exception('Block ID is required');
    }
    
    $blockId = $data['block_id'];
    $classes = isset($data['classes']) ? trim($data['classes']) : '';
    
    // Validate block ID
    if (!is_numeric($blockId) || $blockId <= 0) {
        throw new Exception('Invalid block ID');
    }
    
    // Initialize PagesBlocks
    $pagesBlocks = new PagesBlocks();
    
    // Update only the classes field
    $updateData = [
        'classes' => $classes
    ];
    
    $response = $pagesBlocks->update($blockId, $updateData);
    
    if (isset($response['success']) && $response['success'] === true) {
        echo json_encode([
            'success' => true,
            'message' => 'Block classes updated successfully',
            'data' => [
                'block_id' => $blockId,
                'classes' => $classes
            ]
        ]);
    } else {
        throw new Exception($response['message'] ?? 'Failed to update block classes');
    }
    
} catch (Exception $e) {
    error_log('Error updating block classes: ' . $e->getMessage());
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>