<?php
// Add these headers to fix CORS issues
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight OPTIONS request
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// Error reporting for debugging
if (defined('DEBUG') && DEBUG) {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
    
    // Log the raw input
    error_log('Raw input: ' . file_get_contents('php://input'));
}

require_once __DIR__ . '/PagesBlocks.php';

try {
    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);
    
    // Debug logging
    if (defined('DEBUG') && DEBUG) {
        error_log('Decoded input: ' . print_r($input, true));
    }
    
    // Check for form-encoded data if JSON is not available
    if (!$input && !empty($_POST)) {
        $input = $_POST;
        error_log('Using POST data: ' . print_r($_POST, true));
    }
    
    if (!$input || !isset($input['block_id']) || !isset($input['position'])) {
        echo json_encode([
            'success' => false,
            'message' => 'Missing required fields: block_id and position'
        ]);
        exit;
    }
    
    $blockId = $input['block_id'];
    $newPosition = floatval($input['position']);
    
    // Validate position
    if ($newPosition < 0) {
        echo json_encode([
            'success' => false,
            'message' => 'Position must be a positive number'
        ]);
        exit;
    }
    
    // Debug logging
    if (defined('DEBUG') && DEBUG) {
        error_log('Updating block position - Block ID: ' . $blockId . ', Position: ' . $newPosition);
    }
    
    // Initialize PagesBlocks
    $pagesBlocks = new PagesBlocks();
    
    // Update the block position
    $updateData = [
        'position' => $newPosition
    ];
    
    // Make API call to update block
    $result = $pagesBlocks->update($blockId, $updateData);
    
    // Debug logging
    if (defined('DEBUG') && DEBUG) {
        error_log('Update result: ' . print_r($result, true));
    }
    
    if (isset($result['success']) && $result['success']) {
        echo json_encode([
            'success' => true,
            'message' => 'Block position updated successfully',
            'data' => [
                'block_id' => $blockId,
                'new_position' => $newPosition
            ]
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'Failed to update block position: ' . ($result['message'] ?? 'Unknown error')
        ]);
    }
    
} catch (Exception $e) {
    error_log('Error updating block position: ' . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'Server error: ' . $e->getMessage()
    ]);
}
?>