<?php
require_once __DIR__ . '/../../api/ApiClient.php';
require_once __DIR__ . '/../../config/config.php';

class Auth {
    private $apiClientCms;
    private $apiClientV1;
    
    public function __construct() {
        // Initialize both API clients with correct base URLs
        $this->apiClientCms = new ApiClient(API_BASE_URL_CMS);
        $this->apiClientV1 = new ApiClient(API_BASE_URL_V1);
    }
    
    /**
     * Login for regular users
     */
    public function userLogin($email, $password) {
        try {
            $data = [
                'email' => $email,
                'password' => $password
            ];
            
            $response = $this->apiClientCms->post('user/login', $data);
            
            if ($response['status'] === 'success') {
                // Store token and user info in session
                if (session_status() === PHP_SESSION_NONE) {
                    session_start();
                }
                
                $_SESSION['token'] = $response['token'];
                $_SESSION['user'] = $response['user'];
                
                return [
                    'success' => true,
                    'data' => $response
                ];
            } else {
                return [
                    'success' => false,
                    'message' => $response['message'] ?? 'Login failed. Please check your credentials.'
                ];
            }
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Login for admin users
     */
    public function adminLogin($email, $password) {
        try {
            $data = [
                'email' => $email,
                'password' => $password
            ];
            
            $response = $this->apiClientCms->post('admin/login', $data);
            
            if ($response['status'] === 'success') {
                // Store token and user info in session
                if (session_status() === PHP_SESSION_NONE) {
                    session_start();
                }
                
                $_SESSION['token'] = $response['token'];
                $_SESSION['user'] = $response['user'];
                $_SESSION['is_admin'] = true;
                
                return [
                    'success' => true,
                    'data' => $response
                ];
            } else {
                return [
                    'success' => false,
                    'message' => $response['message'] ?? 'Login failed. Please check your credentials.'
                ];
            }
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Register a new user
     */
    public function register($data) {
        try {
            if($this->isAdmin()) {
                $response = $this->apiClientCms->post('admin/register', $data);
            } else {
                $response = $this->apiClientCms->post('user/register', $data);
            }
            
            if ($response['status'] === 'success') {
                // Optionally auto-login after registration
                if (session_status() === PHP_SESSION_NONE) {
                    session_start();
                }
                
                $_SESSION['token'] = $response['token'];
                $_SESSION['user'] = $response['user'];
                
                return [
                    'success' => true,
                    'data' => $response
                ];
            } else {
                return [
                    'success' => false,
                    'message' => $response['message'] ?? 'Registration failed.'
                ];
            }
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Get user profile
     */
    public function getProfile() {
        try {
            if($this->isAdmin()) {
                $response = $this->apiClientCms->get('admin/profile');
            } else {
                $response = $this->apiClientCms->get('user/profile');
            }
            
            return [
                'success' => true,
                'data' => $response['data'] ?? null
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Update user profile
     */
    public function updateProfile($data) {
        try {
            if($this->isAdmin()) {
                $response = $this->apiClientCms->put('admin/profile', $data);
            } else {
                $response = $this->apiClientCms->put('user/profile', $data);
            }
            
            return [
                'success' => true,
                'data' => $response['data'] ?? null
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Change password
     */
    public function changePassword($data) {
        try {
            if($this->isAdmin()) {
                $response = $this->apiClientCms->post('admin/change-password', $data);
            } else {
                $response = $this->apiClientCms->post('user/change-password', $data);
            }
            
            return [
                'success' => true,
                'data' => $response['data'] ?? null
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Forgot password - send reset link
     */
    public function forgotPassword($email) {
        try {
            $data = [
                'email' => $email
            ];
            
            if($this->isAdmin()) {
                $response = $this->apiClientCms->post('admin/forgot-password', $data);
            } else {
                $response = $this->apiClientCms->post('user/forgot-password', $data);
            }
            
            return [
                'success' => true,
                'message' => $response['message'] ?? 'Password reset link sent to your email.'
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Reset password
     */
    public function resetPassword($data) {
        try {
            if($this->isAdmin()) {
                $response = $this->apiClientCms->post('admin/reset-password', $data);
            } else {
                $response = $this->apiClientCms->post('user/reset-password', $data);
            }
            
            return [
                'success' => true,
                'message' => $response['message'] ?? 'Password has been reset successfully.'
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Logout user
     */
    public function logout() {
        try {
            // Call logout endpoint to invalidate token on server
            if($this->isAdmin()) {
                $this->apiClientCms->post('admin/logout');
            } else {
                $this->apiClientCms->post('user/logout');
            }
            
            // Clear session
            if (session_status() === PHP_SESSION_ACTIVE) {
                session_unset();
                session_destroy();
            }
            
            return [
                'success' => true,
                'message' => 'Logged out successfully.'
            ];
        } catch (Exception $e) {
            // Even if API fails, we should still clear the session
            if (session_status() === PHP_SESSION_ACTIVE) {
                session_unset();
                session_destroy();
            }
            
            return [
                'success' => true,
                'message' => 'Logged out successfully.'
            ];
        }
    }
    
    /**
     * Check if user is logged in
     */
    public function isLoggedIn() {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
        
        return isset($_SESSION['token']) && !empty($_SESSION['token']);
    }
    
    /**
     * Check if user is admin
     */
    public function isAdmin() {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
        
        return isset($_SESSION['is_admin']) && $_SESSION['is_admin'] === true;
    }
}