<?php
require_once __DIR__ . '/../../api/ApiClient.php';
require_once __DIR__ . '/../../config/config.php';

class CommonTags {
    private $apiClientCms;
    private $apiClientV1;
    private $endpoint = 'common_tags';
    
    public function __construct() {
        // Initialize both API clients with correct base URLs
        $this->apiClientCms = new ApiClient(API_BASE_URL_CMS);
        $this->apiClientV1 = new ApiClient(API_BASE_URL_V1);
    }
    
    /**
     * Get all common tags
     */
    public function getList($params = []) {
        try {
            // Set default parameters
            $defaultParams = [
                'page' => 1,
                'sort' => 'id',
                'direction' => 'desc',
                'limit' => 1000  // Add a default limit
            ];
            
            // Merge provided parameters with defaults
            $params = array_merge($defaultParams, $params);
            
            // Build query parameters
            $queryParams = [];
            
            // Add pagination parameter
            if (!empty($params['page'])) {
                $queryParams['page'] = $params['page'];
            }
            
            // Add limit parameter explicitly
            if (!empty($params['limit'])) {
                $queryParams['limit'] = $params['limit'];
            }

            // Add sorting parameter based on API format - use hyphen prefix for descending
            if (!empty($params['sort'])) {
                $sortColumn = $params['sort'];
                $sortDirection = $params['direction'] ?? 'asc';
                
                // Prefix with minus sign for descending order
                if ($sortDirection === 'desc') {
                    $sortColumn = '-' . $sortColumn;
                }
                
                $queryParams['sort'] = $sortColumn;
            }
            
            // Add any additional filters that might be in the params
            foreach ($params as $key => $value) {
                if (!in_array($key, ['page', 'sort', 'direction', 'limit']) && !empty($value)) {
                    $queryParams[$key] = $value;
                }
            }
            
            // Make the paginated request
            $response = $this->apiClientV1->get($this->endpoint, $queryParams);
            
            // Check if response is successful and has data
            if (!isset($response['data'])) {
                return [
                    'success' => false,
                    'message' => 'No data received from API'
                ];
            }
            
            // Extract pagination information directly from the API response
            $paginationData = $response['data'];
            
            // Prepare the return data structure
            $returnData = [
                'success' => true,
                'data' => [
                    'data' => $paginationData['data'] ?? [],
                    'total' => $paginationData['to'] ?? count($paginationData['data'] ?? []),
                    'per_page' => $paginationData['per_page'] ?? $params['limit'],
                    'current_page' => $paginationData['current_page'] ?? 1,
                    'last_page' => isset($paginationData['to']) && isset($paginationData['per_page']) && $paginationData['per_page'] > 0 
                        ? ceil($paginationData['to'] / $paginationData['per_page']) 
                        : 1
                ]
            ];
            
            return $returnData;
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Create a new common tag association
     */
    public function create($data) {
        try {
            // Validate required fields based on the POST body structure
            $requiredFields = ['model_type', 'model_id', 'tag_id'];
            foreach ($requiredFields as $field) {
                if (!isset($data[$field]) || empty($data[$field])) {
                    return [
                        'success' => false,
                        'message' => "Required field '{$field}' is missing or empty"
                    ];
                }
            }

            // Prepare the data structure for API
            $postData = [
                'model_type' => $data['model_type'],
                'model_id' => (int) $data['model_id'],
                'tag_id' => (int) $data['tag_id']
            ];

            // Add optional id field if provided
            if (isset($data['id']) && !empty($data['id'])) {
                $postData['id'] = (int) $data['id'];
            }

            $response = $this->apiClientV1->post($this->endpoint, $postData);
            return [
                'success' => true,
                'data' => $response['data'] ?? null
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Update a common tag association
     */
    public function update($id, $data) {
        try {
            // Prepare the data structure for API
            $putData = [];
            
            if (isset($data['model_type'])) {
                $putData['model_type'] = $data['model_type'];
            }
            
            if (isset($data['model_id'])) {
                $putData['model_id'] = (int) $data['model_id'];
            }
            
            if (isset($data['tag_id'])) {
                $putData['tag_id'] = (int) $data['tag_id'];
            }

            $response = $this->apiClientV1->put($this->endpoint . '/' . $id, $putData);
            return [
                'success' => true,
                'data' => $response['data'] ?? null
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Delete a common tag association
     */
    public function delete($id) {
        try {
            $response = $this->apiClientV1->delete($this->endpoint . '/' . $id);
            return [
                'success' => true,
                'data' => $response['data'] ?? null
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Get common tags by model type
     */
    public function getByModelType($modelType, $params = []) {
        $params['model_type'] = $modelType;
        return $this->getList($params);
    }

    /**
     * Get common tags by model ID
     */
    public function getByModelId($modelId, $params = []) {
        $params['model_id'] = $modelId;
        return $this->getList($params);
    }

    /**
     * Get common tags by tag ID
     */
    public function getByTagId($tagId, $params = []) {
        $params['tag_id'] = $tagId;
        return $this->getList($params);
    }

    /**
     * Get common tags for a specific model
     */
    public function getByModel($modelType, $modelId, $params = []) {
        $params['model_type'] = $modelType;
        $params['model_id'] = $modelId;
        return $this->getList($params);
    }

    /**
     * Create multiple common tag associations
     */
    public function createMultiple($dataArray) {
        $results = [];
        $errors = [];
        
        foreach ($dataArray as $index => $data) {
            $result = $this->create($data);
            if ($result['success']) {
                $results[] = $result['data'];
            } else {
                $errors[] = [
                    'index' => $index,
                    'data' => $data,
                    'error' => $result['message']
                ];
            }
        }
        
        return [
            'success' => empty($errors),
            'data' => $results,
            'errors' => $errors,
            'created_count' => count($results),
            'error_count' => count($errors)
        ];
    }

    /**
     * Remove all tags for a specific model
     */
    public function removeAllForModel($modelType, $modelId) {
        try {
            // First get all tags for this model
            $existingTags = $this->getByModel($modelType, $modelId);
            
            if (!$existingTags['success']) {
                return $existingTags;
            }
            
            $deletedCount = 0;
            $errors = [];
            
            foreach ($existingTags['data']['data'] as $tag) {
                $deleteResult = $this->delete($tag['id']);
                if ($deleteResult['success']) {
                    $deletedCount++;
                } else {
                    $errors[] = [
                        'id' => $tag['id'],
                        'error' => $deleteResult['message']
                    ];
                }
            }
            
            return [
                'success' => empty($errors),
                'deleted_count' => $deletedCount,
                'errors' => $errors
            ];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }
}