<?php
// File: modules/templates/publish_template.php

/**
 * API endpoint to publish a template
 */
require_once __DIR__ . '/Templates.php';

// Set headers
header('Content-Type: application/json');

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode([
        'success' => false,
        'message' => 'Only POST requests are allowed'
    ]);
    exit;
}

// Get JSON data from request body
$jsonData = file_get_contents('php://input');
$data = json_decode($jsonData, true);

// Check if required data is provided
if (!isset($data['template_id']) || empty($data['template_id'])) {
    echo json_encode([
        'success' => false,
        'message' => 'Template ID is required'
    ]);
    exit;
}

$templateId = $data['template_id'];

try {
    // Initialize Templates module
    $templates = new Templates();
    
    // Get current template data
    $templateResponse = $templates->get($templateId);
    
    if (!isset($templateResponse['success']) || $templateResponse['success'] !== true) {
        echo json_encode([
            'success' => false,
            'message' => 'Failed to get template data'
        ]);
        exit;
    }
    
    $templateData = $templateResponse['data'];
    
    // Update template status to published
    $updateData = [
        'template_status' => 1 // Published
    ];
    
    // Update template
    $response = $templates->update($templateId, $updateData);
    
    if (isset($response['success']) && $response['success'] === true) {
        echo json_encode([
            'success' => true,
            'message' => 'Template published successfully',
            'data' => isset($response['data']) ? $response['data'] : null
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => isset($response['message']) ? $response['message'] : 'Failed to publish template'
        ]);
    }
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>