<?php
require_once '../../config/config.php';
require_once 'Templates.php';

header('Content-Type: application/json');

if (!isset($_GET['id'])) {
    http_response_code(400);
    echo json_encode(['error' => 'Template ID is required']);
    exit;
}

$templateId = $_GET['id'];
$templatesModule = new Templates();
$result = $templatesModule->get($templateId);

if ($result['success'] && isset($result['data'])) {
    echo json_encode($result);
} else {
    http_response_code(404);
    echo json_encode([
        'success' => false,
        'error' => 'Template not found',
        'debug' => $result
    ]);
}
