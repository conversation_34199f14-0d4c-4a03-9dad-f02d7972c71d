<?php
require_once __DIR__ . '/../../api/ApiClient.php';
require_once __DIR__ . '/../../config/config.php';

class Templates {
    private $apiClientCms;
    private $apiClientV1;
    private $endpoint = 'templates';
    
    public function __construct() {
        // Initialize both API clients with correct base URLs
        $this->apiClientCms = new ApiClient(API_BASE_URL_CMS);
        $this->apiClientV1 = new ApiClient(API_BASE_URL_V1);
    }

    /**
     * Get list of templates using CMS API with proper pagination handling
     */
    public function getList($params = []) {
        try {
            // Set default parameters
            $defaultParams = [
                'page' => 1,
                'sort' => 'id',
                'direction' => 'desc',
                'limit' => 10
            ];

            // Merge provided parameters with defaults
            $params = array_merge($defaultParams, $params);
            
            // Build query parameters
            $queryParams = [];
            
            // Add pagination parameter
            if (!empty($params['page'])) {
                $queryParams['page'] = $params['page'];
            }
            
            // Add limit parameter explicitly
            if (!empty($params['limit'])) {
                $queryParams['limit'] = $params['limit'];
            }

            // Add sorting parameter based on API format - use hyphen prefix for descending
            if (!empty($params['sort'])) {
                $sortColumn = $params['sort'];
                $sortDirection = $params['direction'] ?? 'asc';
                
                // Prefix with minus sign for descending order
                if ($sortDirection === 'desc') {
                    $sortColumn = '-' . $sortColumn;
                }
                
                $queryParams['sort'] = $sortColumn;
            }
            
            // Add any additional filters that might be in the params
            foreach ($params as $key => $value) {
                if (!in_array($key, ['page', 'sort', 'direction', 'limit']) && !empty($value)) {
                    $queryParams[$key] = $value;
                }
            }
            
            // Make the paginated request using CMS API
            $response = $this->apiClientCms->get($this->endpoint, $queryParams);
            
            // Check if response is successful and has data
            if (!isset($response['data'])) {
                return [
                    'success' => false,
                    'message' => 'No data received from API'
                ];
            }
            
            // Extract pagination information directly from the CMS API response
            $paginationData = $response['data'];
            
            // Prepare the return data structure compatible with TableComponent
            // CMS API returns Laravel pagination format
            $returnData = [
                'success' => true,
                'data' => [
                    'data' => $paginationData['data'] ?? [],
                    // CMS API pagination structure (Laravel format)
                    'current_page' => $paginationData['current_page'] ?? 1,
                    'per_page' => $paginationData['per_page'] ?? $params['limit'],
                    'from' => $paginationData['from'] ?? null,
                    'to' => $paginationData['to'] ?? null,
                    'total' => $paginationData['total'] ?? null,
                    'next_page_url' => $paginationData['next_page_url'] ?? null,
                    'prev_page_url' => $paginationData['prev_page_url'] ?? null,
                    'first_page_url' => $paginationData['first_page_url'] ?? null,
                    'last_page_url' => $paginationData['last_page_url'] ?? null
                ]
            ];

            return $returnData;
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage(),
                'data' => [
                    'data' => [],
                    'current_page' => 1,
                    'per_page' => 10,
                    'from' => null,
                    'to' => null,
                    'total' => 0,
                    'next_page_url' => null,
                    'prev_page_url' => null,
                    'first_page_url' => null,
                    'last_page_url' => null
                ]
            ];
        }
    }

    /**
     * Get a single template by ID using CMS API
     */
    public function get($id) {
        try {
            $response = $this->apiClientCms->get($this->endpoint . '/' . $id);
            return [
                'success' => true,
                'data' => $response['data'] ?? null
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Create a new template using V1 API
     */
    public function create($data) {
        try {
            if (!isset($data['created_at'])) {
                $data['created_at'] = date('Y-m-d H:i:s');
            }
            
            // Store categories and tags for separate handling
            $categories = $data['categories'] ?? [];
            $tags = $data['tags'] ?? [];
            
            // Remove categories and tags from template data
            unset($data['categories'], $data['tags']);
            
            // Create template with only template-specific data
            $response = $this->apiClientV1->post($this->endpoint, $data);
            
            if ($response && isset($response['data']['last_insert_id'])) {
                $templateId = $response['data']['last_insert_id'];
                
                // Handle categories separately
                if (!empty($categories)) {
                    $this->createCategoryMappings($templateId, $categories);
                }
                
                // Handle tags separately using CommonTags
                if (!empty($tags)) {
                    $this->handleTagAssociations($templateId, $tags, 'create');
                }
                
                return [
                    'success' => true,
                    'data' => $response['data'],
                    'template_id' => $templateId
                ];
            }
            
            return [
                'success' => true,
                'data' => $response['data'] ?? null
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Update an existing template using V1 API
     */
    public function update($id, $data) {
        try {
            if (isset($data['created_at'])) {
                $data['created_at'] = date('Y-m-d H:i:s', strtotime($data['created_at']));
            }
            
            // Store categories and tags for mapping
            $categories = $data['categories'] ?? [];
            $tags = $data['tags'] ?? [];
            unset($data['categories'], $data['tags']);
            
            // Update template with only template-specific data
            $response = $this->apiClientV1->put($this->endpoint . '/' . $id, $data);
            
            // Handle categories separately
            if (!empty($categories)) { 
                // Delete existing mappings first
                $this->deleteExistingCategoryMappings($id);
                // Create new mappings
                $this->createCategoryMappings($id, $categories);
            }
            // Handle tags separately using CommonTags
            $this->handleTagAssociations($id, $tags, 'update');
            
            return [
                'success' => true,
                'data' => $response['data'] ?? null
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Delete a template
     */
    public function delete($id) {
        // Delete the template
        try {
            // First delete associated tag associations
            $this->deleteExistingTagAssociations($id);

            $response = $this->apiClientV1->delete($this->endpoint . '/' . $id);
            return [
                'success' => true,
                'data' => $response['data'] ?? null
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Upload an image
     */
    public function uploadImage($data) {
        try {
            $response = $this->apiClientCms->post($this->endpoint . '/upload', $data);
            return [
                'success' => true,
                'data' => $response['data'] ?? null
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }
    /**
     * Handle tag associations for both create and update operations
     */
    private function handleTagAssociations($templateId, $tags, $operation = 'create') {
        try {
            require_once __DIR__ . '/../common_tags/CommonTags.php';
            $commonTags = new CommonTags();
            
            // For update operations, first remove existing associations
            if ($operation === 'update') {
                $this->deleteExistingTagAssociations($templateId);
            }
            
            // Create new tag associations (avoid duplicates)
            if (!empty($tags)) {
                $processedTags = array_unique(array_filter($tags)); // Remove duplicates and empty values
                
                foreach ($processedTags as $tagId) {
                    if (!empty($tagId)) {
                        // Check if association already exists
                        $existingAssociations = $commonTags->getByModel('App\\Models\\Template', $templateId);
                        $alreadyExists = false;
                        
                        if ($existingAssociations['success']) {
                            foreach ($existingAssociations['data']['data'] as $existing) {
                                if ($existing['tag_id'] == $tagId) {
                                    $alreadyExists = true;
                                    break;
                                }
                            }
                        }
                        
                        // Only create if doesn't exist
                        if (!$alreadyExists) {
                            $result = $commonTags->create([
                                'model_type' => 'App\\Models\\Template',
                                'model_id' => (int)$templateId,
                                'tag_id' => (int)$tagId
                            ]);
                            
                            if (!$result['success']) {
                                error_log("Error creating template tag association: " . ($result['message'] ?? 'Unknown error'));
                            }
                        }
                    }
                }
            }
        } catch (Exception $e) {
            error_log("Error handling tag associations: " . $e->getMessage());
        }
    }

    /**
     * Get tags associated with a template from CommonTags
     */
    public function getTemplateTags($templateId) {
        try {
            require_once __DIR__ . '/../common_tags/CommonTags.php';
            $commonTags = new CommonTags();
            
            $result = $commonTags->getByModel('App\\Models\\Template', $templateId);
            
            if ($result['success']) {
                return $result['data']['data'] ?? [];
            }
            
            return [];
        } catch (Exception $e) {
            error_log("Error getting template tags: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Delete existing tag associations for a template
     */
    private function deleteExistingTagAssociations($templateId) {
        try {
            require_once __DIR__ . '/../common_tags/CommonTags.php';
            $commonTags = new CommonTags();
            
            $result = $commonTags->removeAllForModel('App\\Models\\Template', $templateId);
            
            if (!$result['success']) {
                error_log("Error deleting template tag associations: " . ($result['message'] ?? 'Unknown error'));
            }
        } catch (Exception $e) {
            error_log("Error deleting template tag associations: " . $e->getMessage());
        }
    }

    /**
     * Create category mappings for a template
     */
    private function createCategoryMappings($templateId, $categories) {
        require_once __DIR__ . '/../template_categories_mapping/TemplateCategoriesMapping.php';
        $mappingClient = new TemplateCategoriesMapping();
        
        foreach ($categories as $categoryId) {
            try {
                $mappingClient->create([
                    'template_id' => $templateId,
                    'template_category_id' => $categoryId
                ]);
            } catch (Exception $e) {
                error_log("Error creating template category mapping: " . $e->getMessage());
            }
        }
    }

    /**
     * Delete existing category mappings for a template
     */
    private function deleteExistingCategoryMappings($templateId) { 
        require_once __DIR__ . '/../template_categories_mapping/TemplateCategoriesMapping.php';
        $mappingClient = new TemplateCategoriesMapping();
        
        try {
            $mappingClient->deleteByTemplateId($templateId);
        } catch (Exception $e) {
            error_log("Error deleting template category mappings: " . $e->getMessage());
        }
    }

    /**
     * Get template with associated tags (for display purposes)
     */
    public function getTemplateWithTags($id) {
        try {
            // Get template data
            $templateResult = $this->get($id);
            
            if (!$templateResult['success']) {
                return $templateResult;
            }
            
            $template = $templateResult['data'];
            
            // Get associated tags
            $tagAssociations = $this->getTemplateTags($id);
            $template['tags'] = [];
            
            if (!empty($tagAssociations)) {
                require_once __DIR__ . '/../tags/Tags.php';
                $tags = new Tags();
                
                foreach ($tagAssociations as $association) {
                    try {
                        $tagResult = $tags->getList(['id' => $association['tag_id'], 'limit' => 1]);
                        if ($tagResult['success'] && !empty($tagResult['data']['data'])) {
                            $template['tags'][] = $tagResult['data']['data'][0];
                        }
                    } catch (Exception $e) {
                        error_log("Error fetching tag details: " . $e->getMessage());
                    }
                }
            }
            
            return [
                'success' => true,
                'data' => $template
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }
}