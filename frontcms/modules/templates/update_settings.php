<?php
// File: modules/templates/update_settings.php

/**
 * API endpoint to update template settings
 */
require_once __DIR__ . '/Templates.php';

// Set headers
header('Content-Type: application/json');

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode([
        'success' => false,
        'message' => 'Only POST requests are allowed'
    ]);
    exit;
}

// Get JSON data from request body
$jsonData = file_get_contents('php://input');
$data = json_decode($jsonData, true);

// Check if required data is provided
if (!isset($data['template_id']) || empty($data['template_id'])) {
    echo json_encode([
        'success' => false,
        'message' => 'Template ID is required'
    ]);
    exit;
}

$templateId = $data['template_id'];

try {
    // Initialize Templates module
    $templates = new Templates();
    
    // Prepare update data
    $updateData = [];
    
    // Add fields if they exist in the request
    if (isset($data['template_status'])) {
        $updateData['template_status'] = $data['template_status'];
    }
    
    if (isset($data['site_title'])) {
        $updateData['site_title'] = $data['site_title'];
    }
    
    if (isset($data['site_description'])) {
        $updateData['site_description'] = $data['site_description'];
    }
    
    if (isset($data['body_font_family'])) {
        $updateData['body_font_family'] = $data['body_font_family'];
    }
    
    if (isset($data['body_font_size'])) {
        $updateData['body_font_size'] = $data['body_font_size'];
    }
    
    if (isset($data['heading_font_family'])) {
        $updateData['heading_font_family'] = $data['heading_font_family'];
    }
    
    if (isset($data['line_height'])) {
        $updateData['line_height'] = $data['line_height'];
    }
    
    if (isset($data['custom_css'])) {
        $updateData['custom_css'] = $data['custom_css'];
    }
    
    if (isset($data['custom_js'])) {
        $updateData['custom_js'] = $data['custom_js'];
    }
    
    if (isset($data['tailwind_configuration_code'])) {
        $updateData['tailwind_configuration_code'] = $data['tailwind_configuration_code'];
    }
    
    // Update template
    $response = $templates->update($templateId, $updateData);
    
    if (isset($response['success']) && $response['success'] === true) {
        echo json_encode([
            'success' => true,
            'message' => 'Template settings updated successfully',
            'data' => isset($response['data']) ? $response['data'] : null
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => isset($response['message']) ? $response['message'] : 'Failed to update template settings'
        ]);
    }
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>