<?php
require_once __DIR__ . '/../../api/ApiClient.php';
require_once __DIR__ . '/../../config/config.php';

class Blocks
{
    private $apiClientCms;
    private $apiClientV1;
    private $endpoint = 'blocks';

    public function __construct()
    {
        // Initialize both API clients with correct base URLs
        $this->apiClientCms = new ApiClient(API_BASE_URL_CMS);
        $this->apiClientV1 = new ApiClient(API_BASE_URL_V1);
    }

    /**
     * Get all blocks with proper pagination handling
     */
    public function getList($params = [])
    {
        try {
            // Set default parameters
            $defaultParams = [
                'page' => 1,
                'sort' => 'id',
                'direction' => 'desc',
                'limit' => 1000
            ];

            // Merge provided parameters with defaults
            $params = array_merge($defaultParams, $params);

            // Build query parameters
            $queryParams = [];

            // Add pagination parameter
            if (!empty($params['page'])) {
                $queryParams['page'] = $params['page'];
            }

            // Add limit parameter explicitly (V1 API uses 'limit')
            if (!empty($params['limit'])) {
                $queryParams['limit'] = $params['limit'];
            }

            // Add sorting parameter based on API format - use hyphen prefix for descending
            if (!empty($params['sort'])) {
                $sortColumn = $params['sort'];
                $sortDirection = $params['direction'] ?? 'asc';

                // Prefix with minus sign for descending order
                if ($sortDirection === 'desc') {
                    $sortColumn = '-' . $sortColumn;
                }

                $queryParams['sort'] = $sortColumn;
            }

            // Add any additional filters that might be in the params
            foreach ($params as $key => $value) {
                if (!in_array($key, ['page', 'sort', 'direction', 'limit']) && !empty($value)) {
                    $queryParams[$key] = $value;
                }
            }

            // Make the paginated request using V1 API
            $response = $this->apiClientV1->get($this->endpoint, $queryParams);

            // Check if response is successful and has data
            if (!isset($response['data'])) {
                return [
                    'success' => false,
                    'message' => 'No data received from API'
                ];
            }

            // Extract pagination information directly from the V1 API response
            $paginationData = $response['data'];

            // Prepare the return data structure compatible with TableComponent
            $returnData = [
                'success' => true,
                'data' => [
                    'data' => $paginationData['data'] ?? [],
                    // V1 API pagination structure
                    'current_page' => $paginationData['current_page'] ?? 1,
                    'per_page' => $paginationData['per_page'] ?? $params['limit'],
                    'from' => $paginationData['from'] ?? null,
                    'to' => $paginationData['to'] ?? null,
                    'total' => $paginationData['total'] ?? null,
                    'next_page_url' => $paginationData['next_page_url'] ?? null,
                    'prev_page_url' => $paginationData['prev_page_url'] ?? null,
                    'first_page_url' => $paginationData['first_page_url'] ?? null,
                    'last_page_url' => $paginationData['last_page_url'] ?? null
                ]
            ];

            return $returnData;
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Get a single block by ID
     */
    public function get($id)
    {
        try {
            // Make the request using V1 API
            $response = $this->apiClientV1->get($this->endpoint . '/' . $id);

            // Check if response is successful
            if (!isset($response['data'])) {
                return [
                    'success' => false,
                    'message' => 'No data received from API'
                ];
            }

            return [
                'success' => true,
                'data' => $response['data'] ?? null
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Get blocks by category ID with proper pagination handling - FIXED VERSION
     */
    public function getBlocksByCategory($category_id, $params = [])
    {
        try {
            // Set default parameters
            $defaultParams = [
                'page' => 1,
                'sort' => 'id',
                'direction' => 'desc',
                'limit' => 10
            ];

            // Merge provided parameters with defaults
            $params = array_merge($defaultParams, $params);

            // Debug logging
            error_log('Blocks.php - getBlocksByCategory params: ' . print_r($params, true));

            // Build query parameters with category ID
            $queryParams = [
                'category_id' => $category_id
            ];

            // Add pagination parameter
            if (!empty($params['page'])) {
                $queryParams['page'] = $params['page'];
            }

            // FIXED: CMS API expects 'per_page' parameter, not 'limit'
            if (!empty($params['limit'])) {
                $queryParams['per_page'] = $params['limit'];
                error_log('Blocks.php - Setting per_page to: ' . $params['limit']);
            }

            // Add sorting parameter based on API format - use hyphen prefix for descending
            if (!empty($params['sort'])) {
                $sortColumn = $params['sort'];
                $sortDirection = $params['direction'] ?? 'asc';

                // Prefix with minus sign for descending order
                if ($sortDirection === 'desc') {
                    $sortColumn = '-' . $sortColumn;
                }

                $queryParams['sort'] = $sortColumn;
            }

            // Add any additional filters that might be in the params
            foreach ($params as $key => $value) {
                if (!in_array($key, ['page', 'sort', 'direction', 'limit']) && !empty($value)) {
                    $queryParams[$key] = $value;
                }
            }

            // Debug: Log the final query parameters
            error_log('Blocks.php - Final query params: ' . print_r($queryParams, true));

            // Make the request using CMS API
            $response = $this->apiClientCms->get('block-list-by-categories', $queryParams);

            // Debug: Log API response
            error_log('Blocks.php - API Response: ' . print_r($response, true));

            // Check if response is successful
            if (!isset($response['data'])) {
                return [
                    'success' => false,
                    'message' => 'No data received from API'
                ];
            }

            // Prepare the return data structure compatible with TableComponent
            // CMS API returns Laravel pagination format
            $returnData = [
                'success' => true,
                'data' => [
                    'data' => $response['data'] ?? [],
                    // CMS API pagination structure (Laravel format)
                    'current_page' => $response['current_page'] ?? 1,
                    'per_page' => $response['per_page'] ?? $params['limit'],
                    'from' => $response['from'] ?? null,
                    'to' => $response['to'] ?? null,
                    'total' => $response['total'] ?? null,
                    'next_page_url' => $response['next_page_url'] ?? null,
                    'prev_page_url' => $response['prev_page_url'] ?? null,
                    'first_page_url' => $response['first_page_url'] ?? null,
                    'last_page_url' => $response['last_page_url'] ?? null,
                    'has_more_pages' => !empty($response['next_page_url']),
                    'has_previous_pages' => !empty($response['prev_page_url'])
                ]
            ];

            // Debug: Log return data
            error_log('Blocks.php - Return data structure: ' . print_r($returnData, true));

            return $returnData;
        } catch (Exception $e) {
            error_log('Blocks.php - Exception: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Create a new block by category
     */
    public function createBlockByCategory($category_id, $data)
    {
        try {
            // Ensure category_id is included in the data
            $data['category_id'] = $category_id;

            // Make the request using CMS API
            $response = $this->apiClientCms->post('block-list-by-categories', $data);

            return [
                'success' => true,
                'data' => $response['data'] ?? null
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Update a block by category
     */
    public function updateBlockByCategory($id, $category_id, $data)
    {
        try {
            // Ensure category_id is included in the data
            $data['category_id'] = $category_id;

            // Make the request using CMS API
            $response = $this->apiClientCms->put('block-list-by-categories/' . $id, $data);

            return [
                'success' => true,
                'data' => $response['data'] ?? null
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Delete a block by category
     */
    public function deleteBlockByCategory($id, $category_id)
    {
        try {
            // Make the request using CMS V1 API
            $response = $this->apiClientV1->delete('blocks/' . $id);
            $this->deleteBlockCategoryMapping($category_id);

            return [
                'success' => true,
                'data' => $response['data'] ?? null
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Create a new block
     */
    public function create($data)
    {
        try {
            if (!isset($data['created_at'])) {
                $data['created_at'] = date('Y-m-d H:i:s');
            }

            // Store tags for separate handling
            $tags = $data['tags'] ?? [];

            // Remove tags from block data
            unset($data['tags']);

            // Create block with only block-specific data
            $response = $this->apiClientV1->post($this->endpoint, $data);

            if ($response && isset($response['data']['last_insert_id'])) {
                $blockId = $response['data']['last_insert_id'];

                // Handle tags separately using CommonTags
                if (!empty($tags)) {
                    $this->handleTagAssociations($blockId, $tags, 'create');
                }

                return [
                    'success' => true,
                    'data' => $response['data'],
                    'block_id' => $blockId
                ];
            }

            return [
                'success' => true,
                'data' => $response['data'] ?? null
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Update a block
     */
    public function update($id, $data)
    {
        try {
            if (isset($data['created_at'])) {
                $data['created_at'] = date('Y-m-d H:i:s', strtotime($data['created_at']));
            }

            // Store tags for separate handling
            $tags = $data['tags'] ?? [];
            unset($data['tags']);

            // Update block with only block-specific data
            $response = $this->apiClientV1->put($this->endpoint . '/' . $id, $data);

            // Handle tags separately using CommonTags
            $this->handleTagAssociations($id, $tags, 'update');

            return [
                'success' => true,
                'data' => $response['data'] ?? null
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Delete a block
     */
    public function delete($id)
    {
        try {
            // First delete associated tag associations
            $this->deleteExistingTagAssociations($id);

            $response = $this->apiClientV1->delete($this->endpoint . '/' . $id);
            return [
                'success' => true,
                'data' => $response['data'] ?? null
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Handle tag associations for both create and update operations
     */
    private function handleTagAssociations($blockId, $tags, $operation = 'create')
    {
        try {
            require_once __DIR__ . '/../common_tags/CommonTags.php';
            $commonTags = new CommonTags();

            // For update operations, first remove existing associations
            if ($operation === 'update') {
                $this->deleteExistingTagAssociations($blockId);
            }

            // Create new tag associations (avoid duplicates)
            if (!empty($tags)) {
                $processedTags = array_unique(array_filter($tags)); // Remove duplicates and empty values

                foreach ($processedTags as $tagId) {
                    if (!empty($tagId)) {
                        // Check if association already exists
                        $existingAssociations = $commonTags->getByModel('App\\Models\\Block', $blockId);
                        $alreadyExists = false;

                        if ($existingAssociations['success']) {
                            foreach ($existingAssociations['data']['data'] as $existing) {
                                if ($existing['tag_id'] == $tagId) {
                                    $alreadyExists = true;
                                    break;
                                }
                            }
                        }

                        // Only create if doesn't exist
                        if (!$alreadyExists) {
                            $result = $commonTags->create([
                                'model_type' => 'App\\Models\\Block',
                                'model_id' => (int)$blockId,
                                'tag_id' => (int)$tagId
                            ]);

                            if (!$result['success']) {
                                error_log("Error creating block tag association: " . ($result['message'] ?? 'Unknown error'));
                            }
                        }
                    }
                }
            }
        } catch (Exception $e) {
            error_log("Error handling tag associations: " . $e->getMessage());
        }
    }

    /**
     * Get tags associated with a block from CommonTags
     */
    public function getBlockTags($blockId)
    {
        try {
            require_once __DIR__ . '/../common_tags/CommonTags.php';
            $commonTags = new CommonTags();

            $result = $commonTags->getByModel('App\\Models\\Block', $blockId);

            if ($result['success']) {
                return $result['data']['data'] ?? [];
            }

            return [];
        } catch (Exception $e) {
            error_log("Error getting block tags: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Delete existing tag associations for a block
     */
    private function deleteExistingTagAssociations($blockId)
    {
        try {
            require_once __DIR__ . '/../common_tags/CommonTags.php';
            $commonTags = new CommonTags();

            $result = $commonTags->removeAllForModel('App\\Models\\Block', $blockId);

            if (!$result['success']) {
                error_log("Error deleting block tag associations: " . ($result['message'] ?? 'Unknown error'));
            }
        } catch (Exception $e) {
            error_log("Error deleting block tag associations: " . $e->getMessage());
        }
    }

    /**
     * Get block with associated tags (for display purposes)
     */
    public function getBlockWithTags($id)
    {
        try {
            // Get block data
            $blockResult = $this->get($id);

            if (!$blockResult['success']) {
                return $blockResult;
            }

            $block = $blockResult['data'];

            // Get associated tags
            $tagAssociations = $this->getBlockTags($id);
            $block['tags'] = [];

            if (!empty($tagAssociations)) {
                require_once __DIR__ . '/../tags/Tags.php';
                $tags = new Tags();

                foreach ($tagAssociations as $association) {
                    try {
                        $tagResult = $tags->getList(['id' => $association['tag_id'], 'limit' => 1]);
                        if ($tagResult['success'] && !empty($tagResult['data']['data'])) {
                            $block['tags'][] = $tagResult['data']['data'][0];
                        }
                    } catch (Exception $e) {
                        error_log("Error fetching tag details: " . $e->getMessage());
                    }
                }
            }

            return [
                'success' => true,
                'data' => $block
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Get all block categories with proper pagination handling
     */
    public function getBlockCategories($params = [])
    {
        try {
            // Set default parameters
            $defaultParams = [
                'page' => 1,
                'sort' => 'id',
                'direction' => 'asc',
                'limit' => 10,
                'per_page' => 10
            ];

            // Merge provided parameters with defaults
            $params = array_merge($defaultParams, $params);

            // Build query parameters
            $queryParams = [];

            // Add pagination parameter
            if (!empty($params['page'])) {
                $queryParams['page'] = $params['page'];
            }

            // Add limit parameter explicitly (CMS uses different parameter for categories)
            if (!empty($params['limit'])) {
                $queryParams['limit'] = $params['limit'];
            }

             // Add per_page parameter explicitly (CMS uses different parameter for categories)
             if (!empty($params['per_page'])) {
                $queryParams['per_page'] = $params['per_page'];
            }

            // Add sorting parameter based on API format - use hyphen prefix for descending
            if (!empty($params['sort'])) {
                $sortColumn = $params['sort'];
                $sortDirection = $params['direction'] ?? 'asc';

                // Prefix with minus sign for descending order
                if ($sortDirection === 'desc') {
                    $sortColumn = '-' . $sortColumn;
                }

                $queryParams['sort'] = $sortColumn;
            }

            // Add any additional filters that might be in the params
            foreach ($params as $key => $value) {
                if (!in_array($key, ['page', 'sort', 'direction', 'limit', 'per_page']) && !empty($value)) {
                    $queryParams[$key] = $value;
                }
            }

            // Make the request using CMS API
            $response = $this->apiClientCms->get('block-categories', $queryParams);

            // Check if response is successful
            if (!isset($response['data'])) {
                return [
                    'success' => false,
                    'message' => 'No data received from API'
                ];
            }

            // Prepare the return data structure compatible with TableComponent
            // CMS API returns Laravel pagination format
            $returnData = [
                'success' => true,
                'data' => [
                    'data' => $response['data']['data'] ?? [],
                    // CMS API pagination structure (Laravel format)
                    'current_page' => $response['data']['current_page'] ?? 1,
                    'per_page' => $response['data']['per_page'] ?? $params['limit'],
                    'from' => $response['data']['from'] ?? null,
                    'to' => $response['data']['to'] ?? null,
                    'total' => $response['data']['total'] ?? null,
                    'next_page_url' => $response['data']['next_page_url'] ?? null,
                    'prev_page_url' => $response['data']['prev_page_url'] ?? null,
                    'first_page_url' => $response['data']['first_page_url'] ?? null,
                    'last_page_url' => $response['data']['last_page_url'] ?? null
                ]
            ];

            return $returnData;
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Create a block category
     */
    public function createBlockCategory($data)
    {
        try {
            $response = $this->apiClientV1->post('block_categories', $data);
            return [
                'success' => true,
                'data' => $response['data'] ?? null
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Update a block category
     */
    public function updateBlockCategory($id, $data)
    {
        try {
            $response = $this->apiClientV1->put('block_categories/' . $id, $data);
            return [
                'success' => true,
                'data' => $response['data'] ?? null
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Delete a block category
     */
    public function deleteBlockCategory($id)
    {
        try {
            $response = $this->apiClientV1->delete('block_categories/' . $id);
            return [
                'success' => true,
                'data' => $response['data'] ?? null
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Get block category mappings
     */
    public function getBlockCategoryMappings($params = [])
    {
        try {
            // Build query parameters
            $queryParams = $params;

            // Make the request using V1 API
            $response = $this->apiClientV1->get('block_categories_mapping', $queryParams);

            // Check if response is successful
            if (!isset($response['data'])) {
                return [
                    'success' => false,
                    'message' => 'No data received from API'
                ];
            }

            return [
                'success' => true,
                'data' => $response['data'] ?? []
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Create a block category mapping
     */
    public function createBlockCategoryMapping($data)
    {
        try {
            $response = $this->apiClientV1->post('block_categories_mapping', $data);
            return [
                'success' => true,
                'data' => $response['data'] ?? null
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Update a block category mapping
     */
    public function updateBlockCategoryMapping($id, $data)
    {
        try {
            $response = $this->apiClientV1->put('block_categories_mapping/' . $id, $data);
            return [
                'success' => true,
                'data' => $response['data'] ?? null
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Delete a block category mapping
     */
    public function deleteBlockCategoryMapping($id)
    {
        try {
            $response = $this->apiClientV1->delete('block_categories_mapping/' . $id);
            return [
                'success' => true,
                'data' => $response['data'] ?? null
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }
}
