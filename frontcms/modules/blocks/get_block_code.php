<?php 
/**
 * API endpoint to get code for a specific block
 */

require_once __DIR__ . '/Blocks.php';

// Set headers
header('Content-Type: application/json');

// Check if block_id is provided
if (!isset($_GET['block_id']) || empty($_GET['block_id'])) {
    echo json_encode([
        'success' => false,
        'message' => 'Block ID is required'
    ]);
    exit;
}

$blockId = $_GET['block_id'];

try {
    // Initialize Blocks module
    $blocks = new Blocks();
    
    // Get block details
    $response = $blocks->get($blockId);


    if (isset($response['success']) && $response['success'] === true) {
        $block = null;
        
        // Extract block from response
        if (isset($response['data'])) {
            $block = $response['data'];
            
            // Ensure all code fields exist
            $block['tmpl_code'] = isset($block['tmpl_code']) ? $block['tmpl_code'] : '';
            $block['json_code'] = isset($block['json_code']) ? $block['json_code'] : '{}';
            $block['css_code'] = isset($block['css_code']) ? $block['css_code'] : '';
            $block['js_code'] = isset($block['js_code']) ? $block['js_code'] : '';
        }
        
        echo json_encode([
            'success' => true,
            'block' => $block
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => isset($response['message']) ? $response['message'] : 'Failed to get block code'
        ]);
    }
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
