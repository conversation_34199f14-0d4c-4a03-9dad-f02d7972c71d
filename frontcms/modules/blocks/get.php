<?php
/**
 * API endpoint to get multiple blocks by IDs
 */

require_once __DIR__ . '/Blocks.php';

// Set headers
header('Content-Type: application/json');

// Get block IDs from request
$blockIds = [];
if (isset($_GET['ids']) && !empty($_GET['ids'])) {
    $blockIds = explode(',', $_GET['ids']);
}

if (empty($blockIds)) {
    echo json_encode([
        'success' => false,
        'message' => 'No block IDs provided'
    ]);
    exit;
}

try {
    // Initialize Blocks module
    $blocks = new Blocks();
    
    $blocksData = [];
    
    // Get each block by ID
    foreach ($blockIds as $blockId) {
        $response = $blocks->get($blockId);
        
        if (isset($response['success']) && $response['success'] === true && isset($response['data'])) {
            $blocksData[] = $response['data'];
        }
    }
    
    echo json_encode([
        'success' => true,
        'blocks' => $blocksData
    ]);
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}