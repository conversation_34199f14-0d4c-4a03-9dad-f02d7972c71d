<?php
require_once __DIR__ . '/../../api/ApiClient.php';

class TemplateCategoriesMapping {
    private $apiClient;
    private $endpoint = 'template_categories_mapping';
    
    public function __construct() {
        // Use v1 API for mapping
        $this->apiClient = new ApiClient(API_BASE_URL_V1);
    }

    /**
     * Create a new template category mapping
     */
    public function create($data) {
        return $this->apiClient->post($this->endpoint, $data);
    }

    /**
     * Delete all mappings for a template
     */
    public function deleteByTemplateId($templateId) { 
        return $this->apiClient->delete($this->endpoint . '/template_id/' . $templateId); 
    }
}
