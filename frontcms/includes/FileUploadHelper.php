<?php
class FileUploadHelper {
    private $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/svg+xml'];
    private $maxFileSize = 5242880; // 5MB in bytes
    private $uploadBaseDir;
    private $module;
    private $type;
    private $baseUrl;

    public function __construct($module, $type = '') {
        $this->module = $module;
        $this->type = $type;
        // Fix the upload base directory path
        $this->uploadBaseDir = __DIR__ . '/../uploads';
        if (!file_exists($this->uploadBaseDir)) {
            if (!mkdir($this->uploadBaseDir, 0777, true)) {
                throw new Exception('Failed to create upload directory');
            }
            chmod($this->uploadBaseDir, 0777);
        }
        $this->baseUrl = defined('BASE_URL') ? BASE_URL : '';
    }

    /**
     * Handle file upload for a specific module
     * @param array $file The $_FILES array element
     * @param string $customPrefix Optional custom prefix for the file name
     * @return array ['success' => bool, 'url' => string|null, 'message' => string|null]
     */
    public function handleUpload($file, $customPrefix = '') {
        try {
            $this->validateUpload($file);
            
            $uploadDir = $this->createUploadDirectory();
            $uniqueName = $this->generateUniqueFilename($file['name'], $customPrefix);
            $targetPath = $uploadDir . '/' . $uniqueName;
            
            // Debug information
            error_log("Uploading file to: " . $targetPath);
            error_log("File tmp_name: " . $file['tmp_name']);
            error_log("File size: " . $file['size']);
            error_log("File type: " . $file['type']);
            
            if (!move_uploaded_file($file['tmp_name'], $targetPath)) {
                $error = error_get_last();
                throw new Exception('Failed to move uploaded file. Error: ' . ($error['message'] ?? 'Unknown error'));
            }

            // Fix file permissions
            chmod($targetPath, 0777);
            
            // Generate relative URL path
            $relativePath = '/uploads/' . $this->module;
            if ($this->type) {
                $relativePath .= '/' . $this->type;
            }
            $relativePath .= '/' . $uniqueName;
            
            error_log("File uploaded successfully to: " . $targetPath);
            error_log("URL generated: " . $relativePath);
            
            return [
                'success' => true,
                'url' => $relativePath,
                'message' => 'File uploaded successfully'
            ];

        } catch (Exception $e) {
            error_log("Upload error: " . $e->getMessage());
            return [
                'success' => false,
                'url' => null,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Delete a file from the uploads directory
     * @param string $filePath Relative path from uploads directory
     * @return bool
     */
    public function deleteFile($filePath) {
        // Remove leading slash if present
        $filePath = ltrim($filePath, '/');
        
        // Remove BASE_URL if present
        $filePath = str_replace($this->baseUrl, '', $filePath);
        
        // Ensure the file is within our uploads directory
        $validPath = 'uploads/' . $this->module;
        if ($this->type) {
            $validPath .= '/' . $this->type;
        }
        if (strpos($filePath, $validPath) !== 0) {
            return false;
        }

        $fullPath = $this->uploadBaseDir . '/' . $this->module;
        if ($this->type) {
            $fullPath .= '/' . $this->type;
        }
        $fullPath .= '/' . basename($filePath);
        
        if (file_exists($fullPath)) {
            return unlink($fullPath);
        }
        
        return false;
    }

    /**
     * Validate the uploaded file
     * @throws Exception if validation fails
     */
    private function validateUpload($file) {
        if ($file['error'] !== UPLOAD_ERR_OK) {
            throw new Exception('Error uploading file: ' . $this->getUploadErrorMessage($file['error']));
        }

        if ($file['size'] > $this->maxFileSize) {
            throw new Exception('File size exceeds limit of ' . ($this->maxFileSize / 1024 / 1024) . 'MB');
        }

        if (!in_array($file['type'], $this->allowedTypes)) {
            throw new Exception('Invalid file type. Allowed types: ' . implode(', ', array_map(function($type) {
                return str_replace('image/', '', $type);
            }, $this->allowedTypes)));
        }
    }

    /**
     * Create the upload directory if it doesn't exist
     * @return string The full path to the upload directory
     */
    private function createUploadDirectory() {
        $uploadDir = $this->uploadBaseDir . '/' . $this->module;
        if ($this->type) {
            $uploadDir .= '/' . $this->type;
        }
        
        if (!file_exists($uploadDir)) {
            if (!mkdir($uploadDir, 0777, true)) {
                throw new Exception('Failed to create upload directory');
            }
            chmod($uploadDir, 0777);
        }
        
        return $uploadDir;
    }

    /**
     * Generate a unique filename
     * @param string $originalName Original filename
     * @param string $customPrefix Optional prefix for the filename
     * @return string
     */
    private function generateUniqueFilename($originalName, $customPrefix = '') {
        $extension = pathinfo($originalName, PATHINFO_EXTENSION);
        $filename = $customPrefix . uniqid() . '.' . $extension;
        return $filename;
    }

    /**
     * Get upload error message
     * @param int $code PHP upload error code
     * @return string
     */
    private function getUploadErrorMessage($code) {
        switch ($code) {
            case UPLOAD_ERR_INI_SIZE:
                return 'The uploaded file exceeds the upload_max_filesize directive in php.ini';
            case UPLOAD_ERR_FORM_SIZE:
                return 'The uploaded file exceeds the MAX_FILE_SIZE directive in the HTML form';
            case UPLOAD_ERR_PARTIAL:
                return 'The uploaded file was only partially uploaded';
            case UPLOAD_ERR_NO_FILE:
                return 'No file was uploaded';
            case UPLOAD_ERR_NO_TMP_DIR:
                return 'Missing a temporary folder';
            case UPLOAD_ERR_CANT_WRITE:
                return 'Failed to write file to disk';
            case UPLOAD_ERR_EXTENSION:
                return 'A PHP extension stopped the file upload';
            default:
                return 'Unknown upload error';
        }
    }
}
