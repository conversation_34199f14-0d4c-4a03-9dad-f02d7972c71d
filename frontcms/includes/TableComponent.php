<?php

/**
 * Complete Enhanced Table Component with Grid View Support and Simple Pagination
 * FIXED VERSION - Search and Sorting Issues Resolved
 */
class TableComponent
{
    private $columns = [];
    private $data = [];
    private $pagination = [];
    private $sortColumn = '';
    private $sortDirection = 'asc';
    private $tableId = 'dataTable';
    private $tableCustomClass = '';
    private $searchable = true;
    private $searchId = 'tableSearch';
    private $actionButtons = [];
    private $rowActions = [];
    private $viewButtons = [];
    private $gridConfig = [];
    private $defaultView = 'table'; // 'table' or 'grid'
    
    // NEW: Title and Breadcrumb properties
    private $pageTitle = '';
    private $breadcrumbs = [];
    private $showTitleBreadcrumb = false;

    /**
     * Constructor
     */
    public function __construct($data, $columns, $pagination = null)
    {
        $this->data = $data;
        $this->columns = $columns;

        // Get current page and limit from URL
        $current_page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
        $current_limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 10;

        // Initialize pagination with provided data or defaults
        if (is_array($pagination)) {
            $this->pagination = [
                'current_page' => $pagination['current_page'] ?? $current_page,
                'per_page' => $pagination['per_page'] ?? $current_limit,
                'total' => $pagination['total'] ?? null,
                'from' => $pagination['from'] ?? null,
                'to' => $pagination['to'] ?? null,
                'next_page_url' => $pagination['next_page_url'] ?? null,
                'prev_page_url' => $pagination['prev_page_url'] ?? null,
                'first_page_url' => $pagination['first_page_url'] ?? null,
                'last_page_url' => $pagination['last_page_url'] ?? null,
                'has_more_pages' => !empty($pagination['next_page_url']),
                'has_previous_pages' => !empty($pagination['prev_page_url'])
            ];
        } else {
            $this->pagination = [
                'current_page' => 1,
                'per_page' => $current_limit,
                'total' => count($data),
                'from' => null,
                'to' => null,
                'next_page_url' => null,
                'prev_page_url' => null,
                'first_page_url' => null,
                'last_page_url' => null,
                'has_more_pages' => false,
                'has_previous_pages' => false
            ];
        }

        // Set default sort from URL parameters
        if (isset($_GET['sort'])) {
            $sort = $_GET['sort'];
            if (substr($sort, 0, 1) === '-') {
                $this->sortColumn = substr($sort, 1);
                $this->sortDirection = 'desc';
            } else {
                $this->sortColumn = $sort;
                $this->sortDirection = 'asc';
            }
        }
    }

    /**
     * NEW: Set page title and breadcrumb
     */
    public function setTitleBreadcrumb($title, $breadcrumbs = []) {
        $this->pageTitle = $title;
        $this->breadcrumbs = $breadcrumbs;
        $this->showTitleBreadcrumb = true;
        return $this;
    }
    
    /**
     * NEW: Set only page title
     */
    public function setPageTitle($title) {
        $this->pageTitle = $title;
        $this->showTitleBreadcrumb = true;
        return $this;
    }
    
    /**
     * NEW: Set only breadcrumbs
     */
    public function setBreadcrumbs($breadcrumbs) {
        $this->breadcrumbs = $breadcrumbs;
        $this->showTitleBreadcrumb = true;
        return $this;
    }

    /**
     * Set grid view configuration
     */
    public function setGridConfig($config)
    {
        $defaultGridConfig = [
            'enabled' => false,
            'columns' => 'grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6',
            'cardTemplate' => null,
            'imageKey' => 'image',
            'titleKey' => 'name',
            'subtitleKey' => 'id',
            'descriptionKey' => 'description',
            'metaKey' => 'created_at',
            'actions' => [],
            'cardClass' => 'group bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-200 overflow-hidden border border-gray-200',
            'imageClass' => 'h-28 rounded-lg overflow-hidden',
            'titleClass' => 'text-sm font-medium text-gray-800 mb-2 truncate',
            'subtitleClass' => 'text-xs text-gray-500 mb-2',
            'descriptionClass' => 'text-xs text-gray-500 mb-3 line-clamp-2 flex-grow',
            'layout' => 'default' // 'default', 'horizontal', 'minimal', 'detailed'
        ];

        $this->gridConfig = array_merge($defaultGridConfig, $config);
        return $this;
    }

    /**
     * Set default view
     */
    public function setDefaultView($view)
    {
        $this->defaultView = $view;
        return $this;
    }

    /**
     * Set view buttons
     */
    public function setViewButtons($buttons)
    {
        $this->viewButtons = $buttons;
        return $this;
    }

    /**
     * Set table ID
     */
    public function setTableId($id)
    {
        $this->tableId = $id;
        return $this;
    }
    /**
     * Set table ID
     */
    public function setTableCustomClass($class)
    {
        $this->tableCustomClass = $class;
        return $this;
    }

    /**
     * Set search configuration
     */
    public function setSearchConfig($searchable, $searchId = 'tableSearch')
    {
        $this->searchable = $searchable;
        $this->searchId = $searchId;
        return $this;
    }

    /**
     * Set action buttons
     */
    public function setActionButtons($buttons)
    {
        $this->actionButtons = $buttons;
        return $this;
    }

    /**
     * Set row actions
     */
    public function setRowActions($actions)
    {
        $this->rowActions = $actions;
        return $this;
    }

    /**
     * Main render method
     */
    public function render() {
        $output = '';
        $output .= '<header class="bg-white py-3 border-b border-gray-200 mx-4"><div class="flex items-center justify-between">';
        // NEW: Render title and breadcrumb if enabled
        if ($this->showTitleBreadcrumb) {
            $output .= $this->renderTitleBreadcrumb();
        }
        
        $output .= $this->renderActionBar();
        $output .= '</div></header>';
        $output .= $this->renderTableView();

        if ($this->gridConfig['enabled']) {
            $output .= $this->renderGridView();
        }

        $output .= $this->renderPagination();
        $output .= $this->renderJavaScript();

        return $output;
    }

    /**
     * NEW: Render title and breadcrumb section
     */
    private function renderTitleBreadcrumb() {
        $output = '';
        
        // Left section: Title and Breadcrumb
        $output .= '<div class="flex items-center">';
        
        // Breadcrumb and Title together
        if (!empty($this->breadcrumbs) || !empty($this->pageTitle)) {
            $output .= '<div class="flex flex-col">';
            // Title
            if (!empty($this->pageTitle)) {
                $output .= '<h1 class="text-xl font-semibold text-gray-900">' . htmlspecialchars($this->pageTitle) . '</h1>';
            }

            // Breadcrumb
            if (!empty($this->breadcrumbs)) {
                $output .= '<nav class="flex items-center space-x-2 text-[12px] text-gray-500">';
                foreach ($this->breadcrumbs as $index => $breadcrumb) {
                    if ($index > 0) {
                        $output .= '<span>/</span>';
                    }
                    
                    if (isset($breadcrumb['url']) && !empty($breadcrumb['url'])) {
                        $output .= '<a href="' . htmlspecialchars($breadcrumb['url']) . '" class="hover:text-[#0C5BE2] transition-colors">';
                        $output .= htmlspecialchars($breadcrumb['label']);
                        $output .= '</a>';
                    } else {
                        $output .= '<span class="text-gray-700">' . htmlspecialchars($breadcrumb['label']) . '</span>';
                    }
                }
                $output .= '</nav>';
            }
            
            $output .= '</div>';
        }
        
        $output .= '</div>';
        
        return $output;
    }

    /**
     * Render action bar
     */
    private function renderActionBar()
    {
        // Auto-add view toggle buttons if grid is enabled and no custom buttons set
        if ($this->gridConfig['enabled'] && empty($this->viewButtons)) {
            $gridActiveClass = $this->defaultView === 'grid' ? 'active' : '';
            $tableActiveClass = $this->defaultView === 'table' ? 'active' : '';

            $this->viewButtons = [
                [
                    'label' => 'Grid View',
                    'icon' => 'layout-grid',
                    'attributes' => [
                        'onclick' => "toggleView('grid')",
                        'class' => "view-toggle {$gridActiveClass} p-1.5 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-100 transition-all",
                        'data-view' => 'grid'
                    ]
                ],
                [
                    'label' => 'List View',
                    'icon' => 'align-justify',
                    'attributes' => [
                        'onclick' => "toggleView('table')",
                        'class' => "view-toggle {$tableActiveClass} p-1.5 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-100 transition-all",
                        'data-view' => 'table'
                    ]
                ]
            ];
        }
        
        $output = '<div>';
        $output .= '<div class="flex justify-between items-center">';

        // Search input with improved styling
        if ($this->searchable) {
            $searchValue = isset($_GET['search']) ? htmlspecialchars($_GET['search']) : '';
            $output .= '<div class="flex items-center gap-4 mr-4">';
            $output .= '<div class="relative flex-1 max-w-md">';
            $output .= '<div class="absolute inset-y-0 start-0 flex items-center ps-3 pointer-events-none">';
            $output .= '<svg class="w-4 h-4 text-gray-500" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 20">';
            $output .= '<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z"/>';
            $output .= '</svg>';
            $output .= '</div>';
            $output .= '<input type="text" id="' . $this->searchId . '" ';
            $output .= 'class="block w-full p-2 ps-10 text-sm text-gray-900 border border-gray-300 rounded-lg bg-white focus:ring-blue-500 focus:border-blue-500" ';
            $output .= 'placeholder="Search..." value="' . $searchValue . '">';
            $output .= '</div>';
            $output .= '</div>';
        } else {
            $output .= '<div></div>';
        }

        // View toggle buttons with better styling
        if (!empty($this->viewButtons)) {
            $output .= '<div class="ms-auto me-2 flex items-center border rounded-lg overflow-hidden bg-white">';

            foreach ($this->viewButtons as $button) {
                $btnClass = isset($button['attributes']['class']) ? $button['attributes']['class'] : 'p-2 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-100 transition-all';
                $btnAttr = '';

                if (isset($button['attributes'])) {
                    foreach ($button['attributes'] as $attr => $value) {
                        if ($attr !== 'class') {
                            $btnAttr .= ' ' . $attr . '="' . htmlspecialchars($value) . '"';
                        }
                    }
                }

                $output .= '<button type="button" class="' . $btnClass . '"' . $btnAttr . '>';

                if (isset($button['icon'])) {
                    $output .= '<i data-lucide="' . $button['icon'] . '" class="w-4 h-4"></i>';
                }

                $output .= '</button>';
            }

            $output .= '</div>';
        }

        // Action buttons
        if (!empty($this->actionButtons)) {
            $output .= '<div class="flex items-center gap-2">';

            foreach ($this->actionButtons as $button) {
                $btnClass = isset($button['class']) ? $button['class'] : 'text-xs text-white bg-[#0C5BE2] px-2 py-1.5 rounded hover:bg-blue-700 flex items-center gap-1';
                $btnAttr = '';

                if (isset($button['attributes'])) {
                    foreach ($button['attributes'] as $attr => $value) {
                        $btnAttr .= ' ' . $attr . '="' . htmlspecialchars($value) . '"';
                    }
                }

                $output .= '<button type="button" class="' . $btnClass . '"' . $btnAttr . '>';

                if (isset($button['icon'])) {
                    $output .= '<i data-lucide="' . $button['icon'] . '" class="w-3.5 h-3.5"></i>';
                }

                $output .= '<span>' . htmlspecialchars($button['label']) . '</span>';
                $output .= '</button>';
            }

            $output .= '</div>';
        }

        $output .= '</div>';
        $output .= '</div>';

        return $output;
    }

    /**
     * Render table view
     */
    private function renderTableView()
    {
        $hiddenClass = ($this->defaultView === 'grid' && $this->gridConfig['enabled']) ? 'table-view-hidden' : '';

        $output = '<div class="table-view ' . $hiddenClass . ' w-full">';
        $output .= '<div class="p-6 tableWrapper ' . $this->tableCustomClass . '"><div class="relative overflow-x-auto border overflow-hidden border-gray-200 rounded-xl gridTableOverflowWrapper">';
        $output .= '<table class="w-full text-sm text-left rtl:text-right text-gray-500" id="' . $this->tableId . '">';

        $output .= $this->renderTableHeader();
        $output .= $this->renderTableBody();

        $output .= '</table>';
        $output .= '</div></div>';
        $output .= '</div>';

        return $output;
    }

    /**
     * Render grid view
     */
    private function renderGridView()
    {
        if (!$this->gridConfig['enabled']) {
            return '';
        }

        $hiddenClass = $this->defaultView === 'table' ? 'grid-view-hidden' : '';
        $gridColumns = $this->gridConfig['columns'];

        $output = '<div class="grid-view ' . $hiddenClass . ' grid ' . $gridColumns . ' gap-4 mb-6 p-6">';

        if (empty($this->data)) {
            $output .= '<div class="col-span-full text-center text-gray-500 py-8">No data found</div>';
        } else {
            foreach ($this->data as $item) {
                if (isset($this->gridConfig['cardTemplate']) && is_callable($this->gridConfig['cardTemplate'])) {
                    $output .= call_user_func($this->gridConfig['cardTemplate'], $item, $this->gridConfig);
                } else {
                    $output .= $this->renderCard($item);
                }
            }
        }

        $output .= '</div>';

        return $output;
    }

    /**
     * Render card based on layout
     */
    private function renderCard($item)
    {
        $layout = $this->gridConfig['layout'] ?? 'default';

        switch ($layout) {
            case 'horizontal':
                return $this->renderHorizontalCard($item);
            case 'minimal':
                return $this->renderMinimalCard($item);
            case 'detailed':
                return $this->renderDetailedCard($item);
            default:
                return $this->renderDefaultCard($item);
        }
    }

    /**
     * Default card layout
     */
    private function renderDefaultCard($item)
    {
        $imageKey = $this->gridConfig['imageKey'];
        $titleKey = $this->gridConfig['titleKey'];
        $subtitleKey = $this->gridConfig['subtitleKey'];
        $descriptionKey = $this->gridConfig['descriptionKey'];

        $image = isset($item[$imageKey]) ? $item[$imageKey] : '';
        $title = isset($item[$titleKey]) ? $item[$titleKey] : '';
        $subtitle = isset($item[$subtitleKey]) ? $item[$subtitleKey] : '';
        $description = isset($item[$descriptionKey]) ? $item[$descriptionKey] : '';
        
        $output = '<div class="grid-item ' . $this->gridConfig['cardClass'] . '">';
        $output .= '<div class="flex flex-col h-full">';

        // Image section
        if ($image) {
            $output .= '<div class="relative p-3 pb-0">';
            $output .= '<div class="' . $this->gridConfig['imageClass'] . '">';
            $output .= '<img src="' . htmlspecialchars($image) . '" alt="' . htmlspecialchars($title) . '" class="w-full h-full object-cover">';
            $output .= '</div>';
            $output .= '</div>';
        }

        // Content section
        $output .= '<div class="p-3 flex flex-col flex-grow">';

        if ($title) {
            $output .= '<h3 class="' . $this->gridConfig['titleClass'] . '">' . htmlspecialchars($title) . '</h3>';
        }

        if ($subtitle) {
            $output .= '<div class="' . $this->gridConfig['subtitleClass'] . '">ID: ' . htmlspecialchars($subtitle) . '</div>';
        }

        if ($description) {
            $output .= '<p class="' . $this->gridConfig['descriptionClass'] . '">' . htmlspecialchars($description) . '</p>';
        }

        // Actions
        $output .= $this->renderCardActions($item);

        $output .= '</div>';
        $output .= '</div>';
        $output .= '</div>';

        return $output;
    }

    /**
     * Horizontal card layout
     */
    private function renderHorizontalCard($item)
    {
        $imageKey = $this->gridConfig['imageKey'];
        $titleKey = $this->gridConfig['titleKey'];
        $subtitleKey = $this->gridConfig['subtitleKey'];
        $descriptionKey = $this->gridConfig['descriptionKey'];

        $image = isset($item[$imageKey]) ? $item[$imageKey] : '';
        $title = isset($item[$titleKey]) ? $item[$titleKey] : '';
        $subtitle = isset($item[$subtitleKey]) ? $item[$subtitleKey] : '';
        $description = isset($item[$descriptionKey]) ? $item[$descriptionKey] : '';
        
        $output = '<div class="grid-item ' . $this->gridConfig['cardClass'] . '">';
        $output .= '<div class="flex h-full">';

        // Image section (left side)
        if ($image) {
            $output .= '<div class="w-1/3 p-3">';
            $output .= '<div class="h-full rounded-lg overflow-hidden">';
            $output .= '<img src="' . htmlspecialchars($image) . '" alt="' . htmlspecialchars($title) . '" class="w-full h-full object-cover">';
            $output .= '</div>';
            $output .= '</div>';
        }

        // Content section (right side)
        $output .= '<div class="flex-1 p-3 flex flex-col">';

        if ($title) {
            $output .= '<h3 class="' . $this->gridConfig['titleClass'] . '">' . htmlspecialchars($title) . '</h3>';
        }

        if ($subtitle) {
            $output .= '<div class="' . $this->gridConfig['subtitleClass'] . '">ID: ' . htmlspecialchars($subtitle) . '</div>';
        }

        if ($description) {
            $output .= '<p class="' . $this->gridConfig['descriptionClass'] . '">' . htmlspecialchars($description) . '</p>';
        }

        $output .= $this->renderCardActions($item);

        $output .= '</div>';
        $output .= '</div>';
        $output .= '</div>';

        return $output;
    }

    /**
     * Minimal card layout
     */
    private function renderMinimalCard($item)
    {
        $titleKey = $this->gridConfig['titleKey'];
        $subtitleKey = $this->gridConfig['subtitleKey'];

        $title = isset($item[$titleKey]) ? $item[$titleKey] : '';
        $subtitle = isset($item[$subtitleKey]) ? $item[$subtitleKey] : '';
        
        $output = '<div class="grid-item ' . str_replace('shadow-sm hover:shadow-md', 'shadow-none hover:shadow-sm', $this->gridConfig['cardClass']) . '">';
        $output .= '<div class="p-4 text-center">';

        if ($title) {
            $output .= '<h3 class="text-sm font-medium text-gray-800 mb-1">' . htmlspecialchars($title) . '</h3>';
        }

        if ($subtitle) {
            $output .= '<div class="text-xs text-gray-500 mb-2">' . htmlspecialchars($subtitle) . '</div>';
        }

        $output .= $this->renderCardActions($item, 'justify-center');

        $output .= '</div>';
        $output .= '</div>';

        return $output;
    }

    /**
     * Detailed card layout
     */
    private function renderDetailedCard($item)
    {
        return $this->renderDefaultCard($item);
    }

    /**
     * Render card actions
     */
    private function renderCardActions($item, $justify = 'justify-between')
    {
        if (empty($this->gridConfig['actions'])) {
            return '';
        }

        $output = '<div class="flex items-center ' . $justify . ' mt-auto pt-2 border-t border-gray-100">';

        foreach ($this->gridConfig['actions'] as $action) {
            if (isset($action['condition']) && is_callable($action['condition']) && !call_user_func($action['condition'], $item)) {
                continue;
            }

            $url = isset($action['urlFormatter']) && is_callable($action['urlFormatter']) ?
                call_user_func($action['urlFormatter'], $item) : (isset($action['url']) ? $action['url'] : '#');

            $label = isset($action['label']) ? $action['label'] : '';
            $class = isset($action['class']) ? $action['class'] : 'text-xs text-blue-600 font-medium hover:text-blue-800 hover:underline';

            if (isset($action['type']) && $action['type'] === 'button') {
                $btnAttr = '';
                if (isset($action['attributes'])) {
                    foreach ($action['attributes'] as $attr => $attrFormatter) {
                        $attrValue = is_callable($attrFormatter) ? call_user_func($attrFormatter, $item) : $attrFormatter;
                        $btnAttr .= ' ' . $attr . '="' . htmlspecialchars($attrValue) . '"';
                    }
                }
                $output .= '<button class="' . $class . '"' . $btnAttr . '>' . htmlspecialchars($label) . '</button>';
            } else {
                $output .= '<a href="' . htmlspecialchars($url) . '" class="' . $class . '">' . htmlspecialchars($label) . '</a>';
            }
        }

        $output .= '</div>';

        return $output;
    }

    /**
     * Render table header with FIXED sorting URLs
     */
    private function renderTableHeader()
    {
        $output = '<thead class="text-xs text-gray-700 uppercase bg-gray-50">';
        $output .= '<tr class="border-b">';

        foreach ($this->columns as $key => $column) {
            $sortable = isset($column['sortable']) && $column['sortable'] ? true : false;
            $classes = isset($column['class']) ? $column['class'] : 'px-6 py-3';

            $output .= '<th scope="col" class="' . $classes . '">';

            if ($sortable) {
                $currentDirection = ($this->sortColumn === $key) ? $this->sortDirection : 'none';
                $nextDirection = ($currentDirection === 'asc') ? 'desc' : 'asc';

                // Preserve all current query parameters
                $queryParams = $_GET;

                // Set sort parameter
                if ($nextDirection === 'desc') {
                    $queryParams['sort'] = '-' . $key;
                } else {
                    $queryParams['sort'] = $key;
                }

                // Remove old direction parameter if it exists
                unset($queryParams['direction']);

                // Reset to page 1 when sorting changes (only if different column)
                if ($this->sortColumn !== $key) {
                    $queryParams['page'] = 1;
                }

                $sortUrl = '?' . http_build_query($queryParams);

                $output .= '<a href="' . $sortUrl . '" class="flex items-center hover:text-gray-900 transition-colors">';
                $output .= htmlspecialchars($column['label']);

                // Add sort indicator
                if ($currentDirection !== 'none') {
                    $output .= '<svg class="w-3 h-3 ml-1.5" fill="currentColor" viewBox="0 0 24 24">';
                    if ($currentDirection === 'asc') {
                        $output .= '<path d="M7 14l5-5 5 5H7z"/>';
                    } else {
                        $output .= '<path d="M7 10l5 5 5-5H7z"/>';
                    }
                    $output .= '</svg>';
                } else {
                    // Show neutral sort icon on hover
                    $output .= '<svg class="w-3 h-3 ml-1.5 opacity-0 group-hover:opacity-50" fill="currentColor" viewBox="0 0 24 24">';
                    $output .= '<path d="M12 2l3 3h-2v6h-2V5H9l3-3zm0 20l-3-3h2v-6h2v6h2l-3 3z"/>';
                    $output .= '</svg>';
                }

                $output .= '</a>';
            } else {
                $output .= htmlspecialchars($column['label']);
            }

            $output .= '</th>';
        }

        if (!empty($this->rowActions)) {
            $output .= '<th scope="col" class="px-6 py-3 text-center">Actions</th>';
        }

        $output .= '</tr>';
        $output .= '</thead>';

        return $output;
    }

    /**
     * Render table body
     */
    private function renderTableBody()
    {
        $output = '<tbody class="bg-white divide-y divide-gray-200">';

        if (empty($this->data)) {
            $colSpan = count($this->columns) + (!empty($this->rowActions) ? 1 : 0);
            $output .= '<tr>';
            $output .= '<td colspan="' . $colSpan . '" class="px-6 py-3 text-center text-gray-500">No data found</td>';
            $output .= '</tr>';
        } else {
            foreach ($this->data as $row) {
                $output .= '<tr class="hover:bg-gray-50 searchable-row">';

                foreach ($this->columns as $key => $column) {
                    $value = isset($row[$key]) ? $row[$key] : '';
                    $classes = isset($column['cellClass']) ? $column['cellClass'] : 'px-6 py-3 whitespace-nowrap';

                    $output .= '<td class="' . $classes . '">';

                    if (isset($column['formatter']) && is_callable($column['formatter'])) {
                        $output .= call_user_func($column['formatter'], $value, $row);
                    } else {
                        $output .= htmlspecialchars($value);
                    }

                    $output .= '</td>';
                }

                if (!empty($this->rowActions)) {
                    $output .= '<td class="px-6 py-3 whitespace-nowrap text-sm text-center actionColumn">';
                    $output .= '<div class="flex items-center space-x-1 justify-center">';

                    foreach ($this->rowActions as $action) {
                        if (isset($action['condition']) && is_callable($action['condition']) && !call_user_func($action['condition'], $row)) {
                            continue;
                        }

                        $url = isset($action['href']) ? $action['href'] : '#';

                        if (isset($action['urlFormatter']) && is_callable($action['urlFormatter'])) {
                            $url = call_user_func($action['urlFormatter'], $row);
                        }

                        $textColor = isset($action['textColor']) ? $action['textColor'] : 'text-gray-500';
                        $hoverTextColor = isset($action['hoverTextColor']) ? $action['hoverTextColor'] : 'hover:text-gray-700';
                        $bgHoverColor = isset($action['bgHoverColor']) ? $action['bgHoverColor'] : 'hover:bg-gray-100';

                        $output .= '<div class="relative group">';

                        if (isset($action['type']) && $action['type'] === 'button') {
                            $btnAttr = '';
                            if (isset($action['attributes'])) {
                                foreach ($action['attributes'] as $attr => $attrFormatter) {
                                    if ($attr === 'class') {
                                        continue;
                                    }
                                    $attrValue = is_callable($attrFormatter) ? call_user_func($attrFormatter, $row) : $attrFormatter;
                                    $btnAttr .= ' ' . $attr . '="' . htmlspecialchars($attrValue) . '"';
                                }
                            }

                            $baseClass = "p-2 rounded-lg transition-colors duration-200";
                            $colorClass = "$textColor $hoverTextColor $bgHoverColor";

                            $additionalClasses = '';
                            if (isset($action['attributes']['class'])) {
                                $classValue = $action['attributes']['class'];
                                if (is_callable($classValue)) {
                                    $additionalClasses = call_user_func($classValue, $row);
                                } else {
                                    $additionalClasses = $classValue;
                                }

                                $additionalClasses = preg_replace('/(text-[a-z]+-[0-9]+\s*|hover:text-[a-z]+-[0-9]+\s*|hover:bg-[a-z]+-[0-9]+\s*)/', '', $additionalClasses);
                            }

                            $fullClass = trim("$baseClass $colorClass $additionalClasses");

                            $output .= '<button type="button" class="' . $fullClass . '"' . $btnAttr . '>';
                        } else {
                            $baseClass = "block p-2 rounded-lg transition-colors duration-200";
                            $colorClass = "$textColor $hoverTextColor $bgHoverColor";

                            $additionalClasses = isset($action['class']) ? $action['class'] : '';
                            $additionalClasses = preg_replace('/(text-[a-z]+-[0-9]+\s*|hover:text-[a-z]+-[0-9]+\s*|hover:bg-[a-z]+-[0-9]+\s*)/', '', $additionalClasses);

                            $fullClass = trim("$baseClass $colorClass $additionalClasses");

                            $output .= '<a href="' . $url . '" class="' . $fullClass . '">';
                        }

                        if (isset($action['icon'])) {
                            $output .= '<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">';
                            $output .= $action['icon'];
                            $output .= '</svg>';
                        } else {
                            $output .= htmlspecialchars($action['label']);
                        }

                        if (isset($action['type']) && $action['type'] === 'button') {
                            $output .= '</button>';
                        } else {
                            $output .= '</a>';
                        }

                        if (isset($action['tooltip'])) {
                            $output .= '<div class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 hidden group-hover:block w-auto z-10">';
                            $output .= '<div class="bg-gray-800 text-white text-xs rounded py-1 px-2 whitespace-nowrap">';
                            $output .= htmlspecialchars($action['tooltip']);
                            $output .= '<div class="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-800"></div>';
                            $output .= '</div>';
                            $output .= '</div>';
                        }

                        $output .= '</div>';
                    }

                    $output .= '</div>';
                    $output .= '</td>';
                }

                $output .= '</tr>';
            }
        }

        $output .= '</tbody>';

        return $output;
    }

    /**
     * Render pagination with proper URL handling
     */
    private function renderPagination()
    {
        if (empty($this->data)) {
            return '';
        }

        $output = '<div class="bg-white mx-6 paginationWrapper">';
        $output .= '<div class="flex items-center justify-between py-3">';

        // Left side: Results info and per-page dropdown
        $output .= '<div class="flex items-center space-x-4">';

        // Results info
        if (!empty($this->pagination['from']) && !empty($this->pagination['to'])) {
            $output .= '<div class="text-sm text-gray-700">';
            $output .= 'Showing <span class="font-medium">' . $this->pagination['from'] . '</span>';
            $output .= ' to <span class="font-medium">' . $this->pagination['to'] . '</span>';
            if (!empty($this->pagination['total'])) {
                $output .= ' of <span class="font-medium">' . $this->pagination['total'] . '</span> results';
            }
            $output .= '</div>';
        }

        // Per-page limit dropdown
        $currentLimit = $this->pagination['per_page'];
        $limitOptions = [10, 25, 50, 100];

        $output .= '<div class="flex items-center space-x-2">';
        $output .= '<label class="text-sm text-gray-700">Show:</label>';
        $output .= '<select id="perPageSelect" class="border border-gray-300 rounded px-3 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">';

        foreach ($limitOptions as $option) {
            $selected = ($currentLimit == $option) ? 'selected' : '';
            $output .= '<option value="' . $option . '" ' . $selected . '>' . $option . '</option>';
        }

        $output .= '</select>';
        $output .= '<span class="text-sm text-gray-700">per page</span>';
        $output .= '</div>';

        $output .= '</div>';

        // Right side: Navigation buttons
        $output .= '<div class="flex items-center space-x-2">';

        // Previous button
        if ($this->pagination['has_previous_pages'] && !empty($this->pagination['prev_page_url'])) {
            $prevPage = $this->extractPageFromUrl($this->pagination['prev_page_url']);
            $queryParams = $_GET;
            $queryParams['page'] = $prevPage;
            $prevUrl = '?' . http_build_query($queryParams);

            $output .= '<a href="' . $prevUrl . '" class="relative inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors">';
            $output .= '<svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">';
            $output .= '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>';
            $output .= '</svg>';
            $output .= 'Previous';
            $output .= '</a>';
        } else {
            $output .= '<span class="relative inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-300 bg-gray-100 cursor-not-allowed">';
            $output .= '<svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">';
            $output .= '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>';
            $output .= '</svg>';
            $output .= 'Previous';
            $output .= '</span>';
        }

        // Page info
        $output .= '<span class="text-sm text-gray-700 px-3">Page ' . $this->pagination['current_page'] . '</span>';

        // Next button
        if ($this->pagination['has_more_pages'] && !empty($this->pagination['next_page_url'])) {
            $nextPage = $this->extractPageFromUrl($this->pagination['next_page_url']);
            $queryParams = $_GET;
            $queryParams['page'] = $nextPage;
            $nextUrl = '?' . http_build_query($queryParams);

            $output .= '<a href="' . $nextUrl . '" class="relative inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors">';
            $output .= 'Next';
            $output .= '<svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">';
            $output .= '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>';
            $output .= '</svg>';
            $output .= '</a>';
        } else {
            $output .= '<span class="relative inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-300 bg-gray-100 cursor-not-allowed">';
            $output .= 'Next';
            $output .= '<svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">';
            $output .= '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>';
            $output .= '</svg>';
            $output .= '</span>';
        }

        $output .= '</div>';
        $output .= '</div>';
        $output .= '</div>';

        return $output;
    }

    /**
     * Extract page number from URL
     */
    private function extractPageFromUrl($url)
    {
        if (empty($url)) {
            return 1;
        }

        $parsedUrl = parse_url($url);
        if (isset($parsedUrl['query'])) {
            parse_str($parsedUrl['query'], $queryParams);
            return isset($queryParams['page']) ? (int)$queryParams['page'] : 1;
        }

        return 1;
    }

    /**
     * IMPROVED JavaScript with better search and view toggle functionality
     */
    private function renderJavaScript()
    {
        $tableId = $this->tableId;
        $searchId = $this->searchId;
        $searchable = $this->searchable ? 'true' : 'false';
        $gridEnabled = $this->gridConfig['enabled'] ? 'true' : 'false';
        $defaultView = $this->defaultView;

        $output = '<script>';
        $output .= '(function() {';
        $output .= '"use strict";';

        // Debounce function for better performance
        $output .= 'function debounce(func, wait) {';
        $output .= '    let timeout;';
        $output .= '    return function executedFunction(...args) {';
        $output .= '        const later = () => {';
        $output .= '            clearTimeout(timeout);';
        $output .= '            func(...args);';
        $output .= '        };';
        $output .= '        clearTimeout(timeout);';
        $output .= '        timeout = setTimeout(later, wait);';
        $output .= '    };';
        $output .= '}';

        // Main initialization
        $output .= 'document.addEventListener("DOMContentLoaded", function() {';

        // Per-page limit change handler
        $output .= 'const perPageSelect = document.getElementById("perPageSelect");';
        $output .= 'if (perPageSelect) {';
        $output .= '    perPageSelect.addEventListener("change", function() {';
        $output .= '        const currentUrl = new URL(window.location);';
        $output .= '        currentUrl.searchParams.set("limit", this.value);';
        $output .= '        currentUrl.searchParams.set("page", "1");';
        $output .= '        window.location.href = currentUrl.toString();';
        $output .= '    });';
        $output .= '}';

        // Enhanced search functionality
        if ($this->searchable) {
            $output .= 'const searchInput = document.getElementById("' . $searchId . '");';
            $output .= 'if (searchInput) {';

            // Debounced search function
            $output .= '    const performSearch = debounce(function(searchValue) {';
            $output .= '        const tableView = document.querySelector(".table-view");';
            $output .= '        const gridView = document.querySelector(".grid-view");';
            $output .= '        const isTableView = tableView && !tableView.classList.contains("table-view-hidden");';
            $output .= '        let visibleCount = 0;';

            // Table search
            $output .= '        if (isTableView) {';
            $output .= '            const table = document.getElementById("' . $tableId . '");';
            $output .= '            if (table) {';
            $output .= '                const tbody = table.querySelector("tbody");';
            $output .= '                const rows = tbody.querySelectorAll("tr.searchable-row");';
            $output .= '                rows.forEach(function(row) {';
            $output .= '                    const text = row.textContent.toLowerCase();';
            $output .= '                    const isVisible = searchValue === "" || text.includes(searchValue);';
            $output .= '                    row.style.display = isVisible ? "" : "none";';
            $output .= '                    if (isVisible) visibleCount++;';
            $output .= '                });';
            $output .= '            }';
            $output .= '        }';

            // Grid search
            $output .= '        else if (gridView) {';
            $output .= '            const cards = gridView.querySelectorAll(".grid-item:not(.col-span-full)");';
            $output .= '            cards.forEach(function(card) {';
            $output .= '                const text = card.textContent.toLowerCase();';
            $output .= '                const isVisible = searchValue === "" || text.includes(searchValue);';
            $output .= '                card.style.display = isVisible ? "" : "none";';
            $output .= '                if (isVisible) visibleCount++;';
            $output .= '            });';
            $output .= '        }';

            // Show/hide no results message
            $output .= '        const noResultsMsg = document.querySelector(".no-search-results");';
            $output .= '        if (searchValue !== "" && visibleCount === 0) {';
            $output .= '            if (!noResultsMsg) {';
            $output .= '                const msg = document.createElement("div");';
            $output .= '                msg.className = "no-search-results col-span-full text-center text-gray-500 py-8";';
            $output .= '                msg.innerHTML = "No results found for your search.";';
            $output .= '                if (isTableView && table) {';
            $output .= '                    const tbody = table.querySelector("tbody");';
            $output .= '                    const tr = document.createElement("tr");';
            $output .= '                    const td = document.createElement("td");';
            $output .= '                    td.colSpan = tbody.querySelector("tr").cells.length;';
            $output .= '                    td.className = "px-6 py-8 text-center text-gray-500";';
            $output .= '                    td.textContent = "No results found for your search.";';
            $output .= '                    tr.appendChild(td);';
            $output .= '                    tr.className = "no-search-results";';
            $output .= '                    tbody.appendChild(tr);';
            $output .= '                } else if (gridView) {';
            $output .= '                    gridView.appendChild(msg);';
            $output .= '                }';
            $output .= '            }';
            $output .= '        } else if (noResultsMsg) {';
            $output .= '            noResultsMsg.remove();';
            $output .= '        }';
            $output .= '    }, 300);';

            // Event listeners for search
            $output .= '    searchInput.addEventListener("input", function() {';
            $output .= '        const searchValue = this.value.toLowerCase().trim();';
            $output .= '        performSearch(searchValue);';
            $output .= '    });';

            // Clear search on escape
            $output .= '    searchInput.addEventListener("keydown", function(e) {';
            $output .= '        if (e.key === "Escape") {';
            $output .= '            this.value = "";';
            $output .= '            performSearch("");';
            $output .= '        }';
            $output .= '    });';

            // Initial search if there\'s a value
            $output .= '    if (searchInput.value.trim() !== "") {';
            $output .= '        performSearch(searchInput.value.toLowerCase().trim());';
            $output .= '    }';

            $output .= '}';
        }

        // Enhanced view toggle functionality
        if ($this->gridConfig['enabled']) {
            $output .= 'window.toggleView = function(view) {';
            $output .= '    const tableView = document.querySelector(".table-view");';
            $output .= '    const gridView = document.querySelector(".grid-view");';
            $output .= '    const toggleButtons = document.querySelectorAll(".view-toggle");';

            $output .= '    if (view === "grid" && gridView) {';
            $output .= '        if (tableView) {';
            $output .= '            tableView.style.display = "none";';
            $output .= '            tableView.classList.add("table-view-hidden");';
            $output .= '        }';
            $output .= '        gridView.style.display = "grid";';
            $output .= '        gridView.classList.remove("grid-view-hidden");';
            $output .= '        toggleButtons.forEach(function(btn) {';
            $output .= '            btn.classList.remove("active");';
            $output .= '            if (btn.getAttribute("data-view") === "grid") {';
            $output .= '                btn.classList.add("active");';
            $output .= '            }';
            $output .= '        });';
            $output .= '    } else if (view === "table" && tableView) {';
            $output .= '        tableView.style.display = "block";';
            $output .= '        tableView.classList.remove("table-view-hidden");';
            $output .= '        if (gridView) {';
            $output .= '            gridView.style.display = "none";';
            $output .= '            gridView.classList.add("grid-view-hidden");';
            $output .= '        }';
            $output .= '        toggleButtons.forEach(function(btn) {';
            $output .= '            btn.classList.remove("active");';
            $output .= '            if (btn.getAttribute("data-view") === "table") {';
            $output .= '                btn.classList.add("active");';
            $output .= '            }';
            $output .= '        });';
            $output .= '    }';

            // Save preference
            $output .= '    try {';
            $output .= '        localStorage.setItem("preferredView_" + window.location.pathname, view);';
            $output .= '        document.cookie = "preferredView_" + window.location.pathname + "=" + view + "; path=/;";';
            $output .= '    } catch(e) {}';
            $output .= '};';

            // Initialize view from preference or default
            $output .= 'let preferredView = "' . $defaultView . '";';
            $output .= 'try {';
            $output .= '    const saved = localStorage.getItem("preferredView_" + window.location.pathname);';
            $output .= '    if (saved) preferredView = saved;';
            $output .= '} catch(e) {}';

            // Set initial active state for buttons
            $output .= 'const toggleButtons = document.querySelectorAll(".view-toggle");';
            $output .= 'toggleButtons.forEach(function(btn) {';
            $output .= '    btn.classList.remove("active");';
            $output .= '    if (btn.getAttribute("data-view") === preferredView) {';
            $output .= '        btn.classList.add("active");';
            $output .= '    }';
            $output .= '});';

            // Apply preferred view if different from default
            $output .= 'if (preferredView !== "' . $defaultView . '") {';
            $output .= '    setTimeout(function() { toggleView(preferredView); }, 100);';
            $output .= '}';
        }

        // Initialize Lucide icons if available
        $output .= 'if (typeof lucide !== "undefined" && typeof lucide.createIcons === "function") {';
        $output .= '    lucide.createIcons();';
        $output .= '}';

        $output .= '});'; // End DOMContentLoaded

        // CSS for active state and transitions
        $output .= 'const style = document.createElement("style");';
        $output .= 'style.textContent = `';
        $output .= '.view-toggle.active { background-color: #0c5be2 !important; color: white !important; }';
        $output .= '.table-view-hidden { display: none !important; }';
        $output .= '.grid-view-hidden { display: none !important; }';
        $output .= '.searchable-row { transition: opacity 0.2s ease; }';
        $output .= '.no-search-results { animation: fadeIn 0.3s ease; }';
        $output .= '@keyframes fadeIn { from { opacity: 0; } to { opacity: 1; } }';
        $output .= '`;';
        $output .= 'document.head.appendChild(style);';

        $output .= '})();'; // End IIFE
        $output .= '</script>';

        return $output;
    }
}
