<?php
require_once __DIR__ . '/../vendor/autoload.php';
use LightnCandy\LightnCandy;

class PreviewHelper {
    
    // Handlebars helper functions
    private static $helpers = [];
    
    // Initialize helpers
    public static function init() {
        self::$helpers = [
            'mod' => function($arg1, $arg2) {
                return intval($arg1) % intval($arg2);
            },
            'eq' => function($arg1, $arg2) {
                return $arg1 === $arg2;
            },
            'ne' => function($arg1, $arg2) {
                return $arg1 !== $arg2;
            },
            'neq' => function($arg1, $arg2) {
                return $arg1 !== $arg2;
            },
            'lt' => function($arg1, $arg2) {
                return $arg1 < $arg2;
            },
            'gt' => function($arg1, $arg2) {
                return $arg1 > $arg2;
            },
            'lte' => function($arg1, $arg2) {
                return $arg1 <= $arg2;
            },
            'gte' => function($arg1, $arg2) {
                return $arg1 >= $arg2;
            },
            'and' => function() {
                foreach (func_get_args() as $arg) {
                    if (!$arg) return false;
                }
                return true;
            },
            'or' => function() {
                foreach (func_get_args() as $arg) {
                    if ($arg) return true;
                }
                return false;
            },
            'not' => function($arg) {
                return !$arg;
            },
            'isEven' => function($arg1) {
                return intval($arg1) % 2 === 0;
            },
            'isOdd' => function($arg1) {
                return intval($arg1) % 2 !== 0;
            }
        ];
    }
    
    /**
     * Handle AJAX requests for template processing
     */
    public static function handleAjaxRequest() {
        if (isset($_POST['action']) && $_POST['action'] === 'process_template') {
            header('Content-Type: application/json');
            $template = $_POST['template'] ?? '';
            $jsonData = $_POST['json_data'] ?? '{}';
            
            $result = self::processTemplate($template, $jsonData);
            echo json_encode($result);
            exit;
        }
        
        // Handle update_block action properly
        if (isset($_POST['action']) && $_POST['action'] === 'update_block') {
            header('Content-Type: application/json');
            $blockId = $_POST['block_id'] ?? '';
            $jsonCode = $_POST['json_code'] ?? '{}';
            $classes = $_POST['classes'] ?? '';
            
            $result = self::updateBlockData($blockId, $jsonCode, $classes);
            echo json_encode($result);
            exit;
        }
        
        // Handle update_block_position action
        if (isset($_POST['action']) && $_POST['action'] === 'update_block_position') {
            header('Content-Type: application/json');
            $blockId = $_POST['block_id'] ?? '';
            $position = $_POST['position'] ?? '';
            
            if (empty($blockId) || $position === '') {
                echo json_encode(['success' => false, 'message' => 'Block ID and position are required']);
                exit;
            }
            
            $result = self::updateBlockPosition($blockId, $position);
            echo json_encode($result);
            exit;
        }
    }
    
    /**
     * Update block data via API
     */
    private static function updateBlockData($blockId, $jsonCode, $classes) {
        try {
            $requestData = [
                'block_id' => $blockId,
                'json_code' => $jsonCode,
                'classes' => $classes
            ];
            
            // Use the correct API endpoint from user's module structure
            $apiUrl = BASE_URL . '/modules/pages_blocks/update_block.php';
            
            // Debug logging
            if (defined('DEBUG') && DEBUG) {
                error_log('PreviewHelper updateBlockData - URL: ' . $apiUrl);
                error_log('PreviewHelper updateBlockData - Data: ' . print_r($requestData, true));
            }
            
            $response = self::makeApiRequest($apiUrl, $requestData);
            return $response;
        } catch (Exception $e) {
            error_log('PreviewHelper updateBlockData error: ' . $e->getMessage());
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }
    
    /**
     * Update the position of a block
     */
    public static function updateBlockPosition($blockId, $position) {
        try {
            $requestData = [
                'block_id' => $blockId,
                'position' => floatval($position)
            ];
            
            // Debug logging
            if (defined('DEBUG') && DEBUG) {
                error_log('PreviewHelper updateBlockPosition - Block ID: ' . $blockId . ', Position: ' . $position);
            }
            
            // Use the correct API endpoint
            $apiUrl = BASE_URL . '/modules/pages_blocks/update_block_position.php';
            
            $response = self::makeApiRequest($apiUrl, $requestData);
            return $response;
        } catch (Exception $e) {
            error_log('PreviewHelper updateBlockPosition error: ' . $e->getMessage());
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }
    
    /**
     * Make API request with enhanced error handling
     */
    private static function makeApiRequest($url, $data) {
        // Initialize cURL
        $ch = curl_init();
        
        // Set cURL options
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => json_encode($data),
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_CONNECTTIMEOUT => 10,
            CURLOPT_HTTPHEADER => [
                'Content-Type: application/json',
                'Accept: application/json'
            ],
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_SSL_VERIFYPEER => false, // For local development
            CURLOPT_USERAGENT => 'PreviewHelper/1.0'
        ]);
        
        // Execute request
        $result = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $curlError = curl_error($ch);
        curl_close($ch);
        
        // Debug logging
        if (defined('DEBUG') && DEBUG) {
            error_log('API Request - URL: ' . $url);
            error_log('API Request - HTTP Code: ' . $httpCode);
            error_log('API Request - Response: ' . $result);
            if ($curlError) {
                error_log('API Request - cURL Error: ' . $curlError);
            }
        }
        
        // Check for cURL errors
        if ($curlError) {
            throw new Exception("Network error: $curlError");
        }
        
        // Check for empty response
        if ($result === false || empty($result)) {
            throw new Exception("Empty response from server. HTTP Code: $httpCode");
        }
        
        // Check HTTP status code
        if ($httpCode !== 200) {
            throw new Exception("API request failed with HTTP status: $httpCode. Response: " . substr($result, 0, 200));
        }
        
        // Decode JSON response
        $decodedResponse = json_decode($result, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new Exception("Invalid JSON response: " . json_last_error_msg() . ". Response: " . substr($result, 0, 200));
        }
        
        return $decodedResponse;
    }
    
    /**
     * Main preview rendering function
     */
    public static function renderPreview($type, $id, $options = []) {
        // Handle AJAX requests first
        self::handleAjaxRequest();
        
        $previewMode = $options['preview_mode'] ?? false;
        $hideControls = $options['hide_controls'] ?? $previewMode;
        $specificPage = $options['page_slug'] ?? null;
        
        if ($type === 'template') {
            return self::renderTemplatePreview($id, $options);
        } else {
            return self::renderPagePreview($id, $options);
        }
    }

    /**
     * Template preview with all pages
     */
    private static function renderTemplatePreview($templateId, $options = []) {
        require_once __DIR__ . '/../modules/templates/Templates.php';
        require_once __DIR__ . '/../modules/pages/Pages.php';
        
        $templates = new Templates();
        $pages = new Pages();
        
        // Get template data
        $templateResponse = $templates->get($templateId);
        if (!$templateResponse['success']) {
            return ['error' => 'Template not found'];
        }
        $templateData = $templateResponse['data'];
        
        // Get all pages for this template
        $pagesResponse = $pages->getList(['template_id' => $templateId]);
        $templatePages = [];
        if ($pagesResponse['success'] && isset($pagesResponse['data']['data'])) {
            $templatePages = $pagesResponse['data']['data'];
        }
        
        // If specific page requested
        if (!empty($options['page_slug'])) {
            $templatePages = array_filter($templatePages, function($page) use ($options) {
                return $page['slug'] === $options['page_slug'];
            });
        }
        
        // Get all blocks for all pages
        $allBlocks = [];
        foreach ($templatePages as $page) {
            $pageBlocks = self::getPageBlocks($page['id']);
            
            foreach ($pageBlocks as $block) {
                $block['page_name'] = $page['name'];
                $block['page_slug'] = $page['slug'];
                $block['page_id'] = $page['id'];
                $allBlocks[] = $block;
            }
        }
        
        return self::generatePreviewHTML($allBlocks, $templateData, null, $options);
    }

    /**
     * Single page preview
     */
    private static function renderPagePreview($pageId, $options = []) {
        require_once __DIR__ . '/../modules/pages/Pages.php';
        require_once __DIR__ . '/../modules/templates/Templates.php';
        
        $pages = new Pages();
        $templates = new Templates();
        
        // Get page data
        $pageResponse = $pages->getById($pageId);
        if (!$pageResponse['success']) {
            return ['error' => 'Page not found'];
        }
        $pageData = $pageResponse['data'];
        
        // Get template data if page has template
        $templateData = null;
        if (!empty($pageData['template_id'])) {
            $templateResponse = $templates->get($pageData['template_id']);
            if ($templateResponse['success']) {
                $templateData = $templateResponse['data'];
            }
        }
        
        // Get page blocks
        $pageBlocks = self::getPageBlocks($pageId);
        
        // Add page info to blocks
        foreach ($pageBlocks as &$block) {
            $block['page_name'] = $pageData['name'];
            $block['page_slug'] = $pageData['slug'];
            $block['page_id'] = $pageId;
        }
        
        return self::generatePreviewHTML($pageBlocks, $templateData, $pageData, $options);
    }

    /**
     * Get blocks for a specific page
     */
    private static function getPageBlocks($pageId) {
        require_once __DIR__ . '/../modules/pages_blocks/PagesBlocks.php';
        require_once __DIR__ . '/../modules/blocks/Blocks.php';
        
        $pagesBlocks = new PagesBlocks();
        $blocks = new Blocks();
        $pageBlocks = [];
        
        try {
            $response = $pagesBlocks->getByPageId($pageId);
            
            $pageBlocksData = [];
            if (isset($response['success']) && $response['success'] === true) {
                if (isset($response['data']['data']) && is_array($response['data']['data'])) {
                    $pageBlocksData = $response['data']['data'];
                } elseif (isset($response['data']) && is_array($response['data'])) {
                    $pageBlocksData = $response['data'];
                }
            }
            
            foreach ($pageBlocksData as $pageBlock) {
                if (isset($pageBlock['block_id'])) {
                    $blockResponse = $blocks->get($pageBlock['block_id']);
                    
                    if (isset($blockResponse['success']) && $blockResponse['success'] === true && isset($blockResponse['data'])) {
                        $blockData = $blockResponse['data'];
                        
                        $tmplCode = $blockData['tmpl_code'] ?? '';
                        $jsonCode = $pageBlock['json_code'] ?? ($blockData['json_code'] ?? '{}');
                        $cssCode = $pageBlock['css_code'] ?? ($blockData['css_code'] ?? '');
                        $jsCode = $pageBlock['js_code'] ?? ($blockData['js_code'] ?? '');
                        $blockClasses = $pageBlock['classes'] ?? ($blockData['classes'] ?? '');
                        
                        // Process template safely
                        $processedContent = $tmplCode; // Default fallback
                        
                        if (!empty($tmplCode) && !empty($jsonCode) && $jsonCode !== '{}') {
                            try {
                                $templateResult = self::processTemplateToString($tmplCode, $jsonCode);
                                
                                // Ensure we got a string result
                                if (is_string($templateResult) && !empty($templateResult)) {
                                    $processedContent = $templateResult;
                                } else {
                                    error_log("Template processing returned non-string for block {$pageBlock['block_id']}: " . gettype($templateResult));
                                    $processedContent = $tmplCode; // Use original template
                                }
                            } catch (Exception $e) {
                                error_log("Template processing error for block {$pageBlock['block_id']}: " . $e->getMessage());
                                $processedContent = $tmplCode; // Use original template
                            }
                        }
                        
                        // Add block classes
                        if (!empty($blockClasses) && !empty($processedContent)) {
                            $processedContent = self::addClassesToHtml($processedContent, $blockClasses);
                        }
                        
                        // Handle dependencies
                        $dependencies = $blockData['dependencies'] ?? '';
                        $dependenciesArray = [];
                        if (!empty($dependencies)) {
                            $dependenciesArray = json_decode($dependencies, true) ?: [];
                        }
                        
                        $pageBlocks[] = [
                            'id' => $pageBlock['id'],
                            'block_id' => $pageBlock['block_id'],
                            'position' => $pageBlock['position'] ?? 999,
                            'type' => $blockData['type'] ?? 'block',
                            'content' => $processedContent,
                            'css' => $cssCode,
                            'js' => $jsCode,
                            'json_code' => $jsonCode,
                            'classes' => $blockClasses,
                            'name' => $blockData['name'] ?? 'Block',
                            'dependencies' => $dependenciesArray,
                            'tmpl_code' => $tmplCode // Add template code for editing
                        ];
                    }
                }
            }
            
            // Sort blocks by position
            usort($pageBlocks, function($a, $b) {
                return ($a['position'] ?? 999) - ($b['position'] ?? 999);
            });
            
        } catch (Exception $e) {
            error_log('Error loading page blocks: ' . $e->getMessage());
        }
        
        return $pageBlocks;
    }

    /**
     * Process Handlebars template - for AJAX requests
     * This version is optimized for use with the no-reload block update feature
     */
    public static function processTemplate($template, $jsonData) {
        try {
            $data = json_decode($jsonData, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                return ['success' => false, 'message' => 'Invalid JSON data: ' . json_last_error_msg()];
            }

            $phpStr = LightnCandy::compile($template, [
                'flags' => LightnCandy::FLAG_HANDLEBARS | 
                        LightnCandy::FLAG_ERROR_EXCEPTION | 
                        LightnCandy::FLAG_RUNTIMEPARTIAL |
                        LightnCandy::FLAG_NOESCAPE,
                'helpers' => self::$helpers
            ]);
            
            if (!$phpStr) {
                return ['success' => false, 'message' => 'Failed to compile template'];
            }

            $renderer = LightnCandy::prepare($phpStr);
            if (!$renderer) {
                return ['success' => false, 'message' => 'Failed to prepare template renderer'];
            }
            
            $result = $renderer($data);
            
            // If configured for debug logging
            if (defined('DEBUG') && DEBUG) {
                error_log('Template processed successfully, input: ' . substr($jsonData, 0, 100) . '...');
            }
            
            return ['success' => true, 'html' => $result];
        } catch (Exception $e) {
            error_log('Template processing error: ' . $e->getMessage());
            return ['success' => false, 'message' => 'Template processing error: ' . $e->getMessage()];
        }
    }

    /**
     * Process Handlebars template - for internal use (returns string)
     */
    public static function processTemplateToString($template, $jsonData) {
        try {
            $data = json_decode($jsonData, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                error_log('JSON decode error: ' . json_last_error_msg());
                return $template; // Return original template on error
            }

            $phpStr = LightnCandy::compile($template, [
                'flags' => LightnCandy::FLAG_HANDLEBARS | 
                          LightnCandy::FLAG_ERROR_EXCEPTION | 
                          LightnCandy::FLAG_RUNTIMEPARTIAL |
                          LightnCandy::FLAG_NOESCAPE,
                'helpers' => self::$helpers
            ]);
            
            if (!$phpStr) {
                error_log('Failed to compile template');
                return $template;
            }

            $renderer = LightnCandy::prepare($phpStr);
            if (!$renderer) {
                error_log('Failed to prepare template renderer');
                return $template;
            }
            
            return $renderer($data);
        } catch (Exception $e) {
            error_log('Template processing error: ' . $e->getMessage());
            return $template; // Return original template on error
        }
    }

    /**
     * Add CSS classes to HTML content
     */
    private static function addClassesToHtml($html, $classes) {
        if (empty($classes) || empty($html)) {
            return $html;
        }
        
        $pattern = '/^(\s*<[^>]+?)(class\s*=\s*["\']([^"\']*)["\'])([^>]*>)/i';
        if (preg_match($pattern, $html, $matches)) {
            $existingClasses = $matches[3];
            $newClasses = trim($existingClasses . ' ' . $classes);
            return $matches[1] . 'class="' . $newClasses . '"' . $matches[4] . substr($html, strlen($matches[0]));
        } else {
            $pattern = '/^(\s*<[^>\s]+)([^>]*>)/';
            if (preg_match($pattern, $html, $matches)) {
                return $matches[1] . ' class="' . $classes . '"' . $matches[2] . substr($html, strlen($matches[0]));
            }
        }
        
        return $html;
    }

    /**
     * Generate color palette from base color
     */
    private static function generateColorPalette($baseColor) {
        $hex = ltrim($baseColor, '#');
        $r = hexdec(substr($hex, 0, 2));
        $g = hexdec(substr($hex, 2, 2));
        $b = hexdec(substr($hex, 4, 2));
        
        $hsl = self::rgbToHsl($r, $g, $b);
        
        $palette = [];
        $shades = [
            50 => 0.97, 100 => 0.94, 200 => 0.87, 300 => 0.75, 400 => 0.62,
            500 => 0.50, 600 => 0.38, 700 => 0.25, 800 => 0.15, 900 => 0.08, 950 => 0.03
        ];
        
        foreach ($shades as $shade => $lightness) {
            $saturation = $hsl[1];
            if ($lightness > 0.9) {
                $saturation = $hsl[1] * 0.3;
            } elseif ($lightness < 0.1) {
                $saturation = $hsl[1] * 0.5;
            }
            
            $rgb = self::hslToRgb($hsl[0], $saturation, $lightness);
            $hex = sprintf("#%02x%02x%02x", $rgb[0], $rgb[1], $rgb[2]);
            $palette[$shade] = strtoupper($hex);
        }
        
        return $palette;
    }

    private static function rgbToHsl($r, $g, $b) {
        $r /= 255; $g /= 255; $b /= 255;
        $max = max($r, $g, $b); $min = min($r, $g, $b);
        $diff = $max - $min;
        $l = ($max + $min) / 2;
        
        if ($diff == 0) {
            $h = $s = 0;
        } else {
            $s = $l > 0.5 ? $diff / (2 - $max - $min) : $diff / ($max + $min);
            switch ($max) {
                case $r: $h = (($g - $b) / $diff + ($g < $b ? 6 : 0)) / 6; break;
                case $g: $h = (($b - $r) / $diff + 2) / 6; break;
                case $b: $h = (($r - $g) / $diff + 4) / 6; break;
            }
        }
        
        return [$h, $s, $l];
    }

    private static function hslToRgb($h, $s, $l) {
        if ($s == 0) {
            $r = $g = $b = $l;
        } else {
            $hue2rgb = function($p, $q, $t) {
                if ($t < 0) $t += 1;
                if ($t > 1) $t -= 1;
                if ($t < 1/6) return $p + ($q - $p) * 6 * $t;
                if ($t < 1/2) return $q;
                if ($t < 2/3) return $p + ($q - $p) * (2/3 - $t) * 6;
                return $p;
            };
            
            $q = $l < 0.5 ? $l * (1 + $s) : $l + $s - $l * $s;
            $p = 2 * $l - $q;
            
            $r = $hue2rgb($p, $q, $h + 1/3);
            $g = $hue2rgb($p, $q, $h);
            $b = $hue2rgb($p, $q, $h - 1/3);
        }
        
        return [round($r * 255), round($g * 255), round($b * 255)];
    }

    /**
     * Generate complete HTML for preview
     */
    private static function generatePreviewHTML($blocks, $templateData, $pageData, $options = []) {
        $previewMode = $options['preview_mode'] ?? false;
        $hideControls = $options['hide_controls'] ?? $previewMode;
        $isTemplatePage = !empty($options['is_template_page']);
        
        // Collect dependencies
        $dependenciesCSS = [];
        $dependenciesJS = [];
        
        foreach ($blocks as $block) {
            if (!empty($block['dependencies'])) {
                if (isset($block['dependencies']['css']) && is_array($block['dependencies']['css'])) {
                    foreach ($block['dependencies']['css'] as $cssUrl) {
                        if (!in_array($cssUrl, $dependenciesCSS)) {
                            $dependenciesCSS[] = $cssUrl;
                        }
                    }
                }
                if (isset($block['dependencies']['js']) && is_array($block['dependencies']['js'])) {
                    foreach ($block['dependencies']['js'] as $jsUrl) {
                        if (!in_array($jsUrl, $dependenciesJS)) {
                            $dependenciesJS[] = $jsUrl;
                        }
                    }
                }
            }
        }
        
        // Start HTML output
        ob_start();
        ?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars(($pageData['meta_title'] ?? $templateData['site_title'] ?? 'Preview')); ?></title>
    <meta name="description" content="<?php echo htmlspecialchars(($pageData['meta_description'] ?? $templateData['site_description'] ?? '')); ?>">
    
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=<?php echo htmlspecialchars(($templateData['body_font_family'] ?? 'Inter')); ?>:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap" rel="stylesheet">
    
    <?php if (!$hideControls): ?>
    <!-- Flowbite CSS -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/flowbite/2.2.1/flowbite.min.css" rel="stylesheet" />
    <?php endif; ?>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <script>
        tailwind.config = {
            darkMode: 'class',
        }
    </script>
    
    <!-- Block Dependencies CSS -->
    <?php foreach ($dependenciesCSS as $cssUrl): ?>
    <link href="<?php echo htmlspecialchars($cssUrl); ?>" rel="stylesheet" />
    <?php endforeach; ?>
    
    <!-- Custom CSS -->
    <style>
        :root {
            <?php
            // Generate color variables from template config
            if (!empty($templateData['tailwind_configuration_code'])) {
                $config = $templateData['tailwind_configuration_code'];
                if (preg_match('/tailwind\.config\s*=\s*({[\s\S]*})/', $config, $matches)) {
                    $jsonStr = $matches[1];
                    $jsonStr = preg_replace('/\\n/', '', $jsonStr);
                    $jsonStr = preg_replace('/\\"/', '"', $jsonStr);
                    $jsonStr = preg_replace('/\s+/', ' ', $jsonStr);
                    
                    try {
                        $colors = json_decode($jsonStr);
                        if ($colors && isset($colors->theme->extend->colors)) {
                            $primaryBase = $colors->theme->extend->colors->primary ?? '#4F46E5';
                            $secondaryBase = $colors->theme->extend->colors->secondary ?? '#0EA5E9';
                            $accentBase = $colors->theme->extend->colors->accent ?? '#F59E0B';
                        } else {
                            throw new Exception('Invalid color structure');
                        }
                    } catch (Exception $e) {
                        $primaryBase = '#4F46E5';
                        $secondaryBase = '#0EA5E9';
                        $accentBase = '#F59E0B';
                    }
                } else {
                    $primaryBase = '#4F46E5';
                    $secondaryBase = '#0EA5E9';
                    $accentBase = '#F59E0B';
                }
            } else {
                $primaryBase = '#4F46E5';
                $secondaryBase = '#0EA5E9';
                $accentBase = '#F59E0B';
            }
            
            // Generate color palettes
            $primaryPalette = self::generateColorPalette($primaryBase);
            $secondaryPalette = self::generateColorPalette($secondaryBase);
            $accentPalette = self::generateColorPalette($accentBase);
            
            // Output CSS variables
            foreach ($primaryPalette as $shade => $color) {
                echo "        --primary-{$shade}: {$color};\n";
            }
            foreach ($secondaryPalette as $shade => $color) {
                echo "        --secondary-{$shade}: {$color};\n";
            }
            foreach ($accentPalette as $shade => $color) {
                echo "        --accent-{$shade}: {$color};\n";
            }
            ?>
        }
        
        /* Primary Color Classes */
        <?php foreach (array_keys($primaryPalette) as $shade): ?>
        .bg-primary-<?php echo $shade; ?> { background-color: var(--primary-<?php echo $shade; ?>); }
        .text-primary-<?php echo $shade; ?> { color: var(--primary-<?php echo $shade; ?>); }
        .border-primary-<?php echo $shade; ?> { border-color: var(--primary-<?php echo $shade; ?>); }
        <?php endforeach; ?>
        
        /* Secondary Color Classes */
        <?php foreach (array_keys($secondaryPalette) as $shade): ?>
        .bg-secondary-<?php echo $shade; ?> { background-color: var(--secondary-<?php echo $shade; ?>); }
        .text-secondary-<?php echo $shade; ?> { color: var(--secondary-<?php echo $shade; ?>); }
        .border-secondary-<?php echo $shade; ?> { border-color: var(--secondary-<?php echo $shade; ?>); }
        <?php endforeach; ?>
        
        /* Accent Color Classes */
        <?php foreach (array_keys($accentPalette) as $shade): ?>
        .bg-accent-<?php echo $shade; ?> { background-color: var(--accent-<?php echo $shade; ?>); }
        .text-accent-<?php echo $shade; ?> { color: var(--accent-<?php echo $shade; ?>); }
        .border-accent-<?php echo $shade; ?> { border-color: var(--accent-<?php echo $shade; ?>); }
        <?php endforeach; ?>
        
        /* Legacy support */
        .bg-primary { background-color: var(--primary-500); }
        .bg-secondary { background-color: var(--secondary-500); }
        .bg-accent { background-color: var(--accent-500); }
        .text-primary { color: var(--primary-500); }
        .text-secondary { color: var(--secondary-500); }
        .text-accent { color: var(--accent-500); }
        .border-primary { border-color: var(--primary-500); }
        .border-secondary { border-color: var(--secondary-500); }
        .border-accent { border-color: var(--accent-500); }

        /* Base styles */
        body {
            font-family: <?php echo "'" . ($templateData['body_font_family'] ?? 'Inter') . "', sans-serif"; ?>;
            line-height: <?php echo ($templateData['line_height'] ?? '1.5'); ?>;
            color: #111827;
        }
        
        <?php if (!$hideControls): ?>
        /* Block wrapper with hover effect */
        .block-wrapper { 
            position: relative; 
            margin: 0; 
            transition: all 0.2s ease; 
            border: 2px dashed transparent; 
            border-radius: 0; 
            padding: 0; 
        }
        .block-wrapper:hover { 
            border-color: #3b82f6; 
            background-color: rgba(59, 130, 246, 0.02); 
        } 
        .block-wrapper:hover .block-controls { 
            display: flex; 
        }
        
        /* Block edit controls */
        #edit-drawer { z-index: 10000; }
        .block-controls {
            display: none; 
            position: absolute; 
            top: 8px; 
            right: 8px; 
            background: rgba(255, 255, 255, 0.95); 
            backdrop-filter: blur(10px); 
            -webkit-backdrop-filter: blur(10px); 
            border: 1px solid rgba(229, 231, 235, 0.8); 
            border-radius: 6px; 
            padding: 4px; 
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); 
            z-index: 10000; 
            transition: all 0.2s ease;
        }
        .block-controls:hover { background: rgba(255, 255, 255, 1); box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15); }
        .block-controls button { margin: 0 1px; padding: 8px; background: none; border: none; cursor: pointer; transition: all 0.2s ease; border-radius: 4px; color: #6b7280; }
        .block-controls button:hover { background: rgba(59, 130, 246, 0.1); color: #3b82f6; transform: scale(1.05); }
        .block-controls button:active { transform: scale(0.95); }
        .block-controls button.block-delete:hover { background: rgba(239, 68, 68, 0.1); color: #ef4444; }
        .block-controls button.block-edit:hover { background: rgba(16, 185, 129, 0.1); color: #10b981; }
        
        /* Dynamic Drop Zones */
        .drop-zone {
            height: 60px; 
            margin: 8px 0; 
            border: 2px dashed #d1d5db; 
            border-radius: 8px; 
            background-color: #f9fafb; 
            display: none; 
            align-items: center; 
            justify-content: center; 
            transition: all 0.3s ease; 
            opacity: 0;
        }
        .drop-zone.active { display: flex; opacity: 1; border-color: #3b82f6; background-color: #eff6ff; }
        .drop-zone.drag-over { border-color: #1d4ed8; background-color: #dbeafe; transform: scale(1.02); }
        .drop-zone-text { color: #6b7280; font-size: 14px; display: flex; align-items: center; gap: 8px; }
        .main-drop-area {
            min-height: 200px; 
            margin: 16px 0; 
            border: 2px dashed #d1d5db; 
            border-radius: 12px; 
            background-color: #f9fafb; 
            display: flex; 
            align-items: center; 
            justify-content: center; 
            transition: all 0.3s ease;
        }
        .main-drop-area.drag-over { border-color: #3b82f6; background-color: #eff6ff; transform: scale(1.01); }
        
        /* Page sections for template preview */
        <?php if ($isTemplatePage): ?>
        .page-section {
            border: 2px dashed transparent;
            border-radius: 8px;
            margin: 16px 0;
            transition: all 0.2s ease;
        }
        .page-section:hover {
            border-color: #10b981;
            background-color: rgba(16, 185, 129, 0.02);
        }
        .page-section-header {
            background: rgba(16, 185, 129, 0.1);
            border: 1px solid rgba(16, 185, 129, 0.3);
            border-radius: 6px;
            padding: 8px 16px;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        .page-section-title {
            font-weight: 600;
            color: #047857;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .page-actions {
            display: flex;
            gap: 8px;
        }
        .page-action-btn {
            padding: 4px 8px;
            background: rgba(255, 255, 255, 0.8);
            border: 1px solid rgba(16, 185, 129, 0.3);
            border-radius: 4px;
            color: #047857;
            text-decoration: none;
            font-size: 12px;
            transition: all 0.2s ease;
        }
        .page-action-btn:hover {
            background: white;
            border-color: #047857;
        }
        <?php endif; ?>
        <?php else: ?>
        .block-wrapper { margin: 0; }
        .block-controls { display: none !important; }
        .drop-zone { display: none !important; }
        .main-drop-area { display: none !important; }
        <?php endif; ?>

        /* Custom CSS from template */
        <?php if (!empty($templateData['custom_css'])): ?>
        <?php echo $templateData['custom_css']; ?>
        <?php endif; ?>
        
        /* Custom CSS from blocks */
        <?php
        if (!empty($blocks)) {
            foreach ($blocks as $block) {
                if (!empty($block['css'])) {
                    echo "/* Block ID: {$block['block_id']} */\n";
                    echo $block['css'] . "\n\n";
                }
            }
        }
        ?>

        /* Add these styles to the CSS section in preview.php */

/* Block update loading overlay */
.block-loading-overlay {
    position: absolute;
    inset: 0;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 50;
    border-radius: 4px;
    backdrop-filter: blur(2px);
}

/* Loading spinner animation */
.loading-spinner {
    display: inline-block;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: 3px solid rgba(59, 130, 246, 0.2);
    border-top-color: rgba(59, 130, 246, 0.9);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Loading pulse animation for content */
.loading-pulse {
    animation: pulse 1.5s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Enhanced notification styles */
.notification-toast {
    animation: slideIn 0.3s ease-out forwards;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    max-width: 350px;
}

@keyframes slideIn {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

.notification-toast.success {
    background-color: #d1fae5;
    border-left: 4px solid #10b981;
    color: #065f46;
}

.notification-toast.error {
    background-color: #fee2e2;
    border-left: 4px solid #ef4444;
    color: #b91c1c;
}

.notification-toast.info {
    background-color: #dbeafe;
    border-left: 4px solid #3b82f6;
    color: #1e40af;
}
    </style>
    
</head>
<body>
    <div class="page-content">
        <?php if (!$hideControls): ?>
        <!-- Top drop zone -->
        <div class="drop-zone" data-position="0">
            <div class="drop-zone-text">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M12 5v14m-7-7h14"/>
                </svg>
                Drop block here
            </div>
        </div>
        <?php endif; ?>
        
        <?php if (!empty($blocks)): ?>
            <?php 
            $currentPageId = null;
            foreach ($blocks as $index => $block): 
                // Template preview with multiple pages
                if ($isTemplatePage && isset($block['page_id']) && $currentPageId !== $block['page_id']):
                    // Close previous page section if exists
                    if ($currentPageId !== null): ?>
                    </div> <!-- Close previous page section -->
                    <?php endif; ?>
                    
                    <!-- Start new page section -->
                    <div class="page-section" data-page-id="<?php echo $block['page_id']; ?>">
                        <?php if (!$hideControls): ?>
                        <div class="page-section-header">
                            <div class="page-section-title">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z"/>
                                    <path d="M14 2v4a2 2 0 0 0 2 2h4"/>
                                </svg>
                                <?php echo htmlspecialchars($block['page_name']); ?>
                                <span class="text-xs text-gray-500">(<?php echo htmlspecialchars($block['page_slug']); ?>)</span>
                            </div>
                            <div class="page-actions">
                                <a href="<?php echo BASE_URL; ?>/pages/view?id=<?php echo $block['page_id']; ?>" target="_blank" class="page-action-btn">Edit Page</a>
                                <a href="<?php echo BASE_URL; ?>/pages/preview?id=<?php echo $block['page_id']; ?>" target="_blank" class="page-action-btn">Preview</a>
                            </div>
                        </div>
                        <?php endif; ?>
                    
                    <?php $currentPageId = $block['page_id']; ?>
                <?php endif; ?>
                
                <!-- Block content -->
                <div class="block-wrapper section-container relative"
                     <?php if (!$hideControls): ?>
                     data-block-id="<?php echo $block['id']; ?>"
                     data-position="<?php echo $block['position'] ?? ($index + 1); ?>"
                     data-block-type="<?php echo $block['type']; ?>"
                     data-original-block-id="<?php echo $block['block_id']; ?>"
                     data-json-code="<?php echo htmlspecialchars($block['json_code']); ?>"
                     data-block-classes="<?php echo htmlspecialchars($block['classes']); ?>"
                     data-block-name="<?php echo htmlspecialchars($block['name']); ?>"
                     data-section="section<?php echo $block['id']; ?>"
                     data-tmpl-code="<?php echo htmlspecialchars($block['tmpl_code'] ?? ''); ?>"
                     <?php endif; ?>>
                    <?php if (!$hideControls): ?>
                    <div class="block-controls absolute top-0 right-0 flex space-x-1 bg-white p-1 rounded shadow">
                        <button class="block-edit p-1 text-gray-600 hover:text-green-600" title="Edit Block"
                                data-drawer-target="edit-drawer" data-drawer-show="edit-drawer" data-drawer-placement="right" aria-controls="edit-drawer">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                            </svg>
                        </button>
                        <button class="block-move-up p-1 text-gray-600 hover:text-blue-600" title="Move Block Up">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18" />
                            </svg>
                        </button>
                        <button class="block-move-down p-1 text-gray-600 hover:text-blue-600" title="Move Block Down">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3" />
                            </svg>
                        </button>
                        <button class="block-delete p-1 text-gray-600 hover:text-red-600" title="Delete Block">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                            </svg>
                        </button>
                    </div>
                    <?php endif; ?>
                    
                    <?php echo $block['content']; ?>
                </div>
 
                <?php if (!$hideControls): ?>
                <!-- Drop zone after each block -->
                <div class="drop-zone" data-position="<?php echo ($block['position'] ?? ($index + 1)) + 0.5; ?>">
                    <div class="drop-zone-text">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M12 5v14m-7-7h14"/>
                        </svg>
                        Drop block here
                    </div>
                </div>
                <?php endif; ?>
            <?php endforeach; ?>
            
            <?php if ($isTemplatePage && $currentPageId !== null): ?>
            </div> <!-- Close last page section -->
            <?php endif; ?>
        <?php endif; ?>
 
        <?php if (!$hideControls): ?>
        <!-- Main Drop Area (when no blocks or at the end) -->
        <div id="mainDropArea" class="main-drop-area">
            <div class="text-center text-gray-500">
                <div class="flex items-center justify-center flex-col py-10">
                    <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="0.75" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-layout-template-icon lucide-layout-template">
                        <rect width="18" height="7" x="3" y="3" rx="1"/>
                        <rect width="9" height="7" x="3" y="14" rx="1"/>
                        <rect width="5" height="7" x="16" y="14" rx="1"/>
                    </svg>
                    <p>Drag and drop blocks here</p>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>
 
    <?php if (!$hideControls): ?>
    <!-- Edit Drawer -->
    <div id="edit-drawer" class="fixed top-0 right-0 h-screen overflow-hidden flex flex-col transition-transform translate-x-full bg-white w-98 dark:bg-gray-800" tabindex="-1" aria-labelledby="drawer-right-label">
        <h5 id="drawer-right-label" class="flex-shrink-0 p-4 border-b inline-flex items-center mb-4 text-base font-semibold text-gray-500 dark:text-gray-400">
            <svg class="w-4 h-4 me-2.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                <path d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5ZM9.5 4a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3ZM12 15H8a1 1 0 0 1 0-2h1v-3H8a1 1 0 0 1 0-2h2a1 1 0 0 1 1 1v4h1a1 1 0 0 1 0 2Z"/>
            </svg>
            Edit Block
        </h5>
        <button type="button" data-drawer-hide="edit-drawer" aria-controls="edit-drawer" class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 absolute top-2.5 end-2.5 inline-flex items-center justify-center dark:hover:bg-gray-600 dark:hover:text-white">
            <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"/>
            </svg>
            <span class="sr-only">Close menu</span>
        </button>
        <div class="flex-1 overflow-y-auto p-4">
            <form id="blockEditForm">
                <div id="formFields" class="space-y-4">
                    <p class="text-gray-500 text-center py-4">Select a block to edit its content</p>
                </div>
            </form>
        </div>
        <div class="flex-shrink-0 p-4 border-t pt-4 space-y-4 border-t border-gray-200 dark:border-gray-700">
            <div class="flex space-x-2">
                <button id="saveChanges" type="button" class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800 flex-1">
                    Save Changes
                </button>
                <button id="cancelChanges" type="button" data-drawer-hide="edit-drawer" class="py-2.5 px-5 text-sm font-medium text-gray-900 focus:outline-none bg-white rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-100 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700">
                    Cancel
                </button>
            </div>
        </div>
    </div>
    <?php endif; ?>
 
    <!-- Block Dependencies JS -->
    <?php foreach ($dependenciesJS as $jsUrl): ?>
    <script src="<?php echo htmlspecialchars($jsUrl); ?>"></script>
    <?php endforeach; ?>
 
    <?php if (!$hideControls): ?>
    <!-- Flowbite JS -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/flowbite/2.2.1/flowbite.min.js"></script>
    
    <script>
        // Define BASE_URL for JavaScript 
        const BASE_URL = '<?php echo BASE_URL; ?>';

        // Log the base URL for debugging
        console.log('BASE_URL:', BASE_URL);

        // Initialize global tracking variables
        window.currentEditingBlock = null;
        window.processingBlocks = {};
        window.dropZoneInitialized = false;
        window.dragActive = false;

        // Initialize blocks functionality on load
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM Content Loaded - Initializing blocks...');
            initializeBlocks();
            setupBlockControls();
        });

        // Initialize drag and drop zones
        function initializeBlocks() {
            console.log('Setting up drop zones and message listeners...');
            if (!window.dropZoneInitialized) {
                initializeDropZones();
                window.dropZoneInitialized = true;
            }
        }

        function initializeDropZones() {
            console.log('Setting up drop zones');
            
            const dropZones = document.querySelectorAll('.drop-zone');
            const mainDropArea = document.getElementById('mainDropArea');
            
            // Listen for messages from parent window for drag and drop
            window.addEventListener('message', function(event) {
                console.log('Received message from parent:', event.data);
                
                if (event.data.type === 'dragStart') {
                    showDropZones();
                }
                if (event.data.type === 'dragEnd') {
                    hideDropZones();
                }
                
                if (event.data.type === 'addBlock') {
                    const normalizedData = normalizeBlockData(event.data.blockData);
                    if (normalizedData) {
                        addNewBlock(normalizedData);
                    }
                }
            });

            // Setup drop zones
            dropZones.forEach(dropZone => {
                setupDropZone(dropZone);
            });
            
            // Setup main drop area
            if (mainDropArea) {
                setupDropZone(mainDropArea, true);
            }
            
            // Global drag events
            document.addEventListener('dragover', function(e) {
                e.preventDefault();
                if (!window.dragActive) {
                    showDropZones();
                    window.dragActive = true;
                }
            });
            
            document.addEventListener('dragleave', function(e) {
                if (!e.relatedTarget || e.relatedTarget.nodeName === 'HTML') {
                    hideDropZones();
                    window.dragActive = false;
                }
            });
            
            document.addEventListener('drop', function(e) {
                hideDropZones();
                window.dragActive = false;
            });
        }

        // Normalize block data format
        function normalizeBlockData(data) {
            console.log('Normalizing block data:', data);
            
            if (data && data.id) {
                return data;
            }
            
            let normalized = {};
            
            if (data && data.blockId) {
                console.log('Converting blockId to id:', data.blockId);
                normalized.id = data.blockId;
                if (data.blockType) normalized.type = data.blockType;
                if (data.html) normalized.html = data.html;
                return normalized;
            }
            
            console.error('Unable to normalize block data:', data);
            return null;
        }

        function setupDropZone(dropZone, isMainArea = false) {
            dropZone.addEventListener('dragover', (e) => {
                e.preventDefault();
                dropZone.classList.add('drag-over');
            });

            dropZone.addEventListener('dragenter', (e) => {
                e.preventDefault();
                dropZone.classList.add('drag-over');
            });

            dropZone.addEventListener('dragleave', (e) => {
                e.preventDefault();
                dropZone.classList.remove('drag-over');
            });

            dropZone.addEventListener('drop', (e) => {
                e.preventDefault();
                e.stopPropagation();
                dropZone.classList.remove('drag-over');
                
                const position = isMainArea ? getNextPosition() : parseFloat(dropZone.dataset.position);
                processDropEvent(e, position);
            });
        }

        function showDropZones() {
            const dropZones = document.querySelectorAll('.drop-zone');
            dropZones.forEach(zone => {
                zone.classList.add('active');
            });
        }

        function hideDropZones() {
            const dropZones = document.querySelectorAll('.drop-zone');
            dropZones.forEach(zone => {
                zone.classList.remove('active', 'drag-over');
            });
            
            const mainDropArea = document.getElementById('mainDropArea');
            if (mainDropArea) {
                mainDropArea.classList.remove('drag-over');
            }
        }

        function getNextPosition() {
            const blocks = Array.from(document.querySelectorAll('.block-wrapper'));
            if (blocks.length === 0) return 1;
            
            const maxPosition = Math.max(...blocks.map(block => 
                parseFloat(block.getAttribute('data-position') || '0')
            ));
            return Math.floor(maxPosition) + 1;
        }

        function processDropEvent(e, targetPosition = null) {
            try {
                console.log('Processing drop event at position:', targetPosition);
                
                const jsonData = e.dataTransfer.getData('application/json');
                const textData = e.dataTransfer.getData('text/plain');
                
                let blockData;
                
                if (jsonData && jsonData.trim() !== '') {
                    const parsed = JSON.parse(jsonData);
                    blockData = normalizeBlockData(parsed);
                } else if (textData && textData.trim() !== '') {
                    try {
                        const parsed = JSON.parse(textData);
                        blockData = normalizeBlockData(parsed);
                    } catch (innerError) {
                        if (textData.includes('id')) {
                            const idMatch = textData.match(/id["|']?\s*:\s*["|']?([^,"']+)/i);
                            if (idMatch && idMatch[1]) {
                                blockData = { id: idMatch[1] };
                            }
                        }
                    }
                }
                
                if (blockData && blockData.id) {
                    console.log('Processing valid block data:', blockData);
                    
                    // Notify parent window about drop
                    window.parent.postMessage({
                        type: 'blockDropped',
                        blockId: blockData.id,
                        position: {
                            x: e.clientX,
                            y: e.clientY
                        }
                    }, '*');
                    
                    // Add block at specific position
                    addNewBlock(blockData, targetPosition);
                } else {
                    console.error('No valid block data found in drop event');
                }
            } catch (err) {
                console.error('Error handling block drop:', err);
            }
        }

        function addNewBlock(blockData, targetPosition = null) {
            console.log('Adding new block with data:', blockData, 'at position:', targetPosition);
            
            if (!blockData || !blockData.id) {
                console.error('Invalid block data, missing ID:', blockData);
                return;
            }
            
            const blockId = blockData.id;
            const requestId = Date.now() + '_' + Math.random();
            
            if (!window.processingBlocks) {
                window.processingBlocks = {};
            }
            
            if (window.processingBlocks[requestId]) {
                console.warn('Identical request already being processed');
                return;
            }
            
            window.processingBlocks[requestId] = true;
            
            // Calculate position
            let position;
            if (targetPosition !== null) {
                position = targetPosition;
            } else {
                position = getNextPosition();
            }
            
            const pageId = document.getElementById('page-id')?.value;
            if (!pageId) {
                console.error('Missing page ID');
                delete window.processingBlocks[requestId];
                return;
            }

            const requestData = {
                page_id: pageId,
                block_id: blockId,
                position: position
            };
            
            console.log('Sending request with data:', requestData);

            // Use correct API endpoint
            fetch('<?php echo BASE_URL; ?>/modules/pages_blocks/create_block.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(requestData)
            })
            .then(response => response.json())
            .then(data => {
                console.log('Response data:', data);
                delete window.processingBlocks[requestId];
                
                if (data.status === 'success') {
                    console.log('Block added successfully, reloading page...');
                    location.reload();
                } else {
                    console.error('Failed to add block:', data);
                    alert('Failed to add new block: ' + (data.message || 'Unknown error'));
                }
            })
            .catch((error) => {
                console.error('Error adding block:', error);
                alert('Failed to add new block. Please try again.');
                delete window.processingBlocks[requestId];
            });
        }

        // IMPROVED: Block position update function
        function updateBlockPosition(blockId, newPosition, blockElement) {
            // Validate inputs
            if (!blockId || newPosition === undefined || newPosition === null) {
                console.error('Invalid parameters for updateBlockPosition', { blockId, newPosition });
                showNotification('Error: Missing required parameters for position update', 'error');
                return;
            }

            // Disable the block temporarily to prevent multiple clicks
            if (blockElement) {
                blockElement.style.opacity = '0.7';
                blockElement.style.pointerEvents = 'none';
            }
            
            // Visual feedback that something is happening
            const loadingIndicator = document.createElement('div');
            loadingIndicator.className = 'absolute inset-0 bg-white bg-opacity-50 flex items-center justify-center z-50';
            loadingIndicator.innerHTML = `
                <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            `;
            if (blockElement) {
                blockElement.appendChild(loadingIndicator);
            }
            
            console.log(`Updating block ${blockId} to position ${newPosition}`);
            
            // For debugging
            const currentUrl = window.location.href;
            console.log('Current URL:', currentUrl);
            
            // Ensure position is a number and properly formatted
            const formattedPosition = parseFloat(newPosition).toFixed(2);
            
            // Use direct URL to the update_block_position.php endpoint
            const apiUrl = BASE_URL + '/modules/pages_blocks/update_block_position.php';
            console.log('Using API URL:', apiUrl);
            
            // Make the API request using direct JSON
            fetch(apiUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    block_id: blockId,
                    position: formattedPosition
                })
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                console.log('Position update response:', data);
                
                if (data.success) {
                    // Update the position attribute in the DOM immediately
                    if (blockElement) {
                        blockElement.setAttribute('data-position', formattedPosition);
                    }
                    
                    showNotification('Block position updated! Refreshing...', 'success');
                    
                    // Reload page after a brief delay to reflect changes
                    setTimeout(() => {
                        location.reload();
                    }, 1000);
                } else {
                    showNotification('Failed to update position: ' + (data.message || 'Unknown error'), 'error');
                    // Re-enable the block
                    if (blockElement) {
                        blockElement.style.opacity = '1';
                        blockElement.style.pointerEvents = 'auto';
                        if (loadingIndicator && loadingIndicator.parentNode === blockElement) {
                            loadingIndicator.remove();
                        }
                    }
                }
            })
            .catch(error => {
                console.error('Error updating position:', error);
                showNotification('Error updating position: ' + error.message, 'error');
                
                // Re-enable the block
                if (blockElement) {
                    blockElement.style.opacity = '1';
                    blockElement.style.pointerEvents = 'auto';
                    if (loadingIndicator && loadingIndicator.parentNode === blockElement) {
                        loadingIndicator.remove();
                    }
                }
            });
        }

        // Block editing functionality from old_preview.php
        function generateFormFromJSON(jsonCode, blockClasses = '') {
            const formFields = document.getElementById('formFields');
            formFields.innerHTML = '';

            try {
                const data = JSON.parse(jsonCode || '{}');

                // Add classes field at the top
                addClassesField(formFields, blockClasses);

                if (Object.keys(data).length === 0) {
                    const noDataMessage = document.createElement('p');
                    noDataMessage.className = 'text-gray-500 text-center py-4';
                    noDataMessage.textContent = 'No other editable fields found for this block.';
                    formFields.appendChild(noDataMessage);
                    return;
                }

                // Add separator
                const separator = document.createElement('div');
                separator.className = 'border-t border-gray-200 my-4';
                formFields.appendChild(separator);

                // Create a cleaner, section-based UI
                generateSectionBasedForm(data, formFields);

            } catch (error) {
                console.error('Error parsing JSON:', error);
                addClassesField(formFields, blockClasses);
                
                const errorDiv = document.createElement('div');
                errorDiv.className = 'p-4 mb-4 text-sm text-red-800 rounded-lg bg-red-50';
                errorDiv.setAttribute('role', 'alert');
                errorDiv.textContent = 'Error parsing block data. Please check the JSON format.';
                formFields.appendChild(errorDiv);
            }
        }

        function addClassesField(container, blockClasses = '') {
            const classesContainer = document.createElement('div');
            classesContainer.className = 'mb-6 bg-white border border-gray-200 rounded-lg overflow-hidden';
            
            const classesHeader = document.createElement('div');
            classesHeader.className = 'px-4 py-3 bg-gradient-to-r from-purple-50 to-pink-50 border-b border-gray-200';
            classesHeader.innerHTML = `
                <div class="flex items-center space-x-2">
                    <div class="w-2 h-2 bg-purple-500 rounded-full"></div>
                    <h3 class="text-sm font-semibold text-gray-800">Block Classes</h3>
                    <span class="text-xs text-gray-500 bg-white px-2 py-1 rounded-full">CSS Classes</span>
                </div>
            `;
            
            const classesContent = document.createElement('div');
            classesContent.className = 'p-4';
            
            const classesFieldContainer = document.createElement('div');
            classesFieldContainer.className = 'field-container';
            
            const classesLabel = document.createElement('label');
            classesLabel.className = 'block text-sm font-medium text-gray-700 mb-2';
            classesLabel.textContent = 'CSS Classes';
            classesLabel.setAttribute('for', 'block_classes');
            
            const classesInput = document.createElement('input');
            classesInput.type = 'text';
            classesInput.className = 'w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-colors';
            classesInput.name = 'block_classes';
            classesInput.id = 'block_classes';
            classesInput.value = blockClasses || '';
            classesInput.placeholder = 'e.g., container mx-auto py-8';
            classesInput.setAttribute('data-field-path', 'block_classes');
            
            const helpText = document.createElement('p');
            helpText.className = 'mt-1 text-xs text-gray-500';
            helpText.textContent = 'Add CSS classes to customize the block styling. Separate multiple classes with spaces.';
            
            classesFieldContainer.appendChild(classesLabel);
            classesFieldContainer.appendChild(classesInput);
            classesFieldContainer.appendChild(helpText);
            classesContent.appendChild(classesFieldContainer);
            
            classesContainer.appendChild(classesHeader);
            classesContainer.appendChild(classesContent);
            container.appendChild(classesContainer);
        }

        function generateSectionBasedForm(data, container) {
            Object.entries(data).forEach(([sectionKey, sectionValue]) => {
                const sectionContainer = document.createElement('div');
                sectionContainer.className = 'mb-6 bg-white border border-gray-200 rounded-lg overflow-hidden';
                
                const sectionHeader = document.createElement('div');
                sectionHeader.className = 'px-4 py-3 bg-gradient-to-r from-blue-50 to-indigo-50 border-b border-gray-200 cursor-pointer';
                sectionHeader.innerHTML = `
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-2">
                            <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
                            <h3 class="text-sm font-semibold text-gray-800">${formatSectionLabel(sectionKey)}</h3>
                            <span class="text-xs text-gray-500 bg-white px-2 py-1 rounded-full">${getFieldTypeLabel(sectionValue)}</span>
                        </div>
                        <svg class="w-4 h-4 text-gray-500 transform transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </div>
                `;
                
                const sectionContent = document.createElement('div');
                sectionContent.className = 'section-content p-4 space-y-4';
                
                if (Array.isArray(sectionValue)) {
                    createArraySection(sectionContent, sectionKey, sectionValue, sectionKey);
                } else if (typeof sectionValue === 'object' && sectionValue !== null) {
                    createObjectSection(sectionContent, sectionKey, sectionValue, sectionKey);
                } else {
                    createSimpleField(sectionContent, sectionKey, sectionValue, sectionKey);
                }
                
                sectionHeader.addEventListener('click', function() {
                    const isHidden = sectionContent.style.display === 'none';
                    sectionContent.style.display = isHidden ? 'block' : 'none';
                    const arrow = sectionHeader.querySelector('svg');
                    arrow.style.transform = isHidden ? 'rotate(0deg)' : 'rotate(-180deg)';
                });
                
                sectionContainer.appendChild(sectionHeader);
                sectionContainer.appendChild(sectionContent);
                container.appendChild(sectionContainer);
            });
        }

        function createSimpleField(container, key, value, fieldPath) {
            const fieldContainer = document.createElement('div');
            fieldContainer.className = 'field-container';
            
            const label = document.createElement('label');
            label.className = 'block text-sm font-medium text-gray-700 mb-2';
            label.textContent = formatFieldLabel(key);
            
            const input = createInputElement(value, fieldPath);
            
            fieldContainer.appendChild(label);
            fieldContainer.appendChild(input);
            container.appendChild(fieldContainer);
        }

        function createInputElement(value, fieldPath) {
            let input;
            
            if (typeof value === 'boolean') {
                const container = document.createElement('div');
                container.className = 'flex items-center space-x-2';
                
                input = document.createElement('input');
                input.type = 'checkbox';
                input.className = 'w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500';
                input.checked = value;
                
                const label = document.createElement('span');
                label.className = 'text-xs text-gray-600';
                label.textContent = 'Enable';
                
                container.appendChild(input);
                container.appendChild(label);
                
                input.name = fieldPath;
                input.setAttribute('data-field-path', fieldPath);
                input.id = `field_${fieldPath.replace(/\./g, '_').replace(/\[|\]/g, '_')}`;
                
                return container;
            } else if (typeof value === 'number') {
                input = document.createElement('input');
                input.type = 'number';
                input.value = value;
            } else if (typeof value === 'string' && value.length > 100) {
                input = document.createElement('textarea');
                input.rows = 3;
                input.value = value;
            } else {
                input = document.createElement('input');
                input.type = 'text';
                input.value = value || '';
            }
            
            input.className = 'w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors';
            input.name = fieldPath;
            input.setAttribute('data-field-path', fieldPath);
            input.id = `field_${fieldPath.replace(/\./g, '_').replace(/\[|\]/g, '_')}`;
            
            return input;
        }

        function createObjectSection(container, key, value, fieldPath) {
            const fieldsContainer = document.createElement('div');
            fieldsContainer.className = 'grid grid-cols-1 gap-4';
            
            Object.entries(value).forEach(([propKey, propValue]) => {
                if (Array.isArray(propValue)) {
                    createArraySection(fieldsContainer, propKey, propValue, `${fieldPath}.${propKey}`);
                } else if (typeof propValue === 'object' && propValue !== null) {
                    const nestedSection = document.createElement('div');
                    nestedSection.className = 'p-3 bg-gray-50 rounded-lg border border-gray-200';
                    
                    const nestedTitle = document.createElement('h5');
                    nestedTitle.className = 'text-sm font-medium text-gray-700 mb-3';
                    nestedTitle.textContent = formatFieldLabel(propKey);
                    
                    nestedSection.appendChild(nestedTitle);
                    createObjectSection(nestedSection, propKey, propValue, `${fieldPath}.${propKey}`);
                    fieldsContainer.appendChild(nestedSection);
                } else {
                    createSimpleField(fieldsContainer, propKey, propValue, `${fieldPath}.${propKey}`);
                }
            });
            
            container.appendChild(fieldsContainer);
        }

        function createArraySection(container, key, value, fieldPath) {
            const arrayHeader = document.createElement('div');
            arrayHeader.className = 'flex items-center justify-between mb-4 p-3 bg-gray-50 rounded-lg';
            arrayHeader.innerHTML = `
                <div>
                    <h4 class="text-sm font-medium text-gray-800">${formatSectionLabel(key)} Items</h4>
                    <p class="text-xs text-gray-500">${value.length} item(s) currently</p>
                </div>
                <button type="button" class="add-item-btn bg-blue-500 hover:bg-blue-600 text-white px-3 py-1.5 rounded-md text-xs font-medium flex items-center space-x-1 transition-colors">
                    <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                    </svg>
                    <span>Add New Item</span>
                </button>
            `;
            
            container.appendChild(arrayHeader);
            
            const itemsContainer = document.createElement('div');
            itemsContainer.className = 'items-container space-y-3';
            itemsContainer.setAttribute('data-array-container', fieldPath);
            
            value.forEach((item, index) => {
                createArrayItem(itemsContainer, item, `${fieldPath}[${index}]`, index, fieldPath);
            });
            
            container.appendChild(itemsContainer);
            
            arrayHeader.querySelector('.add-item-btn').addEventListener('click', function() {
                const newIndex = itemsContainer.children.length;
                const template = value.length > 0 ? createTemplateObject(value[0]) : {};
                createArrayItem(itemsContainer, template, `${fieldPath}[${newIndex}]`, newIndex, fieldPath);
                
                const counter = arrayHeader.querySelector('.text-xs.text-gray-500');
                counter.textContent = `${itemsContainer.children.length} item(s) currently`;
            });
        }

        function createArrayItem(container, value, fieldPath, index, arrayPath) {
            const itemCard = document.createElement('div');
            itemCard.className = 'item-card bg-white border border-gray-200 rounded-lg overflow-hidden shadow-sm';
            itemCard.setAttribute('data-array-item', fieldPath);
            
            const itemHeader = document.createElement('div');
            itemHeader.className = 'flex items-center justify-between px-4 py-2 bg-gray-50 border-b border-gray-200';
            itemHeader.innerHTML = `
                <div class="flex items-center space-x-2">
                    <div class="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-xs font-medium">
                        ${index + 1}
                    </div>
                    <span class="text-sm font-medium text-gray-700">Item ${index + 1}</span>
                </div>
                <div class="flex items-center space-x-2">
                    <button type="button" class="move-up-btn p-1 text-gray-400 hover:text-blue-500 transition-colors" title="Move Up">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"></path>
                        </svg>
                    </button>
                    <button type="button" class="move-down-btn p-1 text-gray-400 hover:text-blue-500 transition-colors" title="Move Down">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </button>
                    <button type="button" class="remove-item-btn p-1 text-gray-400 hover:text-red-500 transition-colors" title="Remove Item">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                        </svg>
                    </button>
                </div>
            `;
            
            const itemContent = document.createElement('div');
            itemContent.className = 'p-4 space-y-3';
            
            if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
                // Handle nested object - create proper form fields for each property
                Object.entries(value).forEach(([propKey, propValue]) => {
                    if (Array.isArray(propValue)) {
                        // Handle nested arrays within objects
                        createNestedArrayField(itemContent, propKey, propValue, `${fieldPath}.${propKey}`);
                    } else if (typeof propValue === 'object' && propValue !== null) {
                        // Handle nested objects within objects
                        createNestedObjectField(itemContent, propKey, propValue, `${fieldPath}.${propKey}`);
                    } else {
                        // Handle primitive values
                        createFieldInGrid(itemContent, propKey, propValue, `${fieldPath}.${propKey}`);
                    }
                });
            } else if (Array.isArray(value)) {
                // Handle primitive arrays
                createNestedArrayField(itemContent, 'items', value, fieldPath);
            } else {
                // Handle primitive values
                createSimpleField(itemContent, 'value', value, fieldPath);
            }
            
            itemCard.appendChild(itemHeader);
            itemCard.appendChild(itemContent);
            container.appendChild(itemCard);
            
            // Event Listeners
            setupItemControls(itemCard, container, arrayPath);
            
            // Scroll to new item
            setTimeout(() => {
                itemCard.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
            }, 100);
        }

        function createFieldInGrid(container, key, value, fieldPath) {
            const fieldContainer = document.createElement('div');
            fieldContainer.className = 'field-container mb-3';
            
            const label = document.createElement('label');
            label.className = 'block text-xs font-medium text-gray-700 mb-1';
            label.textContent = formatFieldLabel(key);
            label.setAttribute('for', `field_${fieldPath.replace(/\./g, '_').replace(/\[|\]/g, '_')}`);
            
            const input = createInputElement(value, fieldPath);
            
            fieldContainer.appendChild(label);
            fieldContainer.appendChild(input);
            container.appendChild(fieldContainer);
        }

        function createNestedArrayField(container, key, value, fieldPath) {
            const nestedSection = document.createElement('div');
            nestedSection.className = 'nested-array-section p-3 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-200 mb-3';
            
            // Nested Array Header
            const nestedHeader = document.createElement('div');
            nestedHeader.className = 'flex items-center justify-between mb-3';
            nestedHeader.innerHTML = `
                <div>
                    <h5 class="text-sm font-medium text-gray-800">${formatFieldLabel(key)}</h5>
                    <p class="text-xs text-gray-500">${value.length} nested item(s)</p>
                </div>
                <button type="button" class="add-nested-item-btn bg-blue-500 hover:bg-blue-600 text-white px-2 py-1 rounded text-xs font-medium flex items-center space-x-1">
                    <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                    </svg>
                    <span>Add</span>
                </button>
            `;
            
            // Nested Items Container
            const nestedContainer = document.createElement('div');
            nestedContainer.className = 'nested-items-container space-y-2';
            nestedContainer.setAttribute('data-nested-array', fieldPath);
            
            // Add existing nested items
            value.forEach((item, index) => {
                createNestedArrayItem(nestedContainer, item, `${fieldPath}[${index}]`, index, fieldPath);
            });
            
            nestedSection.appendChild(nestedHeader);
            nestedSection.appendChild(nestedContainer);
            container.appendChild(nestedSection);
            
            // Add nested item event
            nestedHeader.querySelector('.add-nested-item-btn').addEventListener('click', function() {
                const newIndex = nestedContainer.children.length;
                const template = value.length > 0 ? createTemplateObject(value[0]) : {};
                createNestedArrayItem(nestedContainer, template, `${fieldPath}[${newIndex}]`, newIndex, fieldPath);
                
                // Update counter
                const counter = nestedHeader.querySelector('.text-xs.text-gray-500');
                counter.textContent = `${nestedContainer.children.length} nested item(s)`;
            });
        }

        function createNestedArrayItem(container, value, fieldPath, index, arrayPath) {
            const nestedItem = document.createElement('div');
            nestedItem.className = 'nested-item bg-white border border-gray-200 rounded p-3';
            nestedItem.setAttribute('data-nested-item', fieldPath);
            
            // Nested Item Header
            const nestedItemHeader = document.createElement('div');
            nestedItemHeader.className = 'flex items-center justify-between mb-2 pb-2 border-b border-gray-100';
            nestedItemHeader.innerHTML = `
                <div class="flex items-center space-x-2">
                    <div class="w-5 h-5 bg-green-100 text-green-600 rounded flex items-center justify-center text-xs font-medium">
                        ${index + 1}
                    </div>
                    <span class="text-xs font-medium text-gray-600">Nested Item ${index + 1}</span>
                </div>
                <button type="button" class="remove-nested-item-btn p-1 text-gray-400 hover:text-red-500 transition-colors">
                    <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            `;
            
            // Nested Item Content
            const nestedItemContent = document.createElement('div');
            nestedItemContent.className = 'space-y-2';
            
            if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
                // Create form fields for each property in the nested object
                Object.entries(value).forEach(([propKey, propValue]) => {
                    createFieldInGrid(nestedItemContent, propKey, propValue, `${fieldPath}.${propKey}`);
                });
            } else {
                // Handle primitive values in nested array
                createFieldInGrid(nestedItemContent, 'value', value, fieldPath);
            }
            
            nestedItem.appendChild(nestedItemHeader);
            nestedItem.appendChild(nestedItemContent);
            container.appendChild(nestedItem);
            
            // Remove nested item event
            nestedItemHeader.querySelector('.remove-nested-item-btn').addEventListener('click', function() {
                if (confirm('Remove this nested item?')) {
                    nestedItem.remove();
                    reindexNestedItems(container, arrayPath);
                    updateNestedCounter(container);
                }
            });
        }

        function createNestedObjectField(container, key, value, fieldPath) {
            const objectSection = document.createElement('div');
            objectSection.className = 'nested-object-section p-3 bg-gray-50 rounded-lg border border-gray-200 mb-3';
            
            const objectHeader = document.createElement('h5');
            objectHeader.className = 'text-sm font-medium text-gray-700 mb-3 pb-2 border-b border-gray-200';
            objectHeader.textContent = formatFieldLabel(key);
            
            const objectContent = document.createElement('div');
            objectContent.className = 'space-y-2';
            
            // Generate fields for object properties
            Object.entries(value).forEach(([propKey, propValue]) => {
                if (Array.isArray(propValue)) {
                    createNestedArrayField(objectContent, propKey, propValue, `${fieldPath}.${propKey}`);
                } else if (typeof propValue === 'object' && propValue !== null) {
                    createNestedObjectField(objectContent, propKey, propValue, `${fieldPath}.${propKey}`);
                } else {
                    createFieldInGrid(objectContent, propKey, propValue, `${fieldPath}.${propKey}`);
                }
            });
            
            objectSection.appendChild(objectHeader);
            objectSection.appendChild(objectContent);
            container.appendChild(objectSection);
        }

        function reindexNestedItems(container, arrayFieldPath) {
            const items = Array.from(container.children);
            items.forEach((item, index) => {
                // Update nested item number display
                const numberBadge = item.querySelector('.w-5.h-5.bg-green-100');
                const titleSpan = item.querySelector('.text-xs.font-medium.text-gray-600');
                if (numberBadge) numberBadge.textContent = index + 1;
                if (titleSpan) titleSpan.textContent = `Nested Item ${index + 1}`;
                
                // Update all field paths
                const inputs = item.querySelectorAll('input, textarea, select');
                inputs.forEach(input => {
                    const oldPath = input.getAttribute('data-field-path');
                    if (oldPath) {
                        const newPath = oldPath.replace(/\[\d+\]/, `[${index}]`);
                        input.setAttribute('data-field-path', newPath);
                        input.name = newPath;
                        input.id = `field_${newPath.replace(/\./g, '_').replace(/\[|\]/g, '_')}`;
                    }
                });
                
                item.setAttribute('data-nested-item', `${arrayFieldPath}[${index}]`);
            });
        }

        function updateNestedCounter(container) {
            const section = container.closest('.nested-array-section');
            const counter = section.querySelector('.text-xs.text-gray-500');
            if (counter) {
                counter.textContent = `${container.children.length} nested item(s)`;
            }
        }

        function setupItemControls(itemCard, container, arrayPath) {
            const removeBtn = itemCard.querySelector('.remove-item-btn');
            const moveUpBtn = itemCard.querySelector('.move-up-btn');
            const moveDownBtn = itemCard.querySelector('.move-down-btn');
            
            // Remove Item
            if (removeBtn) {
                removeBtn.addEventListener('click', function() {
                    if (confirm('Are you sure you want to remove this item?')) {
                        itemCard.remove();
                        reindexArrayItems(container, arrayPath);
                        updateItemCounter(container);
                    }
                });
            }
            
            // Move Up
            if (moveUpBtn) {
                moveUpBtn.addEventListener('click', function() {
                    const prevItem = itemCard.previousElementSibling;
                    if (prevItem) {
                        container.insertBefore(itemCard, prevItem);
                        reindexArrayItems(container, arrayPath);
                    }
                });
            }
            
            // Move Down
            if (moveDownBtn) {
                moveDownBtn.addEventListener('click', function() {
                    const nextItem = itemCard.nextElementSibling;
                    if (nextItem) {
                        container.insertBefore(nextItem, itemCard);
                        reindexArrayItems(container, arrayPath);
                    }
                });
            }
        }

        function updateItemCounter(container) {
            const section = container.closest('.mb-6');
            const counter = section.querySelector('.text-xs.text-gray-500');
            if (counter) {
                counter.textContent = `${container.children.length} item(s) currently`;
            }
        }

        function reindexArrayItems(container, arrayFieldPath) {
            const items = Array.from(container.children);
            items.forEach((item, index) => {
                // Update item number display
                const numberBadge = item.querySelector('.w-6.h-6.bg-blue-100');
                const titleSpan = item.querySelector('.text-sm.font-medium.text-gray-700');
                if (numberBadge) numberBadge.textContent = index + 1;
                if (titleSpan) titleSpan.textContent = `Item ${index + 1}`;
                
                // Update all field paths
                const inputs = item.querySelectorAll('input, textarea, select');
                inputs.forEach(input => {
                    const oldPath = input.getAttribute('data-field-path');
                    if (oldPath) {
                        const newPath = oldPath.replace(/\[\d+\]/, `[${index}]`);
                        input.setAttribute('data-field-path', newPath);
                        input.name = newPath;
                    }
                });
                
                item.setAttribute('data-array-item', `${arrayFieldPath}[${index}]`);
            });
        }

        function formatSectionLabel(key) {
            return key.charAt(0).toUpperCase() + key.slice(1)
                .replace(/([A-Z])/g, ' $1')
                .replace(/_/g, ' ');
        }

        function formatFieldLabel(key) {
            return key.charAt(0).toUpperCase() + key.slice(1)
                .replace(/([A-Z])/g, ' $1')
                .replace(/_/g, ' ');
        }

        function getFieldTypeLabel(value) {
            if (Array.isArray(value)) {
                return `${value.length} items`;
            } else if (typeof value === 'object' && value !== null) {
                return `${Object.keys(value).length} fields`;
            } else {
                return typeof value;
            }
        }

        function createTemplateObject(obj) {
            if (Array.isArray(obj)) {
                return [];
            } else if (typeof obj === 'object' && obj !== null) {
                const template = {};
                Object.keys(obj).forEach(key => {
                    const value = obj[key];
                    if (Array.isArray(value)) {
                        template[key] = [];
                    } else if (typeof value === 'object' && value !== null) {
                        template[key] = createTemplateObject(value);
                    } else if (typeof value === 'number') {
                        template[key] = 0;
                    } else if (typeof value === 'boolean') {
                        template[key] = false;
                    } else {
                        template[key] = '';
                    }
                });
                return template;
            } else if (typeof obj === 'number') {
                return 0;
            } else if (typeof obj === 'boolean') {
                return false;
            } else {
                return '';
            }
        }

        // FIXED: Form data collection functions
        function collectNestedFormData() {
            const result = {};
            const form = document.getElementById('blockEditForm');
            
            if (!form) {
                console.error('Block edit form not found!');
                return null;
            }
            
            const inputs = form.querySelectorAll('input, textarea, select');
            console.log(`Found ${inputs.length} form inputs to process`);

            try {
                inputs.forEach(input => {
                    const fieldPath = input.getAttribute('data-field-path') || input.name;
                    if (!fieldPath) {
                        console.warn('Skipping input without field path:', input);
                        return;
                    }

                    let value;
                    if (input.type === 'checkbox') {
                        value = input.checked;
                    } else if (input.type === 'number') {
                        value = input.value === '' ? null : parseFloat(input.value);
                    } else {
                        value = input.value;
                    }

                    console.log(`Processing field: ${fieldPath} = ${value}`);
                    setNestedValue(result, fieldPath, value);
                });

                const cleanedResult = cleanEmptyValues(result);
                console.log('Final collected data:', cleanedResult);
                return cleanedResult;
            } catch (error) {
                console.error('Error collecting form data:', error);
                showNotification('Error collecting form data. Please check your inputs.', 'error');
                return null;
            }
        }

        function setNestedValue(obj, path, value) {
            const keys = path.split(/[\.\[\]]/).filter(key => key !== '');
            let current = obj;

            for (let i = 0; i < keys.length - 1; i++) {
                const key = keys[i];
                const nextKey = keys[i + 1];

                if (!current[key]) {
                    current[key] = /^\d+$/.test(nextKey) ? [] : {};
                }
                current = current[key];
            }

            const lastKey = keys[keys.length - 1];
            if (/^\d+$/.test(lastKey)) {
                const index = parseInt(lastKey);
                if (!Array.isArray(current)) {
                    console.warn('Expected array but found:', typeof current, 'for path:', path);
                    return;
                }
                current[index] = value;
            } else {
                current[lastKey] = value;
            }
        }

        function cleanEmptyValues(obj) {
            if (Array.isArray(obj)) {
                return obj.map(item => cleanEmptyValues(item)).filter(item => 
                    item !== null && item !== undefined && item !== ''
                );
            } else if (typeof obj === 'object' && obj !== null) {
                const cleaned = {};
                Object.keys(obj).forEach(key => {
                    const cleanedValue = cleanEmptyValues(obj[key]);
                    if (cleanedValue !== null && cleanedValue !== undefined && cleanedValue !== '') {
                        if (Array.isArray(cleanedValue) && cleanedValue.length === 0) {
                            cleaned[key] = cleanedValue;
                        } else if (typeof cleanedValue === 'object' && Object.keys(cleanedValue).length === 0) {
                            return;
                        } else {
                            cleaned[key] = cleanedValue;
                        }
                    }
                });
                return cleaned;
            }
            return obj;
        }

        // Edit functionality
        document.addEventListener('click', function(e) {
            if (e.target.closest('.block-edit')) {
                const blockWrapper = e.target.closest('.block-wrapper');
                prepareEditDrawer(blockWrapper);
            }
        });

        function prepareEditDrawer(blockWrapper) {
            const blockId = blockWrapper.getAttribute('data-block-id');
            const originalBlockId = blockWrapper.getAttribute('data-original-block-id');
            const jsonCode = blockWrapper.getAttribute('data-json-code');
            const blockClasses = blockWrapper.getAttribute('data-block-classes');
            const blockName = blockWrapper.getAttribute('data-block-name');

            window.currentEditingBlock = {
                id: blockId,
                originalBlockId: originalBlockId,
                classes: blockClasses,
                element: blockWrapper
            };

            document.getElementById('drawer-right-label').innerHTML = `
                <svg class="w-4 h-4 me-2.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5ZM9.5 4a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3ZM12 15H8a1 1 0 0 1 0-2h1v-3H8a1 1 0 0 1 0-2h2a1 1 0 0 1 1 1v4h1a1 1 0 0 1 0 2Z"/>
                </svg>
                Edit ${blockName}
            `;

            generateFormFromJSON(jsonCode, blockClasses);
        }

        // Enhanced Save changes functionality - updates without page reload
        document.getElementById('saveChanges').addEventListener('click', function() {
            if (!window.currentEditingBlock) {
                alert('No block is currently being edited.');
                return;
            }

            // Collect form data
            const jsonData = collectNestedFormData();
            if (jsonData === null) {
                return;
            }

            // Get classes from input
            const classesInput = document.getElementById('block_classes');
            const blockClasses = classesInput ? classesInput.value : '';

            // Remove classes from the JSON data (it's handled separately)
            delete jsonData.block_classes;

            // Prepare the request data
            const blockId = window.currentEditingBlock.id;
            const jsonString = JSON.stringify(jsonData);
            const blockElement = window.currentEditingBlock.element;
            const tmplCode = blockElement.getAttribute('data-tmpl-code');

            // Set button to loading state
            const saveBtn = document.getElementById('saveChanges');
            const originalText = saveBtn.textContent;
            saveBtn.textContent = 'Saving...';
            saveBtn.disabled = true;

            // Add loading overlay to the block being edited
            const loadingOverlay = document.createElement('div');
            loadingOverlay.className = 'absolute inset-0 flex items-center justify-center bg-white bg-opacity-80 z-50';
            loadingOverlay.id = 'block-loading-overlay';
            loadingOverlay.innerHTML = `
                <div class="text-center">
                    <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
                    <p class="mt-2 text-sm text-gray-700 font-medium">Updating block...</p>
                </div>
            `;
            
            if (blockElement) {
                blockElement.style.position = 'relative';
                blockElement.appendChild(loadingOverlay);
            }

            // Log what we're sending
            console.log('Sending update with data:', {
                block_id: blockId,
                json_code: jsonString,
                classes: blockClasses
            });

            // Use direct URL to the update_block.php endpoint
            const apiUrl = BASE_URL + '/modules/pages_blocks/update_block.php';
            
            // Make the API request as direct JSON
            fetch(apiUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    block_id: blockId,
                    json_code: jsonString,
                    classes: blockClasses
                })
            })
            .then(response => {
                // Check if response is OK
                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                console.log('Save response:', data);
                
                if (data.success) {
                    // Close the drawer manually
                    const drawer = document.getElementById('edit-drawer');
                    drawer.classList.add('translate-x-full');
                    
                    // Process template with updated data to get new HTML
                    // Use either direct processing or another API call
                    return processTemplateWithData(tmplCode, jsonString, blockClasses, blockElement);
                } else {
                    // Show error notification
                    showNotification('Failed to update block: ' + (data.message || 'Unknown error'), 'error');
                    throw new Error('Update failed: ' + (data.message || 'Unknown error'));
                }
            })
            .then(updatedContent => {
                if (updatedContent && blockElement) {
                    // Update the block attributes
                    blockElement.setAttribute('data-json-code', jsonString);
                    blockElement.setAttribute('data-block-classes', blockClasses);
                    
                    // Update the HTML content
                    updateBlockContent(blockElement, updatedContent, blockClasses);
                    
                    // Show success notification
                    showNotification('Block updated successfully!', 'success');
                }
            })
            .catch((error) => {
                console.error('Error updating block:', error);
                showNotification('Failed to update block: ' + error.message, 'error');
            })
            .finally(() => {
                // Reset button state
                saveBtn.textContent = originalText;
                saveBtn.disabled = false;
                
                // Remove loading overlay
                if (blockElement) {
                    const overlay = blockElement.querySelector('#block-loading-overlay');
                    if (overlay) {
                        overlay.remove();
                    }
                }
            });
        });

        /**
         * Process a template with data using the same API as on the server
         */
        function processTemplateWithData(template, jsonData, classes, blockElement) {
            return new Promise((resolve, reject) => {
                // Option 1: Use the process_template AJAX endpoint
                fetch(window.location.href, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: new URLSearchParams({
                        action: 'process_template',
                        template: template,
                        json_data: jsonData
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        resolve(data.html);
                    } else {
                        reject(new Error(data.message || 'Template processing failed'));
                    }
                })
                .catch(error => {
                    console.error('Error processing template:', error);
                    reject(error);
                });
            });
        }

        /**
         * Update block content without reloading the page
         */
        function updateBlockContent(blockElement, newContent, classes) {
            // Save the controls element to reattach later
            const controls = blockElement.querySelector('.block-controls');
            if (controls) {
                controls.remove();
            }
            
            // Update the HTML content
            blockElement.innerHTML = newContent;
            
            // Reattach controls
            if (controls) {
                blockElement.appendChild(controls);
            }
            
            // Add classes to the block
            if (classes) {
                // Find the first HTML element in the content
                const firstElement = blockElement.querySelector('*');
                if (firstElement) {
                    const currentClasses = firstElement.getAttribute('class') || '';
                    const newClasses = addClassesToElement(currentClasses, classes);
                    firstElement.setAttribute('class', newClasses);
                }
            }
            
            // Re-initialize any scripts
            executeBlockScripts(blockElement);
        }
                                                ``
        /**
         * Add classes to an element, avoiding duplicates
         */
        function addClassesToElement(currentClasses, newClasses) {
            const currentClassList = currentClasses.split(' ').filter(c => c.trim());
            const newClassList = newClasses.split(' ').filter(c => c.trim());
            
            // Add new classes, avoiding duplicates
            newClassList.forEach(cls => {
                if (!currentClassList.includes(cls)) {
                    currentClassList.push(cls);
                }
            });
            
            return currentClassList.join(' ');
        }

        /**
         * Execute any scripts in the updated block
         */
        function executeBlockScripts(blockElement) {
            // Find any script tags in the block
            const scripts = blockElement.querySelectorAll('script');
            scripts.forEach(oldScript => {
                const newScript = document.createElement('script');
                
                // Copy all attributes
                Array.from(oldScript.attributes).forEach(attr => {
                    newScript.setAttribute(attr.name, attr.value);
                });
                
                // Copy the content
                newScript.textContent = oldScript.textContent;
                
                // Replace the old script with the new one
                oldScript.parentNode.replaceChild(newScript, oldScript);
            });
        }

        // Block control functionality 
        function setupBlockControls() {
            const blockWrappers = document.querySelectorAll('.block-wrapper');

            blockWrappers.forEach(wrapper => {
                const controls = wrapper.querySelector('.block-controls');
                
                wrapper.addEventListener('mouseenter', () => {
                    if (controls) controls.style.display = 'flex';
                });
                
                wrapper.addEventListener('mouseleave', () => {
                    if (controls) controls.style.display = 'none';
                });
            });

            // FIXED: Move up functionality
            document.querySelectorAll('.block-move-up').forEach(button => {
                button.addEventListener('click', function() {
                    const blockWrapper = this.closest('.block-wrapper');
                    const blockId = blockWrapper.getAttribute('data-block-id');
                    
                    if (!blockId) {
                        console.error('Block is missing data-block-id attribute');
                        showNotification('Error: Could not identify block', 'error');
                        return;
                    }
                    
                    // Find all blocks and their positions
                    const allBlocks = Array.from(document.querySelectorAll('.block-wrapper'));
                    const currentIndex = allBlocks.indexOf(blockWrapper);
                    
                    if (currentIndex > 0) {
                        const prevBlock = allBlocks[currentIndex - 1];
                        const prevPosition = parseFloat(prevBlock.getAttribute('data-position') || '0');
                        const currentPosition = parseFloat(blockWrapper.getAttribute('data-position') || '0');
                        
                        // Calculate new position - use simple formula to avoid issues
                        // Just place it one position before the previous block
                        const newPosition = Math.max(1, prevPosition - 1);
                        
                        console.log('Moving block up:', {
                            blockId,
                            currentPosition,
                            prevPosition,
                            newPosition
                        });
                        
                        updateBlockPosition(blockId, newPosition, blockWrapper);
                    } else {
                        // Already at the top
                        showNotification('Block is already at the top', 'info');
                    }
                });
            });

            // FIXED: Move down functionality
            document.querySelectorAll('.block-move-down').forEach(button => {
                button.addEventListener('click', function() {
                    const blockWrapper = this.closest('.block-wrapper');
                    const blockId = blockWrapper.getAttribute('data-block-id');
                    
                    if (!blockId) {
                        console.error('Block is missing data-block-id attribute');
                        showNotification('Error: Could not identify block', 'error');
                        return;
                    }
                    
                    // Find all blocks and their positions
                    const allBlocks = Array.from(document.querySelectorAll('.block-wrapper'));
                    const currentIndex = allBlocks.indexOf(blockWrapper);
                    
                    if (currentIndex < allBlocks.length - 1) {
                        const nextBlock = allBlocks[currentIndex + 1];
                        const nextPosition = parseFloat(nextBlock.getAttribute('data-position') || '0');
                        const currentPosition = parseFloat(blockWrapper.getAttribute('data-position') || '0');
                        
                        // Calculate new position - use simple formula to avoid issues
                        // Just place it one position after the next block
                        const newPosition = nextPosition + 1;
                        
                        console.log('Moving block down:', {
                            blockId,
                            currentPosition,
                            nextPosition,
                            newPosition
                        });
                        
                        updateBlockPosition(blockId, newPosition, blockWrapper);
                    } else {
                        // Already at the bottom
                        showNotification('Block is already at the bottom', 'info');
                    }
                });
            });

            // FIXED: Delete functionality
            document.querySelectorAll('.block-delete').forEach(button => {
                button.addEventListener('click', function() {
                    const blockWrapper = this.closest('.block-wrapper');
                    const blockId = blockWrapper.getAttribute('data-block-id');
                    
                    if (confirm('Do you really want to delete this block?')) {
                        const deleteBtn = this;
                        deleteBtn.disabled = true;
                        deleteBtn.innerHTML = '<svg class="animate-spin h-4 w-4" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4" fill="none"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>';
                        
                        fetch('<?php echo BASE_URL; ?>/modules/pages_blocks/delete_block.php', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({
                                block_id: blockId
                            })
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                showNotification('Block deleted successfully!', 'success');
                                const associatedDropZone = blockWrapper.nextElementSibling;
                                blockWrapper.remove();
                                if (associatedDropZone && associatedDropZone.classList.contains('drop-zone')) {
                                    associatedDropZone.remove();
                                }
                            } else {
                                showNotification('Failed to delete the block: ' + (data.message || 'Unknown error'), 'error');
                                deleteBtn.disabled = false;
                                deleteBtn.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" /></svg>';
                            }
                        })
                        .catch((error) => {
                            console.error('Error:', error);
                            showNotification('Failed to delete the block. Please try again.', 'error');
                            deleteBtn.disabled = false;
                            deleteBtn.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" /></svg>';
                        });
                    }
                });
            });
        }

        // Improved notification function
        function showNotification(message, type = 'info') {
            // Remove any existing notifications
            const existingNotifications = document.querySelectorAll('.notification-toast');
            existingNotifications.forEach(notif => notif.remove());
            
            const notification = document.createElement('div');
            notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg notification-toast ${
                type === 'success' ? 'text-green-800 bg-green-100 border-l-4 border-green-500' : 
                type === 'error' ? 'text-red-800 bg-red-100 border-l-4 border-red-500' : 
                'text-blue-800 bg-blue-100 border-l-4 border-blue-500'
            }`;
            
            const icon = type === 'success' ? 
                '<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>' :
                type === 'error' ?
                '<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg>' :
                '<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>';
            
            notification.innerHTML = `
                <div class="flex items-center">
                    <div class="flex-shrink-0 mr-3">
                        ${icon}
                    </div>
                    <div>
                        <p class="font-medium">${message}</p>
                    </div>
                </div>
            `;
            
            document.body.appendChild(notification);
            
            // Add enter animation
            notification.style.transition = 'all 0.3s ease-in-out';
            notification.style.transform = 'translateX(100%)';
            notification.style.opacity = '0';
            
            setTimeout(() => {
                notification.style.transform = 'translateX(0)';
                notification.style.opacity = '1';
            }, 10);
            
            // Auto remove after delay
            setTimeout(() => {
                notification.style.transform = 'translateX(100%)';
                notification.style.opacity = '0';
                setTimeout(() => notification.remove(), 300);
            }, 4000);
        }
    </script>
    <?php endif; ?>
 
    <?php if (!$hideControls): ?>
    <input type="hidden" id="<?php echo $isTemplatePage ? 'template-id' : 'page-id'; ?>" value="<?php echo htmlspecialchars($pageData['id'] ?? $templateData['id'] ?? ''); ?>">
    <?php endif; ?>
     
    <script>
    <?php 
        // Custom JS from template
        if (!empty($templateData['custom_js'])) {
            echo $templateData['custom_js'] . "\n\n";
        }
        
        // Custom JS from blocks
        if (!empty($blocks)) {
            foreach ($blocks as $block) {
                if (!empty($block['js'])) {
                    echo "/* Block ID: {$block['block_id']} */\n";
                    echo $block['js'] . "\n\n";
                }
            }
        }
    ?>
    </script>
 </body>
 </html>
        <?php
        return ob_get_clean();
    }
}

// Initialize the helpers
PreviewHelper::init();
?>