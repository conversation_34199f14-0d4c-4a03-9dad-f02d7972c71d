<?php
/**
 * Table Helper Functions
 * This file contains helper functions to make using the TableComponent easier
 */

require_once __DIR__ . '/TableComponent.php';

/**
 * Create a formatted status badge
 * 
 * @param string $status The status value
 * @param array $options The options for customizing the badge
 * @return string The HTML for the badge
 */
function formatStatusBadge($status, $options = []) {
    $defaultOptions = [
        'active' => [
            'bg' => 'bg-green-100',
            'text' => 'text-green-800'
        ],
        'inactive' => [
            'bg' => 'bg-red-100',
            'text' => 'text-red-800'
        ],
        'default' => [
            'bg' => 'bg-gray-100',
            'text' => 'text-gray-800'
        ]
    ];
    
    $options = array_merge($defaultOptions, $options);
    
    $statusLower = strtolower($status);
    $config = isset($options[$statusLower]) ? $options[$statusLower] : $options['default'];
    
    return '<span class="px-2 py-1 text-xs rounded-full ' . $config['bg'] . ' ' . $config['text'] . '">' . 
           htmlspecialchars($status) . 
           '</span>';
}

/**
 * Format a date to a readable format
 * 
 * @param string $date The date string
 * @param string $format The desired format (default: 'M d, Y H:i')
 * @return string The formatted date
 */
function formatDate($date, $format = 'M d, Y H:i') {
    if (empty($date)) {
        return '-';
    }
    
    return date($format, strtotime($date));
}

/**
 * Create a formatted image with text
 * 
 * @param array $row The data row
 * @param array $options The options for customizing the output
 * @return string The HTML for the image with text
 */
function formatImageWithText($row, $options = []) {
    $defaultOptions = [
        'imageKey' => 'image',
        'titleKey' => 'name',
        'subtitleKey' => 'id',
        'subtitlePrefix' => 'ID: ',
        'placeholderText' => '',
        'imageClass' => 'h-full w-full object-cover rounded',
        'defaultImage' => 'https://placehold.co/400x200/000000/f3f4f6?text=Placeholder'
    ];
    
    $options = array_merge($defaultOptions, $options);
    
    $image = isset($row[$options['imageKey']]) && !empty($row[$options['imageKey']]) 
           ? $row[$options['imageKey']] 
           : $options['defaultImage'];
           
    if ($options['placeholderText'] && strpos($image, 'placehold.co') !== false) {
        $placeholderText = urlencode($options['placeholderText']);
        $image = str_replace('text=Placeholder', 'text=' . $placeholderText, $image);
    }
    
    $title = isset($row[$options['titleKey']]) ? $row[$options['titleKey']] : '';
    $subtitle = isset($row[$options['subtitleKey']]) ? $options['subtitlePrefix'] . $row[$options['subtitleKey']] : '';
    
    $output = '<div class="flex items-center space-x-3">';
    $output .= '<div class="h-12 w-20 flex-shrink-0"><img src="' . htmlspecialchars($image) . '" ';
    $output .= 'alt="' . htmlspecialchars($title) . '" ';
    $output .= 'class="' . $options['imageClass'] . '"></div>';
    $output .= '<div>';
    $output .= '<div class="font-semibold text-gray-900">' . htmlspecialchars($title) . '</div>';
    
    if ($subtitle) {
        $output .= '<div class="text-sm text-gray-500">' . htmlspecialchars($subtitle) . '</div>';
    }
    
    $output .= '</div>';
    $output .= '</div>';
    
    return $output;
}

/**
 * Format a list of categories/tags as badges
 * 
 * @param array $items The array of category/tag items
 * @param array $options The options for customizing the badges
 * @return string The HTML for the badges
 */
function formatCategoryBadges($items, $options = []) {
    $defaultOptions = [
        'nameKey' => 'name',
        'bgClass' => 'bg-blue-100',
        'textClass' => 'text-blue-800',
        'emptyText' => '-'
    ];
    
    $options = array_merge($defaultOptions, $options);
    
    if (empty($items)) {
        return '<span class="text-gray-500">' . $options['emptyText'] . '</span>';
    }
    
    $output = '<div class="flex flex-wrap gap-2">';
    
    foreach ($items as $item) {
        $name = isset($item[$options['nameKey']]) ? $item[$options['nameKey']] : '';
        
        if ($name) {
            $output .= '<span class="px-2 py-1 text-xs font-medium rounded-full ' . 
                      $options['bgClass'] . ' ' . $options['textClass'] . '">';
            $output .= htmlspecialchars($name);
            $output .= '</span>';
        }
    }
    
    $output .= '</div>';
    
    return $output;
}

/**
 * Create a button or link
 * 
 * @param string $label The button/link label
 * @param string $url The URL (for links)
 * @param array $options The options for customizing the button/link
 * @return string The HTML for the button/link
 */
function createButton($label, $url = '#', $options = []) {
    $defaultOptions = [
        'type' => 'link', // 'link' or 'button'
        'class' => 'text-blue-600 hover:text-blue-900',
        'icon' => '',
        'attributes' => []
    ];
    
    $options = array_merge($defaultOptions, $options);
    
    $attrStr = '';
    foreach ($options['attributes'] as $attr => $value) {
        $attrStr .= ' ' . $attr . '="' . htmlspecialchars($value) . '"';
    }
    
    $output = '';
    
    if ($options['type'] === 'button') {
        $output .= '<button type="button" class="' . $options['class'] . '"' . $attrStr . '>';
    } else {
        $output .= '<a href="' . htmlspecialchars($url) . '" class="' . $options['class'] . '"' . $attrStr . '>';
    }
    
    if ($options['icon']) {
        $output .= '<i data-lucide="' . $options['icon'] . '" class="' . 
                  (empty($label) ? '' : 'mr-1 ') . 'w-4 h-4 inline-block"></i>';
    }
    
    if (!empty($label)) {
        $output .= htmlspecialchars($label);
    }
    
    if ($options['type'] === 'button') {
        $output .= '</button>';
    } else {
        $output .= '</a>';
    }
    
    return $output;
}

/**
 * Create a standard action button
 * 
 * @param string $action The action type ('edit', 'view', 'delete', etc.)
 * @param mixed $id The ID of the record
 * @param array $options The options for customizing the button
 * @return string The HTML for the action button
 */
function createActionButton($action, $id, $options = []) {
    $defaultConfigs = [
        'edit' => [
            'label' => 'Edit',
            'icon' => 'edit',
            'class' => 'text-blue-600 hover:text-blue-900 mr-3',
            'attributes' => []
        ],
        'view' => [
            'label' => 'View',
            'icon' => 'eye',
            'class' => 'text-green-600 hover:text-green-900 mr-3',
            'attributes' => []
        ],
        'delete' => [
            'label' => 'Delete',
            'icon' => 'trash-2',
            'class' => 'text-red-600 hover:text-red-900',
            'attributes' => [
                'onclick' => 'return confirm("Are you sure you want to delete this item?")'
            ]
        ]
    ];
    
    if (!isset($defaultConfigs[$action])) {
        return '';
    }
    
    $config = array_merge($defaultConfigs[$action], $options);
    
    if ($action === 'delete') {
        $output = '<form method="POST" class="inline">';
        $output .= '<input type="hidden" name="delete" value="1">';
        $output .= '<input type="hidden" name="id" value="' . $id . '">';
        $output .= '<button type="submit" class="' . $config['class'] . '"';
        
        foreach ($config['attributes'] as $attr => $value) {
            $output .= ' ' . $attr . '="' . htmlspecialchars($value) . '"';
        }
        
        $output .= '>';
        
        if (!empty($config['icon'])) {
            $output .= '<i data-lucide="' . $config['icon'] . '" class="w-4 h-4 inline-block mr-1"></i>';
        }
        
        $output .= htmlspecialchars($config['label']);
        $output .= '</button>';
        $output .= '</form>';
        
        return $output;
    }
    
    $url = '#';
    switch ($action) {
        case 'edit':
            $url = '?edit=' . $id;
            break;
        case 'view':
            $url = 'view/' . $id;
            break;
    }
    
    return createButton($config['label'], $url, [
        'class' => $config['class'],
        'icon' => $config['icon'],
        'attributes' => $config['attributes']
    ]);
}

/**
 * Create action buttons for a table row
 * 
 * @param array $row The data row
 * @param array $actions The array of actions to display
 * @param array $options The options for customizing the actions
 * @return string The HTML for the action buttons
 */
function createRowActions($row, $actions, $options = []) {
    $defaultOptions = [
        'idKey' => 'id',
        'containerClass' => 'flex space-x-3 justify-center'
    ];
    
    $options = array_merge($defaultOptions, $options);
    $id = isset($row[$options['idKey']]) ? $row[$options['idKey']] : null;
    
    if (!$id) {
        return '';
    }
    
    $output = '<div class="' . $options['containerClass'] . '">';
    
    foreach ($actions as $action => $actionOptions) {
        if (is_string($action) && in_array($action, ['edit', 'view', 'delete'])) {
            $output .= createActionButton($action, $id, $actionOptions);
        } else if (is_array($actionOptions)) {
            // Custom action
            $actionUrl = isset($actionOptions['url']) ? $actionOptions['url'] : '#';
            if (strpos($actionUrl, '{id}') !== false) {
                $actionUrl = str_replace('{id}', $id, $actionUrl);
            }
            
            $actionLabel = isset($actionOptions['label']) ? $actionOptions['label'] : 'Action';
            $actionIcon = isset($actionOptions['icon']) ? $actionOptions['icon'] : '';
            $actionClass = isset($actionOptions['class']) ? $actionOptions['class'] : 'text-indigo-600 hover:text-indigo-900 mr-3';
            $actionAttrs = isset($actionOptions['attributes']) ? $actionOptions['attributes'] : [];
            
            $output .= createButton($actionLabel, $actionUrl, [
                'class' => $actionClass,
                'icon' => $actionIcon,
                'attributes' => $actionAttrs
            ]);
        }
    }
    
    $output .= '</div>';
    
    return $output;
}