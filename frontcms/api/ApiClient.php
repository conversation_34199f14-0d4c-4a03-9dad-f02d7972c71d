<?php
/**
 * ApiClient.php
 * Main API client handler for making requests to the external API
 */

class ApiClient {
    private $baseUrl;
    private $apiKey;
    private $apiToken;
    private $verifySSL;

    /**
     * Constructor to initialize API credentials
     * @param string $baseUrl - Base URL for the API
     */
    public function __construct($baseUrl) {
        $this->baseUrl = rtrim($baseUrl, '/');
        $this->apiKey = defined('API_KEY') ? API_KEY : null;
        $this->apiToken = defined('X_API_TOKEN') ? X_API_TOKEN : null;
        $this->verifySSL = false; // For development only
    }

    /**
     * Make a GET request to the API
     * 
     * @param string $endpoint - API endpoint to call
     * @param array $params - Query parameters (optional)
     * @return array - Response data
     */
    public function get($endpoint, $params = []) {
        $endpoint = trim($endpoint, '/');
        if (!empty($params)) {
            $endpoint .= '?' . http_build_query($params);
        }
        return $this->makeRequest('GET', $endpoint);
    }

    /**
     * Make a POST request to the API
     * 
     * @param string $endpoint - API endpoint to call
     * @param array $data - Data to send in request body
     * @return array - Response data
     */
    public function post($endpoint, $data = []) {
        $endpoint = trim($endpoint, '/');
        return $this->makeRequest('POST', $endpoint, $data);
    }

    /**
     * Make a PUT request to the API
     * 
     * @param string $endpoint - API endpoint to call
     * @param array $data - Data to send in request body
     * @return array - Response data
     */
    public function put($endpoint, $data = []) {
        $endpoint = trim($endpoint, '/');
        return $this->makeRequest('PUT', $endpoint, $data);
    }

    /**
     * Make a DELETE request to the API
     * 
     * @param string $endpoint - API endpoint to call
     * @return array - Response data
     */
    public function delete($endpoint) {
        $endpoint = trim($endpoint, '/');
        return $this->makeRequest('DELETE', $endpoint);
    }

    /**
     * Make an HTTP request to the API
     * 
     * @param string $method - HTTP method (GET, POST, PUT, DELETE)
     * @param string $endpoint - API endpoint to call
     * @param array $data - Data to send in request body (optional)
     * @return array - Response data
     * @throws Exception if the API request fails
     */
    private function makeRequest($method, $endpoint, $data = null) {
        $url = $this->baseUrl . '/' . $endpoint;
        $ch = curl_init($url);

        $headers = [
            'Content-Type: application/json',
            'Accept: application/json'
        ];

        if ($this->apiToken) {
            $headers[] = 'Authorization: Bearer ' . $this->apiToken;
        }

        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

        if ($data !== null) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        }

        $response = curl_exec($ch);
        $error = curl_error($ch);
        $statusCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        
        curl_close($ch);

        if ($error) {
            throw new Exception('API request failed: ' . $error);
        }

        $responseData = json_decode($response, true);
        
        if ($statusCode >= 400) {
            throw new Exception(isset($responseData['message']) ? $responseData['message'] : 'API request failed with status ' . $statusCode);
        }

        return $responseData;
    }
}
