<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/ApiClient.php';

// Set headers for JSON response
header('Content-Type: application/json');

// Initialize response
$response = [
    'status' => 'error',
    'message' => 'Invalid request',
    'url' => null
];

try {
    // Check if file was uploaded
    if (!isset($_FILES['image'])) {
        throw new Exception('No image file uploaded');
    }

    $file = $_FILES['image'];
    
    // Validate file
    if ($file['error'] !== UPLOAD_ERR_OK) {
        throw new Exception('File upload failed');
    }

    // Validate file type
    $allowedTypes = ['image/jpeg', 'image/png', 'image/gif'];
    $finfo = finfo_open(FILEINFO_MIME_TYPE);
    $mimeType = finfo_file($finfo, $file['tmp_name']);
    finfo_close($finfo);

    if (!in_array($mimeType, $allowedTypes)) {
        throw new Exception('Invalid file type. Only JPG, PNG and GIF are allowed.');
    }

    // Create upload directory if it doesn't exist
    $uploadDir = __DIR__ . '/../uploads';
    if (!file_exists($uploadDir)) {
        mkdir($uploadDir, 0777, true);
    }

    // Generate unique filename
    $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
    $filename = uniqid() . '.' . $extension;
    $uploadPath = $uploadDir . '/' . $filename;

    // Move uploaded file
    if (!move_uploaded_file($file['tmp_name'], $uploadPath)) {
        throw new Exception('Failed to save uploaded file');
    }

    // Generate public URL using config constant
    $publicUrl = UPLOAD_URL . '/' . $filename;

    // Return success response
    $response = [
        'status' => 'success',
        'message' => 'File uploaded successfully',
        'url' => $publicUrl
    ];

} catch (Exception $e) {
    http_response_code(400);
    $response = [
        'status' => 'error',
        'message' => $e->getMessage()
    ];
    
    if (defined('DEBUG') && DEBUG) {
        error_log("Upload Error: " . $e->getMessage());
        error_log("Stack Trace: " . $e->getTraceAsString());
    }
}

// Send response
echo json_encode($response);
