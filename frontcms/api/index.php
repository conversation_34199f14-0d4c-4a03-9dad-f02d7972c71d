<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/ApiClient.php';

// Get request method and URI
$method = $_SERVER['REQUEST_METHOD'];
$uri = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
$uri = str_replace('/api/', '', $uri);
$segments = explode('/', $uri);

// Set headers for JSON response
header('Content-Type: application/json');

// Initialize response
$response = [
    'status' => 'error',
    'message' => 'Invalid request',
    'data' => null
];

try {
    // Special handling for upload endpoint
    if ($segments[0] === 'upload') {
        require_once __DIR__ . '/upload.php';
        exit;
    }
    
    // Get module name from first segment
    $moduleName = $segments[0] ?? '';
    
    // Check if module exists
    if (!isset(MODULES[$moduleName])) {
        throw new Exception('Invalid module');
    }
    
    // Convert module name to class name (e.g. block_categories -> BlockCategories)
    $className = str_replace('_', '', ucwords($moduleName, '_'));
    
    // Load module class
    $moduleFile = __DIR__ . "/../modules/{$moduleName}/{$className}.php";
    if (!file_exists($moduleFile)) {
        throw new Exception("Module file not found: {$moduleFile}");
    }
    
    require_once $moduleFile;
    $module = new $className();
    
    // Handle request based on method
    switch ($method) {
        case 'GET':
            if (isset($segments[1])) {
                // Get single item
                $response = $module->get($segments[1]);
            } else {
                // Get list
                $response = $module->getList();
            }
            break;
            
        case 'POST':
            // Create new item
            $data = json_decode(file_get_contents('php://input'), true);
            $response = $module->create($data);
            break;
            
        case 'PUT':
            // Update item
            if (!isset($segments[1])) {
                throw new Exception('ID is required for update');
            }
            $data = json_decode(file_get_contents('php://input'), true);
            $response = $module->update($segments[1], $data);
            break;
            
        case 'DELETE':
            // Delete item
            if (!isset($segments[1])) {
                throw new Exception('ID is required for delete');
            }
            $response = $module->delete($segments[1]);
            break;
            
        default:
            throw new Exception('Method not allowed');
    }
    
} catch (Exception $e) {
    http_response_code(400);
    $response = [
        'status' => 'error',
        'message' => $e->getMessage()
    ];
    
    if (defined('DEBUG') && DEBUG) {
        error_log("API Error: " . $e->getMessage());
        error_log("Stack Trace: " . $e->getTraceAsString());
    }
}

// Send response
echo json_encode($response);
