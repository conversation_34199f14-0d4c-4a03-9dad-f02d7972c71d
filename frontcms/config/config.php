<?php
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Detect environment based on server name
$isLocal = in_array($_SERVER['SERVER_NAME'], ['localhost', '127.0.0.1']);

// Debug mode
define('DEBUG', $isLocal);

// Set base paths based on environment
if ($isLocal) {
    define('BASE_URL', '/frontcms');
    // define('API_BASE_URL_V1', 'http://127.0.0.1:8000/api/v1');
    // define('API_BASE_URL_CMS', 'http://127.0.0.1:8000/api/cms');
    define('API_BASE_URL_V1', 'http://**********:8000/api/v1');
    define('API_BASE_URL_CMS', 'http://**********:8000/api/cms');
} else {
    // Live environment
    define('BASE_URL', '');  // Empty because we're at domain root
    define('API_BASE_URL_V1', 'http://**********:8000/api/v1');
    define('API_BASE_URL_CMS', 'http://**********:8000/api/cms');
}

// API Configuration
define('API_KEY', '38fbb795-1135-11f0-8c08-7e463f901988'); // This is the X-API-KEY value
define('X_API_TOKEN', $_SESSION['token']); // This is the Bearer token
define('BEARER_TOKEN', $_SESSION['token']); // Add your Bearer token here

// Common configurations
define('UPLOAD_DIR', __DIR__ . '/../uploads');
define('UPLOAD_URL', BASE_URL . '/uploads');
define('API_PATH', BASE_URL . '/api');

// Available modules configuration
define('MODULES', [
    'block_categories' => [
        'name' => 'Block Categories',
        'endpoint' => 'block_categories'  
    ],
    'template_categories' => [
        'name' => 'Template Categories',
        'endpoint' => 'template_categories'
    ],
    'tags' => [
        'name' => 'Tags',
        'endpoint' => 'tags'
    ],
    'templates' => [
        'name' => 'Templates',
        'endpoint' => 'templates'
    ],
    'pages' => [
        'name' => 'Pages',
        'endpoint' => 'pages'
    ],
    'blocks' => [
        'name' => 'Blocks',
        'endpoint' => 'blocks'
    ],
    'pages_blocks' => [
        'name' => 'Page Blocks',
        'endpoint' => 'page_blocks'
    ]
]);

// Error reporting
error_reporting(E_ALL);
ini_set('display_errors', $isLocal ? 1 : 0);
